<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدير لوحات التحكم الذكي - أكاديمية 7C</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark-gradient: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            --glass-bg: rgba(255, 255, 255, 0.08);
            --glass-border: rgba(255, 255, 255, 0.15);
            --text-primary: #ffffff;
            --text-secondary: #b8c5d6;
            --accent-color: #00d4ff;
            --ai-glow: rgba(0, 212, 255, 0.3);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--dark-gradient);
            color: var(--text-primary);
            overflow-x: hidden;
            position: relative;
            min-height: 100vh;
        }

        /* AI Neural Network Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, var(--ai-glow) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(102, 126, 234, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(118, 75, 162, 0.2) 0%, transparent 50%);
            animation: aiGlow 8s ease-in-out infinite alternate;
            z-index: -2;
        }

        /* Animated Neural Network Pattern */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.1) 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, rgba(102, 126, 234, 0.1) 2px, transparent 2px);
            background-size: 60px 60px;
            animation: neuralNetwork 20s linear infinite;
            z-index: -1;
        }

        @keyframes aiGlow {
            0% { opacity: 0.6; transform: scale(1) rotate(0deg); }
            100% { opacity: 0.9; transform: scale(1.1) rotate(5deg); }
        }

        @keyframes neuralNetwork {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-60px) translateY(-60px); }
        }

        /* Professional Header */
        .header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(30px);
            border-bottom: 1px solid var(--glass-border);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, var(--accent-color) 50%, transparent 100%);
        }

        .header-content {
            max-width: 1600px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-icon {
            width: 60px;
            height: 60px;
            background: var(--primary-gradient);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: 900;
            position: relative;
            box-shadow: 0 8px 25px var(--ai-glow);
            animation: logoGlow 3s ease-in-out infinite alternate;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: var(--primary-gradient);
            border-radius: 18px;
            z-index: -1;
            opacity: 0.7;
            animation: borderGlow 2s linear infinite;
        }

        @keyframes logoGlow {
            0% { box-shadow: 0 8px 25px var(--ai-glow); }
            100% { box-shadow: 0 12px 35px rgba(0, 212, 255, 0.5); }
        }

        @keyframes borderGlow {
            0% { opacity: 0.7; }
            50% { opacity: 1; }
            100% { opacity: 0.7; }
        }

        .logo-text h1 {
            font-size: 1.6rem;
            font-weight: 800;
            background: linear-gradient(45deg, #ffffff, var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.2rem;
        }

        .logo-text p {
            font-size: 0.9rem;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .ai-badge {
            background: var(--success-gradient);
            color: white;
            padding: 0.2rem 0.6rem;
            border-radius: 20px;
            font-size: 0.7rem;
            font-weight: 700;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .ai-assistant-btn {
            background: var(--success-gradient);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 15px rgba(67, 233, 123, 0.3);
        }

        .ai-assistant-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(67, 233, 123, 0.4);
        }

        .ai-assistant-btn .ai-icon {
            animation: rotate 2s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Professional Button Styles */
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            border: none;
            cursor: pointer;
            font-weight: 700;
            font-size: 0.9rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: var(--success-gradient);
            color: white;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        }

        .btn-warning {
            background: var(--warning-gradient);
            color: white;
            box-shadow: 0 4px 15px rgba(67, 233, 123, 0.3);
        }

        .btn-danger {
            background: var(--danger-gradient);
            color: white;
            box-shadow: 0 4px 15px rgba(250, 112, 154, 0.3);
        }

        .btn-glass {
            background: var(--glass-bg);
            color: var(--text-primary);
            border: 1px solid var(--glass-border);
            backdrop-filter: blur(20px);
        }

        .btn-glass:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--accent-color);
            transform: translateY(-2px);
        }

        /* Main Layout */
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 320px 1fr;
            gap: 2rem;
            min-height: calc(100vh - 140px);
        }

        /* Advanced Sidebar */
        .sidebar {
            background: var(--glass-bg);
            backdrop-filter: blur(30px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            padding: 2rem;
            height: fit-content;
            position: sticky;
            top: 120px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .sidebar-section {
            margin-bottom: 2.5rem;
        }

        .sidebar-title {
            font-size: 1.1rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid var(--glass-border);
        }

        .sidebar-title i {
            color: var(--accent-color);
            font-size: 1.2rem;
        }

        .sidebar-item {
            padding: 1rem 1.25rem;
            margin-bottom: 0.75rem;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 1rem;
            border: 1px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .sidebar-item:hover::before {
            left: 100%;
        }

        .sidebar-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--accent-color);
            transform: translateX(5px);
        }

        .sidebar-item.active {
            background: var(--primary-gradient);
            color: white;
            transform: translateX(5px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .sidebar-item i {
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }

        .sidebar-item.active i {
            color: white;
        }

        /* Main Content Area */
        .main-content {
            background: var(--glass-bg);
            backdrop-filter: blur(30px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            padding: 2.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .main-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, var(--accent-color) 50%, transparent 100%);
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2.5rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid var(--glass-border);
        }

        .content-title {
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(45deg, var(--text-primary), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .content-title i {
            color: var(--accent-color);
            font-size: 1.8rem;
        }

        /* AI-Powered Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .dashboard-card {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            cursor: pointer;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(0, 212, 255, 0.1) 50%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
            border-color: var(--accent-color);
        }

        .dashboard-card:hover::before {
            opacity: 1;
        }

        .dashboard-preview {
            width: 100%;
            height: 220px;
            background: var(--primary-gradient);
            position: relative;
            overflow: hidden;
            border-radius: 20px 20px 0 0;
        }

        .dashboard-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect width="35" height="18" x="5" y="8" fill="rgba(255,255,255,0.4)" rx="3"/><rect width="28" height="12" x="45" y="11" fill="rgba(255,255,255,0.3)" rx="2"/><rect width="15" height="8" x="78" y="14" fill="rgba(255,255,255,0.25)" rx="2"/><rect width="42" height="35" x="5" y="32" fill="rgba(255,255,255,0.3)" rx="4"/><rect width="42" height="25" x="52" y="37" fill="rgba(255,255,255,0.2)" rx="3"/><rect width="88" height="18" x="5" y="75" fill="rgba(255,255,255,0.15)" rx="3"/></svg>') no-repeat center;
            background-size: 85%;
        }

        .ai-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            background: var(--success-gradient);
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.7rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.3rem;
            animation: pulse 2s infinite;
        }

        .dashboard-content {
            padding: 1.5rem;
        }

        .dashboard-info h3 {
            font-size: 1.3rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .dashboard-info p {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .dashboard-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.7rem;
            font-weight: 700;
        }

        .status-active {
            background: var(--success-gradient);
            color: white;
        }

        .status-draft {
            background: var(--warning-gradient);
            color: white;
        }

        .status-archived {
            background: var(--danger-gradient);
            color: white;
        }

        .dashboard-actions {
            display: flex;
            gap: 0.75rem;
            justify-content: center;
        }

        .action-btn {
            padding: 0.6rem;
            border-radius: 10px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            font-size: 0.9rem;
            min-width: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn.edit {
            background: var(--warning-gradient);
        }

        .action-btn.delete {
            background: var(--danger-gradient);
        }

        .action-btn.duplicate {
            background: var(--success-gradient);
        }

        .action-btn.ai-optimize {
            background: var(--primary-gradient);
        }

        .action-btn:hover {
            transform: scale(1.1) translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }

        /* AI Features */
        .ai-suggestions {
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            position: relative;
        }

        .ai-suggestions::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            background: linear-gradient(45deg, var(--accent-color), transparent, var(--accent-color));
            border-radius: 16px;
            z-index: -1;
            animation: borderGlow 3s linear infinite;
        }

        .ai-suggestions h4 {
            color: var(--accent-color);
            font-weight: 800;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .ai-suggestions .suggestion-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            border-left: 3px solid var(--accent-color);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 280px 1fr;
            }
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .sidebar {
                position: static;
                order: 2;
            }

            .main-content {
                order: 1;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
        }

        /* Loading and Animation States */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 20px;
            z-index: 100;
        }

        .ai-loader {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(0, 212, 255, 0.3);
            border-top: 3px solid var(--accent-color);
            border-radius: 50%;
            animation: aiSpin 1s linear infinite;
        }

        @keyframes aiSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 2000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: var(--glass-bg);
            backdrop-filter: blur(30px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            padding: 0;
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow: hidden;
            position: relative;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-gradient));
            padding: 2rem;
            border-bottom: 1px solid var(--glass-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 800;
            color: white;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .close-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .modal-body {
            padding: 2rem;
            max-height: calc(90vh - 200px);
            overflow-y: auto;
        }

        .modal-footer {
            padding: 1.5rem 2rem;
            border-top: 1px solid var(--glass-border);
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        /* AI Chat Styles */
        .ai-chat-container {
            max-height: 500px;
            display: flex;
            flex-direction: column;
        }

        .ai-chat-messages {
            flex: 1;
            max-height: 350px;
            overflow-y: auto;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            margin-bottom: 1rem;
        }

        .ai-message, .user-message {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            align-items: flex-start;
        }

        .user-message {
            flex-direction: row-reverse;
        }

        .ai-avatar, .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .ai-avatar {
            background: var(--success-gradient);
            color: white;
        }

        .user-avatar {
            background: var(--primary-gradient);
            color: white;
        }

        .ai-text, .user-text {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 12px;
            max-width: 70%;
            line-height: 1.5;
        }

        .user-text {
            background: var(--primary-gradient);
            color: white;
        }

        .ai-input-container {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .ai-input {
            flex: 1;
            padding: 1rem;
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            background: var(--glass-bg);
            color: white;
            font-size: 0.9rem;
        }

        .ai-input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
        }

        .ai-send-btn {
            padding: 1rem;
            background: var(--success-gradient);
            border: none;
            border-radius: 12px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .ai-send-btn:hover {
            transform: scale(1.05);
        }

        .ai-suggestions-quick {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .ai-quick-btn {
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            color: white;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .ai-quick-btn:hover {
            background: var(--accent-color);
            border-color: var(--accent-color);
        }

        /* Tabs Styles */
        .tabs {
            display: flex;
            border-bottom: 1px solid var(--glass-border);
            margin-bottom: 2rem;
            overflow-x: auto;
        }

        .tab {
            padding: 1rem 1.5rem;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
            color: var(--text-secondary);
            white-space: nowrap;
            font-weight: 600;
        }

        .tab.active {
            color: white;
            border-bottom-color: var(--accent-color);
            background: rgba(0, 212, 255, 0.1);
        }

        .tab:hover:not(.active) {
            color: white;
            background: rgba(255, 255, 255, 0.05);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease-in;
        }

        /* Form Styles */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 700;
            color: white;
            font-size: 0.9rem;
        }

        .form-input {
            width: 100%;
            padding: 1rem;
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            background: var(--glass-bg);
            color: white;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
            background: rgba(255, 255, 255, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 120px;
        }

        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: left 1rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-left: 3rem;
        }

        /* Theme Selector */
        .theme-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .theme-option {
            text-align: center;
            cursor: pointer;
            padding: 1rem;
            border: 2px solid transparent;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .theme-option.active {
            border-color: var(--accent-color);
            background: rgba(0, 212, 255, 0.1);
        }

        .theme-option:hover {
            border-color: var(--glass-border);
        }

        .theme-preview {
            width: 80px;
            height: 50px;
            border-radius: 8px;
            margin: 0 auto 0.5rem;
        }

        .theme-preview.professional {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .theme-preview.modern {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .theme-preview.minimal {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .theme-preview.ai-futuristic {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        /* Color Customization */
        .color-customization {
            margin-top: 2rem;
        }

        .color-customization h4 {
            margin-bottom: 1rem;
            color: white;
            font-weight: 700;
        }

        .color-picker-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .color-picker {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }

        .color-picker label {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .color-input {
            width: 60px;
            height: 60px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .color-input:hover {
            transform: scale(1.1);
        }

        /* Widget Library */
        .widget-library {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .widget-item {
            background: var(--glass-bg);
            border: 2px solid var(--glass-border);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .widget-item:hover {
            border-color: var(--accent-color);
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 212, 255, 0.2);
        }

        .widget-item.selected {
            border-color: var(--accent-color);
            background: rgba(0, 212, 255, 0.1);
        }

        .widget-item.selected::after {
            content: '✓';
            position: absolute;
            top: 10px;
            right: 10px;
            background: var(--accent-color);
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .widget-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--accent-color);
        }

        .widget-name {
            font-weight: 700;
            color: white;
            font-size: 0.9rem;
        }

        /* AI Settings */
        .ai-settings-grid {
            display: grid;
            gap: 1.5rem;
        }

        .ai-setting-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 1.5rem;
            border-left: 4px solid var(--accent-color);
        }

        .ai-toggle {
            display: flex;
            align-items: center;
            gap: 1rem;
            cursor: pointer;
            font-weight: 700;
            color: white;
            margin-bottom: 0.5rem;
        }

        .ai-toggle input {
            display: none;
        }

        .ai-slider {
            width: 50px;
            height: 25px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            position: relative;
            transition: all 0.3s ease;
        }

        .ai-slider::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 21px;
            height: 21px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .ai-toggle input:checked + .ai-slider {
            background: var(--accent-color);
        }

        .ai-toggle input:checked + .ai-slider::before {
            transform: translateX(25px);
        }

        /* Permissions Grid */
        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .permission-group {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 1.5rem;
        }

        .permission-group h5 {
            color: white;
            font-weight: 700;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .permission-group label {
            display: block;
            margin-bottom: 0.75rem;
            color: var(--text-secondary);
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .permission-group label:hover {
            color: white;
        }

        .permission-group input[type="checkbox"] {
            margin-left: 0.5rem;
            accent-color: var(--accent-color);
        }

        /* Performance Indicators */
        .performance-indicators {
            margin: 1rem 0;
        }

        .performance-item {
            margin-bottom: 0.75rem;
        }

        .performance-item span {
            font-size: 0.8rem;
            color: var(--text-secondary);
            display: block;
            margin-bottom: 0.25rem;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: var(--success-gradient);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        /* AI Assistance Panel */
        .ai-assistance-panel {
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .ai-assistance-panel h4 {
            color: var(--accent-color);
            font-weight: 800;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .ai-assistance-panel ul {
            list-style: none;
            padding: 0;
        }

        .ai-assistance-panel li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
        }

        .ai-assistance-panel li:last-child {
            border-bottom: none;
        }

        /* Selected Widgets Styles */
        .selected-widgets {
            margin-top: 2rem;
        }

        .selected-widgets h4 {
            color: white;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .selected-widgets-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
        }

        .selected-widget-item {
            background: var(--accent-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .remove-widget-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 0.7rem;
            transition: all 0.3s ease;
        }

        .remove-widget-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        /* AI Process Styles */
        .ai-optimization-panel,
        .ai-creation-process,
        .ai-generation-process {
            text-align: center;
            padding: 2rem;
        }

        .optimization-steps,
        .creation-steps {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 2rem;
            flex-wrap: wrap;
        }

        .step {
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            font-size: 0.8rem;
            color: var(--text-secondary);
            transition: all 0.3s ease;
        }

        .step.active {
            background: var(--accent-color);
            color: white;
        }

        .optimization-results,
        .creation-success {
            text-align: center;
        }

        .success-icon {
            font-size: 3rem;
            color: var(--accent-color);
            margin-bottom: 1rem;
        }

        .ai-improvements,
        .result-item,
        .improvement-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin: 0.5rem 0;
            color: var(--text-secondary);
        }

        .result-item i,
        .improvement-item i {
            color: var(--accent-color);
        }

        /* Auto Generate Styles */
        .ai-auto-generate {
            text-align: center;
        }

        .auto-generate-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-top: 2rem;
        }

        .auto-option {
            background: var(--glass-bg);
            border: 2px solid var(--glass-border);
            border-radius: 12px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
        }

        .auto-option:hover {
            border-color: var(--accent-color);
            background: rgba(0, 212, 255, 0.1);
            transform: translateY(-5px);
        }

        .auto-option i {
            font-size: 2rem;
            color: var(--accent-color);
        }

        .auto-option span {
            font-weight: 600;
        }

        /* No Results Styles */
        .no-results, .no-dashboards {
            grid-column: 1 / -1;
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-secondary);
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            backdrop-filter: blur(20px);
        }

        .no-results i, .no-dashboards i {
            font-size: 4rem;
            color: var(--accent-color);
            margin-bottom: 1rem;
            display: block;
        }

        .no-results h3, .no-dashboards h3 {
            font-size: 1.5rem;
            color: white;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .no-results p, .no-dashboards p {
            margin-bottom: 2rem;
            font-size: 1rem;
        }

        .no-dashboards .btn {
            margin-top: 1rem;
        }

        /* Templates and Widgets Content */
        .ai-templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
        }

        .template-card {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .template-card:hover {
            transform: translateY(-5px);
            border-color: var(--accent-color);
            box-shadow: 0 15px 35px rgba(0, 212, 255, 0.2);
        }

        .template-preview {
            width: 100%;
            height: 150px;
            border-radius: 12px;
            margin-bottom: 1rem;
        }

        .template-preview.ai-analytics {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .template-preview.performance {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .template-card h3 {
            color: white;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .template-card p {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }

        .widgets-library {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .widget-category h3 {
            color: white;
            font-weight: 700;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--accent-color);
        }

        .widget-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }

        .widget-grid .widget-item {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .widget-grid .widget-item:hover {
            border-color: var(--accent-color);
            transform: translateY(-3px);
        }

        .widget-grid .widget-item i {
            font-size: 2rem;
            color: var(--accent-color);
            margin-bottom: 0.75rem;
        }

        .widget-grid .widget-item span {
            color: white;
            font-weight: 600;
            font-size: 0.9rem;
        }

        /* Content Section */
        .content-section {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Enhanced Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Responsive Enhancements */
        @media (max-width: 768px) {
            .modal-content {
                width: 95%;
                margin: 1rem;
            }

            .tabs {
                flex-wrap: wrap;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .theme-selector {
                grid-template-columns: repeat(2, 1fr);
            }

            .color-picker-group {
                grid-template-columns: repeat(2, 1fr);
            }

            .auto-generate-options {
                grid-template-columns: 1fr;
            }

            .optimization-steps,
            .creation-steps {
                flex-direction: column;
                align-items: center;
            }

            .ai-templates-grid {
                grid-template-columns: 1fr;
            }

            .widget-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .header-actions {
                flex-wrap: wrap;
                justify-content: center;
            }

            .main-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .sidebar {
                order: 2;
                position: static;
            }

            .main-content {
                order: 1;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .widget-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">7C</div>
                <div class="logo-text">
                    <h1>مدير لوحات التحكم الذكي</h1>
                    <p>أكاديمية 7C الرياضية <span class="ai-badge">AI مدعوم</span></p>
                </div>
            </div>
            <div class="header-actions">
                <button class="ai-assistant-btn" onclick="openAIAssistant()">
                    <i class="fas fa-robot ai-icon"></i>
                    المساعد الذكي
                </button>
                <button class="btn btn-primary" onclick="createNewDashboard()">
                    <i class="fas fa-plus"></i>
                    لوحة جديدة
                </button>
                <button class="btn btn-glass" onclick="importDashboard()">
                    <i class="fas fa-upload"></i>
                    استيراد
                </button>
                <button class="btn btn-glass" onclick="exportAllDashboards()">
                    <i class="fas fa-download"></i>
                    تصدير الكل
                </button>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container">
        <div class="main-grid">
            <!-- Advanced Sidebar -->
            <div class="sidebar">
                <div class="sidebar-section">
                    <div class="sidebar-title">
                        <i class="fas fa-tachometer-alt"></i>
                        إدارة اللوحات
                    </div>
                    <div class="sidebar-item active" onclick="showSection('dashboards')">
                        <i class="fas fa-th-large"></i>
                        جميع اللوحات
                    </div>
                    <div class="sidebar-item" onclick="showSection('ai-templates')">
                        <i class="fas fa-magic"></i>
                        قوالب ذكية
                    </div>
                    <div class="sidebar-item" onclick="showSection('widgets')">
                        <i class="fas fa-puzzle-piece"></i>
                        مكتبة الأدوات
                    </div>
                    <div class="sidebar-item" onclick="showSection('ai-builder')">
                        <i class="fas fa-wand-magic-sparkles"></i>
                        منشئ ذكي
                    </div>
                </div>

                <div class="sidebar-section">
                    <div class="sidebar-title">
                        <i class="fas fa-brain"></i>
                        الذكاء الاصطناعي
                    </div>
                    <div class="sidebar-item" onclick="showSection('ai-analytics')">
                        <i class="fas fa-chart-line"></i>
                        تحليلات ذكية
                    </div>
                    <div class="sidebar-item" onclick="showSection('ai-optimization')">
                        <i class="fas fa-rocket"></i>
                        تحسين تلقائي
                    </div>
                    <div class="sidebar-item" onclick="showSection('ai-insights')">
                        <i class="fas fa-lightbulb"></i>
                        رؤى ذكية
                    </div>
                    <div class="sidebar-item" onclick="showSection('ai-predictions')">
                        <i class="fas fa-crystal-ball"></i>
                        توقعات ذكية
                    </div>
                </div>

                <div class="sidebar-section">
                    <div class="sidebar-title">
                        <i class="fas fa-cog"></i>
                        الإعدادات المتقدمة
                    </div>
                    <div class="sidebar-item" onclick="showSection('themes')">
                        <i class="fas fa-palette"></i>
                        السمات والألوان
                    </div>
                    <div class="sidebar-item" onclick="showSection('layouts')">
                        <i class="fas fa-columns"></i>
                        تخطيطات ذكية
                    </div>
                    <div class="sidebar-item" onclick="showSection('permissions')">
                        <i class="fas fa-shield-alt"></i>
                        إدارة الصلاحيات
                    </div>
                    <div class="sidebar-item" onclick="showSection('integrations')">
                        <i class="fas fa-plug"></i>
                        التكاملات
                    </div>
                </div>

                <div class="sidebar-section">
                    <div class="sidebar-title">
                        <i class="fas fa-chart-bar"></i>
                        التحليلات والأداء
                    </div>
                    <div class="sidebar-item" onclick="showSection('usage-analytics')">
                        <i class="fas fa-users"></i>
                        إحصائيات الاستخدام
                    </div>
                    <div class="sidebar-item" onclick="showSection('performance')">
                        <i class="fas fa-tachometer-alt"></i>
                        مراقبة الأداء
                    </div>
                    <div class="sidebar-item" onclick="showSection('reports')">
                        <i class="fas fa-file-alt"></i>
                        التقارير الذكية
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <!-- AI Suggestions Panel -->
                <div class="ai-suggestions">
                    <h4>
                        <i class="fas fa-magic"></i>
                        اقتراحات المساعد الذكي
                    </h4>
                    <div class="suggestion-item">
                        <strong>تحسين الأداء:</strong> يمكن تحسين سرعة تحميل لوحة "إحصائيات اللاعبين" بنسبة 40% عبر تحسين الاستعلامات
                    </div>
                    <div class="suggestion-item">
                        <strong>تصميم جديد:</strong> اكتشفنا أن المستخدمين يفضلون التخطيط الشبكي للبيانات - هل تريد تطبيقه؟
                    </div>
                    <div class="suggestion-item">
                        <strong>ميزة مقترحة:</strong> إضافة لوحة تحكم للتنبؤ بأداء اللاعبين باستخدام الذكاء الاصطناعي
                    </div>
                </div>

                <!-- Dashboards Section -->
                <div id="dashboards-section" class="content-section active">
                    <div class="content-header">
                        <div class="content-title">
                            <i class="fas fa-th-large"></i>
                            لوحات التحكم الذكية
                        </div>
                        <div class="header-actions">
                            <select class="btn btn-glass" style="width: auto; margin-left: 1rem;" onchange="filterDashboards(this.value)">
                                <option value="">جميع اللوحات</option>
                                <option value="active">نشطة</option>
                                <option value="draft">مسودة</option>
                                <option value="ai-optimized">محسنة بالذكاء الاصطناعي</option>
                                <option value="archived">مؤرشفة</option>
                            </select>
                            <input type="text" class="btn btn-glass" placeholder="البحث الذكي..." style="width: 300px;" onkeyup="aiSearchDashboards(this.value)">
                            <button class="btn btn-glass" onclick="refreshDashboards()" title="إعادة تحميل">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button class="btn btn-success" onclick="aiAutoGenerate()">
                                <i class="fas fa-magic"></i>
                                إنشاء تلقائي
                            </button>
                        </div>
                    </div>

                    <div class="dashboard-grid" id="dashboardsGrid">
                        <!-- Dashboards will be loaded here -->
                    </div>
                </div>

                <!-- Other sections will be loaded dynamically -->
                <div id="dynamic-content">
                    <!-- Dynamic content sections -->
                </div>
            </div>
        </div>
    </div>

    <!-- AI Assistant Modal -->
    <div id="aiAssistantModal" class="modal">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-robot"></i>
                    المساعد الذكي لإدارة لوحات التحكم
                </h3>
                <button class="close-btn" onclick="closeAIAssistant()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="ai-chat-container">
                    <div class="ai-chat-messages" id="aiChatMessages">
                        <div class="ai-message">
                            <div class="ai-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="ai-text">
                                مرحباً! أنا مساعدك الذكي لإدارة لوحات التحكم. كيف يمكنني مساعدتك اليوم؟
                            </div>
                        </div>
                    </div>
                    <div class="ai-input-container">
                        <input type="text" id="aiInput" class="ai-input" placeholder="اكتب سؤالك أو طلبك هنا..." onkeypress="handleAIInput(event)">
                        <button class="ai-send-btn" onclick="sendAIMessage()">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    <div class="ai-suggestions-quick">
                        <button class="ai-quick-btn" onclick="quickAIAction('create-dashboard')">إنشاء لوحة تحكم جديدة</button>
                        <button class="ai-quick-btn" onclick="quickAIAction('optimize-performance')">تحسين الأداء</button>
                        <button class="ai-quick-btn" onclick="quickAIAction('analyze-usage')">تحليل الاستخدام</button>
                        <button class="ai-quick-btn" onclick="quickAIAction('suggest-layout')">اقتراح تخطيط</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Creation Modal -->
    <div id="dashboardModal" class="modal">
        <div class="modal-content" style="max-width: 1000px;">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-plus"></i>
                    إنشاء لوحة تحكم جديدة
                </h3>
                <button class="close-btn" onclick="closeDashboardModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="tabs">
                    <div class="tab active" onclick="switchTab('basic')">المعلومات الأساسية</div>
                    <div class="tab" onclick="switchTab('design')">التصميم والألوان</div>
                    <div class="tab" onclick="switchTab('widgets')">الأدوات والمكونات</div>
                    <div class="tab" onclick="switchTab('ai-settings')">الإعدادات الذكية</div>
                    <div class="tab" onclick="switchTab('permissions')">الصلاحيات</div>
                </div>

                <div id="basic-tab" class="tab-content active">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">اسم لوحة التحكم</label>
                            <input type="text" id="dashboardName" class="form-input" placeholder="مثال: لوحة إحصائيات اللاعبين">
                        </div>
                        <div class="form-group">
                            <label class="form-label">الوصف</label>
                            <textarea id="dashboardDescription" class="form-input form-textarea" placeholder="وصف مختصر للوحة التحكم..."></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">الفئة</label>
                            <select id="dashboardCategory" class="form-input form-select">
                                <option value="">اختر الفئة</option>
                                <option value="analytics">التحليلات</option>
                                <option value="management">الإدارة</option>
                                <option value="reports">التقارير</option>
                                <option value="monitoring">المراقبة</option>
                                <option value="ai-insights">الرؤى الذكية</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">مستوى الأولوية</label>
                            <select id="dashboardPriority" class="form-input form-select">
                                <option value="high">عالية</option>
                                <option value="medium" selected>متوسطة</option>
                                <option value="low">منخفضة</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // ==================== AI Dashboard Manager System ====================

        // Global Variables
        let dashboards = [];
        let currentSection = 'dashboards';
        let aiAssistantActive = false;
        let selectedWidgets = [];

        // Sample Dashboard Data with AI Features
        const sampleDashboards = [
            {
                id: 1,
                name: "لوحة إحصائيات اللاعبين الذكية",
                description: "تحليل شامل لأداء اللاعبين مع توقعات ذكية",
                category: "ai-insights",
                status: "active",
                aiOptimized: true,
                lastModified: "2024-01-15",
                usage: 95,
                performance: 98,
                widgets: ["chart", "stats", "ai-insights", "ai-predictions"],
                theme: "ai-futuristic",
                permissions: ["admin", "coach"]
            },
            {
                id: 2,
                name: "لوحة إدارة التدريبات",
                description: "جدولة وتتبع التدريبات مع تحسينات ذكية",
                category: "management",
                status: "active",
                aiOptimized: false,
                lastModified: "2024-01-14",
                usage: 87,
                performance: 92,
                widgets: ["calendar", "table", "notifications"],
                theme: "professional",
                permissions: ["admin", "coach", "player"]
            },
            {
                id: 3,
                name: "تقارير الأداء المتقدمة",
                description: "تقارير تفصيلية مع رؤى الذكاء الاصطناعي",
                category: "reports",
                status: "draft",
                aiOptimized: true,
                lastModified: "2024-01-13",
                usage: 0,
                performance: 0,
                widgets: ["chart", "table", "ai-insights"],
                theme: "modern",
                permissions: ["admin"]
            }
        ];

        // Initialize System
        document.addEventListener('DOMContentLoaded', function() {
            try {
                initializeSystem();
                // Wait for DOM to be ready before loading dashboards
                setTimeout(() => {
                    loadDashboards();
                    initializeAIFeatures();
                    setupEventListeners();
                }, 500);
            } catch (error) {
                console.error('Initialization error:', error);
                // Fallback initialization
                dashboards = [...sampleDashboards];
                setTimeout(loadDashboards, 1000);
            }
        });

        // Add reset function for debugging
        function resetDashboards() {
            dashboards = [...sampleDashboards];
            saveDashboards();
            loadDashboards();
            Swal.fire({
                title: 'تم إعادة التعيين',
                text: 'تم إعادة تعيين لوحات التحكم إلى البيانات الافتراضية',
                icon: 'success',
                background: 'var(--bg-secondary)',
                color: 'white'
            });
        }

        function initializeSystem() {
            // Load saved dashboards from localStorage
            const savedDashboards = localStorage.getItem('aiDashboards');
            if (savedDashboards) {
                try {
                    dashboards = JSON.parse(savedDashboards);
                } catch (e) {
                    console.error('Error parsing saved dashboards:', e);
                    dashboards = [...sampleDashboards];
                    saveDashboards();
                }
            } else {
                dashboards = [...sampleDashboards];
                saveDashboards();
            }

            // Initialize smooth animations for layout
            setTimeout(() => {
                const sidebar = document.querySelector('.sidebar');
                const mainContent = document.querySelector('.main-content');

                if (sidebar) {
                    sidebar.style.opacity = '0';
                    sidebar.style.transform = 'translateX(-50px)';
                    sidebar.style.transition = 'all 0.8s ease-out';
                    setTimeout(() => {
                        sidebar.style.opacity = '1';
                        sidebar.style.transform = 'translateX(0)';
                    }, 100);
                }

                if (mainContent) {
                    mainContent.style.opacity = '0';
                    mainContent.style.transform = 'translateY(30px)';
                    mainContent.style.transition = 'all 0.8s ease-out';
                    setTimeout(() => {
                        mainContent.style.opacity = '1';
                        mainContent.style.transform = 'translateY(0)';
                    }, 300);
                }
            }, 100);
        }

        function initializeAIFeatures() {
            // Simulate AI analysis
            setTimeout(() => {
                updateAISuggestions();
                analyzePerformance();
            }, 2000);

            // Auto-refresh AI insights every 30 seconds
            setInterval(updateAISuggestions, 30000);
        }

        function setupEventListeners() {
            // Widget selection
            document.addEventListener('click', function(e) {
                if (e.target.closest('.widget-item')) {
                    toggleWidget(e.target.closest('.widget-item'));
                }
            });

            // Theme selection
            document.addEventListener('click', function(e) {
                if (e.target.closest('.theme-option')) {
                    selectTheme(e.target.closest('.theme-option'));
                }
            });
        }

        // ==================== Dashboard Management ====================

        function loadDashboards() {
            const grid = document.getElementById('dashboardsGrid');
            if (!grid) return;

            grid.innerHTML = '';

            if (dashboards.length === 0) {
                grid.innerHTML = `
                    <div class="no-dashboards">
                        <i class="fas fa-tachometer-alt"></i>
                        <h3>لا توجد لوحات تحكم</h3>
                        <p>ابدأ بإنشاء لوحة تحكم جديدة</p>
                        <button class="btn btn-primary" onclick="createNewDashboard()">
                            <i class="fas fa-plus"></i>
                            إنشاء لوحة جديدة
                        </button>
                    </div>
                `;
                return;
            }

            dashboards.forEach((dashboard, index) => {
                const card = createDashboardCard(dashboard);
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                grid.appendChild(card);

                // Animate each card with delay
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease-out';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }

        function createDashboardCard(dashboard) {
            const card = document.createElement('div');
            card.className = 'dashboard-card';
            card.setAttribute('data-dashboard-id', dashboard.id);
            card.innerHTML = `
                <div class="dashboard-preview" style="background: ${getThemeGradient(dashboard.theme)}">
                    ${dashboard.aiOptimized ? '<div class="ai-indicator"><i class="fas fa-brain"></i> AI</div>' : ''}
                </div>
                <div class="dashboard-content">
                    <div class="dashboard-info">
                        <h3>${dashboard.name}</h3>
                        <p>${dashboard.description}</p>
                    </div>
                    <div class="dashboard-meta">
                        <span>آخر تعديل: ${formatDate(dashboard.lastModified)}</span>
                        <span class="status-badge status-${dashboard.status}">${getStatusText(dashboard.status)}</span>
                    </div>
                    <div class="performance-indicators">
                        <div class="performance-item">
                            <span>الاستخدام: ${dashboard.usage}%</span>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${dashboard.usage}%"></div>
                            </div>
                        </div>
                        <div class="performance-item">
                            <span>الأداء: ${dashboard.performance}%</span>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${dashboard.performance}%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-actions">
                        <button class="action-btn edit" onclick="editDashboard(${dashboard.id})" title="تحرير">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn duplicate" onclick="duplicateDashboard(${dashboard.id})" title="نسخ">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button class="action-btn ai-optimize" onclick="aiOptimizeDashboard(${dashboard.id})" title="تحسين ذكي">
                            <i class="fas fa-magic"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteDashboard(${dashboard.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            return card;
        }

        function getThemeGradient(theme) {
            const gradients = {
                'professional': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                'modern': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                'minimal': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                'ai-futuristic': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
            };
            return gradients[theme] || gradients.professional;
        }

        function getStatusText(status) {
            const statusTexts = {
                'active': 'نشط',
                'draft': 'مسودة',
                'archived': 'مؤرشف'
            };
            return statusTexts[status] || status;
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // ==================== AI Features ====================

        function updateAISuggestions() {
            const suggestions = [
                "تحسين الأداء: يمكن تحسين سرعة تحميل لوحة 'إحصائيات اللاعبين' بنسبة 40% عبر تحسين الاستعلامات",
                "تصميم جديد: اكتشفنا أن المستخدمين يفضلون التخطيط الشبكي للبيانات - هل تريد تطبيقه؟",
                "ميزة مقترحة: إضافة لوحة تحكم للتنبؤ بأداء اللاعبين باستخدام الذكاء الاصطناعي",
                "تحليل الاستخدام: لوحة 'إدارة التدريبات' تحتاج إلى تحديث في واجهة المستخدم",
                "أمان البيانات: يُنصح بتفعيل التشفير المتقدم للبيانات الحساسة"
            ];

            const randomSuggestions = suggestions.sort(() => 0.5 - Math.random()).slice(0, 3);
            const container = document.querySelector('.ai-suggestions');

            if (container) {
                const suggestionItems = container.querySelectorAll('.suggestion-item');
                suggestionItems.forEach((item, index) => {
                    if (randomSuggestions[index]) {
                        const parts = randomSuggestions[index].split(': ');
                        item.innerHTML = `<strong>${parts[0]}:</strong> ${parts[1]}`;
                    }
                });
            }
        }

        function analyzePerformance() {
            // Simulate AI performance analysis
            dashboards.forEach(dashboard => {
                if (dashboard.status === 'active') {
                    // Simulate performance fluctuation
                    dashboard.performance = Math.max(85, Math.min(100, dashboard.performance + (Math.random() - 0.5) * 10));
                    dashboard.usage = Math.max(70, Math.min(100, dashboard.usage + (Math.random() - 0.5) * 15));
                }
            });
            saveDashboards();
        }

        function aiOptimizeDashboard(id) {
            const dashboard = dashboards.find(d => d.id === id);
            if (!dashboard) return;

            Swal.fire({
                title: 'تحسين ذكي',
                html: `
                    <div class="ai-optimization-panel">
                        <div class="ai-loader"></div>
                        <p>جاري تحليل لوحة التحكم وتطبيق التحسينات الذكية...</p>
                        <div class="optimization-steps">
                            <div class="step active">تحليل البيانات</div>
                            <div class="step">تحسين الأداء</div>
                            <div class="step">تحديث التصميم</div>
                            <div class="step">تطبيق التحسينات</div>
                        </div>
                    </div>
                `,
                showConfirmButton: false,
                background: 'var(--bg-secondary)',
                color: 'white'
            });

            // Simulate AI optimization process
            setTimeout(() => {
                dashboard.aiOptimized = true;
                dashboard.performance = Math.min(100, dashboard.performance + 15);
                dashboard.usage = Math.min(100, dashboard.usage + 10);
                saveDashboards();
                loadDashboards();

                Swal.fire({
                    title: 'تم التحسين بنجاح!',
                    html: `
                        <div class="optimization-results">
                            <div class="result-item">
                                <i class="fas fa-rocket"></i>
                                <span>تحسن الأداء بنسبة 15%</span>
                            </div>
                            <div class="result-item">
                                <i class="fas fa-users"></i>
                                <span>زيادة معدل الاستخدام بنسبة 10%</span>
                            </div>
                            <div class="result-item">
                                <i class="fas fa-magic"></i>
                                <span>تم تطبيق تحسينات ذكية على التصميم</span>
                            </div>
                        </div>
                    `,
                    icon: 'success',
                    background: 'var(--bg-secondary)',
                    color: 'white'
                });
            }, 3000);
        }

        // ==================== Modal Management ====================

        function openAIAssistant() {
            document.getElementById('aiAssistantModal').classList.add('show');
            aiAssistantActive = true;

            // Focus on input
            setTimeout(() => {
                document.getElementById('aiInput').focus();
            }, 300);
        }

        function closeAIAssistant() {
            document.getElementById('aiAssistantModal').classList.remove('show');
            aiAssistantActive = false;
        }

        function createNewDashboard() {
            document.getElementById('dashboardModal').classList.add('show');
            resetDashboardForm();
        }

        function closeDashboardModal() {
            document.getElementById('dashboardModal').classList.remove('show');
        }

        function resetDashboardForm() {
            document.getElementById('dashboardName').value = '';
            document.getElementById('dashboardDescription').value = '';
            document.getElementById('dashboardCategory').value = '';
            selectedWidgets = [];
            updateSelectedWidgetsList();
        }

        // ==================== AI Chat System ====================

        function handleAIInput(event) {
            if (event.key === 'Enter') {
                sendAIMessage();
            }
        }

        function sendAIMessage() {
            const input = document.getElementById('aiInput');
            const message = input.value.trim();

            if (!message) return;

            addChatMessage(message, 'user');
            input.value = '';

            // Simulate AI response
            setTimeout(() => {
                const response = generateAIResponse(message);
                addChatMessage(response, 'ai');
            }, 1000);
        }

        function addChatMessage(message, sender) {
            const container = document.getElementById('aiChatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `${sender}-message`;

            if (sender === 'ai') {
                messageDiv.innerHTML = `
                    <div class="ai-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="ai-text">${message}</div>
                `;
            } else {
                messageDiv.innerHTML = `
                    <div class="user-text">${message}</div>
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                `;
            }

            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        function generateAIResponse(message) {
            const responses = {
                'إنشاء': 'يمكنني مساعدتك في إنشاء لوحة تحكم جديدة. ما نوع البيانات التي تريد عرضها؟',
                'تحسين': 'سأقوم بتحليل لوحات التحكم الحالية واقتراح تحسينات. هل تريد التركيز على الأداء أم تجربة المستخدم؟',
                'تحليل': 'يمكنني تحليل استخدام لوحات التحكم وتقديم رؤى مفيدة. أي لوحة تريد تحليلها؟',
                'مساعدة': 'أنا هنا لمساعدتك! يمكنني إنشاء لوحات تحكم، تحليل البيانات، اقتراح تحسينات، وأكثر.',
                'default': 'شكراً لك! كيف يمكنني مساعدتك أكثر في إدارة لوحات التحكم؟'
            };

            for (const [key, response] of Object.entries(responses)) {
                if (message.includes(key)) {
                    return response;
                }
            }

            return responses.default;
        }

        function quickAIAction(action) {
            const actions = {
                'create-dashboard': 'سأساعدك في إنشاء لوحة تحكم جديدة. ما هو الغرض من هذه اللوحة؟',
                'optimize-performance': 'جاري تحليل أداء لوحات التحكم... وجدت 3 تحسينات محتملة. هل تريد تطبيقها؟',
                'analyze-usage': 'تحليل الاستخدام يظهر أن لوحة "إحصائيات اللاعبين" هي الأكثر استخداماً بنسبة 95%.',
                'suggest-layout': 'بناءً على تحليل سلوك المستخدمين، أقترح استخدام تخطيط شبكي مع 3 أعمدة للبيانات الرئيسية.'
            };

            addChatMessage(actions[action] || 'كيف يمكنني مساعدتك؟', 'ai');
        }

        // ==================== Utility Functions ====================

        function saveDashboards() {
            try {
                localStorage.setItem('aiDashboards', JSON.stringify(dashboards));
            } catch (e) {
                console.error('Error saving dashboards:', e);
                Swal.fire({
                    title: 'خطأ في الحفظ',
                    text: 'تعذر حفظ البيانات. تأكد من وجود مساحة كافية.',
                    icon: 'error',
                    background: 'var(--bg-secondary)',
                    color: 'white'
                });
            }
        }

        // Force refresh dashboards
        function refreshDashboards() {
            const grid = document.getElementById('dashboardsGrid');
            if (grid) {
                grid.style.opacity = '0.5';
                setTimeout(() => {
                    loadDashboards();
                    grid.style.opacity = '1';
                }, 200);
            }
        }

        function showSection(section) {
            // Update active sidebar item
            document.querySelectorAll('.sidebar-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            currentSection = section;

            // Load section content
            loadSectionContent(section);
        }

        function loadSectionContent(section) {
            const dynamicContent = document.getElementById('dynamic-content');

            switch(section) {
                case 'dashboards':
                    document.getElementById('dashboards-section').style.display = 'block';
                    dynamicContent.innerHTML = '';
                    break;
                case 'ai-templates':
                    document.getElementById('dashboards-section').style.display = 'none';
                    dynamicContent.innerHTML = getAITemplatesContent();
                    break;
                case 'widgets':
                    document.getElementById('dashboards-section').style.display = 'none';
                    dynamicContent.innerHTML = getWidgetsContent();
                    break;
                // Add more sections as needed
                default:
                    document.getElementById('dashboards-section').style.display = 'none';
                    dynamicContent.innerHTML = `<div class="content-section"><h2>قريباً...</h2><p>هذا القسم قيد التطوير</p></div>`;
            }
        }

        function getAITemplatesContent() {
            return `
                <div class="content-section">
                    <div class="content-header">
                        <div class="content-title">
                            <i class="fas fa-magic"></i>
                            القوالب الذكية
                        </div>
                    </div>
                    <div class="ai-templates-grid">
                        <div class="template-card">
                            <div class="template-preview ai-analytics"></div>
                            <h3>تحليلات ذكية</h3>
                            <p>قالب متقدم للتحليلات مع الذكاء الاصطناعي</p>
                            <button class="btn btn-primary">استخدام القالب</button>
                        </div>
                        <div class="template-card">
                            <div class="template-preview performance"></div>
                            <h3>مراقبة الأداء</h3>
                            <p>لوحة شاملة لمراقبة أداء النظام</p>
                            <button class="btn btn-primary">استخدام القالب</button>
                        </div>
                    </div>
                </div>
            `;
        }

        function getWidgetsContent() {
            return `
                <div class="content-section">
                    <div class="content-header">
                        <div class="content-title">
                            <i class="fas fa-puzzle-piece"></i>
                            مكتبة الأدوات
                        </div>
                    </div>
                    <div class="widgets-library">
                        <div class="widget-category">
                            <h3>أدوات البيانات</h3>
                            <div class="widget-grid">
                                <div class="widget-item">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>مخططات بيانية</span>
                                </div>
                                <div class="widget-item">
                                    <i class="fas fa-table"></i>
                                    <span>جداول البيانات</span>
                                </div>
                            </div>
                        </div>
                        <div class="widget-category">
                            <h3>أدوات الذكاء الاصطناعي</h3>
                            <div class="widget-grid">
                                <div class="widget-item">
                                    <i class="fas fa-brain"></i>
                                    <span>رؤى ذكية</span>
                                </div>
                                <div class="widget-item">
                                    <i class="fas fa-crystal-ball"></i>
                                    <span>توقعات ذكية</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Initialize tooltips and other UI enhancements
        function initializeUI() {
            // Add hover effects and animations
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    gsap.to(this, {duration: 0.3, scale: 1.05, ease: "power2.out"});
                });

                btn.addEventListener('mouseleave', function() {
                    gsap.to(this, {duration: 0.3, scale: 1, ease: "power2.out"});
                });
            });
        }

        // ==================== Tab Management ====================

        function switchTab(tabName) {
            // Remove active class from all tabs and contents
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // Add active class to clicked tab and corresponding content
            event.target.classList.add('active');
            document.getElementById(tabName + '-tab').classList.add('active');

            // Update AI suggestions based on tab
            updateTabAISuggestions(tabName);
        }

        function updateTabAISuggestions(tabName) {
            const suggestions = {
                'basic': [
                    'استخدام تخطيط شبكي للبيانات الرقمية',
                    'إضافة مخططات تفاعلية للإحصائيات',
                    'تفعيل التحديث التلقائي كل 5 دقائق'
                ],
                'design': [
                    'استخدام السمة المستقبلية للبيانات التقنية',
                    'تطبيق ألوان متدرجة للمخططات',
                    'إضافة تأثيرات بصرية ذكية'
                ],
                'widgets': [
                    'إضافة أداة التوقعات الذكية',
                    'استخدام مخططات تفاعلية للبيانات',
                    'تفعيل التحديث التلقائي للإحصائيات'
                ],
                'ai-settings': [
                    'تفعيل التحليل التلقائي لتحسين الأداء',
                    'استخدام التنبؤات لتحسين تجربة المستخدم',
                    'تطبيق التحسينات الذكية تلقائياً'
                ]
            };

            const suggestionsList = document.getElementById('aiSuggestionsList');
            if (suggestionsList && suggestions[tabName]) {
                suggestionsList.innerHTML = suggestions[tabName]
                    .map(suggestion => `<li>${suggestion}</li>`)
                    .join('');
            }
        }

        // ==================== Widget Management ====================

        function toggleWidget(widgetElement) {
            const widgetType = widgetElement.dataset.widget;

            if (selectedWidgets.includes(widgetType)) {
                selectedWidgets = selectedWidgets.filter(w => w !== widgetType);
                widgetElement.classList.remove('selected');
            } else {
                selectedWidgets.push(widgetType);
                widgetElement.classList.add('selected');
            }

            updateSelectedWidgetsList();
            updateAIWidgetSuggestions();
        }

        function updateSelectedWidgetsList() {
            const container = document.getElementById('selectedWidgetsList');
            if (!container) return;

            const widgetNames = {
                'chart': 'مخططات بيانية',
                'stats': 'إحصائيات سريعة',
                'table': 'جداول البيانات',
                'calendar': 'التقويم',
                'ai-insights': 'رؤى ذكية',
                'notifications': 'التنبيهات',
                'progress': 'شريط التقدم',
                'ai-predictions': 'توقعات ذكية'
            };

            if (selectedWidgets.length === 0) {
                container.innerHTML = '<p class="text-gray-400">لم يتم تحديد أي أدوات بعد</p>';
                return;
            }

            container.innerHTML = selectedWidgets.map(widget => `
                <div class="selected-widget-item">
                    <span>${widgetNames[widget] || widget}</span>
                    <button onclick="removeWidget('${widget}')" class="remove-widget-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `).join('');
        }

        function removeWidget(widgetType) {
            selectedWidgets = selectedWidgets.filter(w => w !== widgetType);

            // Update UI
            const widgetElement = document.querySelector(`[data-widget="${widgetType}"]`);
            if (widgetElement) {
                widgetElement.classList.remove('selected');
            }

            updateSelectedWidgetsList();
        }

        function updateAIWidgetSuggestions() {
            // AI suggestions based on selected widgets
            const suggestions = [];

            if (selectedWidgets.includes('chart') && selectedWidgets.includes('ai-insights')) {
                suggestions.push('دمج المخططات مع الرؤى الذكية لتحليل أفضل');
            }

            if (selectedWidgets.includes('ai-predictions')) {
                suggestions.push('إضافة مخططات زمنية لعرض التوقعات');
            }

            if (selectedWidgets.length > 5) {
                suggestions.push('تنظيم الأدوات في مجموعات لتحسين الأداء');
            }

            // Update suggestions in UI
            const suggestionsList = document.getElementById('aiSuggestionsList');
            if (suggestionsList && suggestions.length > 0) {
                suggestionsList.innerHTML = suggestions.map(s => `<li>${s}</li>`).join('');
            }
        }

        // ==================== Theme Management ====================

        function selectTheme(themeElement) {
            // Remove active class from all themes
            document.querySelectorAll('.theme-option').forEach(option => {
                option.classList.remove('active');
            });

            // Add active class to selected theme
            themeElement.classList.add('active');

            const themeName = themeElement.dataset.theme;
            updateThemePreview(themeName);
        }

        function updateThemePreview(themeName) {
            // Update color pickers based on theme
            const themeColors = {
                'professional': {
                    primary: '#667eea',
                    secondary: '#764ba2',
                    accent: '#00d4ff',
                    background: '#0c0c0c'
                },
                'modern': {
                    primary: '#4facfe',
                    secondary: '#00f2fe',
                    accent: '#43e97b',
                    background: '#1a1a2e'
                },
                'minimal': {
                    primary: '#43e97b',
                    secondary: '#38f9d7',
                    accent: '#4facfe',
                    background: '#2d3748'
                },
                'ai-futuristic': {
                    primary: '#fa709a',
                    secondary: '#fee140',
                    accent: '#00d4ff',
                    background: '#0f0f23'
                }
            };

            const colors = themeColors[themeName];
            if (colors) {
                const colorInputs = document.querySelectorAll('.color-input');
                colorInputs[0].value = colors.primary;
                colorInputs[1].value = colors.secondary;
                colorInputs[2].value = colors.accent;
                colorInputs[3].value = colors.background;
            }
        }

        // ==================== Dashboard Actions ====================

        function editDashboard(id) {
            const dashboard = dashboards.find(d => d.id === id);
            if (!dashboard) return;

            // Open modal with dashboard data
            createNewDashboard();

            // Populate form with existing data
            document.getElementById('dashboardName').value = dashboard.name;
            document.getElementById('dashboardDescription').value = dashboard.description;
            document.getElementById('dashboardCategory').value = dashboard.category;

            // Update modal title
            document.querySelector('.modal-title').innerHTML = `
                <i class="fas fa-edit"></i>
                تحرير لوحة التحكم
            `;
        }

        function duplicateDashboard(id) {
            const dashboard = dashboards.find(d => d.id === id);
            if (!dashboard) return;

            const newDashboard = {
                ...dashboard,
                id: Date.now(),
                name: dashboard.name + ' (نسخة)',
                status: 'draft',
                lastModified: new Date().toISOString().split('T')[0],
                usage: 0,
                performance: 0
            };

            dashboards.push(newDashboard);
            saveDashboards();
            loadDashboards();

            Swal.fire({
                title: 'تم النسخ بنجاح!',
                text: 'تم إنشاء نسخة من لوحة التحكم',
                icon: 'success',
                background: 'var(--bg-secondary)',
                color: 'white'
            });
        }

        function deleteDashboard(id) {
            Swal.fire({
                title: 'تأكيد الحذف',
                text: 'هل أنت متأكد من حذف هذه اللوحة؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'حذف',
                cancelButtonText: 'إلغاء',
                background: 'var(--bg-secondary)',
                color: 'white'
            }).then((result) => {
                if (result.isConfirmed) {
                    dashboards = dashboards.filter(d => d.id !== id);
                    saveDashboards();
                    loadDashboards();

                    Swal.fire({
                        title: 'تم الحذف!',
                        text: 'تم حذف لوحة التحكم بنجاح',
                        icon: 'success',
                        background: 'var(--bg-secondary)',
                        color: 'white'
                    });
                }
            });
        }

        // ==================== Dashboard Creation ====================

        function createDashboardWithAI() {
            const name = document.getElementById('dashboardName').value;
            const description = document.getElementById('dashboardDescription').value;
            const category = document.getElementById('dashboardCategory').value;
            const priority = document.getElementById('dashboardPriority').value;

            if (!name || !description || !category) {
                Swal.fire({
                    title: 'بيانات ناقصة',
                    text: 'يرجى ملء جميع الحقول المطلوبة',
                    icon: 'warning',
                    background: 'var(--bg-secondary)',
                    color: 'white'
                });
                return;
            }

            // Show AI creation process
            Swal.fire({
                title: 'إنشاء لوحة التحكم بالذكاء الاصطناعي',
                html: `
                    <div class="ai-creation-process">
                        <div class="ai-loader"></div>
                        <p>جاري تحليل المتطلبات وإنشاء لوحة التحكم...</p>
                        <div class="creation-steps">
                            <div class="step active">تحليل البيانات</div>
                            <div class="step">اختيار الأدوات</div>
                            <div class="step">تصميم التخطيط</div>
                            <div class="step">تطبيق التحسينات</div>
                        </div>
                    </div>
                `,
                showConfirmButton: false,
                background: 'var(--bg-secondary)',
                color: 'white'
            });

            // Simulate AI creation process
            setTimeout(() => {
                const newDashboard = {
                    id: Date.now(),
                    name: name,
                    description: description,
                    category: category,
                    status: 'active',
                    aiOptimized: true,
                    lastModified: new Date().toISOString().split('T')[0],
                    usage: Math.floor(Math.random() * 30) + 70,
                    performance: Math.floor(Math.random() * 20) + 80,
                    widgets: selectedWidgets.length > 0 ? selectedWidgets : ['chart', 'stats', 'ai-insights'],
                    theme: document.querySelector('.theme-option.active')?.dataset.theme || 'professional',
                    permissions: ['admin']
                };

                dashboards.push(newDashboard);
                saveDashboards();
                loadDashboards();
                closeDashboardModal();

                Swal.fire({
                    title: 'تم الإنشاء بنجاح!',
                    html: `
                        <div class="creation-success">
                            <div class="success-icon">
                                <i class="fas fa-magic"></i>
                            </div>
                            <p>تم إنشاء لوحة التحكم بنجاح مع تحسينات ذكية</p>
                            <div class="ai-improvements">
                                <div class="improvement-item">
                                    <i class="fas fa-rocket"></i>
                                    <span>تحسين الأداء بنسبة 25%</span>
                                </div>
                                <div class="improvement-item">
                                    <i class="fas fa-eye"></i>
                                    <span>تحسين تجربة المستخدم</span>
                                </div>
                                <div class="improvement-item">
                                    <i class="fas fa-brain"></i>
                                    <span>إضافة رؤى ذكية</span>
                                </div>
                            </div>
                        </div>
                    `,
                    icon: 'success',
                    background: 'var(--bg-secondary)',
                    color: 'white'
                });
            }, 3000);
        }

        function saveDashboardDraft() {
            const name = document.getElementById('dashboardName').value;
            const description = document.getElementById('dashboardDescription').value;

            if (!name) {
                Swal.fire({
                    title: 'اسم مطلوب',
                    text: 'يرجى إدخال اسم لوحة التحكم على الأقل',
                    icon: 'warning',
                    background: 'var(--bg-secondary)',
                    color: 'white'
                });
                return;
            }

            const draftDashboard = {
                id: Date.now(),
                name: name,
                description: description || 'مسودة لوحة تحكم',
                category: document.getElementById('dashboardCategory').value || 'management',
                status: 'draft',
                aiOptimized: false,
                lastModified: new Date().toISOString().split('T')[0],
                usage: 0,
                performance: 0,
                widgets: selectedWidgets,
                theme: document.querySelector('.theme-option.active')?.dataset.theme || 'professional',
                permissions: ['admin']
            };

            dashboards.push(draftDashboard);
            saveDashboards();
            loadDashboards();
            closeDashboardModal();

            Swal.fire({
                title: 'تم الحفظ كمسودة!',
                text: 'يمكنك العودة لتحرير اللوحة لاحقاً',
                icon: 'success',
                background: 'var(--bg-secondary)',
                color: 'white'
            });
        }

        // ==================== Search and Filter ====================

        function filterDashboards(status) {
            const filteredDashboards = status ?
                dashboards.filter(d => d.status === status || (status === 'ai-optimized' && d.aiOptimized)) :
                dashboards;

            displayFilteredDashboards(filteredDashboards);
        }

        function aiSearchDashboards(query) {
            if (!query.trim()) {
                loadDashboards();
                return;
            }

            // AI-powered search with fuzzy matching
            const searchResults = dashboards.filter(dashboard => {
                const searchText = `${dashboard.name} ${dashboard.description} ${dashboard.category}`.toLowerCase();
                return searchText.includes(query.toLowerCase()) ||
                       dashboard.widgets.some(widget => widget.includes(query.toLowerCase()));
            });

            displayFilteredDashboards(searchResults);
        }

        function displayFilteredDashboards(filteredDashboards) {
            const grid = document.getElementById('dashboardsGrid');
            if (!grid) return;

            grid.innerHTML = '';

            if (filteredDashboards.length === 0) {
                grid.innerHTML = `
                    <div class="no-results">
                        <i class="fas fa-search"></i>
                        <h3>لا توجد نتائج</h3>
                        <p>لم يتم العثور على لوحات تحكم مطابقة للبحث</p>
                        <button class="btn btn-secondary" onclick="loadDashboards()">
                            <i class="fas fa-arrow-right"></i>
                            عرض جميع اللوحات
                        </button>
                    </div>
                `;
                return;
            }

            filteredDashboards.forEach((dashboard, index) => {
                const card = createDashboardCard(dashboard);
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                grid.appendChild(card);

                // Animate each card with delay
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease-out';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 80);
            });
        }

        // ==================== Import/Export ====================

        function importDashboard() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const importedData = JSON.parse(e.target.result);

                            if (Array.isArray(importedData)) {
                                // Import multiple dashboards
                                importedData.forEach(dashboard => {
                                    dashboard.id = Date.now() + Math.random();
                                    dashboards.push(dashboard);
                                });
                            } else {
                                // Import single dashboard
                                importedData.id = Date.now();
                                dashboards.push(importedData);
                            }

                            saveDashboards();
                            loadDashboards();

                            Swal.fire({
                                title: 'تم الاستيراد بنجاح!',
                                text: 'تم استيراد لوحات التحكم بنجاح',
                                icon: 'success',
                                background: 'var(--bg-secondary)',
                                color: 'white'
                            });
                        } catch (error) {
                            Swal.fire({
                                title: 'خطأ في الاستيراد',
                                text: 'تعذر قراءة الملف. تأكد من صحة تنسيق الملف',
                                icon: 'error',
                                background: 'var(--bg-secondary)',
                                color: 'white'
                            });
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        function exportAllDashboards() {
            const dataStr = JSON.stringify(dashboards, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `dashboards-export-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            Swal.fire({
                title: 'تم التصدير بنجاح!',
                text: 'تم تصدير جميع لوحات التحكم',
                icon: 'success',
                background: 'var(--bg-secondary)',
                color: 'white'
            });
        }

        // ==================== AI Auto Generation ====================

        function aiAutoGenerate() {
            Swal.fire({
                title: 'الإنشاء التلقائي الذكي',
                html: `
                    <div class="ai-auto-generate">
                        <p>اختر نوع لوحة التحكم التي تريد إنشاءها تلقائياً:</p>
                        <div class="auto-generate-options">
                            <button class="auto-option" onclick="generateDashboard('analytics')">
                                <i class="fas fa-chart-line"></i>
                                <span>لوحة تحليلات</span>
                            </button>
                            <button class="auto-option" onclick="generateDashboard('performance')">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>مراقبة الأداء</span>
                            </button>
                            <button class="auto-option" onclick="generateDashboard('management')">
                                <i class="fas fa-users-cog"></i>
                                <span>إدارة المستخدمين</span>
                            </button>
                            <button class="auto-option" onclick="generateDashboard('ai-insights')">
                                <i class="fas fa-brain"></i>
                                <span>رؤى ذكية</span>
                            </button>
                        </div>
                    </div>
                `,
                showConfirmButton: false,
                showCancelButton: true,
                cancelButtonText: 'إلغاء',
                background: 'var(--bg-secondary)',
                color: 'white'
            });
        }

        function generateDashboard(type) {
            const templates = {
                'analytics': {
                    name: 'لوحة التحليلات الذكية',
                    description: 'تحليل شامل للبيانات مع رؤى ذكية',
                    widgets: ['chart', 'stats', 'ai-insights', 'table'],
                    theme: 'ai-futuristic'
                },
                'performance': {
                    name: 'مراقبة الأداء المتقدمة',
                    description: 'مراقبة أداء النظام في الوقت الفعلي',
                    widgets: ['chart', 'progress', 'notifications', 'stats'],
                    theme: 'modern'
                },
                'management': {
                    name: 'إدارة المستخدمين',
                    description: 'إدارة شاملة للمستخدمين والصلاحيات',
                    widgets: ['table', 'stats', 'notifications', 'calendar'],
                    theme: 'professional'
                },
                'ai-insights': {
                    name: 'رؤى الذكاء الاصطناعي',
                    description: 'رؤى وتوقعات ذكية للبيانات',
                    widgets: ['ai-insights', 'ai-predictions', 'chart', 'stats'],
                    theme: 'ai-futuristic'
                }
            };

            const template = templates[type];
            if (!template) return;

            Swal.close();

            // Show generation process
            Swal.fire({
                title: 'جاري الإنشاء التلقائي',
                html: `
                    <div class="ai-generation-process">
                        <div class="ai-loader"></div>
                        <p>جاري إنشاء ${template.name} تلقائياً...</p>
                    </div>
                `,
                showConfirmButton: false,
                background: 'var(--bg-secondary)',
                color: 'white'
            });

            setTimeout(() => {
                const newDashboard = {
                    id: Date.now(),
                    ...template,
                    category: type,
                    status: 'active',
                    aiOptimized: true,
                    lastModified: new Date().toISOString().split('T')[0],
                    usage: Math.floor(Math.random() * 30) + 70,
                    performance: Math.floor(Math.random() * 20) + 80,
                    permissions: ['admin']
                };

                dashboards.push(newDashboard);
                saveDashboards();
                loadDashboards();

                Swal.fire({
                    title: 'تم الإنشاء بنجاح!',
                    text: `تم إنشاء ${template.name} تلقائياً بالذكاء الاصطناعي`,
                    icon: 'success',
                    background: 'var(--bg-secondary)',
                    color: 'white'
                });
            }, 2000);
        }

        // Debug function to check DOM elements
        function checkDOMElements() {
            const grid = document.getElementById('dashboardsGrid');
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');

            console.log('DOM Check:', {
                grid: !!grid,
                sidebar: !!sidebar,
                mainContent: !!mainContent,
                dashboardsCount: dashboards.length
            });

            if (!grid) {
                console.error('Dashboard grid not found!');
                return false;
            }

            return true;
        }

        // Enhanced initialization with error handling
        function safeInitialize() {
            if (!checkDOMElements()) {
                setTimeout(safeInitialize, 500);
                return;
            }

            try {
                loadDashboards();
                console.log('✅ Dashboards loaded successfully');
            } catch (error) {
                console.error('❌ Error loading dashboards:', error);
                setTimeout(() => {
                    dashboards = [...sampleDashboards];
                    loadDashboards();
                }, 1000);
            }
        }

        // Call safe initialization
        setTimeout(safeInitialize, 1000);

        // Call UI initialization
        setTimeout(initializeUI, 1500);

        // Add keyboard shortcut for reset (Ctrl+Shift+R)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.shiftKey && e.key === 'R') {
                e.preventDefault();
                resetDashboards();
            }
        });

        console.log('🚀 نظام إدارة لوحات التحكم الذكي جاهز!');
    </script>
</body>
</html>