<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التسجيل في أكاديمية 7C الرياضية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
            color: white;
        }

        .registration-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .registration-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #8B4513, #D2691E);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            font-weight: 900;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #D2691E, #8B4513);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.1rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
        }

        .form-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #D2691E;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
        }

        .form-input {
            width: 100%;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #D2691E;
            background: rgba(255, 255, 255, 0.15);
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .form-select {
            width: 100%;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            cursor: pointer;
        }

        .form-select option {
            background: #1e293b;
            color: white;
        }

        .selected-plan {
            background: linear-gradient(135deg, rgba(210, 105, 30, 0.2), rgba(139, 69, 19, 0.2));
            border: 2px solid #D2691E;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .plan-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .plan-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .plan-info h3 {
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.25rem;
        }

        .plan-price {
            font-size: 1.2rem;
            font-weight: 600;
            color: #D2691E;
        }

        .plan-features {
            list-style: none;
            margin-top: 1rem;
        }

        .plan-features li {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .plan-features i {
            color: #10b981;
        }

        .submit-btn {
            width: 100%;
            padding: 1.2rem 2rem;
            background: linear-gradient(135deg, #8B4513, #D2691E);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 2rem;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.3);
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            margin-bottom: 2rem;
            transition: color 0.3s ease;
        }

        .back-link:hover {
            color: #D2691E;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .registration-card {
                padding: 2rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="registration-container">
        <div class="registration-card">
            <a href="homepage.html" class="back-link">
                <i class="fas fa-arrow-right"></i>
                العودة للصفحة الرئيسية
            </a>

            <div class="header">
                <div class="logo">7C</div>
                <h1>التسجيل في الأكاديمية</h1>
                <p>انضم إلى أكاديمية 7C الرياضية وابدأ رحلتك نحو التميز الرياضي</p>
            </div>

            <!-- عرض الخطة المختارة -->
            <div id="selectedPlanSection" class="selected-plan" style="display: none;">
                <div class="plan-header">
                    <div id="selectedPlanIcon" class="plan-icon"></div>
                    <div class="plan-info">
                        <h3 id="selectedPlanName">الخطة المختارة</h3>
                        <div id="selectedPlanPrice" class="plan-price">السعر</div>
                    </div>
                </div>
                <ul id="selectedPlanFeatures" class="plan-features">
                    <!-- سيتم ملؤها ديناميكياً -->
                </ul>
                <button type="button" onclick="changePlan()" style="background: transparent; border: 1px solid rgba(255,255,255,0.3); color: white; padding: 0.5rem 1rem; border-radius: 8px; cursor: pointer; margin-top: 1rem;">
                    تغيير الخطة
                </button>
            </div>

            <form id="registrationForm" class="form-grid">
                <div class="form-section">
                    <h2 class="section-title">
                        <i class="fas fa-user"></i>
                        المعلومات الشخصية
                    </h2>
                    
                    <div class="form-group">
                        <label class="form-label">الاسم الكامل *</label>
                        <input type="text" class="form-input" id="fullName" placeholder="أدخل الاسم الكامل" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">تاريخ الميلاد *</label>
                        <input type="date" class="form-input" id="birthDate" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">الجنس *</label>
                        <select class="form-select" id="gender" required>
                            <option value="">اختر الجنس</option>
                            <option value="male">ذكر</option>
                            <option value="female">أنثى</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">رقم الهوية/الإقامة *</label>
                        <input type="text" class="form-input" id="nationalId" placeholder="أدخل رقم الهوية" required>
                    </div>
                </div>

                <div class="form-section">
                    <h2 class="section-title">
                        <i class="fas fa-phone"></i>
                        معلومات الاتصال
                    </h2>
                    
                    <div class="form-group">
                        <label class="form-label">رقم الهاتف *</label>
                        <input type="tel" class="form-input" id="phone" placeholder="05xxxxxxxx" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">البريد الإلكتروني *</label>
                        <input type="email" class="form-input" id="email" placeholder="<EMAIL>" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">العنوان *</label>
                        <input type="text" class="form-input" id="address" placeholder="أدخل العنوان الكامل" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">اسم ولي الأمر (للقُصر)</label>
                        <input type="text" class="form-input" id="guardianName" placeholder="اسم ولي الأمر">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">رقم هاتف ولي الأمر (للقُصر)</label>
                        <input type="tel" class="form-input" id="guardianPhone" placeholder="05xxxxxxxx">
                    </div>
                </div>
            </form>

            <button type="submit" class="submit-btn" onclick="submitRegistration()">
                <i class="fas fa-paper-plane"></i>
                إرسال طلب التسجيل
            </button>
        </div>
    </div>

    <script>
        // بيانات الخطط
        const plans = {
            basic: {
                name: 'الخطة الأساسية',
                price: '200 ريال شهرياً',
                icon: 'fas fa-star',
                color: 'linear-gradient(135deg, #f59e0b, #d97706)',
                features: [
                    'تدريبات أساسية 3 مرات أسبوعياً',
                    'وصول للملاعب الأساسية',
                    'تقييم شهري للأداء',
                    'دعم فني أساسي',
                    'تطبيق الهاتف المحمول'
                ]
            },
            premium: {
                name: 'الخطة المتقدمة',
                price: '350 ريال شهرياً',
                icon: 'fas fa-gem',
                color: 'linear-gradient(135deg, #3b82f6, #1e40af)',
                features: [
                    'تدريبات متقدمة 5 مرات أسبوعياً',
                    'وصول لجميع الملاعب',
                    'تقييم أسبوعي للأداء',
                    'تحليلات متقدمة بالذكاء الاصطناعي',
                    'جلسات تدريب شخصية',
                    'دعم فني متقدم 24/7'
                ]
            },
            vip: {
                name: 'الخطة الذهبية',
                price: '500 ريال شهرياً',
                icon: 'fas fa-crown',
                color: 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
                features: [
                    'تدريبات يومية مخصصة',
                    'وصول حصري لجميع المرافق',
                    'تقييم يومي للأداء',
                    'مدرب شخصي مخصص',
                    'برامج تغذية مخصصة',
                    'جلسات علاج طبيعي',
                    'دعم فني VIP 24/7'
                ]
            }
        };

        let selectedPlan = null;

        // تحميل الخطة من URL
        window.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const planType = urlParams.get('plan');
            
            if (planType && plans[planType]) {
                selectPlan(planType);
            }
        });

        function selectPlan(planType) {
            selectedPlan = planType;
            const plan = plans[planType];
            
            // عرض قسم الخطة المختارة
            document.getElementById('selectedPlanSection').style.display = 'block';
            
            // تحديث معلومات الخطة
            const iconElement = document.getElementById('selectedPlanIcon');
            iconElement.innerHTML = '<i class="' + plan.icon + '"></i>';
            iconElement.style.background = plan.color;
            
            document.getElementById('selectedPlanName').textContent = plan.name;
            document.getElementById('selectedPlanPrice').textContent = plan.price;
            
            // تحديث الميزات
            const featuresElement = document.getElementById('selectedPlanFeatures');
            featuresElement.innerHTML = '';
            plan.features.forEach(function(feature) {
                const li = document.createElement('li');
                li.innerHTML = '<i class="fas fa-check"></i>' + feature;
                featuresElement.appendChild(li);
            });
        }

        function changePlan() {
            Swal.fire({
                title: 'اختيار خطة الاشتراك',
                html: createPlanSelectionHTML(),
                showCancelButton: true,
                confirmButtonText: 'تأكيد الاختيار',
                cancelButtonText: 'إلغاء',
                width: '800px',
                customClass: {
                    popup: 'plan-selection-modal'
                }
            });
        }

        function createPlanSelectionHTML() {
            let html = '<div style="text-align: right;">';
            
            Object.keys(plans).forEach(function(key) {
                const plan = plans[key];
                const isSelected = selectedPlan === key;
                
                html += '<div onclick="selectPlanInModal(\'' + key + '\')" style="' +
                    'border: 2px solid ' + (isSelected ? '#D2691E' : 'transparent') + '; ' +
                    'background: ' + (isSelected ? 'rgba(210, 105, 30, 0.1)' : 'rgba(255,255,255,0.05)') + '; ' +
                    'border-radius: 12px; padding: 1rem; margin-bottom: 1rem; cursor: pointer; transition: all 0.3s ease;">' +
                    '<div style="display: flex; align-items: center; gap: 1rem;">' +
                    '<div style="width: 40px; height: 40px; background: ' + plan.color + '; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white;">' +
                    '<i class="' + plan.icon + '"></i>' +
                    '</div>' +
                    '<div>' +
                    '<h4 style="margin: 0; color: white;">' + plan.name + '</h4>' +
                    '<p style="margin: 0; color: #D2691E; font-weight: 600;">' + plan.price + '</p>' +
                    '</div>' +
                    '</div>' +
                    '</div>';
            });
            
            html += '</div>';
            return html;
        }

        function selectPlanInModal(planType) {
            // إزالة التحديد من جميع الخطط
            document.querySelectorAll('[onclick^="selectPlanInModal"]').forEach(function(element) {
                element.style.border = '2px solid transparent';
                element.style.background = 'rgba(255,255,255,0.05)';
            });
            
            // تحديد الخطة الجديدة
            event.target.closest('[onclick^="selectPlanInModal"]').style.border = '2px solid #D2691E';
            event.target.closest('[onclick^="selectPlanInModal"]').style.background = 'rgba(210, 105, 30, 0.1)';
            
            selectedPlan = planType;
            
            // تحديث الخطة المعروضة
            setTimeout(function() {
                selectPlan(planType);
                Swal.close();
            }, 500);
        }

        function submitRegistration() {
            // التحقق من الحقول المطلوبة
            const requiredFields = ['fullName', 'birthDate', 'gender', 'nationalId', 'phone', 'email', 'address'];
            let isValid = true;
            
            requiredFields.forEach(function(fieldId) {
                const field = document.getElementById(fieldId);
                if (!field.value.trim()) {
                    field.style.borderColor = '#ef4444';
                    isValid = false;
                } else {
                    field.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                }
            });
            
            if (!isValid) {
                Swal.fire({
                    title: 'خطأ في البيانات',
                    text: 'يرجى ملء جميع الحقول المطلوبة',
                    icon: 'error',
                    confirmButtonColor: '#8B4513'
                });
                return;
            }
            
            if (!selectedPlan) {
                Swal.fire({
                    title: 'لم يتم اختيار خطة',
                    text: 'يرجى اختيار خطة اشتراك قبل المتابعة',
                    icon: 'warning',
                    confirmButtonColor: '#8B4513'
                });
                return;
            }
            
            // جمع البيانات
            const formData = {
                fullName: document.getElementById('fullName').value,
                birthDate: document.getElementById('birthDate').value,
                gender: document.getElementById('gender').value,
                nationalId: document.getElementById('nationalId').value,
                phone: document.getElementById('phone').value,
                email: document.getElementById('email').value,
                address: document.getElementById('address').value,
                guardianName: document.getElementById('guardianName').value,
                guardianPhone: document.getElementById('guardianPhone').value,
                selectedPlan: selectedPlan,
                registrationDate: new Date().toISOString()
            };
            
            // حفظ البيانات في localStorage
            let registrations = JSON.parse(localStorage.getItem('academy_registrations') || '[]');
            registrations.push(formData);
            localStorage.setItem('academy_registrations', JSON.stringify(registrations));
            
            // عرض رسالة نجاح
            Swal.fire({
                title: 'تم التسجيل بنجاح!',
                html: '<div style="text-align: right;">' +
                    '<p>تم إرسال طلب التسجيل بنجاح في ' + plans[selectedPlan].name + '</p>' +
                    '<p>سيتم التواصل معك خلال 24 ساعة لتأكيد التسجيل وترتيب موعد البدء.</p>' +
                    '<div style="background: rgba(16, 185, 129, 0.1); border: 1px solid rgba(16, 185, 129, 0.3); border-radius: 8px; padding: 1rem; margin-top: 1rem;">' +
                    '<h5 style="color: #10b981; margin-bottom: 0.5rem;">معلومات التسجيل:</h5>' +
                    '<p style="margin: 0; color: #64748b;">الاسم: ' + formData.fullName + '</p>' +
                    '<p style="margin: 0; color: #64748b;">الخطة: ' + plans[selectedPlan].name + '</p>' +
                    '<p style="margin: 0; color: #64748b;">البريد الإلكتروني: ' + formData.email + '</p>' +
                    '</div>' +
                    '</div>',
                icon: 'success',
                confirmButtonColor: '#8B4513',
                confirmButtonText: 'العودة للصفحة الرئيسية'
            }).then(function() {
                window.location.href = 'homepage.html';
            });
        }

        console.log('✅ تم تحميل صفحة التسجيل بنجاح');
    </script>
</body>
</html>
