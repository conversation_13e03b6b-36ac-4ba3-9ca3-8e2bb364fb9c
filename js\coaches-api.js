/**
 * نظام API للاتصال بقاعدة بيانات المدربين
 * يتصل مباشرة بـ coaches7c database عبر coaches_management.php
 */

class CoachesAPI {
    constructor() {
        this.baseURL = './api/coaches_management.php';
        this.isOnline = navigator.onLine;
        this.cache = new Map();
        
        // مراقبة حالة الاتصال
        window.addEventListener('online', () => {
            this.isOnline = true;
            console.log('✅ تم استعادة الاتصال بالإنترنت');
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            console.log('⚠️ انقطع الاتصال بالإنترنت - سيتم استخدام البيانات المحفوظة');
        });
    }

    /**
     * إرسال طلب HTTP مع معالجة الأخطاء
     */
    async makeRequest(url, options = {}) {
        try {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            };

            const finalOptions = { ...defaultOptions, ...options };
            
            console.log(`🌐 إرسال طلب: ${options.method || 'GET'} ${url}`);
            
            const response = await fetch(url, finalOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP Error: ${response.status} - ${response.statusText}`);
            }

            const data = await response.json();
            
            if (!data.success && data.error) {
                throw new Error(data.error);
            }

            return data;
            
        } catch (error) {
            console.error('❌ خطأ في الطلب:', error);
            throw error;
        }
    }

    /**
     * اختبار الاتصال بـ API
     */
    async testConnection() {
        try {
            const response = await this.makeRequest(`${this.baseURL}?test=1`);
            console.log('✅ اختبار API نجح:', response);
            return response;
        } catch (error) {
            console.error('❌ فشل اختبار API:', error);
            throw error;
        }
    }

    /**
     * جلب جميع المدربين مع إمكانية البحث والفلترة
     */
    async getAllCoaches(params = {}) {
        try {
            const queryParams = new URLSearchParams(params);
            const url = `${this.baseURL}?${queryParams}`;
            
            const response = await this.makeRequest(url);
            
            if (response.success) {
                console.log(`✅ تم جلب ${response.data.length} مدرب من قاعدة البيانات`);
                return response.data;
            } else {
                throw new Error(response.error || 'فشل في جلب المدربين');
            }
            
        } catch (error) {
            console.error('❌ خطأ في جلب المدربين:', error);
            throw error;
        }
    }

    /**
     * جلب مدرب واحد بالمعرف
     */
    async getCoachById(id) {
        try {
            const url = `${this.baseURL}?id=${id}`;
            const response = await this.makeRequest(url);
            
            if (response.success) {
                console.log(`✅ تم جلب بيانات المدرب: ${id}`);
                return response.data;
            } else {
                throw new Error(response.error || 'المدرب غير موجود');
            }
            
        } catch (error) {
            console.error(`❌ خطأ في جلب المدرب ${id}:`, error);
            throw error;
        }
    }

    /**
     * إضافة مدرب جديد
     */
    async addCoach(coachData) {
        try {
            const response = await this.makeRequest(this.baseURL, {
                method: 'POST',
                body: JSON.stringify(coachData)
            });
            
            if (response.success) {
                console.log('✅ تم إضافة المدرب بنجاح:', response.data);
                return response.data;
            } else {
                throw new Error(response.error || 'فشل في إضافة المدرب');
            }
            
        } catch (error) {
            console.error('❌ خطأ في إضافة المدرب:', error);
            throw error;
        }
    }

    /**
     * تحديث بيانات مدرب
     */
    async updateCoach(id, coachData) {
        try {
            const response = await this.makeRequest(`${this.baseURL}?id=${id}`, {
                method: 'PUT',
                body: JSON.stringify(coachData)
            });
            
            if (response.success) {
                console.log(`✅ تم تحديث المدرب ${id} بنجاح`);
                return response.data;
            } else {
                throw new Error(response.error || 'فشل في تحديث المدرب');
            }
            
        } catch (error) {
            console.error(`❌ خطأ في تحديث المدرب ${id}:`, error);
            throw error;
        }
    }

    /**
     * حذف مدرب
     */
    async deleteCoach(id) {
        try {
            const response = await this.makeRequest(`${this.baseURL}?id=${id}`, {
                method: 'DELETE'
            });
            
            if (response.success) {
                console.log(`✅ تم حذف المدرب ${id} بنجاح`);
                return true;
            } else {
                throw new Error(response.error || 'فشل في حذف المدرب');
            }
            
        } catch (error) {
            console.error(`❌ خطأ في حذف المدرب ${id}:`, error);
            throw error;
        }
    }

    /**
     * البحث في المدربين
     */
    async searchCoaches(searchTerm) {
        try {
            return await this.getAllCoaches({ search: searchTerm });
        } catch (error) {
            console.error('❌ خطأ في البحث:', error);
            throw error;
        }
    }

    /**
     * فلترة المدربين حسب التخصص
     */
    async filterBySpecialization(specialization) {
        try {
            return await this.getAllCoaches({ specialization: specialization });
        } catch (error) {
            console.error('❌ خطأ في الفلترة:', error);
            throw error;
        }
    }

    /**
     * جلب إحصائيات المدربين
     */
    async getCoachesStats() {
        try {
            const url = `${this.baseURL}?action=dashboard_data`;
            const response = await this.makeRequest(url);
            
            if (response.success) {
                console.log('✅ تم جلب إحصائيات المدربين');
                return response.data;
            } else {
                throw new Error(response.error || 'فشل في جلب الإحصائيات');
            }
            
        } catch (error) {
            console.error('❌ خطأ في جلب الإحصائيات:', error);
            throw error;
        }
    }
}

// إنشاء مثيل عام للـ API
const coachesAPI = new CoachesAPI();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CoachesAPI;
}

// رسالة تأكيد التحميل
console.log('✅ تم تحميل نظام API للمدربين - جاهز للاتصال بقاعدة البيانات');
