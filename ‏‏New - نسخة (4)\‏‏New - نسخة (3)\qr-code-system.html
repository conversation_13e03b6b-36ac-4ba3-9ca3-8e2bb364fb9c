<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام QR Code الذكي - أكاديمية 7C</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
    <style>
        :root {
            --primary-color: #1e40af;
            --secondary-color: #3b82f6;
            --accent-color: #60a5fa;
            --success-color: #228B22;
            --warning-color: #DAA520;
            --danger-color: #B22222;
            --text-dark: #ffffff;
            --text-light: #cccccc;
            --bg-light: #1a1a1a;
            --card-light: #2d2d2d;
            --input-bg: #3d3d3d;
            --border-color: #4d4d4d;
            --hover-bg: #404040;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--bg-light);
            color: var(--text-dark);
            transition: all 0.3s ease;
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card {
            background: var(--card-light);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            border: 1px solid rgba(139, 69, 19, 0.2);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(139, 69, 19, 0.3);
        }

        .qr-glow {
            box-shadow: 0 0 30px rgba(30, 64, 175, 0.5);
            animation: qrGlow 3s ease-in-out infinite alternate;
        }

        @keyframes qrGlow {
            from { box-shadow: 0 0 20px rgba(30, 64, 175, 0.3); }
            to { box-shadow: 0 0 40px rgba(30, 64, 175, 0.7); }
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
        }

        .search-box {
            background: var(--input-bg);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 12px 20px;
            color: var(--text-dark);
            outline: none;
            transition: all 0.3s ease;
        }

        .search-box:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.2);
        }

        .qr-scanner-container {
            background: #000;
            border-radius: 15px;
            overflow: hidden;
            position: relative;
        }

        .scanner-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            border: 3px solid var(--primary-color);
            border-radius: 15px;
            box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
        }

        .scanner-overlay::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            border: 3px solid var(--secondary-color);
            border-radius: 15px;
            animation: scannerPulse 2s infinite;
        }

        @keyframes scannerPulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.05); }
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            color: white;
        }

        .status-active { background: linear-gradient(135deg, #10b981, #059669); }
        .status-inactive { background: linear-gradient(135deg, #ef4444, #dc2626); }
        .status-pending { background: linear-gradient(135deg, #fbbf24, #f59e0b); }

        .qr-code-display {
            background: white;
            padding: 20px;
            border-radius: 15px;
            display: inline-block;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .attendance-log {
            max-height: 400px;
            overflow-y: auto;
        }

        .attendance-item {
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .attendance-item:hover {
            background: var(--hover-bg);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .stat-card {
            background: var(--card-light);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(139, 69, 19, 0.2);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
            margin: 10px 0;
        }

        .mobile-menu-btn {
            display: none;
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .scanner-overlay {
                width: 150px;
                height: 150px;
            }
        }

        /* Loading Animation */
        .loading-spinner {
            border: 4px solid rgba(139, 69, 19, 0.3);
            border-radius: 50%;
            border-top: 4px solid var(--primary-color);
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Tab Content Styles */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block !important;
        }

        .hidden {
            display: none !important;
        }

        /* Print Styles */
        @media print {
            body { background: white; color: black; }
            .no-print { display: none; }
            .qr-code-display {
                background: white;
                box-shadow: none;
                border: 2px solid #000;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="bg-gradient-to-r from-blue-900 to-blue-700 text-white p-6 shadow-lg">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center">
                <button class="mobile-menu-btn mr-4 p-2 rounded-lg hover:bg-white hover:bg-opacity-20" onclick="toggleMobileMenu()">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-white rounded-xl flex items-center justify-center text-2xl font-bold text-blue-800 ml-4">
                        7C
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold">نظام QR Code الذكي</h1>
                        <p class="text-blue-100">إدارة متقدمة لرموز QR والحضور التلقائي</p>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-4 space-x-reverse">
                <button onclick="window.location.href='admin-advanced.html'" class="bg-white bg-opacity-20 px-4 py-2 rounded-lg hover:bg-opacity-30 transition-all">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة للوحة التحكم
                </button>
                <div class="flex items-center space-x-3 space-x-reverse">
                    <img src="https://via.placeholder.com/40x40/8B4513/FFFFFF?text=Admin" alt="Admin" class="w-10 h-10 rounded-full">
                    <div>
                        <p class="font-semibold">مدير النظام</p>
                        <p class="text-sm text-amber-100">متصل الآن</p>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container mx-auto p-6">
        <!-- Navigation Tabs -->
        <div class="flex flex-wrap gap-2 mb-8 bg-gray-800 p-2 rounded-xl">
            <button class="tab-btn active" data-tab="dashboard" onclick="switchTab('dashboard')">
                <i class="fas fa-tachometer-alt ml-2"></i>
                لوحة التحكم
            </button>
            <button class="tab-btn" data-tab="generate" onclick="switchTab('generate')">
                <i class="fas fa-plus-circle ml-2"></i>
                إنشاء QR Code
            </button>
            <button class="tab-btn" data-tab="scan" onclick="switchTab('scan')">
                <i class="fas fa-camera ml-2"></i>
                مسح QR Code
            </button>
            <button class="tab-btn" data-tab="attendance" onclick="switchTab('attendance')">
                <i class="fas fa-clock ml-2"></i>
                سجل الحضور
            </button>
            <button class="tab-btn" data-tab="reports" onclick="switchTab('reports')">
                <i class="fas fa-chart-bar ml-2"></i>
                التقارير
            </button>
            <button class="tab-btn" data-tab="settings" onclick="switchTab('settings')">
                <i class="fas fa-cog ml-2"></i>
                الإعدادات
            </button>
        </div>

        <!-- Dashboard Tab -->
        <div id="dashboard-tab" class="tab-content active">
            <!-- Statistics Cards -->
            <div class="stats-grid mb-8">
                <div class="stat-card">
                    <i class="fas fa-qrcode text-4xl text-blue-600 mb-3"></i>
                    <div class="stat-number" id="total-qr-codes">0</div>
                    <p class="text-gray-400">إجمالي رموز QR</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-user-check text-4xl text-green-600 mb-3"></i>
                    <div class="stat-number" id="active-players">0</div>
                    <p class="text-gray-400">لاعبين نشطين</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-calendar-check text-4xl text-purple-600 mb-3"></i>
                    <div class="stat-number" id="today-scans">0</div>
                    <p class="text-gray-400">مسح اليوم</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-link text-4xl text-orange-600 mb-3"></i>
                    <div class="stat-number">
                        <a href="attendance-system.html" class="text-orange-600 hover:text-orange-500">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                    <p class="text-gray-400">نظام الحضور الرئيسي</p>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <div class="card p-6 text-center">
                    <i class="fas fa-plus-circle text-4xl text-blue-600 mb-4"></i>
                    <h3 class="text-lg font-bold mb-2">إنشاء QR Code</h3>
                    <p class="text-gray-400 mb-4">إنشاء رمز QR جديد للاعب</p>
                    <button onclick="switchTab('generate')" class="btn-primary">
                        ابدأ الآن
                    </button>
                </div>

                <div class="card p-6 text-center">
                    <i class="fas fa-camera text-4xl text-green-600 mb-4"></i>
                    <h3 class="text-lg font-bold mb-2">مسح QR Code</h3>
                    <p class="text-gray-400 mb-4">مسح رمز QR لتسجيل الحضور</p>
                    <button onclick="switchTab('scan')" class="btn-primary">
                        ابدأ المسح
                    </button>
                </div>

                <div class="card p-6 text-center">
                    <i class="fas fa-chart-bar text-4xl text-purple-600 mb-4"></i>
                    <h3 class="text-lg font-bold mb-2">التقارير</h3>
                    <p class="text-gray-400 mb-4">عرض إحصائيات الاستخدام</p>
                    <button onclick="switchTab('reports')" class="btn-primary">
                        عرض التقارير
                    </button>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card p-6">
                <h3 class="text-xl font-bold mb-4 flex items-center">
                    <i class="fas fa-clock text-blue-600 ml-3"></i>
                    آخر عمليات المسح
                </h3>
                <div class="space-y-3" id="recent-scans">
                    <div class="text-center text-gray-400 py-4">
                        <i class="fas fa-history text-3xl mb-2"></i>
                        <p>لا توجد عمليات مسح حديثة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Generate QR Code Tab -->
        <div id="generate-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Player Selection -->
                <div class="card p-6">
                    <h3 class="text-xl font-bold mb-6 flex items-center">
                        <i class="fas fa-user-plus text-blue-600 ml-3"></i>
                        اختيار اللاعب
                    </h3>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">البحث عن لاعب</label>
                            <input type="text" id="player-search" placeholder="ابحث بالاسم أو الرقم الأكاديمي..."
                                   class="search-box w-full" oninput="searchPlayers()">
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">اللاعبين المتاحين</label>
                            <div class="max-h-64 overflow-y-auto border border-gray-600 rounded-lg">
                                <div id="players-list" class="space-y-2 p-3">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </div>
                            </div>
                        </div>

                        <div id="selected-player-info" class="hidden bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">معلومات اللاعب المحدد:</h4>
                            <div id="player-details"></div>
                        </div>

                        <button id="generate-qr-btn" onclick="generateQRCode()"
                                class="btn-primary w-full disabled:opacity-50" disabled>
                            <i class="fas fa-qrcode ml-2"></i>
                            إنشاء QR Code
                        </button>
                    </div>
                </div>

                <!-- QR Code Display -->
                <div class="card p-6">
                    <h3 class="text-xl font-bold mb-6 flex items-center">
                        <i class="fas fa-qrcode text-green-600 ml-3"></i>
                        QR Code المُنشأ
                    </h3>

                    <div id="qr-display-area" class="text-center">
                        <div class="border-2 border-dashed border-gray-600 rounded-lg p-8 mb-4">
                            <i class="fas fa-qrcode text-6xl text-gray-500 mb-4"></i>
                            <p class="text-gray-400">سيظهر QR Code هنا بعد الإنشاء</p>
                        </div>
                    </div>

                    <div id="qr-actions" class="hidden space-y-3">
                        <button onclick="downloadQRCode()" class="btn-primary w-full">
                            <i class="fas fa-download ml-2"></i>
                            تحميل QR Code
                        </button>
                        <button onclick="printQRCode()" class="btn-primary w-full">
                            <i class="fas fa-print ml-2"></i>
                            طباعة QR Code
                        </button>
                        <button onclick="shareQRCode()" class="btn-primary w-full">
                            <i class="fas fa-share ml-2"></i>
                            مشاركة QR Code
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scan QR Code Tab -->
        <div id="scan-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Scanner -->
                <div class="card p-6">
                    <h3 class="text-xl font-bold mb-6 flex items-center">
                        <i class="fas fa-camera text-blue-600 ml-3"></i>
                        مسح QR Code للحضور
                    </h3>

                    <div class="space-y-4">
                        <div class="qr-scanner-container" id="qr-scanner">
                            <div id="scanner-placeholder" class="h-64 flex items-center justify-center bg-gray-800 rounded-lg">
                                <div class="text-center">
                                    <i class="fas fa-camera text-4xl text-gray-500 mb-3"></i>
                                    <p class="text-gray-400">انقر لبدء المسح</p>
                                </div>
                            </div>
                            <div class="scanner-overlay hidden"></div>
                        </div>

                        <div class="flex space-x-3 space-x-reverse">
                            <button id="start-scan-btn" onclick="startScanning()" class="btn-primary flex-1">
                                <i class="fas fa-play ml-2"></i>
                                بدء المسح
                            </button>
                            <button id="stop-scan-btn" onclick="stopScanning()" class="btn-primary flex-1 hidden">
                                <i class="fas fa-stop ml-2"></i>
                                إيقاف المسح
                            </button>
                        </div>

                        <div class="text-center">
                            <p class="text-sm text-gray-400 mb-2">أو</p>
                            <input type="file" id="qr-file-input" accept="image/*" class="hidden" onchange="scanFromFile()">
                            <button onclick="document.getElementById('qr-file-input').click()"
                                    class="text-blue-600 hover:text-blue-500 transition-colors">
                                <i class="fas fa-upload ml-2"></i>
                                رفع صورة QR Code
                            </button>
                        </div>

                        <div class="bg-blue-900 bg-opacity-50 p-4 rounded-lg">
                            <h4 class="font-semibold mb-2 flex items-center">
                                <i class="fas fa-info-circle text-blue-400 ml-2"></i>
                                ملاحظة مهمة
                            </h4>
                            <p class="text-sm text-blue-200">
                                سيتم تسجيل الحضور تلقائياً في نظام الحضور الرئيسي عند مسح QR Code بنجاح
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Scan Results -->
                <div class="card p-6">
                    <h3 class="text-xl font-bold mb-6 flex items-center">
                        <i class="fas fa-info-circle text-green-600 ml-3"></i>
                        نتائج المسح
                    </h3>

                    <div id="scan-results" class="space-y-4">
                        <div class="text-center text-gray-400 py-8">
                            <i class="fas fa-search text-4xl mb-3"></i>
                            <p>لم يتم مسح أي QR Code بعد</p>
                        </div>
                    </div>

                    <div id="attendance-actions" class="hidden space-y-3 mt-6">
                        <button onclick="confirmAttendance()" class="btn-primary w-full">
                            <i class="fas fa-check ml-2"></i>
                            تأكيد تسجيل الحضور
                        </button>
                        <button onclick="openAttendanceSystem()" class="btn-primary w-full">
                            <i class="fas fa-external-link-alt ml-2"></i>
                            فتح نظام الحضور الرئيسي
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reports Tab -->
        <div id="reports-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="card p-6">
                    <h3 class="text-xl font-bold mb-6 flex items-center">
                        <i class="fas fa-chart-pie text-purple-600 ml-3"></i>
                        إحصائيات الاستخدام
                    </h3>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center p-3 bg-gray-700 rounded-lg">
                            <span>إجمالي رموز QR المُنشأة</span>
                            <span class="font-bold text-blue-400" id="report-total-qr">0</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-700 rounded-lg">
                            <span>عمليات المسح اليوم</span>
                            <span class="font-bold text-green-400" id="report-today-scans">0</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-700 rounded-lg">
                            <span>معدل نجاح المسح</span>
                            <span class="font-bold text-purple-400" id="report-success-rate">100%</span>
                        </div>
                    </div>
                </div>

                <div class="card p-6">
                    <h3 class="text-xl font-bold mb-6 flex items-center">
                        <i class="fas fa-download text-green-600 ml-3"></i>
                        تصدير البيانات
                    </h3>
                    <div class="space-y-3">
                        <button onclick="exportQRCodes()" class="btn-primary w-full">
                            <i class="fas fa-qrcode ml-2"></i>
                            تصدير جميع رموز QR
                        </button>
                        <button onclick="exportScanHistory()" class="btn-primary w-full">
                            <i class="fas fa-history ml-2"></i>
                            تصدير سجل المسح
                        </button>
                        <button onclick="exportAttendanceReport()" class="btn-primary w-full">
                            <i class="fas fa-file-excel ml-2"></i>
                            تقرير الحضور الشامل
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div id="settings-tab" class="tab-content hidden">
            <div class="card p-6">
                <h3 class="text-xl font-bold mb-6 flex items-center">
                    <i class="fas fa-cog text-gray-600 ml-3"></i>
                    إعدادات النظام
                </h3>

                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium mb-2">حجم QR Code</label>
                        <select id="qr-size-setting" class="search-box w-full">
                            <option value="200">صغير (200x200)</option>
                            <option value="300" selected>متوسط (300x300)</option>
                            <option value="400">كبير (400x400)</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium mb-2">مستوى تصحيح الأخطاء</label>
                        <select id="qr-error-correction" class="search-box w-full">
                            <option value="L">منخفض (7%)</option>
                            <option value="M" selected>متوسط (15%)</option>
                            <option value="Q">عالي (25%)</option>
                            <option value="H">عالي جداً (30%)</option>
                        </select>
                    </div>

                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" id="auto-attendance" class="ml-2" checked>
                            <span>تسجيل الحضور التلقائي عند المسح</span>
                        </label>
                    </div>

                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" id="sound-notifications" class="ml-2" checked>
                            <span>تفعيل الأصوات التنبيهية</span>
                        </label>
                    </div>

                    <button onclick="saveSettings()" class="btn-primary">
                        <i class="fas fa-save ml-2"></i>
                        حفظ الإعدادات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // ==================== متغيرات عامة ====================
        let selectedPlayer = null;
        let currentQRCode = null;
        let html5QrCode = null;
        let scanHistory = [];
        let qrCodes = [];
        let settings = {
            qrSize: 300,
            errorCorrection: 'M',
            autoAttendance: true,
            soundNotifications: true
        };

        // ==================== تحميل البيانات ====================
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء تحميل نظام QR Code...');

            // إضافة بيانات تجريبية إذا لم توجد
            addTestDataIfNeeded();

            loadSettings();
            loadPlayers();
            loadQRCodes();
            loadScanHistory();
            updateStatistics();
            setupTabStyles();
            console.log('✅ نظام QR Code جاهز!');
        });

        // ==================== إضافة بيانات تجريبية ====================
        function addTestDataIfNeeded() {
            // التحقق من وجود بيانات لاعبين
            let hasPlayers = false;
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('academy_player_')) {
                    hasPlayers = true;
                    break;
                }
            }

            // إضافة بيانات تجريبية إذا لم توجد
            if (!hasPlayers) {
                const testPlayers = [
                    {
                        id: 'DEMO001',
                        firstName: 'أحمد',
                        familyName: 'محمد',
                        academicNumber: '7C-2024-DEMO001',
                        age: 16,
                        category: 'ناشئين',
                        joinDate: '2024-01-15',
                        loyaltyPoints: 50,
                        subscriptionStatus: 'active'
                    },
                    {
                        id: 'DEMO002',
                        firstName: 'سارة',
                        familyName: 'علي',
                        academicNumber: '7C-2024-DEMO002',
                        age: 14,
                        category: 'براعم',
                        joinDate: '2024-02-01',
                        loyaltyPoints: 30,
                        subscriptionStatus: 'active'
                    },
                    {
                        id: 'DEMO003',
                        firstName: 'محمد',
                        familyName: 'خالد',
                        academicNumber: '7C-2024-DEMO003',
                        age: 18,
                        category: 'شباب',
                        joinDate: '2024-01-20',
                        loyaltyPoints: 75,
                        subscriptionStatus: 'active'
                    }
                ];

                testPlayers.forEach(player => {
                    const key = `academy_player_${player.academicNumber}`;
                    localStorage.setItem(key, JSON.stringify(player));
                });

                console.log('✅ تم إضافة بيانات تجريبية للاختبار');

                // إظهار إشعار للمستخدم
                setTimeout(() => {
                    showNotification('تم إضافة بيانات تجريبية للاختبار - يمكنك الآن إنشاء QR Code', 'info');
                }, 2000);
            }
        }

        // ==================== إعداد أنماط التبويبات ====================
        function setupTabStyles() {
            const style = document.createElement('style');
            style.textContent = `
                .tab-btn {
                    padding: 12px 20px;
                    background: rgba(255, 255, 255, 0.1);
                    color: #cccccc;
                    border: none;
                    border-radius: 8px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    font-weight: 500;
                }
                .tab-btn:hover {
                    background: rgba(30, 64, 175, 0.3);
                    color: white;
                }
                .tab-btn.active {
                    background: linear-gradient(135deg, #1e40af, #3b82f6);
                    color: white;
                    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
                }
            `;
            document.head.appendChild(style);
        }

        // ==================== إدارة التبويبات ====================
        function switchTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
                tab.classList.remove('active');
            });

            // إزالة الحالة النشطة من جميع الأزرار
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // إظهار التبويب المحدد
            const targetTab = document.getElementById(tabName + '-tab');
            if (targetTab) {
                targetTab.classList.remove('hidden');
                targetTab.classList.add('active');
            }

            // تفعيل الزر المحدد
            const targetBtn = document.querySelector(`[data-tab="${tabName}"]`);
            if (targetBtn) {
                targetBtn.classList.add('active');
            }

            // تحديث البيانات حسب التبويب
            if (tabName === 'dashboard') {
                updateStatistics();
            } else if (tabName === 'generate') {
                loadPlayers();
            } else if (tabName === 'reports') {
                updateReports();
            }
        }

        // ==================== تحميل اللاعبين ====================
        function loadPlayers() {
            try {
                // تحميل اللاعبين من localStorage
                const playersData = [];

                // البحث في مفاتيح localStorage المختلفة
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (key.startsWith('academy_player_') || key.startsWith('player_'))) {
                        try {
                            const playerData = JSON.parse(localStorage.getItem(key));
                            if (playerData && playerData.academicNumber) {
                                playersData.push(playerData);
                            }
                        } catch (e) {
                            console.warn('خطأ في تحميل بيانات اللاعب:', key);
                        }
                    }
                }

                // إزالة التكرارات
                const uniquePlayers = playersData.filter((player, index, self) =>
                    index === self.findIndex(p => p.academicNumber === player.academicNumber)
                );

                displayPlayersList(uniquePlayers);
                updateStatistics();

            } catch (error) {
                console.error('خطأ في تحميل اللاعبين:', error);
                showNotification('خطأ في تحميل بيانات اللاعبين', 'error');
            }
        }

        // ==================== عرض قائمة اللاعبين ====================
        function displayPlayersList(players) {
            const playersList = document.getElementById('players-list');
            if (!playersList) return;

            if (players.length === 0) {
                playersList.innerHTML = `
                    <div class="text-center text-gray-400 py-4">
                        <i class="fas fa-users text-3xl mb-2"></i>
                        <p>لا توجد بيانات لاعبين</p>
                        <p class="text-sm">يرجى إضافة لاعبين من نظام إدارة الطلبات</p>
                    </div>
                `;
                return;
            }

            playersList.innerHTML = players.map(player => `
                <div class="player-item p-3 border border-gray-600 rounded-lg cursor-pointer hover:bg-gray-600 transition-all"
                     onclick="selectPlayer('${player.academicNumber}')">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-semibold">${player.firstName || player.name || 'غير محدد'} ${player.familyName || ''}</h4>
                            <p class="text-sm text-gray-400">الرقم الأكاديمي: ${player.academicNumber}</p>
                            <p class="text-sm text-gray-400">الفئة: ${player.category || 'غير محدد'}</p>
                        </div>
                        <div class="text-left">
                            ${hasQRCode(player.academicNumber) ?
                                '<i class="fas fa-qrcode text-green-500" title="يوجد QR Code"></i>' :
                                '<i class="fas fa-plus-circle text-blue-500" title="إنشاء QR Code"></i>'
                            }
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // ==================== البحث في اللاعبين ====================
        function searchPlayers() {
            const searchTerm = document.getElementById('player-search').value.toLowerCase();
            const playerItems = document.querySelectorAll('.player-item');

            playerItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // ==================== اختيار لاعب ====================
        function selectPlayer(academicNumber) {
            try {
                // البحث عن اللاعب
                let playerData = null;
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.includes(academicNumber)) {
                        try {
                            const data = JSON.parse(localStorage.getItem(key));
                            if (data && data.academicNumber === academicNumber) {
                                playerData = data;
                                break;
                            }
                        } catch (e) {
                            continue;
                        }
                    }
                }

                if (!playerData) {
                    showNotification('لم يتم العثور على بيانات اللاعب', 'error');
                    return;
                }

                selectedPlayer = playerData;

                // عرض معلومات اللاعب
                const playerInfo = document.getElementById('selected-player-info');
                const playerDetails = document.getElementById('player-details');

                if (playerInfo && playerDetails) {
                    playerDetails.innerHTML = `
                        <div class="space-y-2">
                            <p><strong>الاسم:</strong> ${playerData.firstName || playerData.name || 'غير محدد'} ${playerData.familyName || ''}</p>
                            <p><strong>الرقم الأكاديمي:</strong> ${playerData.academicNumber}</p>
                            <p><strong>الفئة العمرية:</strong> ${playerData.category || 'غير محدد'}</p>
                            <p><strong>تاريخ الانضمام:</strong> ${playerData.joinDate || 'غير محدد'}</p>
                            <p><strong>الحالة:</strong> <span class="status-badge status-active">نشط</span></p>
                        </div>
                    `;
                    playerInfo.classList.remove('hidden');
                }

                // تفعيل زر الإنشاء
                const generateBtn = document.getElementById('generate-qr-btn');
                if (generateBtn) {
                    generateBtn.disabled = false;
                    generateBtn.classList.remove('disabled:opacity-50');
                }

                // تحديد العنصر المختار
                document.querySelectorAll('.player-item').forEach(item => {
                    item.classList.remove('bg-blue-600');
                });
                event.target.closest('.player-item').classList.add('bg-blue-600');

            } catch (error) {
                console.error('خطأ في اختيار اللاعب:', error);
                showNotification('خطأ في اختيار اللاعب', 'error');
            }
        }

        // ==================== إنشاء QR Code ====================
        function generateQRCode() {
            if (!selectedPlayer) {
                showNotification('يرجى اختيار لاعب أولاً', 'warning');
                return;
            }

            try {
                // إنشاء بيانات QR Code
                const qrData = {
                    type: 'academy_player',
                    academicNumber: selectedPlayer.academicNumber,
                    playerName: selectedPlayer.firstName || selectedPlayer.name || 'غير محدد',
                    category: selectedPlayer.category || 'غير محدد',
                    joinDate: selectedPlayer.joinDate || new Date().toISOString().split('T')[0],
                    timestamp: new Date().toISOString()
                };

                const qrString = JSON.stringify(qrData);

                // إنشاء QR Code
                const canvas = document.createElement('canvas');
                QRCode.toCanvas(canvas, qrString, {
                    width: settings.qrSize,
                    margin: 2,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    },
                    errorCorrectionLevel: settings.errorCorrection
                }, function (error) {
                    if (error) {
                        console.error('خطأ في إنشاء QR Code:', error);
                        showNotification('خطأ في إنشاء QR Code', 'error');
                        return;
                    }

                    // عرض QR Code
                    const displayArea = document.getElementById('qr-display-area');
                    displayArea.innerHTML = `
                        <div class="qr-code-display qr-glow">
                            <div class="text-center mb-4">
                                <h4 class="font-bold text-gray-800">${qrData.playerName}</h4>
                                <p class="text-sm text-gray-600">${qrData.academicNumber}</p>
                            </div>
                            ${canvas.outerHTML}
                            <div class="text-center mt-4">
                                <p class="text-xs text-gray-600">أكاديمية 7C للتدريب الرياضي</p>
                                <p class="text-xs text-gray-500">${new Date().toLocaleDateString('ar-SA')}</p>
                            </div>
                        </div>
                    `;

                    // إظهار أزرار الإجراءات
                    document.getElementById('qr-actions').classList.remove('hidden');

                    // حفظ QR Code
                    currentQRCode = {
                        ...qrData,
                        canvas: canvas,
                        createdAt: new Date().toISOString()
                    };

                    saveQRCode(currentQRCode);
                    updateStatistics();

                    showNotification('تم إنشاء QR Code بنجاح', 'success');
                });

            } catch (error) {
                console.error('خطأ في إنشاء QR Code:', error);
                showNotification('خطأ في إنشاء QR Code', 'error');
            }
        }

        // ==================== بدء مسح QR Code ====================
        function startScanning() {
            try {
                const scannerElement = document.getElementById('qr-scanner');
                const placeholder = document.getElementById('scanner-placeholder');
                const overlay = document.querySelector('.scanner-overlay');
                const startBtn = document.getElementById('start-scan-btn');
                const stopBtn = document.getElementById('stop-scan-btn');

                // إخفاء placeholder وإظهار overlay
                placeholder.classList.add('hidden');
                overlay.classList.remove('hidden');
                startBtn.classList.add('hidden');
                stopBtn.classList.remove('hidden');

                // إنشاء مسح QR Code
                html5QrCode = new Html5Qrcode("qr-scanner");

                html5QrCode.start(
                    { facingMode: "environment" }, // الكاميرا الخلفية
                    {
                        fps: 10,
                        qrbox: { width: 200, height: 200 }
                    },
                    (decodedText, decodedResult) => {
                        // نجح المسح
                        handleScanSuccess(decodedText);
                        stopScanning();
                    },
                    (errorMessage) => {
                        // خطأ في المسح (طبيعي)
                        console.log('مسح...', errorMessage);
                    }
                ).catch(err => {
                    console.error('خطأ في بدء المسح:', err);
                    showNotification('خطأ في الوصول للكاميرا', 'error');
                    stopScanning();
                });

            } catch (error) {
                console.error('خطأ في بدء المسح:', error);
                showNotification('خطأ في بدء المسح', 'error');
            }
        }

        // ==================== إيقاف مسح QR Code ====================
        function stopScanning() {
            try {
                if (html5QrCode) {
                    html5QrCode.stop().then(() => {
                        html5QrCode.clear();
                        html5QrCode = null;
                    }).catch(err => {
                        console.error('خطأ في إيقاف المسح:', err);
                    });
                }

                // إعادة تعيين الواجهة
                const placeholder = document.getElementById('scanner-placeholder');
                const overlay = document.querySelector('.scanner-overlay');
                const startBtn = document.getElementById('start-scan-btn');
                const stopBtn = document.getElementById('stop-scan-btn');

                placeholder.classList.remove('hidden');
                overlay.classList.add('hidden');
                startBtn.classList.remove('hidden');
                stopBtn.classList.add('hidden');

            } catch (error) {
                console.error('خطأ في إيقاف المسح:', error);
            }
        }

        // ==================== معالجة نجاح المسح ====================
        function handleScanSuccess(decodedText) {
            try {
                // محاولة تحليل البيانات
                let qrData;
                try {
                    qrData = JSON.parse(decodedText);
                } catch (e) {
                    // إذا لم تكن JSON، اعتبرها رقم أكاديمي
                    qrData = { academicNumber: decodedText };
                }

                // التحقق من صحة البيانات
                if (!qrData.academicNumber) {
                    showNotification('QR Code غير صالح - لا يحتوي على رقم أكاديمي', 'error');
                    return;
                }

                // البحث عن اللاعب
                const playerData = findPlayerByAcademicNumber(qrData.academicNumber);
                if (!playerData) {
                    showNotification('لم يتم العثور على اللاعب في النظام', 'error');
                    return;
                }

                // عرض نتائج المسح
                displayScanResults(playerData, qrData);

                // تسجيل المسح
                recordScan(playerData, qrData);

                // تسجيل الحضور التلقائي إذا كان مفعلاً
                if (settings.autoAttendance) {
                    autoMarkAttendance(playerData);
                }

                // تشغيل صوت النجاح
                if (settings.soundNotifications) {
                    playSuccessSound();
                }

                showNotification(`تم مسح QR Code للاعب: ${playerData.firstName || playerData.name}`, 'success');

            } catch (error) {
                console.error('خطأ في معالجة المسح:', error);
                showNotification('خطأ في معالجة QR Code', 'error');
            }
        }

        // ==================== البحث عن لاعب بالرقم الأكاديمي ====================
        function findPlayerByAcademicNumber(academicNumber) {
            try {
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.includes(academicNumber)) {
                        try {
                            const data = JSON.parse(localStorage.getItem(key));
                            if (data && data.academicNumber === academicNumber) {
                                return data;
                            }
                        } catch (e) {
                            continue;
                        }
                    }
                }
                return null;
            } catch (error) {
                console.error('خطأ في البحث عن اللاعب:', error);
                return null;
            }
        }

        // ==================== عرض نتائج المسح ====================
        function displayScanResults(playerData, qrData) {
            const scanResults = document.getElementById('scan-results');
            const attendanceActions = document.getElementById('attendance-actions');

            if (scanResults) {
                scanResults.innerHTML = `
                    <div class="bg-green-900 bg-opacity-50 p-4 rounded-lg border border-green-500">
                        <div class="flex items-center mb-3">
                            <i class="fas fa-check-circle text-green-400 text-2xl ml-3"></i>
                            <h4 class="text-lg font-bold text-green-300">تم المسح بنجاح</h4>
                        </div>
                        <div class="space-y-2">
                            <p><strong>الاسم:</strong> ${playerData.firstName || playerData.name || 'غير محدد'} ${playerData.familyName || ''}</p>
                            <p><strong>الرقم الأكاديمي:</strong> ${playerData.academicNumber}</p>
                            <p><strong>الفئة العمرية:</strong> ${playerData.category || 'غير محدد'}</p>
                            <p><strong>تاريخ الانضمام:</strong> ${playerData.joinDate || 'غير محدد'}</p>
                            <p><strong>وقت المسح:</strong> ${new Date().toLocaleString('ar-SA')}</p>
                        </div>
                    </div>
                `;

                // إظهار أزرار الإجراءات
                if (attendanceActions) {
                    attendanceActions.classList.remove('hidden');
                }
            }
        }

        // ==================== تسجيل الحضور التلقائي ====================
        function autoMarkAttendance(playerData) {
            try {
                // محاكاة تسجيل الحضور في النظام الرئيسي
                const attendanceRecord = {
                    playerId: playerData.academicNumber,
                    playerName: playerData.firstName || playerData.name,
                    status: 'present',
                    timestamp: new Date(),
                    method: 'qr_code',
                    location: 'أكاديمية 7C'
                };

                // حفظ في localStorage
                const attendanceKey = `qr_attendance_${new Date().toISOString().split('T')[0]}`;
                const todayAttendance = JSON.parse(localStorage.getItem(attendanceKey) || '[]');

                // التحقق من عدم التكرار
                const existingRecord = todayAttendance.find(record =>
                    record.playerId === playerData.academicNumber
                );

                if (!existingRecord) {
                    todayAttendance.push(attendanceRecord);
                    localStorage.setItem(attendanceKey, JSON.stringify(todayAttendance));

                    // إضافة نقاط الولاء
                    addLoyaltyPointsForAttendance(playerData);

                    console.log('✅ تم تسجيل الحضور تلقائياً');
                } else {
                    console.log('⚠️ الحضور مسجل مسبقاً');
                }

            } catch (error) {
                console.error('خطأ في تسجيل الحضور التلقائي:', error);
            }
        }

        // ==================== إضافة نقاط الولاء للحضور ====================
        function addLoyaltyPointsForAttendance(playerData) {
            try {
                const pointsToAdd = 10; // نقاط الحضور

                // تحديث نقاط اللاعب
                if (playerData.loyaltyPoints !== undefined) {
                    playerData.loyaltyPoints = (playerData.loyaltyPoints || 0) + pointsToAdd;

                    // حفظ البيانات المحدثة
                    const playerKey = `academy_player_${playerData.academicNumber}`;
                    localStorage.setItem(playerKey, JSON.stringify(playerData));

                    console.log(`✅ تم إضافة ${pointsToAdd} نقطة ولاء للاعب ${playerData.firstName || playerData.name}`);
                }

            } catch (error) {
                console.error('خطأ في إضافة نقاط الولاء:', error);
            }
        }

        // ==================== تسجيل عملية المسح ====================
        function recordScan(playerData, qrData) {
            try {
                const scanRecord = {
                    id: Date.now(),
                    playerId: playerData.academicNumber,
                    playerName: playerData.firstName || playerData.name,
                    scanTime: new Date().toISOString(),
                    qrData: qrData,
                    success: true
                };

                scanHistory.unshift(scanRecord);

                // الاحتفاظ بآخر 100 عملية مسح فقط
                if (scanHistory.length > 100) {
                    scanHistory = scanHistory.slice(0, 100);
                }

                // حفظ في localStorage
                localStorage.setItem('qr_scan_history', JSON.stringify(scanHistory));

                // تحديث عرض آخر العمليات
                updateRecentScans();

            } catch (error) {
                console.error('خطأ في تسجيل عملية المسح:', error);
            }
        }

        // ==================== تحديث آخر عمليات المسح ====================
        function updateRecentScans() {
            const recentScansElement = document.getElementById('recent-scans');
            if (!recentScansElement) return;

            const recentScans = scanHistory.slice(0, 5);

            if (recentScans.length === 0) {
                recentScansElement.innerHTML = `
                    <div class="text-center text-gray-400 py-4">
                        <i class="fas fa-history text-3xl mb-2"></i>
                        <p>لا توجد عمليات مسح حديثة</p>
                    </div>
                `;
                return;
            }

            recentScansElement.innerHTML = recentScans.map(scan => `
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div>
                        <h4 class="font-semibold">${scan.playerName}</h4>
                        <p class="text-sm text-gray-400">${scan.playerId}</p>
                    </div>
                    <div class="text-left">
                        <p class="text-sm text-green-400">
                            <i class="fas fa-check-circle ml-1"></i>
                            نجح
                        </p>
                        <p class="text-xs text-gray-400">
                            ${new Date(scan.scanTime).toLocaleString('ar-SA')}
                        </p>
                    </div>
                </div>
            `).join('');
        }

        // ==================== الدوال المساعدة ====================
        function hasQRCode(academicNumber) {
            const qrCodesData = JSON.parse(localStorage.getItem('qr_codes') || '[]');
            return qrCodesData.some(qr => qr.academicNumber === academicNumber);
        }

        function saveQRCode(qrCodeData) {
            try {
                const qrCodesData = JSON.parse(localStorage.getItem('qr_codes') || '[]');

                // إزالة QR Code القديم للاعب إن وجد
                const filteredQRCodes = qrCodesData.filter(qr =>
                    qr.academicNumber !== qrCodeData.academicNumber
                );

                // إضافة QR Code الجديد
                filteredQRCodes.push({
                    academicNumber: qrCodeData.academicNumber,
                    playerName: qrCodeData.playerName,
                    category: qrCodeData.category,
                    createdAt: qrCodeData.createdAt,
                    qrString: JSON.stringify(qrCodeData)
                });

                localStorage.setItem('qr_codes', JSON.stringify(filteredQRCodes));
                qrCodes = filteredQRCodes;

            } catch (error) {
                console.error('خطأ في حفظ QR Code:', error);
            }
        }

        function loadQRCodes() {
            try {
                qrCodes = JSON.parse(localStorage.getItem('qr_codes') || '[]');
            } catch (error) {
                console.error('خطأ في تحميل QR Codes:', error);
                qrCodes = [];
            }
        }

        function loadScanHistory() {
            try {
                scanHistory = JSON.parse(localStorage.getItem('qr_scan_history') || '[]');
                updateRecentScans();
            } catch (error) {
                console.error('خطأ في تحميل سجل المسح:', error);
                scanHistory = [];
            }
        }

        function loadSettings() {
            try {
                const savedSettings = JSON.parse(localStorage.getItem('qr_settings') || '{}');
                settings = { ...settings, ...savedSettings };

                // تطبيق الإعدادات على الواجهة
                if (document.getElementById('qr-size-setting')) {
                    document.getElementById('qr-size-setting').value = settings.qrSize;
                }
                if (document.getElementById('qr-error-correction')) {
                    document.getElementById('qr-error-correction').value = settings.errorCorrection;
                }
                if (document.getElementById('auto-attendance')) {
                    document.getElementById('auto-attendance').checked = settings.autoAttendance;
                }
                if (document.getElementById('sound-notifications')) {
                    document.getElementById('sound-notifications').checked = settings.soundNotifications;
                }

            } catch (error) {
                console.error('خطأ في تحميل الإعدادات:', error);
            }
        }

        // ==================== تحديث الإحصائيات ====================
        function updateStatistics() {
            try {
                // إجمالي رموز QR
                const totalQRElement = document.getElementById('total-qr-codes');
                if (totalQRElement) {
                    totalQRElement.textContent = qrCodes.length;
                }

                // اللاعبين النشطين
                const activePlayersElement = document.getElementById('active-players');
                if (activePlayersElement) {
                    let activeCount = 0;
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        if (key && key.startsWith('academy_player_')) {
                            activeCount++;
                        }
                    }
                    activePlayersElement.textContent = activeCount;
                }

                // مسح اليوم
                const todayScansElement = document.getElementById('today-scans');
                if (todayScansElement) {
                    const today = new Date().toISOString().split('T')[0];
                    const todayScans = scanHistory.filter(scan =>
                        scan.scanTime.startsWith(today)
                    );
                    todayScansElement.textContent = todayScans.length;
                }

            } catch (error) {
                console.error('خطأ في تحديث الإحصائيات:', error);
            }
        }

        // ==================== دوال الإجراءات ====================
        function downloadQRCode() {
            if (!currentQRCode || !currentQRCode.canvas) {
                showNotification('لا يوجد QR Code للتحميل', 'warning');
                return;
            }

            try {
                const link = document.createElement('a');
                link.download = `QR_${currentQRCode.academicNumber}_${new Date().toISOString().split('T')[0]}.png`;
                link.href = currentQRCode.canvas.toDataURL();
                link.click();

                showNotification('تم تحميل QR Code بنجاح', 'success');
            } catch (error) {
                console.error('خطأ في تحميل QR Code:', error);
                showNotification('خطأ في تحميل QR Code', 'error');
            }
        }

        function printQRCode() {
            if (!currentQRCode) {
                showNotification('لا يوجد QR Code للطباعة', 'warning');
                return;
            }

            try {
                const printWindow = window.open('', '_blank');
                const qrDisplay = document.querySelector('.qr-code-display');

                printWindow.document.write(`
                    <html>
                        <head>
                            <title>طباعة QR Code - ${currentQRCode.playerName}</title>
                            <style>
                                body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
                                .qr-container { border: 2px solid #000; padding: 20px; display: inline-block; }
                                h1 { color: #1e40af; margin-bottom: 10px; }
                                .info { margin: 10px 0; }
                            </style>
                        </head>
                        <body>
                            <div class="qr-container">
                                <h1>أكاديمية 7C للتدريب الرياضي</h1>
                                <div class="info">
                                    <strong>${currentQRCode.playerName}</strong><br>
                                    الرقم الأكاديمي: ${currentQRCode.academicNumber}<br>
                                    الفئة: ${currentQRCode.category}
                                </div>
                                ${qrDisplay.querySelector('canvas').outerHTML}
                                <div class="info">
                                    تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-SA')}
                                </div>
                            </div>
                        </body>
                    </html>
                `);

                printWindow.document.close();
                printWindow.print();

                showNotification('تم إرسال QR Code للطباعة', 'success');
            } catch (error) {
                console.error('خطأ في طباعة QR Code:', error);
                showNotification('خطأ في طباعة QR Code', 'error');
            }
        }

        function confirmAttendance() {
            showNotification('تم تأكيد تسجيل الحضور', 'success');
        }

        function openAttendanceSystem() {
            window.open('attendance-system.html', '_blank');
        }

        function saveSettings() {
            try {
                settings.qrSize = parseInt(document.getElementById('qr-size-setting').value);
                settings.errorCorrection = document.getElementById('qr-error-correction').value;
                settings.autoAttendance = document.getElementById('auto-attendance').checked;
                settings.soundNotifications = document.getElementById('sound-notifications').checked;

                localStorage.setItem('qr_settings', JSON.stringify(settings));
                showNotification('تم حفظ الإعدادات بنجاح', 'success');
            } catch (error) {
                console.error('خطأ في حفظ الإعدادات:', error);
                showNotification('خطأ في حفظ الإعدادات', 'error');
            }
        }

        function updateReports() {
            try {
                document.getElementById('report-total-qr').textContent = qrCodes.length;

                const today = new Date().toISOString().split('T')[0];
                const todayScans = scanHistory.filter(scan => scan.scanTime.startsWith(today));
                document.getElementById('report-today-scans').textContent = todayScans.length;

                const successRate = scanHistory.length > 0 ?
                    Math.round((scanHistory.filter(s => s.success).length / scanHistory.length) * 100) : 100;
                document.getElementById('report-success-rate').textContent = successRate + '%';
            } catch (error) {
                console.error('خطأ في تحديث التقارير:', error);
            }
        }

        // ==================== دوال الإشعارات والأصوات ====================
        function showNotification(message, type = 'info') {
            // استخدام SweetAlert2 للإشعارات
            const icons = {
                success: 'success',
                error: 'error',
                warning: 'warning',
                info: 'info'
            };

            Swal.fire({
                title: message,
                icon: icons[type] || 'info',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                background: '#2d2d2d',
                color: '#ffffff'
            });
        }

        function playSuccessSound() {
            try {
                // إنشاء صوت نجاح بسيط
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);

                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.3);
            } catch (error) {
                console.log('لا يمكن تشغيل الصوت');
            }
        }

        // ==================== دوال التصدير ====================
        function exportQRCodes() {
            try {
                const data = qrCodes.map(qr => ({
                    'الرقم الأكاديمي': qr.academicNumber,
                    'اسم اللاعب': qr.playerName,
                    'الفئة': qr.category,
                    'تاريخ الإنشاء': new Date(qr.createdAt).toLocaleDateString('ar-SA')
                }));

                const csv = convertToCSV(data);
                downloadCSV(csv, 'qr_codes_export.csv');
                showNotification('تم تصدير بيانات QR Codes', 'success');
            } catch (error) {
                console.error('خطأ في التصدير:', error);
                showNotification('خطأ في التصدير', 'error');
            }
        }

        function exportScanHistory() {
            try {
                const data = scanHistory.map(scan => ({
                    'الرقم الأكاديمي': scan.playerId,
                    'اسم اللاعب': scan.playerName,
                    'وقت المسح': new Date(scan.scanTime).toLocaleString('ar-SA'),
                    'النتيجة': scan.success ? 'نجح' : 'فشل'
                }));

                const csv = convertToCSV(data);
                downloadCSV(csv, 'scan_history_export.csv');
                showNotification('تم تصدير سجل المسح', 'success');
            } catch (error) {
                console.error('خطأ في التصدير:', error);
                showNotification('خطأ في التصدير', 'error');
            }
        }

        function convertToCSV(data) {
            if (data.length === 0) return '';

            const headers = Object.keys(data[0]);
            const csvContent = [
                headers.join(','),
                ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
            ].join('\n');

            return '\uFEFF' + csvContent; // إضافة BOM للدعم العربي
        }

        function downloadCSV(csv, filename) {
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            link.click();
        }
    </script>
</body>
</html>
