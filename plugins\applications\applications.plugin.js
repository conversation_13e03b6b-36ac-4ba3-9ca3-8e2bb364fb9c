// ==================== Plugin: إدارة طلبات الانضمام ====================
export const ApplicationsManagementPlugin = {
    id: 'applications',
    name: 'إدارة طلبات الانضمام',
    init() {
        if (!document.getElementById('applications-plugin-style')) {
            const style = document.createElement('style');
            style.id = 'applications-plugin-style';
            style.innerHTML = `
                .plugin-section {background: #181f2a; color: #fff; border-radius: 18px; box-shadow: 0 2px 16px #0002; max-width: 900px; margin: 32px auto 0; padding: 32px 24px 24px; font-family: 'Cairo',sans-serif;}
                .plugin-header {display: flex; align-items: center; justify-content: space-between; margin-bottom: 18px;}
                .plugin-header h2 {font-size: 1.5rem; font-weight: bold; margin: 0;}
                .plugin-btn {background: linear-gradient(90deg,#4f8cff,#6f6bff); color: #fff; border: none; border-radius: 8px; padding: 10px 22px; font-size: 1rem; font-weight: 600; cursor: pointer; transition: background 0.2s;}
                .plugin-btn:hover {background: linear-gradient(90deg,#6f6bff,#4f8cff);}
                .plugin-table {width: 100%; border-collapse: collapse; background: #232b3b; border-radius: 12px; overflow: hidden; margin-top: 10px;}
                .plugin-table th, .plugin-table td {padding: 12px 8px; text-align: center;}
                .plugin-table th {background: #232b3b; color: #8bb4ff; font-weight: 700; border-bottom: 2px solid #2d3950;}
                .plugin-table tr {transition: background 0.2s;}
                .plugin-table tr:hover {background: #232b3bcc;}
                .plugin-table td {border-bottom: 1px solid #232b3b;}
                .plugin-action-btn {background: #2d3950; color: #8bb4ff; border: none; border-radius: 6px; padding: 6px 16px; margin: 0 2px; font-size: 0.95rem; cursor: pointer; transition: background 0.2s;}
                .plugin-action-btn:hover {background: #4f8cff; color: #fff;}
                .plugin-modal-bg {position: fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.45); z-index: 9999; display: flex; align-items: center; justify-content: center;}
                .plugin-modal {background: #232b3b; border-radius: 16px; padding: 32px 24px 24px; min-width: 320px; max-width: 95vw; box-shadow: 0 4px 32px #0005;}
                .plugin-modal h3 {margin-top:0; color:#8bb4ff; font-size:1.2rem; margin-bottom:18px;}
                .plugin-modal label {display:block; margin-bottom:6px; color:#b6cfff; font-size:1rem;}
                .plugin-modal input {width:100%; padding:8px 10px; border-radius:6px; border:none; background:#181f2a; color:#fff; margin-bottom:16px; font-size:1rem;}
                .plugin-modal select {width:100%; padding:8px 10px; border-radius:6px; border:none; background:#181f2a; color:#fff; margin-bottom:16px; font-size:1rem;}
                .plugin-modal .modal-actions {display:flex; justify-content:flex-end; gap:10px;}
                .plugin-modal .plugin-btn {padding:8px 18px; font-size:1rem;}
            `;
            document.head.appendChild(style);
        }
        if (!document.getElementById('applications-plugin-container')) {
            const container = document.createElement('section');
            container.id = 'applications-plugin-container';
            container.className = 'plugin-section';
            container.innerHTML = `
                <div class="plugin-header">
                    <h2>إدارة طلبات الانضمام</h2>
                    <button id="add-application-btn" class="plugin-btn">إضافة طلب</button>
                </div>
                <table class="plugin-table" id="applications-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الهاتف</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="applications-table-body">
                        <!-- سيتم تعبئة الطلبات هنا -->
                    </tbody>
                </table>
            `;
            document.body.prepend(container);
        }
        this.renderApplications();
        document.getElementById('add-application-btn').onclick = () => this.openApplicationModal();
    },
    destroy() {
        const container = document.getElementById('applications-plugin-container');
        if (container) container.remove();
        const style = document.getElementById('applications-plugin-style');
        if (style) style.remove();
        this.closeModal();
    },
    renderApplications() {
        const apps = JSON.parse(localStorage.getItem('plugin_applications') || '[]');
        const tbody = document.getElementById('applications-table-body');
        if (!tbody) return;
        tbody.innerHTML = apps.length ? apps.map((a, i) => `
            <tr>
                <td>${a.name || ''}</td>
                <td>${a.phone || ''}</td>
                <td>${a.status || ''}</td>
                <td>
                    <button class="plugin-action-btn" onclick="window.PluginManager.plugins.applications.openApplicationModal(${i})">تعديل</button>
                    <button class="plugin-action-btn" onclick="window.PluginManager.plugins.applications.deleteApplication(${i})">حذف</button>
                </td>
            </tr>
        `).join('') : '<tr><td colspan="4">لا يوجد طلبات</td></tr>';
    },
    openApplicationModal(index = null) {
        this.closeModal();
        const apps = JSON.parse(localStorage.getItem('plugin_applications') || '[]');
        const app = index !== null ? apps[index] : { name: '', phone: '', status: 'معلق' };
        const modalBg = document.createElement('div');
        modalBg.className = 'plugin-modal-bg';
        modalBg.id = 'applications-plugin-modal-bg';
        modalBg.innerHTML = `
            <div class="plugin-modal">
                <h3>${index !== null ? 'تعديل طلب' : 'إضافة طلب جديد'}</h3>
                <label>اسم المتقدم</label>
                <input id="modal-app-name" type="text" value="${app.name || ''}" placeholder="مثال: فهد العتيبي" />
                <label>رقم الهاتف</label>
                <input id="modal-app-phone" type="text" value="${app.phone || ''}" placeholder="05xxxxxxxx" />
                <label>الحالة</label>
                <select id="modal-app-status">
                    <option${app.status==='معلق'?' selected':''}>معلق</option>
                    <option${app.status==='مقبول'?' selected':''}>مقبول</option>
                    <option${app.status==='مرفوض'?' selected':''}>مرفوض</option>
                </select>
                <div class="modal-actions">
                    <button class="plugin-btn" id="save-app-btn">${index !== null ? 'حفظ التعديلات' : 'إضافة'}</button>
                    <button class="plugin-btn" id="cancel-app-btn" style="background:#2d3950;">إلغاء</button>
                </div>
            </div>
        `;
        document.body.appendChild(modalBg);
        document.getElementById('cancel-app-btn').onclick = () => this.closeModal();
        document.getElementById('save-app-btn').onclick = () => {
            const name = document.getElementById('modal-app-name').value.trim();
            const phone = document.getElementById('modal-app-phone').value.trim();
            const status = document.getElementById('modal-app-status').value;
            if (!name || !phone) {
                alert('يرجى تعبئة جميع الحقول');
                return;
            }
            if (index !== null) {
                apps[index] = { name, phone, status };
            } else {
                apps.push({ name, phone, status });
            }
            localStorage.setItem('plugin_applications', JSON.stringify(apps));
            this.closeModal();
            this.renderApplications();
        };
        setTimeout(() => {
            document.getElementById('modal-app-name').focus();
        }, 100);
    },
    closeModal() {
        const modalBg = document.getElementById('applications-plugin-modal-bg');
        if (modalBg) modalBg.remove();
    },
    deleteApplication(index) {
        if (!confirm('هل أنت متأكد من حذف الطلب؟')) return;
        const apps = JSON.parse(localStorage.getItem('plugin_applications') || '[]');
        apps.splice(index, 1);
        localStorage.setItem('plugin_applications', JSON.stringify(apps));
        this.renderApplications();
    }
};
