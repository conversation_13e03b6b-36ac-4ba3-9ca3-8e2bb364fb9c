<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استوديو الشخصيات الكرتونية الذكي المحسن - أكاديمية 7C</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        :root {
            --primary: #667eea;
            --secondary: #764ba2;
            --accent: #ff6b9d;
            --success: #4ecdc4;
            --warning: #ffe66d;
            --danger: #ff6b6b;
            --dark: #1a1a2e;
            --darker: #16213e;
            --light: #ffffff;
            --gray: #6c757d;
            --glass: rgba(255, 255, 255, 0.1);
            --border: rgba(255, 255, 255, 0.2);
            --shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, var(--darker) 100%);
            color: var(--light);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* خلفية متحركة محسنة */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(255, 107, 157, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(78, 205, 196, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(255, 230, 109, 0.1) 0%, transparent 50%);
            animation: float 12s ease-in-out infinite alternate;
            z-index: -1;
        }

        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); opacity: 0.8; }
            100% { transform: translateY(-20px) rotate(2deg); opacity: 1; }
        }

        /* رأس الصفحة المحسن */
        .header {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            animation: pulse 2s infinite;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .logo-text h1 {
            font-size: 1.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, var(--accent), var(--success));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.2rem;
        }

        .logo-text p {
            font-size: 0.9rem;
            color: var(--gray);
        }

        .nav-menu {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        /* أزرار محسنة */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-family: inherit;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-accent {
            background: linear-gradient(135deg, var(--accent), #ff8fab);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success), #36b3a8);
            color: white;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning), #f6c23e);
            color: var(--dark);
            box-shadow: 0 4px 15px rgba(255, 230, 109, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger), #e74a3b);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .btn-glass {
            background: var(--glass);
            color: var(--light);
            border: 1px solid var(--border);
            backdrop-filter: blur(10px);
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        /* تخطيط رئيسي محسن */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .studio-grid {
            display: grid;
            grid-template-columns: 320px 1fr 300px;
            gap: 2rem;
            min-height: calc(100vh - 120px);
        }

        /* شريط جانبي محسن */
        .sidebar {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 20px;
            padding: 1.5rem;
            height: fit-content;
            position: sticky;
            top: 120px;
            box-shadow: var(--shadow);
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--accent);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border);
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            font-size: 0.9rem;
            color: var(--light);
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            color: var(--light);
            font-size: 0.9rem;
            font-family: inherit;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--accent);
            background: rgba(255, 255, 255, 0.1);
            box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: left 0.75rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-left: 2.5rem;
        }

        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }

        /* شبكة الألوان المحسنة */
        .color-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.75rem;
            margin-top: 0.5rem;
        }

        .color-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }

        .color-input {
            width: 50px;
            height: 50px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            border: 2px solid var(--border);
            transition: all 0.3s ease;
        }

        .color-input:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .color-label {
            font-size: 0.8rem;
            color: var(--gray);
            font-weight: 500;
        }

        /* منطقة الاستوديو الرئيسية */
        .studio-main {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 20px;
            padding: 2rem;
            position: relative;
            box-shadow: var(--shadow);
        }

        .studio-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .studio-title {
            font-size: 2.2rem;
            font-weight: 800;
            background: linear-gradient(45deg, var(--accent), var(--success), var(--warning));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }

        .studio-subtitle {
            color: var(--gray);
            font-size: 1rem;
            line-height: 1.5;
        }

        /* قسم التوليد المحسن */
        .generate-section {
            text-align: center;
            margin: 2rem 0;
        }

        .generate-btn {
            background: linear-gradient(135deg, var(--accent), var(--success));
            color: white;
            border: none;
            padding: 1.5rem 3rem;
            border-radius: 15px;
            font-size: 1.2rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(255, 107, 157, 0.3);
            font-family: inherit;
            position: relative;
            overflow: hidden;
        }

        .generate-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .generate-btn:hover::before {
            left: 100%;
        }

        .generate-btn:hover:not(:disabled) {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 10px 30px rgba(255, 107, 157, 0.4);
        }

        .generate-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        /* لوحة الشخصية المحسنة */
        .character-canvas {
            background: rgba(255, 255, 255, 0.02);
            border: 2px dashed var(--border);
            border-radius: 15px;
            min-height: 450px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin: 2rem 0;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .character-canvas.has-image {
            border-style: solid;
            border-color: var(--accent);
            background: rgba(255, 107, 157, 0.05);
            box-shadow: 0 0 20px rgba(255, 107, 157, 0.2);
        }

        .character-image {
            max-width: 100%;
            max-height: 400px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .character-image:hover {
            transform: scale(1.02);
        }

        .placeholder {
            text-align: center;
            color: var(--gray);
            padding: 2rem;
        }

        .placeholder-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .placeholder-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }

        /* لوحة معلومات الشخصية */
        .character-panel {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 20px;
            padding: 1.5rem;
            height: fit-content;
            position: sticky;
            top: 120px;
            box-shadow: var(--shadow);
        }

        .character-info {
            margin-bottom: 1.5rem;
        }

        .character-name {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--accent);
            text-align: center;
        }

        .character-details {
            font-size: 0.9rem;
            line-height: 1.6;
            color: var(--light);
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem;
            border-radius: 10px;
            border: 1px solid var(--border);
        }

        .character-details p {
            margin-bottom: 0.5rem;
        }

        .character-details strong {
            color: var(--accent);
        }

        /* أزرار الإجراءات المحسنة */
        .character-actions {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .action-btn {
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            justify-content: center;
            font-family: inherit;
            position: relative;
            overflow: hidden;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .action-btn:hover::before {
            left: 100%;
        }

        .action-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .action-save { 
            background: linear-gradient(135deg, var(--success), #36b3a8); 
            color: white; 
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }

        .action-edit { 
            background: linear-gradient(135deg, var(--warning), #f6c23e); 
            color: var(--dark); 
            box-shadow: 0 4px 15px rgba(255, 230, 109, 0.3);
        }

        .action-export { 
            background: linear-gradient(135deg, var(--primary), var(--secondary)); 
            color: white; 
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .action-delete { 
            background: linear-gradient(135deg, var(--danger), #e74a3b); 
            color: white; 
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .action-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        /* حالات التحميل المحسنة */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            display: none;
            align-items: center;
            justify-content: center;
            border-radius: 20px;
            z-index: 100;
        }

        .loading-content {
            text-align: center;
            color: white;
            padding: 2rem;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 107, 157, 0.3);
            border-top: 4px solid var(--accent);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .loading-subtext {
            font-size: 0.9rem;
            color: var(--gray);
        }

        /* الإحصائيات المحسنة */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid var(--border);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--accent);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.8rem;
            color: var(--gray);
            font-weight: 500;
        }

        /* القوالب السريعة المحسنة */
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
            margin-top: 1rem;
        }

        .template-btn {
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 0.85rem;
            color: var(--light);
            font-family: inherit;
            position: relative;
            overflow: hidden;
        }

        .template-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 107, 157, 0.2), transparent);
            transition: left 0.5s;
        }

        .template-btn:hover::before {
            left: 100%;
        }

        .template-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.2);
        }

        .template-icon {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        /* تصميم متجاوب محسن */
        @media (max-width: 1200px) {
            .studio-grid {
                grid-template-columns: 280px 1fr 250px;
                gap: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .studio-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .sidebar, .character-panel {
                position: static;
                order: 2;
            }
            
            .studio-main {
                order: 1;
            }
            
            .header-content {
                flex-direction: column;
                text-align: center;
            }
            
            .nav-menu {
                justify-content: center;
            }
            
            .studio-title {
                font-size: 1.8rem;
            }
            
            .generate-btn {
                padding: 1rem 2rem;
                font-size: 1rem;
            }
            
            .color-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .templates-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .character-canvas {
                min-height: 300px;
            }
        }

        /* رسائل التنبيه المخصصة */
        .swal2-popup {
            background: var(--dark) !important;
            color: var(--light) !important;
            border: 1px solid var(--border) !important;
        }

        .swal2-title {
            color: var(--light) !important;
        }

        .swal2-content {
            color: var(--gray) !important;
        }

        /* مؤشر الحالة */
        .status-indicator {
            position: fixed;
            top: 100px;
            left: 20px;
            background: var(--glass);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border);
            border-radius: 10px;
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
            z-index: 999;
            display: none;
        }

        .status-indicator.show {
            display: block;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- مؤشر الحالة -->
    <div id="statusIndicator" class="status-indicator">
        <i class="fas fa-info-circle"></i>
        <span id="statusText">جاهز</span>
    </div>

    <!-- رأس الصفحة -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🎨</div>
                <div class="logo-text">
                    <h1>استوديو الشخصيات الذكي المحسن</h1>
                    <p>أكاديمية 7C الإبداعية - نسخة محسنة</p>
                </div>
            </div>
            <nav class="nav-menu">
                <button class="btn btn-glass" onclick="showGallery()" id="galleryBtn">
                    <i class="fas fa-images"></i> المعرض
                </button>
                <button class="btn btn-glass" onclick="showProjects()" id="projectsBtn">
                    <i class="fas fa-folder"></i> المشاريع
                </button>
                <button class="btn btn-glass" onclick="showSettings()" id="settingsBtn">
                    <i class="fas fa-cog"></i> الإعدادات
                </button>
                <button class="btn btn-primary" onclick="showHelp()" id="helpBtn">
                    <i class="fas fa-question-circle"></i> المساعدة
                </button>
            </nav>
        </div>
    </header>

    <!-- الحاوي الرئيسي -->
    <div class="container">
        <div class="studio-grid">
            <!-- الشريط الجانبي لتخصيص الشخصية -->
            <div class="sidebar">
                <div class="section-title">
                    <i class="fas fa-user-edit"></i>
                    تخصيص الشخصية
                </div>

                <div class="form-group">
                    <label class="form-label">اسم الشخصية *</label>
                    <input type="text" id="characterName" class="form-input" placeholder="أدخل اسم الشخصية" maxlength="50">
                    <small style="color: var(--gray); font-size: 0.8rem;">الحد الأقصى 50 حرف</small>
                </div>

                <div class="form-group">
                    <label class="form-label">العمر</label>
                    <select id="characterAge" class="form-input form-select">
                        <option value="">اختر العمر</option>
                        <option value="طفل">طفل (5-12 سنة)</option>
                        <option value="مراهق">مراهق (13-19 سنة)</option>
                        <option value="بالغ">بالغ (20-40 سنة)</option>
                        <option value="كبير">كبير السن (40+ سنة)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">الجنس</label>
                    <select id="characterGender" class="form-input form-select">
                        <option value="">اختر الجنس</option>
                        <option value="ذكر">ذكر</option>
                        <option value="أنثى">أنثى</option>
                        <option value="محايد">محايد</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">نوع الشخصية</label>
                    <select id="characterType" class="form-input form-select">
                        <option value="">اختر النوع</option>
                        <option value="بطل">بطل</option>
                        <option value="شرير">شرير</option>
                        <option value="مساعد">مساعد</option>
                        <option value="كوميدي">كوميدي</option>
                        <option value="حكيم">حكيم</option>
                        <option value="غامض">غامض</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">نمط الرسم</label>
                    <select id="artStyle" class="form-input form-select">
                        <option value="كرتوني">كرتوني كلاسيكي</option>
                        <option value="أنمي">أنمي</option>
                        <option value="ديزني">ديزني</option>
                        <option value="بيكسار">بيكسار</option>
                        <option value="تشيبي">تشيبي</option>
                        <option value="واقعي">واقعي</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">الوصف التفصيلي</label>
                    <textarea id="characterDescription" class="form-input form-textarea" placeholder="اكتب وصفاً تفصيلياً للشخصية..." maxlength="500"></textarea>
                    <small style="color: var(--gray); font-size: 0.8rem;">الحد الأقصى 500 حرف</small>
                </div>

                <div class="section-title" style="margin-top: 1.5rem;">
                    <i class="fas fa-palette"></i>
                    الألوان والمظهر
                </div>

                <div class="color-grid">
                    <div class="color-item">
                        <input type="color" id="skinColor" class="color-input" value="#ffdbac">
                        <span class="color-label">البشرة</span>
                    </div>
                    <div class="color-item">
                        <input type="color" id="hairColor" class="color-input" value="#8b4513">
                        <span class="color-label">الشعر</span>
                    </div>
                    <div class="color-item">
                        <input type="color" id="eyeColor" class="color-input" value="#4169e1">
                        <span class="color-label">العيون</span>
                    </div>
                    <div class="color-item">
                        <input type="color" id="clothesColor" class="color-input" value="#ff6b9d">
                        <span class="color-label">الملابس</span>
                    </div>
                    <div class="color-item">
                        <input type="color" id="accessoryColor" class="color-input" value="#ffd700">
                        <span class="color-label">الإكسسوار</span>
                    </div>
                    <div class="color-item">
                        <input type="color" id="backgroundColor" class="color-input" value="#87ceeb">
                        <span class="color-label">الخلفية</span>
                    </div>
                </div>

                <div class="section-title" style="margin-top: 1.5rem;">
                    <i class="fas fa-magic"></i>
                    قوالب سريعة
                </div>

                <div class="templates-grid">
                    <button class="template-btn" onclick="applyTemplate('hero')" data-template="hero">
                        <span class="template-icon">🦸</span>
                        بطل
                    </button>
                    <button class="template-btn" onclick="applyTemplate('villain')" data-template="villain">
                        <span class="template-icon">🦹</span>
                        شرير
                    </button>
                    <button class="template-btn" onclick="applyTemplate('princess')" data-template="princess">
                        <span class="template-icon">👸</span>
                        أميرة
                    </button>
                    <button class="template-btn" onclick="applyTemplate('wizard')" data-template="wizard">
                        <span class="template-icon">🧙</span>
                        ساحر
                    </button>
                    <button class="template-btn" onclick="applyTemplate('animal')" data-template="animal">
                        <span class="template-icon">🐱</span>
                        حيوان
                    </button>
                    <button class="template-btn" onclick="applyTemplate('robot')" data-template="robot">
                        <span class="template-icon">🤖</span>
                        روبوت
                    </button>
                </div>

                <div style="margin-top: 1.5rem;">
                    <button class="btn btn-warning" onclick="resetForm()" style="width: 100%;">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين النموذج
                    </button>
                </div>
            </div>

            <!-- منطقة الاستوديو الرئيسية -->
            <div class="studio-main">
                <div class="studio-header">
                    <h1 class="studio-title">مولد الشخصيات الكرتونية المحسن</h1>
                    <p class="studio-subtitle">أنشئ شخصيات احترافية لمسلسلك باستخدام الذكاء الاصطناعي المتقدم أو نظام الرسم المحسن</p>
                </div>

                <div class="generate-section">
                    <button class="generate-btn" id="generateBtn" onclick="generateCharacter()">
                        <i class="fas fa-magic"></i>
                        توليد شخصية جديدة
                    </button>
                    <div style="margin-top: 1rem;">
                        <button class="btn btn-glass" onclick="generateRandomCharacter()" style="margin-left: 0.5rem;">
                            <i class="fas fa-dice"></i>
                            شخصية عشوائية
                        </button>
                        <button class="btn btn-glass" onclick="generateVariation()">
                            <i class="fas fa-sync-alt"></i>
                            تنويع الشخصية
                        </button>
                    </div>
                </div>

                <div class="character-canvas" id="characterCanvas">
                    <div class="placeholder">
                        <div class="placeholder-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="placeholder-text">
                            اضغط على "توليد شخصية جديدة" لإنشاء شخصيتك الأولى<br>
                            أو اختر قالباً سريعاً من الشريط الجانبي
                        </div>
                    </div>
                </div>

                <!-- طبقة التحميل المحسنة -->
                <div class="loading-overlay" id="loadingOverlay">
                    <div class="loading-content">
                        <div class="loading-spinner"></div>
                        <div class="loading-text" id="loadingText">جاري توليد الشخصية...</div>
                        <div class="loading-subtext" id="loadingSubtext">يرجى الانتظار، هذا قد يستغرق بضع ثوانٍ</div>
                        <div style="margin-top: 1rem;">
                            <button class="btn btn-danger" onclick="cancelGeneration()" id="cancelBtn">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- لوحة معلومات الشخصية -->
            <div class="character-panel">
                <div class="character-info">
                    <h3 class="character-name" id="displayCharacterName">اسم الشخصية</h3>
                    <div class="character-details" id="characterDetails">
                        <p><strong>العمر:</strong> <span id="displayAge">غير محدد</span></p>
                        <p><strong>الجنس:</strong> <span id="displayGender">غير محدد</span></p>
                        <p><strong>النوع:</strong> <span id="displayType">غير محدد</span></p>
                        <p><strong>النمط:</strong> <span id="displayStyle">غير محدد</span></p>
                        <p><strong>الوصف:</strong> <span id="displayDescription">لم يتم إنشاء شخصية بعد</span></p>
                        <p><strong>القصة:</strong> <span id="displayStory">لا توجد قصة</span></p>
                    </div>
                </div>

                <div class="character-actions">
                    <button class="action-btn action-save" onclick="saveCharacter()" disabled id="saveBtn">
                        <i class="fas fa-save"></i>
                        حفظ الشخصية
                    </button>

                    <button class="action-btn action-edit" onclick="editCharacter()" disabled id="editBtn">
                        <i class="fas fa-edit"></i>
                        تحرير الشخصية
                    </button>

                    <button class="action-btn action-export" onclick="exportCharacter()" disabled id="exportBtn">
                        <i class="fas fa-download"></i>
                        تصدير الشخصية
                    </button>

                    <button class="action-btn action-delete" onclick="deleteCharacter()" disabled id="deleteBtn">
                        <i class="fas fa-trash"></i>
                        حذف الشخصية
                    </button>
                </div>

                <div class="section-title">
                    <i class="fas fa-chart-bar"></i>
                    إحصائيات المشروع
                </div>

                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number" id="totalCharacters">0</div>
                        <div class="stat-label">إجمالي الشخصيات</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="savedCharacters">0</div>
                        <div class="stat-label">المحفوظة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="projectsCount">1</div>
                        <div class="stat-label">المشاريع</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="generatedToday">0</div>
                        <div class="stat-label">اليوم</div>
                    </div>
                </div>

                <div style="margin-top: 1.5rem;">
                    <button class="btn btn-glass" onclick="showCharacterHistory()" style="width: 100%; margin-bottom: 0.5rem;">
                        <i class="fas fa-history"></i>
                        تاريخ الشخصيات
                    </button>
                    <button class="btn btn-glass" onclick="exportAllCharacters()" style="width: 100%;">
                        <i class="fas fa-file-export"></i>
                        تصدير جميع الشخصيات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // ==================== المتغيرات العامة ====================
        let currentCharacter = null;
        let characters = JSON.parse(localStorage.getItem('cartoonCharacters') || '[]');
        let projects = JSON.parse(localStorage.getItem('cartoonProjects') || '[{"id": 1, "name": "مشروع افتراضي", "characters": [], "createdAt": "' + new Date().toISOString() + '"}]');
        let currentProject = projects[0];
        let isGenerating = false;
        let generationCancelled = false;
        let apiSettings = JSON.parse(localStorage.getItem('apiSettings') || '{"useOpenAI": false, "apiKey": "", "quality": "high"}');

        // إعدادات OpenAI API
        const OPENAI_API_KEY = '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';
        const OPENAI_BASE_URL = 'https://openrouter.ai/api/v1';

        // قوالب الشخصيات المحسنة
        const characterTemplates = {
            hero: {
                name: 'البطل الشجاع',
                age: 'بالغ',
                gender: 'ذكر',
                type: 'بطل',
                description: 'بطل شجاع يحارب من أجل العدالة والخير، يتمتع بقوة جسدية وإرادة قوية',
                skinColor: '#ffdbac',
                hairColor: '#8b4513',
                eyeColor: '#4169e1',
                clothesColor: '#ff0000',
                accessoryColor: '#ffd700',
                backgroundColor: '#87ceeb'
            },
            villain: {
                name: 'الشرير الماكر',
                age: 'بالغ',
                gender: 'ذكر',
                type: 'شرير',
                description: 'شرير ماكر وذكي يسعى للسيطرة على العالم بخطط محكمة ومعقدة',
                skinColor: '#f5deb3',
                hairColor: '#000000',
                eyeColor: '#8b0000',
                clothesColor: '#800080',
                accessoryColor: '#000000',
                backgroundColor: '#2f4f4f'
            },
            princess: {
                name: 'الأميرة الجميلة',
                age: 'بالغ',
                gender: 'أنثى',
                type: 'بطل',
                description: 'أميرة جميلة وذكية تحب مساعدة الآخرين وتقود شعبها بحكمة ولطف',
                skinColor: '#ffdbac',
                hairColor: '#ffd700',
                eyeColor: '#00bfff',
                clothesColor: '#ff69b4',
                accessoryColor: '#ffd700',
                backgroundColor: '#ffe4e1'
            },
            wizard: {
                name: 'الساحر الحكيم',
                age: 'كبير',
                gender: 'ذكر',
                type: 'حكيم',
                description: 'ساحر حكيم يمتلك قوى سحرية عظيمة ومعرفة واسعة بأسرار الكون',
                skinColor: '#f5deb3',
                hairColor: '#d3d3d3',
                eyeColor: '#9370db',
                clothesColor: '#4b0082',
                accessoryColor: '#ffd700',
                backgroundColor: '#191970'
            },
            animal: {
                name: 'الحيوان الأليف',
                age: 'طفل',
                gender: 'محايد',
                type: 'مساعد',
                description: 'حيوان أليف لطيف ومخلص، يساعد الأبطال في مغامراتهم بذكاء وشجاعة',
                skinColor: '#daa520',
                hairColor: '#8b4513',
                eyeColor: '#000000',
                clothesColor: '#ff6347',
                accessoryColor: '#32cd32',
                backgroundColor: '#98fb98'
            },
            robot: {
                name: 'الروبوت الذكي',
                age: 'محايد',
                gender: 'محايد',
                type: 'مساعد',
                description: 'روبوت ذكي ومتطور يساعد في المهام المختلفة بتقنيات متقدمة',
                skinColor: '#c0c0c0',
                hairColor: '#696969',
                eyeColor: '#00ff00',
                clothesColor: '#4682b4',
                accessoryColor: '#ff4500',
                backgroundColor: '#708090'
            }
        };

        // ==================== التهيئة ====================
        document.addEventListener('DOMContentLoaded', function() {
            initializeSystem();
            updateStats();
            loadCharacterForm();
            setupEventListeners();
            showStatus('النظام جاهز للاستخدام', 'success');
            console.log('🎨 استوديو الشخصيات الكرتونية المحسن جاهز!');
        });

        function initializeSystem() {
            // تحميل البيانات المحفوظة
            loadSavedData();

            // تطبيق الإعدادات
            applySettings();

            // تحديث واجهة المستخدم
            updateUI();
        }

        function loadSavedData() {
            try {
                const savedCharacters = localStorage.getItem('cartoonCharacters');
                if (savedCharacters) {
                    characters = JSON.parse(savedCharacters);
                }

                const savedProjects = localStorage.getItem('cartoonProjects');
                if (savedProjects) {
                    projects = JSON.parse(savedProjects);
                    currentProject = projects[0] || createDefaultProject();
                }

                const savedCurrentCharacter = localStorage.getItem('currentCharacter');
                if (savedCurrentCharacter) {
                    currentCharacter = JSON.parse(savedCurrentCharacter);
                    if (currentCharacter) {
                        displayCharacter(currentCharacter);
                        enableActionButtons();
                    }
                }
            } catch (error) {
                console.error('خطأ في تحميل البيانات المحفوظة:', error);
                showStatus('خطأ في تحميل البيانات المحفوظة', 'error');
            }
        }

        function createDefaultProject() {
            return {
                id: 1,
                name: "مشروع افتراضي",
                characters: [],
                createdAt: new Date().toISOString()
            };
        }

        function setupEventListeners() {
            // مراقبة تغييرات النموذج
            const formInputs = document.querySelectorAll('.form-input');
            formInputs.forEach(input => {
                input.addEventListener('change', validateForm);
                input.addEventListener('input', validateForm);
            });

            // مراقبة تغييرات الألوان
            const colorInputs = document.querySelectorAll('.color-input');
            colorInputs.forEach(input => {
                input.addEventListener('change', updateColorPreview);
            });

            // اختصارات لوحة المفاتيح
            document.addEventListener('keydown', handleKeyboardShortcuts);

            // حفظ تلقائي
            setInterval(autoSave, 30000); // كل 30 ثانية
        }

        function handleKeyboardShortcuts(event) {
            if (event.ctrlKey || event.metaKey) {
                switch(event.key) {
                    case 's':
                        event.preventDefault();
                        if (currentCharacter) saveCharacter();
                        break;
                    case 'n':
                        event.preventDefault();
                        generateCharacter();
                        break;
                    case 'r':
                        event.preventDefault();
                        resetForm();
                        break;
                    case 'e':
                        event.preventDefault();
                        if (currentCharacter) exportCharacter();
                        break;
                }
            }
        }

        // ==================== توليد الشخصيات ====================
        async function generateCharacter() {
            if (isGenerating) return;

            // التحقق من صحة النموذج
            if (!validateForm()) {
                showStatus('يرجى ملء الحقول المطلوبة', 'error');
                return;
            }

            isGenerating = true;
            generationCancelled = false;

            const generateBtn = document.getElementById('generateBtn');
            const loadingOverlay = document.getElementById('loadingOverlay');

            // تعطيل الزر وإظهار التحميل
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التوليد...';
            loadingOverlay.style.display = 'flex';

            showStatus('جاري توليد الشخصية...', 'info');

            try {
                // الحصول على بيانات النموذج
                const characterData = getCharacterFormData();

                // محاولة التوليد باستخدام OpenAI أولاً
                let generatedCharacter;
                if (apiSettings.useOpenAI && OPENAI_API_KEY) {
                    try {
                        generatedCharacter = await generateWithOpenAI(characterData);
                    } catch (error) {
                        console.warn('فشل التوليد بـ OpenAI، التبديل للنظام المحلي:', error);
                        generatedCharacter = await generateWithCanvas(characterData);
                    }
                } else {
                    generatedCharacter = await generateWithCanvas(characterData);
                }

                if (!generationCancelled) {
                    displayCharacter(generatedCharacter);
                    enableActionButtons();
                    updateStats();
                    showStatus('تم توليد الشخصية بنجاح!', 'success');

                    // إشعار نجاح
                    Swal.fire({
                        title: 'تم التوليد بنجاح!',
                        text: 'تم إنشاء شخصية جديدة بنجاح',
                        icon: 'success',
                        confirmButtonText: 'رائع!',
                        background: '#1a1a2e',
                        color: '#ffffff',
                        timer: 3000
                    });
                }

            } catch (error) {
                console.error('خطأ في التوليد:', error);
                showStatus('حدث خطأ أثناء التوليد', 'error');

                Swal.fire({
                    title: 'خطأ في التوليد',
                    text: 'حدث خطأ أثناء توليد الشخصية. يرجى المحاولة مرة أخرى.',
                    icon: 'error',
                    confirmButtonText: 'حسناً',
                    background: '#1a1a2e',
                    color: '#ffffff'
                });
            } finally {
                // إعادة تعيين واجهة المستخدم
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-magic"></i> توليد شخصية جديدة';
                loadingOverlay.style.display = 'none';
                isGenerating = false;
            }
        }

        function cancelGeneration() {
            generationCancelled = true;
            isGenerating = false;

            const generateBtn = document.getElementById('generateBtn');
            const loadingOverlay = document.getElementById('loadingOverlay');

            generateBtn.disabled = false;
            generateBtn.innerHTML = '<i class="fas fa-magic"></i> توليد شخصية جديدة';
            loadingOverlay.style.display = 'none';

            showStatus('تم إلغاء التوليد', 'warning');
        }

        // ==================== التوليد باستخدام OpenAI ====================
        async function generateWithOpenAI(characterData) {
            const prompt = createDetailedPrompt(characterData);

            updateLoadingText('الاتصال بـ OpenAI...', 'جاري إنشاء الصورة باستخدام الذكاء الاصطناعي');

            try {
                const response = await fetch(`${OPENAI_BASE_URL}/images/generations`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${OPENAI_API_KEY}`,
                        'Content-Type': 'application/json',
                        'HTTP-Referer': window.location.origin,
                        'X-Title': 'Cartoon Character Studio'
                    },
                    body: JSON.stringify({
                        model: "dall-e-3",
                        prompt: prompt,
                        n: 1,
                        size: "1024x1024",
                        quality: apiSettings.quality === 'high' ? 'hd' : 'standard',
                        style: 'vivid'
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                updateLoadingText('توليد القصة...', 'جاري إنشاء قصة الشخصية');

                // توليد قصة باستخدام GPT
                const story = await generateStoryWithGPT(characterData);

                const character = {
                    id: Date.now(),
                    ...characterData,
                    imageUrl: data.data[0].url,
                    story: story,
                    createdAt: new Date().toISOString(),
                    personality: generatePersonality(characterData),
                    generatedWith: 'OpenAI'
                };

                currentCharacter = character;
                return character;

            } catch (error) {
                console.error('خطأ في OpenAI:', error);
                throw error;
            }
        }

        async function generateStoryWithGPT(characterData) {
            const prompt = `أنشئ قصة قصيرة ومثيرة لشخصية كرتونية:
            الاسم: ${characterData.name}
            العمر: ${characterData.age}
            الجنس: ${characterData.gender}
            النوع: ${characterData.type}
            الوصف: ${characterData.description}

            اكتب قصة من 3-4 جمل تشرح خلفية الشخصية ومغامراتها.`;

            try {
                const response = await fetch(`${OPENAI_BASE_URL}/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${OPENAI_API_KEY}`,
                        'Content-Type': 'application/json',
                        'HTTP-Referer': window.location.origin,
                        'X-Title': 'Cartoon Character Studio'
                    },
                    body: JSON.stringify({
                        model: "gpt-3.5-turbo",
                        messages: [
                            {
                                role: "system",
                                content: "أنت كاتب قصص أطفال محترف. اكتب قصص قصيرة وممتعة للشخصيات الكرتونية باللغة العربية."
                            },
                            {
                                role: "user",
                                content: prompt
                            }
                        ],
                        max_tokens: 300,
                        temperature: 0.8
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    return data.choices[0].message.content;
                } else {
                    return generateCharacterStory(characterData);
                }
            } catch (error) {
                console.warn('فشل في توليد القصة بـ GPT، استخدام النظام المحلي');
                return generateCharacterStory(characterData);
            }
        }

        function createDetailedPrompt(data) {
            const styleMap = {
                'كرتوني': 'cartoon style',
                'أنمي': 'anime style',
                'ديزني': 'Disney style',
                'بيكسار': 'Pixar 3D style',
                'تشيبي': 'chibi style',
                'واقعي': 'realistic style'
            };

            const typeMap = {
                'بطل': 'heroic character',
                'شرير': 'villain character',
                'مساعد': 'sidekick character',
                'كوميدي': 'funny character',
                'حكيم': 'wise character',
                'غامض': 'mysterious character'
            };

            const ageMap = {
                'طفل': 'child',
                'مراهق': 'teenager',
                'بالغ': 'adult',
                'كبير': 'elderly'
            };

            const genderMap = {
                'ذكر': 'male',
                'أنثى': 'female',
                'محايد': 'neutral'
            };

            return `${styleMap[data.artStyle] || 'cartoon style'} ${typeMap[data.type] || 'character'},
            ${ageMap[data.age] || 'adult'} ${genderMap[data.gender] || 'character'},
            ${data.description},
            skin color: ${data.skinColor},
            hair color: ${data.hairColor},
            eye color: ${data.eyeColor},
            clothes color: ${data.clothesColor},
            background: ${data.backgroundColor},
            high quality, detailed, professional animation character design, full body, standing pose`;
        }

        // ==================== التوليد باستخدام Canvas ====================
        async function generateWithCanvas(characterData) {
            updateLoadingText('رسم الشخصية...', 'جاري إنشاء الشخصية باستخدام النظام المحلي');

            // محاكاة وقت التوليد
            await new Promise(resolve => setTimeout(resolve, 2000));

            const imageUrl = await generateAdvancedCharacterImage(characterData);
            const story = generateCharacterStory(characterData);

            const character = {
                id: Date.now(),
                ...characterData,
                imageUrl: imageUrl,
                story: story,
                createdAt: new Date().toISOString(),
                personality: generatePersonality(characterData),
                generatedWith: 'Canvas'
            };

            currentCharacter = character;
            return character;
        }

        async function generateAdvancedCharacterImage(data) {
            const canvas = document.createElement('canvas');
            canvas.width = 500;
            canvas.height = 600;
            const ctx = canvas.getContext('2d');

            // خلفية متدرجة
            const gradient = ctx.createLinearGradient(0, 0, 0, 600);
            gradient.addColorStop(0, data.backgroundColor);
            gradient.addColorStop(1, adjustColor(data.backgroundColor, -30));
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 500, 600);

            // رسم الشخصية بناءً على النوع
            await drawCharacterByType(ctx, data);

            // إضافة تأثيرات بصرية
            addVisualEffects(ctx, data);

            // إضافة اسم الشخصية
            drawCharacterName(ctx, data.name);

            return canvas.toDataURL('image/png');
        }

        async function drawCharacterByType(ctx, data) {
            const centerX = 250;
            const centerY = 300;

            // رسم الجسم الأساسي
            drawBasicBody(ctx, centerX, centerY, data);

            // رسم الرأس
            drawHead(ctx, centerX, centerY - 120, data);

            // رسم الملامح
            drawFacialFeatures(ctx, centerX, centerY - 120, data);

            // رسم الشعر
            drawHair(ctx, centerX, centerY - 120, data);

            // رسم الملابس
            drawClothes(ctx, centerX, centerY, data);

            // رسم الإكسسوارات
            drawAccessories(ctx, centerX, centerY, data);
        }

        function drawBasicBody(ctx, x, y, data) {
            // الجسم
            ctx.fillStyle = data.clothesColor;
            ctx.fillRect(x - 40, y - 60, 80, 120);

            // الذراعان
            ctx.fillStyle = data.skinColor;
            ctx.fillRect(x - 70, y - 40, 25, 80);
            ctx.fillRect(x + 45, y - 40, 25, 80);

            // الساقان
            ctx.fillRect(x - 25, y + 60, 20, 100);
            ctx.fillRect(x + 5, y + 60, 20, 100);
        }

        function drawHead(ctx, x, y, data) {
            // الرأس
            ctx.fillStyle = data.skinColor;
            ctx.beginPath();
            ctx.arc(x, y, 50, 0, 2 * Math.PI);
            ctx.fill();

            // إضافة ظلال
            ctx.fillStyle = adjustColor(data.skinColor, -20);
            ctx.beginPath();
            ctx.arc(x + 15, y + 15, 45, 0, 2 * Math.PI);
            ctx.fill();

            ctx.fillStyle = data.skinColor;
            ctx.beginPath();
            ctx.arc(x, y, 45, 0, 2 * Math.PI);
            ctx.fill();
        }

        function drawFacialFeatures(ctx, x, y, data) {
            // العيون
            ctx.fillStyle = data.eyeColor;
            ctx.beginPath();
            ctx.arc(x - 15, y - 10, 8, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(x + 15, y - 10, 8, 0, 2 * Math.PI);
            ctx.fill();

            // بؤبؤ العين
            ctx.fillStyle = '#000000';
            ctx.beginPath();
            ctx.arc(x - 15, y - 10, 4, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(x + 15, y - 10, 4, 0, 2 * Math.PI);
            ctx.fill();

            // الأنف
            ctx.fillStyle = adjustColor(data.skinColor, -10);
            ctx.beginPath();
            ctx.arc(x, y + 5, 3, 0, 2 * Math.PI);
            ctx.fill();

            // الفم
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(x, y + 15, 10, 0, Math.PI);
            ctx.stroke();
        }

        function drawHair(ctx, x, y, data) {
            ctx.fillStyle = data.hairColor;

            // شعر أساسي
            ctx.beginPath();
            ctx.arc(x, y - 25, 55, Math.PI, 2 * Math.PI);
            ctx.fill();

            // تفاصيل الشعر
            for (let i = 0; i < 5; i++) {
                ctx.beginPath();
                ctx.arc(x - 40 + i * 20, y - 45, 8, 0, 2 * Math.PI);
                ctx.fill();
            }
        }

        function drawClothes(ctx, x, y, data) {
            // قميص
            ctx.fillStyle = data.clothesColor;
            ctx.fillRect(x - 45, y - 65, 90, 130);

            // تفاصيل الملابس
            ctx.strokeStyle = adjustColor(data.clothesColor, -30);
            ctx.lineWidth = 2;
            ctx.strokeRect(x - 45, y - 65, 90, 130);

            // أزرار
            ctx.fillStyle = data.accessoryColor;
            for (let i = 0; i < 3; i++) {
                ctx.beginPath();
                ctx.arc(x, y - 40 + i * 25, 3, 0, 2 * Math.PI);
                ctx.fill();
            }
        }

        function drawAccessories(ctx, x, y, data) {
            // إكسسوار حسب نوع الشخصية
            ctx.fillStyle = data.accessoryColor;

            switch(data.type) {
                case 'بطل':
                    // عباءة
                    ctx.fillRect(x - 60, y - 70, 20, 140);
                    ctx.fillRect(x + 40, y - 70, 20, 140);
                    break;
                case 'شرير':
                    // قبعة
                    ctx.beginPath();
                    ctx.arc(x, y - 75, 30, 0, 2 * Math.PI);
                    ctx.fill();
                    break;
                case 'أميرة':
                    // تاج
                    ctx.fillRect(x - 25, y - 75, 50, 10);
                    for (let i = 0; i < 3; i++) {
                        ctx.fillRect(x - 15 + i * 15, y - 85, 5, 15);
                    }
                    break;
                case 'ساحر':
                    // عصا سحرية
                    ctx.fillRect(x + 60, y - 100, 5, 80);
                    ctx.beginPath();
                    ctx.arc(x + 62, y - 105, 8, 0, 2 * Math.PI);
                    ctx.fill();
                    break;
            }
        }

        function addVisualEffects(ctx, data) {
            // تأثيرات ضوئية
            const gradient = ctx.createRadialGradient(250, 150, 0, 250, 150, 200);
            gradient.addColorStop(0, 'rgba(255, 255, 255, 0.1)');
            gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 500, 600);
        }

        function drawCharacterName(ctx, name) {
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 24px Cairo, Arial';
            ctx.textAlign = 'center';
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = 2;
            ctx.strokeText(name, 250, 550);
            ctx.fillText(name, 250, 550);
        }

        function adjustColor(color, amount) {
            const num = parseInt(color.replace("#", ""), 16);
            const amt = Math.round(2.55 * amount);
            const R = (num >> 16) + amt;
            const G = (num >> 8 & 0x00FF) + amt;
            const B = (num & 0x0000FF) + amt;
            return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
        }

        // ==================== الوظائف المساعدة ====================
        function getCharacterFormData() {
            return {
                name: document.getElementById('characterName').value.trim() || 'شخصية جديدة',
                age: document.getElementById('characterAge').value || 'بالغ',
                gender: document.getElementById('characterGender').value || 'محايد',
                type: document.getElementById('characterType').value || 'مساعد',
                artStyle: document.getElementById('artStyle').value || 'كرتوني',
                description: document.getElementById('characterDescription').value.trim() || 'شخصية كرتونية رائعة',
                skinColor: document.getElementById('skinColor').value,
                hairColor: document.getElementById('hairColor').value,
                eyeColor: document.getElementById('eyeColor').value,
                clothesColor: document.getElementById('clothesColor').value,
                accessoryColor: document.getElementById('accessoryColor').value,
                backgroundColor: document.getElementById('backgroundColor').value
            };
        }

        function validateForm() {
            const name = document.getElementById('characterName').value.trim();
            const nameInput = document.getElementById('characterName');

            if (!name) {
                nameInput.style.borderColor = '#ff6b6b';
                return false;
            } else {
                nameInput.style.borderColor = '';
                return true;
            }
        }

        function updateColorPreview() {
            // يمكن إضافة معاينة فورية للألوان هنا
            showStatus('تم تحديث الألوان', 'info');
        }

        function generateCharacterStory(data) {
            const stories = {
                'بطل': `${data.name} هو بطل شجاع نشأ في قرية صغيرة. تدرب على فنون القتال منذ صغره وأصبح حامي الضعفاء. يسافر عبر الأراضي لمحاربة الشر ونشر العدالة.`,
                'شرير': `${data.name} شخصية معقدة ذات ماض مؤلم. يسعى للسيطرة على العالم من خلال خطط ماكرة ومعقدة. رغم شره، إلا أن في قلبه بقايا خير قد تظهر يوماً ما.`,
                'مساعد': `${data.name} مساعد مخلص ووفي للأبطال. يتمتع بذكاء حاد وحس فكاهي رائع. دائماً ما يجد حلولاً إبداعية للمشاكل ويرفع معنويات الفريق في الأوقات الصعبة.`,
                'كوميدي': `${data.name} شخصية مرحة تحب إضحاك الآخرين. لديه طرق مبتكرة ومضحكة لحل المشاكل. رغم مرحه، إلا أنه شجاع عند الحاجة ويقف مع أصدقائه.`,
                'حكيم': `${data.name} شخصية حكيمة عاشت لسنوات طويلة. يمتلك معرفة واسعة بأسرار الكون ويقدم النصائح القيمة للأبطال. كلماته قليلة لكنها مؤثرة وعميقة.`,
                'غامض': `${data.name} شخصية غامضة لها أسرار كثيرة. ظهوره المفاجئ يغير مجرى الأحداث دائماً. لا أحد يعرف دوافعه الحقيقية، لكن أفعاله تؤثر على مصير الجميع.`
            };

            return stories[data.type] || `${data.name} شخصية مميزة لها دور مهم في القصة. تتمتع بصفات فريدة تجعلها محبوبة من الجميع.`;
        }

        function generatePersonality(data) {
            const personalities = {
                'بطل': ['شجاع', 'عادل', 'مخلص', 'قوي', 'محب للخير', 'حامي'],
                'شرير': ['ماكر', 'ذكي', 'طموح', 'معقد', 'قوي الإرادة', 'غامض'],
                'مساعد': ['مخلص', 'مرح', 'مساعد', 'ودود', 'موثوق', 'ذكي'],
                'كوميدي': ['مرح', 'مبدع', 'اجتماعي', 'متفائل', 'محب للمرح', 'مضحك'],
                'حكيم': ['حكيم', 'صبور', 'متزن', 'عالم', 'مرشد', 'هادئ'],
                'غامض': ['غامض', 'هادئ', 'ذكي', 'مستقل', 'لا يُتوقع', 'مثير']
            };

            return personalities[data.type] || ['مميز', 'فريد', 'مثير للاهتمام'];
        }

        function displayCharacter(character) {
            const canvas = document.getElementById('characterCanvas');
            canvas.innerHTML = `
                <img src="${character.imageUrl}" alt="${character.name}" class="character-image" onclick="showCharacterDetails()">
            `;
            canvas.classList.add('has-image');

            // تحديث لوحة معلومات الشخصية
            document.getElementById('displayCharacterName').textContent = character.name;
            document.getElementById('displayAge').textContent = character.age;
            document.getElementById('displayGender').textContent = character.gender;
            document.getElementById('displayType').textContent = character.type;
            document.getElementById('displayStyle').textContent = character.artStyle;
            document.getElementById('displayDescription').textContent = character.description;
            document.getElementById('displayStory').textContent = character.story;
        }

        function showCharacterDetails() {
            if (!currentCharacter) return;

            Swal.fire({
                title: currentCharacter.name,
                html: `
                    <div style="text-align: right; line-height: 1.6;">
                        <img src="${currentCharacter.imageUrl}" style="width: 200px; height: auto; border-radius: 10px; margin-bottom: 1rem;">
                        <p><strong>العمر:</strong> ${currentCharacter.age}</p>
                        <p><strong>الجنس:</strong> ${currentCharacter.gender}</p>
                        <p><strong>النوع:</strong> ${currentCharacter.type}</p>
                        <p><strong>النمط:</strong> ${currentCharacter.artStyle}</p>
                        <p><strong>الوصف:</strong> ${currentCharacter.description}</p>
                        <p><strong>القصة:</strong> ${currentCharacter.story}</p>
                        <p><strong>الشخصية:</strong> ${currentCharacter.personality ? currentCharacter.personality.join(', ') : 'غير محدد'}</p>
                        <p><strong>تاريخ الإنشاء:</strong> ${new Date(currentCharacter.createdAt).toLocaleDateString('ar-SA')}</p>
                        <p><strong>تم التوليد بـ:</strong> ${currentCharacter.generatedWith || 'غير محدد'}</p>
                    </div>
                `,
                width: '600px',
                background: '#1a1a2e',
                color: '#ffffff',
                confirmButtonText: 'إغلاق'
            });
        }

        // ==================== وظائف القوالب ====================
        function applyTemplate(templateName) {
            const template = characterTemplates[templateName];
            if (!template) {
                showStatus('القالب غير موجود', 'error');
                return;
            }

            // ملء النموذج ببيانات القالب
            document.getElementById('characterName').value = template.name;
            document.getElementById('characterAge').value = template.age;
            document.getElementById('characterGender').value = template.gender;
            document.getElementById('characterType').value = template.type;
            document.getElementById('characterDescription').value = template.description;
            document.getElementById('skinColor').value = template.skinColor;
            document.getElementById('hairColor').value = template.hairColor;
            document.getElementById('eyeColor').value = template.eyeColor;
            document.getElementById('clothesColor').value = template.clothesColor;
            document.getElementById('accessoryColor').value = template.accessoryColor;
            document.getElementById('backgroundColor').value = template.backgroundColor;

            // تأثير بصري للقالب المطبق
            const templateBtn = document.querySelector(`[data-template="${templateName}"]`);
            if (templateBtn) {
                templateBtn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    templateBtn.style.transform = '';
                }, 150);
            }

            showStatus(`تم تطبيق قالب ${template.name}`, 'success');

            Swal.fire({
                title: 'تم تطبيق القالب!',
                text: `تم تطبيق قالب ${template.name} بنجاح`,
                icon: 'success',
                timer: 1500,
                showConfirmButton: false,
                background: '#1a1a2e',
                color: '#ffffff'
            });
        }

        function resetForm() {
            Swal.fire({
                title: 'إعادة تعيين النموذج',
                text: 'هل أنت متأكد من إعادة تعيين جميع الحقول؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، أعد التعيين',
                cancelButtonText: 'إلغاء',
                background: '#1a1a2e',
                color: '#ffffff'
            }).then((result) => {
                if (result.isConfirmed) {
                    // إعادة تعيين جميع الحقول
                    document.getElementById('characterName').value = '';
                    document.getElementById('characterAge').value = '';
                    document.getElementById('characterGender').value = '';
                    document.getElementById('characterType').value = '';
                    document.getElementById('characterDescription').value = '';
                    document.getElementById('skinColor').value = '#ffdbac';
                    document.getElementById('hairColor').value = '#8b4513';
                    document.getElementById('eyeColor').value = '#4169e1';
                    document.getElementById('clothesColor').value = '#ff6b9d';
                    document.getElementById('accessoryColor').value = '#ffd700';
                    document.getElementById('backgroundColor').value = '#87ceeb';

                    showStatus('تم إعادة تعيين النموذج', 'success');
                }
            });
        }

        // ==================== إدارة الشخصيات ====================
        function saveCharacter() {
            if (!currentCharacter) {
                showStatus('لا توجد شخصية للحفظ', 'error');
                return;
            }

            try {
                // التحقق من عدم وجود الشخصية مسبقاً
                const existingIndex = characters.findIndex(char => char.id === currentCharacter.id);

                if (existingIndex !== -1) {
                    // تحديث الشخصية الموجودة
                    characters[existingIndex] = currentCharacter;
                    showStatus('تم تحديث الشخصية', 'success');
                } else {
                    // إضافة شخصية جديدة
                    characters.push(currentCharacter);
                    currentProject.characters.push(currentCharacter.id);
                    showStatus('تم حفظ الشخصية الجديدة', 'success');
                }

                // حفظ في localStorage
                localStorage.setItem('cartoonCharacters', JSON.stringify(characters));
                localStorage.setItem('cartoonProjects', JSON.stringify(projects));

                updateStats();

                Swal.fire({
                    title: 'تم الحفظ!',
                    text: 'تم حفظ الشخصية في مشروعك بنجاح',
                    icon: 'success',
                    confirmButtonText: 'رائع!',
                    background: '#1a1a2e',
                    color: '#ffffff',
                    timer: 2000
                });

            } catch (error) {
                console.error('خطأ في الحفظ:', error);
                showStatus('فشل في حفظ الشخصية', 'error');

                Swal.fire({
                    title: 'خطأ في الحفظ',
                    text: 'حدث خطأ أثناء حفظ الشخصية',
                    icon: 'error',
                    background: '#1a1a2e',
                    color: '#ffffff'
                });
            }
        }

        function editCharacter() {
            if (!currentCharacter) {
                showStatus('لا توجد شخصية للتحرير', 'error');
                return;
            }

            Swal.fire({
                title: 'تحرير الشخصية',
                html: `
                    <div style="text-align: right;">
                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem;">اسم جديد:</label>
                            <input type="text" id="newName" class="swal2-input" value="${currentCharacter.name}" maxlength="50">
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem;">وصف جديد:</label>
                            <textarea id="newDescription" class="swal2-textarea" maxlength="500">${currentCharacter.description}</textarea>
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem;">قصة جديدة:</label>
                            <textarea id="newStory" class="swal2-textarea" maxlength="1000">${currentCharacter.story || ''}</textarea>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'حفظ التغييرات',
                cancelButtonText: 'إلغاء',
                background: '#1a1a2e',
                color: '#ffffff',
                width: '600px',
                preConfirm: () => {
                    const newName = document.getElementById('newName').value.trim();
                    const newDescription = document.getElementById('newDescription').value.trim();
                    const newStory = document.getElementById('newStory').value.trim();

                    if (!newName) {
                        Swal.showValidationMessage('يرجى إدخال اسم الشخصية');
                        return false;
                    }

                    if (newName.length > 50) {
                        Swal.showValidationMessage('اسم الشخصية طويل جداً (الحد الأقصى 50 حرف)');
                        return false;
                    }

                    return { name: newName, description: newDescription, story: newStory };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    currentCharacter.name = result.value.name;
                    currentCharacter.description = result.value.description;
                    currentCharacter.story = result.value.story;
                    currentCharacter.updatedAt = new Date().toISOString();

                    // تحديث العرض
                    displayCharacter(currentCharacter);

                    // حفظ تلقائي
                    autoSave();

                    showStatus('تم تحديث الشخصية', 'success');

                    Swal.fire({
                        title: 'تم التحديث!',
                        text: 'تم تحديث بيانات الشخصية بنجاح',
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false,
                        background: '#1a1a2e',
                        color: '#ffffff'
                    });
                }
            });
        }

        function exportCharacter() {
            if (!currentCharacter) {
                showStatus('لا توجد شخصية للتصدير', 'error');
                return;
            }

            Swal.fire({
                title: 'تصدير الشخصية',
                text: 'اختر تنسيق التصدير',
                icon: 'question',
                showCancelButton: true,
                showDenyButton: true,
                confirmButtonText: 'صورة PNG عالية الجودة',
                denyButtonText: 'بيانات JSON',
                cancelButtonText: 'إلغاء',
                background: '#1a1a2e',
                color: '#ffffff'
            }).then((result) => {
                if (result.isConfirmed) {
                    exportAsImage();
                } else if (result.isDenied) {
                    exportAsJSON();
                }
            });
        }

        function exportAsImage() {
            try {
                const link = document.createElement('a');
                link.download = `${currentCharacter.name}_${Date.now()}.png`;
                link.href = currentCharacter.imageUrl;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showStatus('تم تصدير الصورة بنجاح', 'success');

                Swal.fire({
                    title: 'تم التصدير!',
                    text: 'تم تصدير صورة الشخصية بنجاح',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false,
                    background: '#1a1a2e',
                    color: '#ffffff'
                });

            } catch (error) {
                console.error('خطأ في تصدير الصورة:', error);
                showStatus('فشل في تصدير الصورة', 'error');
            }
        }

        function exportAsJSON() {
            try {
                const exportData = {
                    character: currentCharacter,
                    exportDate: new Date().toISOString(),
                    version: '2.0'
                };

                const dataStr = JSON.stringify(exportData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `${currentCharacter.name}_data_${Date.now()}.json`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showStatus('تم تصدير البيانات بنجاح', 'success');

                Swal.fire({
                    title: 'تم التصدير!',
                    text: 'تم تصدير بيانات الشخصية بنجاح',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false,
                    background: '#1a1a2e',
                    color: '#ffffff'
                });

            } catch (error) {
                console.error('خطأ في تصدير البيانات:', error);
                showStatus('فشل في تصدير البيانات', 'error');
            }
        }

        function deleteCharacter() {
            if (!currentCharacter) {
                showStatus('لا توجد شخصية للحذف', 'error');
                return;
            }

            Swal.fire({
                title: 'تأكيد الحذف',
                text: `هل أنت متأكد من حذف شخصية "${currentCharacter.name}"؟`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#ff6b6b',
                background: '#1a1a2e',
                color: '#ffffff'
            }).then((result) => {
                if (result.isConfirmed) {
                    // مسح العرض
                    const canvas = document.getElementById('characterCanvas');
                    canvas.innerHTML = `
                        <div class="placeholder">
                            <div class="placeholder-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="placeholder-text">
                                اضغط على "توليد شخصية جديدة" لإنشاء شخصيتك الأولى<br>
                                أو اختر قالباً سريعاً من الشريط الجانبي
                            </div>
                        </div>
                    `;
                    canvas.classList.remove('has-image');

                    // إعادة تعيين معلومات الشخصية
                    document.getElementById('displayCharacterName').textContent = 'اسم الشخصية';
                    document.getElementById('displayAge').textContent = 'غير محدد';
                    document.getElementById('displayGender').textContent = 'غير محدد';
                    document.getElementById('displayType').textContent = 'غير محدد';
                    document.getElementById('displayStyle').textContent = 'غير محدد';
                    document.getElementById('displayDescription').textContent = 'لم يتم إنشاء شخصية بعد';
                    document.getElementById('displayStory').textContent = 'لا توجد قصة';

                    // تعطيل أزرار الإجراءات
                    disableActionButtons();

                    currentCharacter = null;
                    localStorage.removeItem('currentCharacter');

                    showStatus('تم حذف الشخصية', 'success');

                    Swal.fire({
                        title: 'تم الحذف!',
                        text: 'تم حذف الشخصية بنجاح',
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false,
                        background: '#1a1a2e',
                        color: '#ffffff'
                    });
                }
            });
        }

        // ==================== وظائف مساعدة للواجهة ====================
        function enableActionButtons() {
            document.getElementById('saveBtn').disabled = false;
            document.getElementById('editBtn').disabled = false;
            document.getElementById('exportBtn').disabled = false;
            document.getElementById('deleteBtn').disabled = false;
        }

        function disableActionButtons() {
            document.getElementById('saveBtn').disabled = true;
            document.getElementById('editBtn').disabled = true;
            document.getElementById('exportBtn').disabled = true;
            document.getElementById('deleteBtn').disabled = true;
        }

        function updateStats() {
            const today = new Date().toDateString();
            const todayCharacters = characters.filter(char =>
                new Date(char.createdAt).toDateString() === today
            ).length;

            document.getElementById('totalCharacters').textContent = characters.length;
            document.getElementById('savedCharacters').textContent = characters.length;
            document.getElementById('projectsCount').textContent = projects.length;
            document.getElementById('generatedToday').textContent = todayCharacters;
        }

        function updateLoadingText(mainText, subText) {
            document.getElementById('loadingText').textContent = mainText;
            document.getElementById('loadingSubtext').textContent = subText;
        }

        function showStatus(message, type = 'info') {
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');

            statusText.textContent = message;
            indicator.className = 'status-indicator show';

            // تغيير اللون حسب النوع
            switch(type) {
                case 'success':
                    indicator.style.borderColor = '#4ecdc4';
                    break;
                case 'error':
                    indicator.style.borderColor = '#ff6b6b';
                    break;
                case 'warning':
                    indicator.style.borderColor = '#ffe66d';
                    break;
                default:
                    indicator.style.borderColor = '#667eea';
            }

            // إخفاء بعد 3 ثوان
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 3000);
        }

        function loadCharacterForm() {
            // تحميل القيم الافتراضية إذا لزم الأمر
            if (!document.getElementById('characterName').value) {
                document.getElementById('characterName').value = '';
            }
        }

        function applySettings() {
            // تطبيق الإعدادات المحفوظة
            if (apiSettings.useOpenAI) {
                showStatus('تم تفعيل OpenAI API', 'success');
            }
        }

        function updateUI() {
            // تحديث واجهة المستخدم
            updateStats();

            if (currentCharacter) {
                enableActionButtons();
            } else {
                disableActionButtons();
            }
        }

        function autoSave() {
            if (currentCharacter) {
                try {
                    localStorage.setItem('currentCharacter', JSON.stringify(currentCharacter));
                    localStorage.setItem('cartoonCharacters', JSON.stringify(characters));
                    localStorage.setItem('cartoonProjects', JSON.stringify(projects));
                    console.log('تم الحفظ التلقائي');
                } catch (error) {
                    console.error('خطأ في الحفظ التلقائي:', error);
                }
            }
        }

        // ==================== وظائف التنقل ====================
        function showGallery() {
            if (characters.length === 0) {
                Swal.fire({
                    title: 'المعرض فارغ',
                    text: 'لم يتم حفظ أي شخصيات بعد. قم بإنشاء وحفظ شخصيات أولاً.',
                    icon: 'info',
                    background: '#1a1a2e',
                    color: '#ffffff'
                });
                return;
            }

            let galleryHTML = '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; max-height: 400px; overflow-y: auto;">';
            characters.forEach((char, index) => {
                galleryHTML += `
                    <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.1); border-radius: 10px; cursor: pointer;" onclick="loadCharacterFromGallery(${index})">
                        <img src="${char.imageUrl}" style="width: 80px; height: 80px; border-radius: 10px; margin-bottom: 0.5rem; object-fit: cover;">
                        <div style="font-weight: bold; margin-bottom: 0.25rem; font-size: 0.9rem;">${char.name}</div>
                        <div style="font-size: 0.8rem; color: #999;">${char.type}</div>
                        <div style="font-size: 0.7rem; color: #666; margin-top: 0.25rem;">${new Date(char.createdAt).toLocaleDateString('ar-SA')}</div>
                    </div>
                `;
            });
            galleryHTML += '</div>';

            Swal.fire({
                title: `معرض الشخصيات (${characters.length})`,
                html: galleryHTML,
                width: '800px',
                background: '#1a1a2e',
                color: '#ffffff',
                showCloseButton: true,
                showConfirmButton: false
            });
        }

        function loadCharacterFromGallery(index) {
            const character = characters[index];
            if (character) {
                currentCharacter = character;
                displayCharacter(character);
                enableActionButtons();
                Swal.close();
                showStatus(`تم تحميل شخصية ${character.name}`, 'success');
            }
        }

        function showProjects() {
            const projectsHTML = `
                <div style="text-align: right; line-height: 1.6;">
                    <h4>المشروع الحالي</h4>
                    <p><strong>الاسم:</strong> ${currentProject.name}</p>
                    <p><strong>عدد الشخصيات:</strong> ${currentProject.characters.length}</p>
                    <p><strong>تاريخ الإنشاء:</strong> ${new Date(currentProject.createdAt).toLocaleDateString('ar-SA')}</p>

                    <hr style="margin: 1rem 0; border-color: rgba(255,255,255,0.2);">

                    <h4>إحصائيات عامة</h4>
                    <p><strong>إجمالي المشاريع:</strong> ${projects.length}</p>
                    <p><strong>إجمالي الشخصيات:</strong> ${characters.length}</p>
                    <p><strong>الشخصيات المولدة اليوم:</strong> ${characters.filter(char =>
                        new Date(char.createdAt).toDateString() === new Date().toDateString()
                    ).length}</p>
                </div>
            `;

            Swal.fire({
                title: 'مشاريعي',
                html: projectsHTML,
                background: '#1a1a2e',
                color: '#ffffff',
                confirmButtonText: 'إغلاق'
            });
        }

        function showSettings() {
            Swal.fire({
                title: 'الإعدادات',
                html: `
                    <div style="text-align: right;">
                        <h4>إعدادات التوليد</h4>
                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem;">
                                <input type="checkbox" id="useOpenAI" ${apiSettings.useOpenAI ? 'checked' : ''}>
                                استخدام OpenAI API
                            </label>
                        </div>

                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem;">جودة الصورة:</label>
                            <select id="qualitySetting" class="swal2-select">
                                <option value="high" ${apiSettings.quality === 'high' ? 'selected' : ''}>عالية</option>
                                <option value="standard" ${apiSettings.quality === 'standard' ? 'selected' : ''}>متوسطة</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem;">حالة API:</label>
                            <p style="color: ${OPENAI_API_KEY ? '#4ecdc4' : '#ff6b6b'};">
                                ${OPENAI_API_KEY ? '✅ متصل' : '❌ غير متصل'}
                            </p>
                        </div>

                        <hr style="margin: 1rem 0; border-color: rgba(255,255,255,0.2);">

                        <h4>إعدادات التخزين</h4>
                        <div style="margin-bottom: 1rem;">
                            <button onclick="clearAllData()" class="swal2-confirm swal2-styled" style="background-color: #ff6b6b;">
                                مسح جميع البيانات
                            </button>
                        </div>

                        <div style="margin-bottom: 1rem;">
                            <button onclick="exportAllCharacters()" class="swal2-confirm swal2-styled" style="background-color: #4ecdc4;">
                                تصدير جميع الشخصيات
                            </button>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'حفظ الإعدادات',
                cancelButtonText: 'إلغاء',
                background: '#1a1a2e',
                color: '#ffffff',
                width: '500px',
                preConfirm: () => {
                    const useOpenAI = document.getElementById('useOpenAI').checked;
                    const quality = document.getElementById('qualitySetting').value;

                    return { useOpenAI, quality };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    apiSettings.useOpenAI = result.value.useOpenAI;
                    apiSettings.quality = result.value.quality;

                    localStorage.setItem('apiSettings', JSON.stringify(apiSettings));

                    showStatus('تم حفظ الإعدادات', 'success');

                    Swal.fire({
                        title: 'تم الحفظ!',
                        text: 'تم حفظ الإعدادات بنجاح',
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false,
                        background: '#1a1a2e',
                        color: '#ffffff'
                    });
                }
            });
        }

        function showHelp() {
            const helpHTML = `
                <div style="text-align: right; line-height: 1.6;">
                    <h4>🎨 كيفية استخدام الاستوديو</h4>

                    <h5>1. إنشاء شخصية جديدة:</h5>
                    <ul style="text-align: right;">
                        <li>املأ النموذج في الشريط الجانبي</li>
                        <li>اختر الألوان المناسبة</li>
                        <li>اضغط "توليد شخصية جديدة"</li>
                    </ul>

                    <h5>2. استخدام القوالب السريعة:</h5>
                    <ul style="text-align: right;">
                        <li>اضغط على أي قالب من الستة المتاحة</li>
                        <li>سيتم ملء النموذج تلقائياً</li>
                        <li>يمكنك تعديل أي تفاصيل</li>
                    </ul>

                    <h5>3. اختصارات لوحة المفاتيح:</h5>
                    <ul style="text-align: right;">
                        <li><kbd>Ctrl+S</kbd> - حفظ الشخصية</li>
                        <li><kbd>Ctrl+N</kbd> - توليد شخصية جديدة</li>
                        <li><kbd>Ctrl+E</kbd> - تصدير الشخصية</li>
                        <li><kbd>Ctrl+R</kbd> - إعادة تعيين النموذج</li>
                    </ul>

                    <h5>4. الميزات المتقدمة:</h5>
                    <ul style="text-align: right;">
                        <li>حفظ تلقائي كل 30 ثانية</li>
                        <li>تكامل مع OpenAI API</li>
                        <li>تصدير بصيغ متعددة</li>
                        <li>معرض الشخصيات التفاعلي</li>
                    </ul>
                </div>
            `;

            Swal.fire({
                title: 'دليل الاستخدام',
                html: helpHTML,
                width: '600px',
                background: '#1a1a2e',
                color: '#ffffff',
                confirmButtonText: 'فهمت'
            });
        }

        // ==================== وظائف إضافية ====================
        function generateRandomCharacter() {
            const names = ['زين', 'نور', 'سارة', 'أحمد', 'فاطمة', 'علي', 'مريم', 'محمد', 'عائشة', 'يوسف'];
            const ages = ['طفل', 'مراهق', 'بالغ', 'كبير'];
            const genders = ['ذكر', 'أنثى', 'محايد'];
            const types = ['بطل', 'شرير', 'مساعد', 'كوميدي', 'حكيم', 'غامض'];
            const styles = ['كرتوني', 'أنمي', 'ديزني', 'بيكسار', 'تشيبي', 'واقعي'];

            document.getElementById('characterName').value = names[Math.floor(Math.random() * names.length)];
            document.getElementById('characterAge').value = ages[Math.floor(Math.random() * ages.length)];
            document.getElementById('characterGender').value = genders[Math.floor(Math.random() * genders.length)];
            document.getElementById('characterType').value = types[Math.floor(Math.random() * types.length)];
            document.getElementById('artStyle').value = styles[Math.floor(Math.random() * styles.length)];
            document.getElementById('characterDescription').value = 'شخصية عشوائية مثيرة للاهتمام';

            // ألوان عشوائية
            document.getElementById('skinColor').value = getRandomColor();
            document.getElementById('hairColor').value = getRandomColor();
            document.getElementById('eyeColor').value = getRandomColor();
            document.getElementById('clothesColor').value = getRandomColor();
            document.getElementById('accessoryColor').value = getRandomColor();
            document.getElementById('backgroundColor').value = getRandomColor();

            showStatus('تم إنشاء شخصية عشوائية', 'success');
        }

        function getRandomColor() {
            const colors = ['#ff6b9d', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff', '#5f27cd'];
            return colors[Math.floor(Math.random() * colors.length)];
        }

        function generateVariation() {
            if (!currentCharacter) {
                showStatus('لا توجد شخصية لإنشاء تنويع منها', 'error');
                return;
            }

            // إنشاء تنويع بألوان مختلفة
            document.getElementById('characterName').value = currentCharacter.name + ' المطور';
            document.getElementById('skinColor').value = adjustColor(currentCharacter.skinColor, Math.random() * 40 - 20);
            document.getElementById('hairColor').value = adjustColor(currentCharacter.hairColor, Math.random() * 40 - 20);
            document.getElementById('clothesColor').value = adjustColor(currentCharacter.clothesColor, Math.random() * 40 - 20);

            showStatus('تم إنشاء تنويع للشخصية', 'success');
        }

        function showCharacterHistory() {
            if (characters.length === 0) {
                Swal.fire({
                    title: 'لا يوجد تاريخ',
                    text: 'لم يتم إنشاء أي شخصيات بعد',
                    icon: 'info',
                    background: '#1a1a2e',
                    color: '#ffffff'
                });
                return;
            }

            const sortedCharacters = [...characters].sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

            let historyHTML = '<div style="max-height: 400px; overflow-y: auto;">';
            sortedCharacters.forEach((char, index) => {
                historyHTML += `
                    <div style="display: flex; align-items: center; padding: 0.5rem; margin-bottom: 0.5rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                        <img src="${char.imageUrl}" style="width: 40px; height: 40px; border-radius: 50%; margin-left: 1rem;">
                        <div style="flex: 1; text-align: right;">
                            <div style="font-weight: bold;">${char.name}</div>
                            <div style="font-size: 0.8rem; color: #999;">${new Date(char.createdAt).toLocaleString('ar-SA')}</div>
                        </div>
                        <div style="font-size: 0.8rem; color: #666;">${char.generatedWith || 'غير محدد'}</div>
                    </div>
                `;
            });
            historyHTML += '</div>';

            Swal.fire({
                title: 'تاريخ الشخصيات',
                html: historyHTML,
                width: '600px',
                background: '#1a1a2e',
                color: '#ffffff'
            });
        }

        function exportAllCharacters() {
            if (characters.length === 0) {
                showStatus('لا توجد شخصيات للتصدير', 'error');
                return;
            }

            const exportData = {
                characters: characters,
                projects: projects,
                exportDate: new Date().toISOString(),
                version: '2.0',
                totalCharacters: characters.length
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `جميع_الشخصيات_${Date.now()}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showStatus(`تم تصدير ${characters.length} شخصية`, 'success');
        }

        function clearAllData() {
            Swal.fire({
                title: 'تحذير!',
                text: 'هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف الكل',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#ff6b6b',
                background: '#1a1a2e',
                color: '#ffffff'
            }).then((result) => {
                if (result.isConfirmed) {
                    localStorage.clear();
                    location.reload();
                }
            });
        }

        // تشغيل النظام عند تحميل الصفحة
        console.log('🎨 استوديو الشخصيات الكرتونية المحسن - جاهز للاستخدام!');
        console.log('📝 الميزات المتاحة:');
        console.log('   - توليد شخصيات بالذكاء الاصطناعي أو Canvas');
        console.log('   - قوالب سريعة للشخصيات');
        console.log('   - حفظ وتصدير الشخصيات');
        console.log('   - معرض تفاعلي للشخصيات');
        console.log('   - اختصارات لوحة المفاتيح');
        console.log('   - حفظ تلقائي');
    </script>
</body>
</html>
