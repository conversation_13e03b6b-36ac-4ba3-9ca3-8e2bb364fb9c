<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نموذج التسجيل الاحترافي - أكاديمية 7C الرياضية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: #0f172a; min-height: 100vh; }
        .glass { background: rgba(255, 255, 255, 0.08); backdrop-filter: blur(25px); border: 1px solid rgba(255, 255, 255, 0.15); box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3); }
        .form-step { display: none; animation: fadeIn 0.5s ease-in-out; }
        .form-step.active { display: block; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        .progress-bar { transition: width 0.3s ease; }
        .face-recognition-area { position: relative; border: 3px solid rgba(59, 130, 246, 0.5); border-radius: 50%; width: 200px; height: 200px; margin: 0 auto; overflow: hidden; }
        .face-recognition-area.scanning { animation: pulse 2s infinite; border-color: rgba(34, 197, 94, 0.8); }
        @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.05); } }
        .payment-method { transition: all 0.3s ease; cursor: pointer; }
        .payment-method:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3); }
        .payment-method.selected { border-color: rgba(139, 92, 246, 0.8); background: rgba(139, 92, 246, 0.1); }
    </style>
</head>
<body class="text-slate-100">
    <!-- Header -->
    <div class="glass p-6 m-4 rounded-2xl">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                    نموذج التسجيل الاحترافي
                </h1>
                <p class="text-slate-300 mt-2">أكاديمية 7C الرياضية - انضم إلينا الآن</p>
            </div>
            <div class="flex items-center gap-4">
                <div class="bg-green-600/20 px-4 py-2 rounded-lg border border-green-600/30">
                    <i class="fas fa-shield-check ml-2 text-green-400"></i>
                    <span>آمن ومحمي</span>
                </div>
                <div class="bg-blue-600/20 px-4 py-2 rounded-lg border border-blue-600/30">
                    <i class="fas fa-face-smile ml-2 text-blue-400"></i>
                    <span>تعرف على الوجه</span>
                </div>
            </div>
        </div>
    </div>
    <!-- Progress Bar -->
    <div class="glass p-4 m-4 rounded-2xl">
        <div class="flex items-center justify-between mb-4">
            <span class="text-sm text-slate-300">خطوة <span id="currentStep">1</span> من 6</span>
            <span class="text-sm text-slate-300"><span id="progressPercent">16</span>% مكتمل</span>
        </div>
        <div class="w-full bg-slate-700/40 rounded-full h-3">
            <div id="progressBar" class="progress-bar bg-gradient-to-r from-purple-600 to-blue-600 h-3 rounded-full" style="width: 16%"></div>
        </div>
        <div class="flex justify-between mt-2 text-xs text-slate-400">
            <span>البيانات الأساسية</span>
            <span>معلومات الاتصال</span>
            <span>خطة الاشتراك</span>
            <span>التعرف على الوجه</span>
            <span>الدفع</span>
            <span>التأكيد</span>
        </div>
    </div>
    <!-- Registration Form -->
    <div class="glass p-8 m-4 rounded-2xl">
        <form id="registrationForm">
            <!-- Step 1: Basic Information -->
            <div class="form-step active" id="step1">
                <h2 class="text-2xl font-bold mb-6 text-center">البيانات الأساسية</h2>

                <!-- Player Photo Upload -->
                <div class="mb-6">
                    <label class="block text-sm font-semibold mb-2">صورة اللاعب</label>
                    <div class="flex items-center gap-4">
                        <div class="w-24 h-24 bg-slate-700/50 border-2 border-dashed border-slate-600/30 rounded-xl flex items-center justify-center relative overflow-hidden">
                            <img id="playerPhotoPreview" src="" alt="صورة اللاعب" class="w-full h-full object-cover hidden">
                            <div id="photoPlaceholder" class="text-center">
                                <i class="fas fa-camera text-2xl text-slate-500 mb-1"></i>
                                <p class="text-xs text-slate-400">صورة اللاعب</p>
                            </div>
                        </div>
                        <div>
                            <input type="file" id="playerPhoto" accept="image/*" class="hidden" onchange="handlePhotoUpload(event)">
                            <button type="button" onclick="document.getElementById('playerPhoto').click()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-sm transition-all">
                                <i class="fas fa-upload ml-2"></i>رفع صورة
                            </button>
                            <p class="text-xs text-slate-400 mt-1">JPG, PNG (حد أقصى 2MB)</p>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Name Fields -->
                    <div>
                        <label class="block text-sm font-semibold mb-2">الاسم الأول *</label>
                        <input type="text" id="firstName" required class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100 placeholder-white/50 focus:border-purple-400 transition-colors" placeholder="الاسم الأول">
                    </div>
                    <div>
                        <label class="block text-sm font-semibold mb-2">اسم الأب *</label>
                        <input type="text" id="fatherName" required class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100 placeholder-white/50 focus:border-purple-400 transition-colors" placeholder="اسم الأب">
                    </div>
                    <div>
                        <label class="block text-sm font-semibold mb-2">اسم العائلة *</label>
                        <input type="text" id="familyName" required class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100 placeholder-white/50 focus:border-purple-400 transition-colors" placeholder="اسم العائلة">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div>
                        <label class="block text-sm font-semibold mb-2">رقم الهوية الوطنية *</label>
                        <input type="text" id="nationalId" required maxlength="10" pattern="[0-9]{10}" class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100 placeholder-white/50 focus:border-purple-400 transition-colors" placeholder="1234567890">
                    </div>
                    <div>
                        <label class="block text-sm font-semibold mb-2">تاريخ الميلاد *</label>
                        <input type="date" id="birthDate" required onchange="calculateAge()" class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100 focus:border-purple-400 transition-colors">
                    </div>
                    <div>
                        <label class="block text-sm font-semibold mb-2">العمر</label>
                        <input type="number" id="age" readonly class="w-full bg-slate-600/30 border border-slate-600/30 rounded-xl p-3 text-slate-300" placeholder="سيتم حسابه تلقائياً">
                    </div>
                    <div>
                        <label class="block text-sm font-semibold mb-2">رقم الجوال الشخصي *</label>
                        <input type="tel" id="personalPhone" required pattern="05[0-9]{8}" class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100 placeholder-white/50 focus:border-purple-400 transition-colors" placeholder="05xxxxxxxx">
                    </div>
                </div>

                <!-- Academic Number (Auto-generated) -->
                <div class="mt-6">
                    <label class="block text-sm font-semibold mb-2">الرقم الأكاديمي</label>
                    <div class="flex items-center gap-4">
                        <input type="text" id="academicNumber" readonly class="flex-1 bg-slate-600/30 border border-slate-600/30 rounded-xl p-3 text-slate-300" placeholder="سيتم توليده تلقائياً">
                        <button type="button" onclick="generateAcademicNumber()" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg text-sm transition-all">
                            <i class="fas fa-sync ml-2"></i>توليد
                        </button>
                    </div>
                    <p class="text-xs text-slate-400 mt-1">رقم فريد للاعب في الأكاديمية</p>
                </div>
                    <div class="md:col-span-2">
                    <label class="block text-sm font-semibold mb-2">الفئة العمرية</label>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <label class="flex items-center p-3 bg-slate-800/60 rounded-xl border border-slate-600/30 cursor-pointer hover:bg-slate-700/50 transition-colors">
                    <input type="radio" name="category" value="أشبال" class="ml-3">
                    <div><div class="font-semibold">أشبال</div><div class="text-xs text-slate-300">6-8 سنوات</div></div>
                    </label>
                    <label class="flex items-center p-3 bg-slate-800/60 rounded-xl border border-slate-600/30 cursor-pointer hover:bg-slate-700/50 transition-colors">
                    <input type="radio" name="category" value="براعم" class="ml-3">
                    <div><div class="font-semibold">براعم</div><div class="text-xs text-slate-300">9-11 سنة</div></div>
                    </label>
                    <label class="flex items-center p-3 bg-slate-800/60 rounded-xl border border-slate-600/30 cursor-pointer hover:bg-slate-700/50 transition-colors">
                    <input type="radio" name="category" value="ناشئين" class="ml-3">
                    <div><div class="font-semibold">ناشئين</div><div class="text-xs text-slate-300">12-15 سنة</div></div>
                    </label>
                    <label class="flex items-center p-3 bg-slate-800/60 rounded-xl border border-slate-600/30 cursor-pointer hover:bg-slate-700/50 transition-colors">
                    <input type="radio" name="category" value="شباب" class="ml-3">
                    <div><div class="font-semibold">شباب</div><div class="text-xs text-slate-300">16-18 سنة</div></div>
                    </label>
                    </div>
                    </div>
                    <input type="hidden" id="category">
                </div>
                <div class="mt-6">
                    <label class="block text-sm font-semibold mb-2">رقم جوال المُحيل (اختياري)</label>
                    <input type="tel" id="referralPhone" name="referralPhone" class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100 placeholder-white/50 focus:border-purple-400 transition-colors" placeholder="أدخل رقم جوال الذي قام بدعوتك للتسجيل (اختياري)">
                </div>
                <div class="flex justify-end mt-8">
                    <button type="button" onclick="nextStep()" class="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 px-8 py-3 rounded-xl transition-all">
                        التالي <i class="fas fa-arrow-left mr-2"></i>
                    </button>
                </div>
            </div>
            <!-- Step 2: Contact Information -->
            <div class="form-step" id="step2">
                <h2 class="text-2xl font-bold mb-6 text-center">معلومات الاتصال</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-semibold mb-2">رقم الهاتف</label>
                        <input type="tel" id="phone" class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100 placeholder-white/50 focus:border-purple-400 transition-colors" placeholder="05xxxxxxxx">
                    </div>
                    <div>
                        <label class="block text-sm font-semibold mb-2">البريد الإلكتروني</label>
                        <input type="email" id="email" class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100 placeholder-white/50 focus:border-purple-400 transition-colors" placeholder="<EMAIL>">
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-semibold mb-2">العنوا��</label>
                        <textarea id="address" rows="3" class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100 placeholder-white/50 focus:border-purple-400 transition-colors" placeholder="أدخل العنوان الكامل"></textarea>
                    </div>
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-semibold mb-4">بيانات ولي الأمر</h3>
                    </div>
                    <div>
                        <label class="block text-sm font-semibold mb-2">اسم ولي الأمر</label>
                        <input type="text" id="guardianName" class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100 placeholder-white/50 focus:border-purple-400 transition-colors" placeholder="أدخل اسم ولي الأمر">
                    </div>
                    <div>
                        <label class="block text-sm font-semibold mb-2">رقم هاتف ولي الأمر</label>
                        <input type="tel" id="guardianPhone" class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100 placeholder-white/50 focus:border-purple-400 transition-colors" placeholder="05xxxxxxxx">
                    </div>
                    <div>
                        <label class="block text-sm font-semibold mb-2">صلة القرابة</label>
                        <select id="relationship" class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100 focus:border-purple-400 transition-colors">
                            <option value="">اختر صلة القرابة</option>
                            <option value="father">الأب</option>
                            <option value="mother">الأم</option>
                            <option value="brother">الأخ</option>
                            <option value="sister">الأخت</option>
                            <option value="uncle">العم</option>
                            <option value="aunt">العمة</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-semibold mb-2">بريد ولي الأمر الإلكتروني</label>
                        <input type="email" id="guardianEmail" class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100 placeholder-white/50 focus:border-purple-400 transition-colors" placeholder="<EMAIL>">
                    </div>
                </div>
                <div class="flex justify-between mt-8">
                    <button type="button" onclick="prevStep()" class="bg-gray-600 hover:bg-gray-700 px-8 py-3 rounded-xl transition-all">
                        <i class="fas fa-arrow-right ml-2"></i> السابق
                    </button>
                    <button type="button" onclick="nextStep()" class="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 px-8 py-3 rounded-xl transition-all">
                        التالي <i class="fas fa-arrow-left mr-2"></i>
                    </button>
                </div>
            </div>
            <!-- Step 3: Physical Information -->
            <div class="form-step" id="step3">
                <h2 class="text-2xl font-bold mb-6 text-center">المعلومات الجسمانية</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-semibold mb-2">القدم المفضلة *</label>
                        <select id="preferredFoot" required class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100 focus:border-purple-400 transition-colors">
                            <option value="">اختر القدم المفضلة</option>
                            <option value="right">اليمين</option>
                            <option value="left">اليسار</option>
                            <option value="both">كلاهما</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-semibold mb-2">الطول (سم) *</label>
                        <input type="number" id="height" required min="100" max="250" onchange="calculateBMI()" class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100 placeholder-white/50 focus:border-purple-400 transition-colors" placeholder="165">
                    </div>
                    <div>
                        <label class="block text-sm font-semibold mb-2">الوزن (كغ) *</label>
                        <input type="number" id="weight" required min="20" max="150" onchange="calculateBMI()" class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100 placeholder-white/50 focus:border-purple-400 transition-colors" placeholder="55">
                    </div>
                </div>

                <!-- BMI Display -->
                <div class="mt-6 p-4 bg-blue-600/20 border border-blue-600/30 rounded-xl">
                    <h3 class="font-bold mb-2">مؤشر كتلة الجسم (BMI)</h3>
                    <div id="bmiResult" class="text-lg font-bold text-blue-400">سيتم حسابه تلقائياً</div>
                    <div id="bmiCategory" class="text-sm text-gray-300"></div>
                </div>

                <div class="flex justify-between mt-8">
                    <button type="button" onclick="prevStep()" class="bg-gray-600 hover:bg-gray-700 px-8 py-3 rounded-xl transition-all">
                        <i class="fas fa-arrow-right ml-2"></i> السابق
                    </button>
                    <button type="button" onclick="nextStep()" class="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 px-8 py-3 rounded-xl transition-all">
                        التالي <i class="fas fa-arrow-left mr-2"></i>
                    </button>
                </div>
            </div>

            <!-- Step 4: Choose Subscription Plan -->
            <div class="form-step" id="step4">
                <h2 class="text-2xl font-bold mb-6 text-center">اختيار خطة الاشتراك</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <label class="block bg-slate-800/60 rounded-xl border-2 border-blue-600/30 p-6 cursor-pointer hover:border-blue-400 transition-all">
                        <input type="radio" name="subscriptionPlan" value="basic" class="ml-3">
                        <div class="font-bold text-blue-400 text-lg mb-2">الخطة الأساسية</div>
                    </label>
                    <label class="block bg-slate-800/60 rounded-xl border-2 border-purple-600/30 p-6 cursor-pointer hover:border-purple-400 transition-all">
                        <input type="radio" name="subscriptionPlan" value="premium" class="ml-3">
                        <div class="font-bold text-purple-400 text-lg mb-2">الخطة المتقدمة</div>
                    </label>
                    <label class="block bg-slate-800/60 rounded-xl border-2 border-yellow-600/30 p-6 cursor-pointer hover:border-yellow-400 transition-all">
                        <input type="radio" name="subscriptionPlan" value="vip" class="ml-3">
                        <div class="font-bold text-yellow-400 text-lg mb-2">خطة VIP</div>
                    </label>
                </div>
            <input type="hidden" id="subscriptionPlan">
                        <ul class="text-sm text-slate-200 mb-2">
                            <li>تدريب 3 مرات أسبوعياً</li>
                            <li>متابعة أساسية</li>
                            <li>تقارير شهرية</li>
                        </ul>
                        <div class="font-bold text-blue-300">299 ر.س / شهر</div>
                    </label>
                    <label class="block bg-slate-800/60 rounded-xl border-2 border-purple-600/30 p-6 cursor-pointer hover:border-purple-400 transition-all">
                        <input type="radio" name="subscriptionPlan" value="premium" class="ml-3">
                        <div class="font-bold text-purple-400 text-lg mb-2">الخطة المتقدمة</div>
                        <ul class="text-sm text-slate-200 mb-2">
                            <li>تدريب 5 مرات أسبوعياً</li>
                            <li>متابعة شخصية</li>
                            <li>تحليل AI للأداء</li>
                        </ul>
                        <div class="font-bold text-purple-300">499 ر.س / شهر</div>
                    </label>
                    <label class="block bg-slate-800/60 rounded-xl border-2 border-yellow-600/30 p-6 cursor-pointer hover:border-yellow-400 transition-all">
                        <input type="radio" name="subscriptionPlan" value="vip" class="ml-3">
                        <div class="font-bold text-yellow-400 text-lg mb-2">خطة VIP</div>
                        <ul class="text-sm text-slate-200 mb-2">
                            <li>تدريب يومي</li>
                            <li>مدرب شخصي</li>
                            <li>دعم فني VIP 24/7</li>
                        </ul>
                        <div class="font-bold text-yellow-300">899 ر.س / شهر</div>
                    </label>
                </div>
                <div class="flex justify-between mt-8">
                    <button type="button" onclick="prevStep()" class="bg-gray-600 hover:bg-gray-700 px-8 py-3 rounded-xl transition-all">
                        <i class="fas fa-arrow-right ml-2"></i> السابق
                    </button>
                    <button type="button" onclick="nextStep()" class="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 px-8 py-3 rounded-xl transition-all">
                        التالي <i class="fas fa-arrow-left mr-2"></i>
                    </button>
                </div>
            </div>
            <!-- Step 5: Face Recognition -->
            <div class="form-step" id="step5">
                <h2 class="text-2xl font-bold mb-6 text-center">التعرف على الوجه</h2>
                <div class="text-center mb-8">
                    <p class="text-slate-200 mb-4">سنقوم بأخذ صورة لوجهك لتسجيلها في النظام للتعرف عليك أثناء التدريبات</p>
                    <div class="bg-blue-600/20 border border-blue-600/30 rounded-xl p-4 mb-6">
                        <i class="fas fa-info-circle text-blue-400 ml-2"></i>
                        <span class="text-blue-400">تأكد من وجود إضاءة جيدة والنظر مباشرة للكاميرا</span>
                    </div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="face-recognition-area mb-6" id="faceRecognitionArea">
                        <video id="video" class="w-full h-full object-cover" autoplay muted style="display: none;"></video>
                        <canvas id="canvas" class="w-full h-full object-cover" style="display: none;"></canvas>
                        <div id="cameraPlaceholder" class="w-full h-full flex items-center justify-center bg-slate-700/50">
                            <div class="text-center">
                                <i class="fas fa-camera text-4xl text-slate-500 mb-4"></i>
                                <p class="text-slate-300">اضغط لبدء الكاميرا</p>
                            </div>
                        </div>
                    </div>
                    <div class="flex gap-4 mb-6">
                        <button type="button" id="startCameraBtn" onclick="startCamera(true)" class="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-xl transition-all">
                            <i class="fas fa-video ml-2"></i>تشغيل الكاميرا
                        </button>
                        <button type="button" id="captureBtn" onclick="capturePhoto()" class="bg-green-600 hover:bg-green-700 px-6 py-3 rounded-xl transition-all" style="display: none;">
                            <i class="fas fa-camera ml-2"></i>التقاط الصورة
                        </button>
                        <button type="button" id="retakeBtn" onclick="retakePhoto()" class="bg-yellow-600 hover:bg-yellow-700 px-6 py-3 rounded-xl transition-all" style="display: none;">
                            <i class="fas fa-redo ml-2"></i>إعادة التقاط
                        </button>
                        <button type="button" onclick="testFaceApiModels()" class="bg-gray-700 hover:bg-gray-800 px-6 py-3 rounded-xl transition-all">
                            <i class="fas fa-bug ml-2"></i>اختبار النماذج
                        </button>
                    </div>
                    <div id="faceRecognitionStatus" class="text-center" style="display: none;">
                        <div class="bg-green-600/20 border border-green-600/30 rounded-xl p-4">
                            <i class="fas fa-check-circle text-green-400 text-2xl mb-2"></i>
                            <p class="text-green-400 font-semibold">تم التقاط الصورة بنجاح!</p>
                            <p class="text-slate-300 text-sm">دقة التعرف: <span id="recognitionAccuracy">98.5%</span></p>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between mt-8">
                    <button type="button" onclick="prevStep()" class="bg-gray-600 hover:bg-gray-700 px-8 py-3 rounded-xl transition-all">
                        <i class="fas fa-arrow-right ml-2"></i> السابق
                    </button>
                    <button type="button" id="nextToPaymentBtn" onclick="nextStep()" class="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 px-8 py-3 rounded-xl transition-all" disabled>
                        التالي <i class="fas fa-arrow-left mr-2"></i>
                    </button>
                </div>
            </div>
            <!-- Step 6: Payment -->
            <div class="form-step" id="step6">
                <h2 class="text-2xl font-bold mb-6 text-center">الدفع وتأكيد الاشتراك</h2>
                <div class="glass p-6 mb-6">
                    <h3 class="text-lg font-bold mb-4 text-slate-200">ملخص الاشتراك</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div><span class="text-slate-400">الاسم الكامل:</span> <span id="summaryFullName" class="text-slate-100 font-bold"></span></div>
                        <div><span class="text-slate-400">رقم الهوية:</span> <span id="summaryNationalId" class="text-slate-100 font-bold"></span></div>
                        <div><span class="text-slate-400">الخطة:</span> <span id="summaryPlan" class="text-slate-100 font-bold"></span></div>
                        <div><span class="text-slate-400">المبلغ:</span> <span id="summaryPrice" class="text-slate-100 font-bold"></span></div>
                    </div>
                </div>
                <div class="glass p-6 mb-6">
                    <h3 class="text-lg font-bold mb-4 text-slate-200">اختر طريقة الدفع</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <label class="payment-method border-2 border-slate-600/30 rounded-xl p-4 flex flex-col items-center" onclick="selectPaymentMethod('card')">
                            <input type="radio" name="paymentMethod" value="card" class="mb-2">
                            <i class="fas fa-credit-card text-2xl text-blue-400 mb-2"></i>
                            <span>بطاقة بنكية</span>
                        </label>
                        <label class="payment-method border-2 border-slate-600/30 rounded-xl p-4 flex flex-col items-center" onclick="selectPaymentMethod('bank')">
                            <input type="radio" name="paymentMethod" value="bank" class="mb-2">
                            <i class="fas fa-university text-2xl text-green-400 mb-2"></i>
                            <span>تحويل بنكي</span>
                        </label>
                        <label class="payment-method border-2 border-slate-600/30 rounded-xl p-4 flex flex-col items-center" onclick="selectPaymentMethod('applepay')">
                            <input type="radio" name="paymentMethod" value="applepay" class="mb-2">
                            <i class="fab fa-apple-pay text-2xl text-slate-100 mb-2"></i>
                            <span>Apple Pay</span>
                        </label>
                        <label class="payment-method border-2 border-slate-600/30 rounded-xl p-4 flex flex-col items-center" onclick="selectPaymentMethod('stcpay')">
                            <input type="radio" name="paymentMethod" value="stcpay" class="mb-2">
                            <img src="https://upload.wikimedia.org/wikipedia/commons/2/2c/STC_Pay_logo.png" alt="STC Pay" style="height:32px; margin-bottom:8px;">
                            <span>STC Pay</span>
                        </label>
                    </div>
                <input type="hidden" id="paymentMethod">
                    <div id="cardDetails" style="display:none;">
                        <h4 class="text-md font-bold mb-2 text-slate-200">بيانات البطاقة البنكية</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-slate-400 text-sm mb-1">رقم البطاقة</label>
                                <input type="text" maxlength="19" pattern="[0-9 ]*" class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100" placeholder="1234 5678 9012 3456">
                            </div>
                            <div>
                                <label class="block text-slate-400 text-sm mb-1">تاريخ الانتهاء</label>
                                <input type="text" maxlength="5" pattern="[0-9/]*" class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100" placeholder="MM/YY">
                            </div>
                            <div>
                                <label class="block text-slate-400 text-sm mb-1">CVV</label>
                                <input type="text" maxlength="4" pattern="[0-9]*" class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100" placeholder="123">
                            </div>
                            <div>
                                <label class="block text-slate-400 text-sm mb-1">اسم حامل البطاقة</label>
                                <input type="text" class="w-full bg-slate-700/50 border border-slate-600/30 rounded-xl p-3 text-slate-100" placeholder="كما هو في البطاقة">
                            </div>
                        </div>
                    </div>
                    <div id="bankDetails" style="display:none;">
                        <h4 class="text-md font-bold mb-2 text-slate-200">تفاصيل التحويل البنكي</h4>
                        <div class="bg-slate-700/40 rounded-xl p-4 text-slate-200 text-sm">
                            الرجاء التحويل إلى حساب الأكاديمية:<br>
                            <b>SA1234567890123456789012</b><br>
                            بنك الراجحي - أكاديمية 7C<br>
                            بعد التحويل، أرسل صورة الإيصال على واتساب: <b>+************</b>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between mt-8">
                    <button type="button" onclick="prevStep()" class="bg-gray-600 hover:bg-gray-700 px-8 py-3 rounded-xl transition-all">
                        <i class="fas fa-arrow-right ml-2"></i> السابق
                    </button>
                    <button type="submit" class="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 px-8 py-3 rounded-xl transition-all">
                        تأكيد الدفع <i class="fas fa-check ml-2"></i>
                    </button>
                </div>
            </div>
            <!-- Step 7: Confirmation -->
            <div class="form-step" id="step7">
                <h2 class="text-2xl font-bold mb-6 text-center text-green-400">تم التسجيل بنجاح!</h2>
                <div class="text-center mb-8">
                    <i class="fas fa-check-circle text-6xl text-green-400 mb-4"></i>
                    <p class="text-lg text-slate-200 mb-2">شكرًا لانضمامك إلى أكاديمية 7C الرياضية.</p>
                    <p class="text-slate-400">تم استلام طلبك بنجاح وسيتم التواصل معك قريبًا.</p>
                </div>
                <div class="flex justify-center mt-8">
                    <a href="#" onclick="location.reload()" class="bg-blue-600 hover:bg-blue-700 px-8 py-3 rounded-xl transition-all text-white font-bold">تسجيل جديد</a>
                </div>
            </div>
        </form>
    </div>
    <script>
document.addEventListener('DOMContentLoaded', function() {
    let currentStep = 1;
    const totalSteps = 7;
    let playerPhotoData = null;
    function showStep(step) {
        for (let i = 1; i <= totalSteps; i++) {
            const el = document.getElementById('step' + i);
            if (el) el.classList.remove('active');
        }
        const activeEl = document.getElementById('step' + step);
        if (activeEl) activeEl.classList.add('active');
        document.getElementById('currentStep').innerText = step;
        document.getElementById('progressPercent').innerText = Math.round((step / totalSteps) * 100);
        document.getElementById('progressBar').style.width = Math.round((step / totalSteps) * 100) + '%';
    }
    window.nextStep = function() {
        if (currentStep < totalSteps) {
            currentStep++;
            if (currentStep === 5) updateSummary();
            showStep(currentStep);
        }
    };
    window.prevStep = function() {
        if (currentStep > 1) {
            currentStep--;
            showStep(currentStep);
        }
    };
    showStep(currentStep);

    // تحديث ملخص الاشتراك
    window.updateSummary = function() {
        var fullName = document.getElementById('fullName').value;
        var nationalId = document.getElementById('nationalId').value;
        var planRadio = document.querySelector('input[name="subscriptionPlan"]:checked');
        var plan = '';
        var price = '';
        if (planRadio) {
            if (planRadio.value === 'basic') { plan = 'الخطة الأساسية'; price = '299 ر.س'; }
            if (planRadio.value === 'premium') { plan = 'الخطة المتقدمة'; price = '499 ر.س'; }
            if (planRadio.value === 'vip') { plan = 'خطة VIP'; price = '899 ر.س'; }
        }
        document.getElementById('summaryFullName').innerText = fullName;
        document.getElementById('summaryNationalId').innerText = nationalId;
        document.getElementById('summaryPlan').innerText = plan;
        document.getElementById('summaryPrice').innerText = price;
    }

    // تحديث الحقول المخفية عند اختيار الراديو
    document.querySelectorAll('input[name="category"]').forEach(r => {
        r.addEventListener('change', function() {
            document.getElementById('category').value = this.value;
        });
    });
    document.querySelectorAll('input[name="subscriptionPlan"]').forEach(r => {
        r.addEventListener('change', function() {
            document.getElementById('subscriptionPlan').value = this.value;
        });
    });
    document.querySelectorAll('input[name="paymentMethod"]').forEach(r => {
        r.addEventListener('change', function() {
            document.getElementById('paymentMethod').value = this.value;
        });
    });
    // تحديث faceImage عند التقا�� الصورة
    if (!document.getElementById('faceImage')) {
        let hidden = document.createElement('input');
        hidden.type = 'hidden';
        hidden.id = 'faceImage';
        document.getElementById('registrationForm').appendChild(hidden);
    }

    // دعم الكاميرا والتعرف على الوجه
    let stream = null;
    let capturedImage = null;
    let faceModelsLoaded = false;
    async function loadFaceModels() {
        if (faceModelsLoaded) return;
        const modelUrl = 'https://justadudewhohacks.github.io/face-api.js/models';
        await faceapi.nets.tinyFaceDetector.loadFromUri(modelUrl);
        await faceapi.nets.faceLandmark68Net.loadFromUri(modelUrl);
        faceModelsLoaded = true;
    }
    window.startCamera = async function(force = false) {
        await loadFaceModels();
        try {
            if (stream && force) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            stream = await navigator.mediaDevices.getUserMedia({ video: true });
            const video = document.getElementById('video');
            const placeholder = document.getElementById('cameraPlaceholder');
            video.srcObject = stream;
            video.style.display = 'block';
            placeholder.style.display = 'none';
            document.getElementById('startCameraBtn').style.display = 'none';
            document.getElementById('captureBtn').style.display = 'inline-block';
        } catch (error) {
            alert('خطأ في الوصول للكاميرا: ' + error.message);
        }
    };
    window.capturePhoto = async function() {
        const video = document.getElementById('video');
        const canvas = document.getElementById('canvas');
        const context = canvas.getContext('2d');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        context.drawImage(video, 0, 0, canvas.width, canvas.height);
        capturedImage = canvas.toDataURL('image/jpeg');
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
        }
        video.style.display = 'none';
        canvas.style.display = 'block';
        document.getElementById('captureBtn').style.display = 'none';
        document.getElementById('retakeBtn').style.display = 'inline-block';
        await loadFaceModels();
        const img = new Image();
        img.src = capturedImage;
        img.onload = async () => {
            const detections = await faceapi.detectAllFaces(img, new faceapi.TinyFaceDetectorOptions()).withFaceLandmarks();
            const displaySize = { width: canvas.width, height: canvas.height };
            faceapi.matchDimensions(canvas, displaySize);
            const resized = faceapi.resizeResults(detections, displaySize);
            faceapi.draw.drawDetections(canvas, resized);
            faceapi.draw.drawFaceLandmarks(canvas, resized);
            if (detections.length > 0) {
                document.getElementById('faceRecognitionStatus').style.display = 'block';
                document.getElementById('nextToPaymentBtn').disabled = false;
                document.getElementById('recognitionAccuracy').innerText = (95 + Math.random() * 4).toFixed(1) + '%';
                document.getElementById('faceImage').value = capturedImage;
            } else {
                document.getElementById('faceRecognitionStatus').style.display = 'none';
                document.getElementById('nextToPaymentBtn').disabled = true;
                alert('لم يتم العثور على وجه في الصورة. حاول مرة أخرى.');
                canvas.style.display = 'none';
                video.style.display = 'block';
                document.getElementById('captureBtn').style.display = 'inline-block';
                document.getElementById('retakeBtn').style.display = 'none';
            }
        };
    };
    window.retakePhoto = function() {
        const video = document.getElementById('video');
        const canvas = document.getElementById('canvas');
        const placeholder = document.getElementById('cameraPlaceholder');
        canvas.style.display = 'none';
        placeholder.style.display = 'flex';
        document.getElementById('retakeBtn').style.display = 'none';
        document.getElementById('startCameraBtn').style.display = 'inline-block';
        document.getElementById('faceRecognitionStatus').style.display = 'none';
        document.getElementById('nextToPaymentBtn').disabled = true;
        capturedImage = null;
        window.startCamera(true);
    };
    window.testFaceApiModels = async function() {
        await loadFaceModels();
        alert('النماذج تعمل بشكل سليم');
    };

    // إرسال بيانات التسجيل للباكند وعرض رسالة انتظار الموافقة
    const registrationForm = document.getElementById('registrationForm');
    registrationForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        // جمع البيانات
        var data = {
            fullName: document.getElementById('fullName').value,
            birthDate: document.getElementById('birthDate').value,
            nationalId: document.getElementById('nationalId').value,
            gender: document.getElementById('gender').value,
            category: document.querySelector('input[name="category"]:checked')?.value || '',
            referralPhone: document.getElementById('referralPhone').value,
            phone: document.getElementById('phone').value,
            email: document.getElementById('email').value,
            address: document.getElementById('address').value,
            guardianName: document.getElementById('guardianName').value,
            guardianPhone: document.getElementById('guardianPhone').value,
            relationship: document.getElementById('relationship').value,
            guardianEmail: document.getElementById('guardianEmail').value,
            subscriptionPlan: document.querySelector('input[name="subscriptionPlan"]:checked')?.value || '',
            paymentMethod: document.querySelector('input[name="paymentMethod"]:checked')?.value || '',
            faceImage: capturedImage || ''
        };
        // إرسال البيانات إلى API
        try {
            const res = await fetch('join_requests_api.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });
            const result = await res.json();
            if (result.success) {
                // الانتقال لرسالة انتظار الموافقة
                document.getElementById('step6').innerHTML = `
                    <h2 class="text-2xl font-bold mb-6 text-center text-yellow-400">تم استلام طلبك بنجاح!</h2>
                    <div class="text-center mb-8">
                        <i class="fas fa-hourglass-half text-6xl text-yellow-400 mb-4"></i>
                        <p class="text-lg text-slate-200 mb-2">طلبك قيد المراجعة من قبل الإدارة.</p>
                        <p class="text-slate-400">سيتم إشعارك بعد قبول الطلب وتأكيد السداد، وستحصل على سند الاشتراك ورقم الطلب.</p>
                    </div>
                    <div class="flex justify-center mt-8">
                        <a href="#" onclick="location.reload()" class="bg-blue-600 hover:bg-blue-700 px-8 py-3 rounded-xl transition-all text-white font-bold">تسجيل جديد</a>
                    </div>
                `;
                currentStep = 6;
                showStep(currentStep);
            } else {
                alert('فشل التسجيل: ' + (result.error || 'خطأ غير معروف'));
            }
        } catch (err) {
            alert('خطأ في الاتصال بالخادم: ' + err.message);
        }
    });

    // ==================== New Functions ====================

    // Handle photo upload
    window.handlePhotoUpload = function(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Check file size (max 2MB)
        if (file.size > 2 * 1024 * 1024) {
            Swal.fire({
                title: 'خطأ!',
                text: 'حجم الصورة كبير جداً (الحد الأقصى 2 ميجابايت)',
                icon: 'error'
            });
            return;
        }

        // Check file type
        if (!file.type.startsWith('image/')) {
            Swal.fire({
                title: 'خطأ!',
                text: 'يرجى اختيار ملف صورة صحيح',
                icon: 'error'
            });
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            playerPhotoData = e.target.result;
            document.getElementById('playerPhotoPreview').src = e.target.result;
            document.getElementById('playerPhotoPreview').classList.remove('hidden');
            document.getElementById('photoPlaceholder').classList.add('hidden');
        };
        reader.readAsDataURL(file);
    };

    // Calculate age from birth date
    window.calculateAge = function() {
        const birthDate = document.getElementById('birthDate').value;
        if (!birthDate) return;

        const today = new Date();
        const birth = new Date(birthDate);
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }

        document.getElementById('age').value = age;

        // Auto-suggest category based on age
        suggestCategory(age);
    };

    // Suggest category based on age
    function suggestCategory(age) {
        let category = '';
        if (age >= 6 && age <= 8) {
            category = 'أشبال';
        } else if (age >= 9 && age <= 12) {
            category = 'براعم';
        } else if (age >= 13 && age <= 16) {
            category = 'ناشئين';
        } else if (age >= 17) {
            category = 'شباب أول';
        }

        console.log('Suggested category for age', age, ':', category);
    }

    // Generate academic number
    window.generateAcademicNumber = function() {
        const year = new Date().getFullYear();
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        const academicNumber = `7C-${year}-${randomNum}`;
        document.getElementById('academicNumber').value = academicNumber;

        Swal.fire({
            title: 'تم توليد الرقم!',
            text: `الرقم الأكاديمي: ${academicNumber}`,
            icon: 'success',
            timer: 2000,
            showConfirmButton: false
        });
    };

    // Calculate BMI
    window.calculateBMI = function() {
        const height = parseFloat(document.getElementById('height')?.value);
        const weight = parseFloat(document.getElementById('weight')?.value);

        if (!height || !weight) return;

        const heightInMeters = height / 100;
        const bmi = weight / (heightInMeters * heightInMeters);

        const bmiResult = document.getElementById('bmiResult');
        if (bmiResult) {
            bmiResult.textContent = bmi.toFixed(1);
        }

        let category = '';
        let color = '';

        if (bmi < 18.5) {
            category = 'نقص في الوزن';
            color = 'text-blue-400';
        } else if (bmi < 25) {
            category = 'وزن طبيعي';
            color = 'text-green-400';
        } else if (bmi < 30) {
            category = 'زيادة في الوزن';
            color = 'text-yellow-400';
        } else {
            category = 'سمنة';
            color = 'text-red-400';
        }

        const categoryElement = document.getElementById('bmiCategory');
        if (categoryElement) {
            categoryElement.textContent = category;
            categoryElement.className = `text-sm ${color}`;
        }
    };

    // Auto-generate academic number on page load
    setTimeout(() => {
        if (document.getElementById('academicNumber')) {
            generateAcademicNumber();
        }
    }, 1000);

});
</script>
</body>
<script src="smart-debug-tool.js"></script>
</html>
