<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام طلبات الانضمام المتقدم - أكاديمية 7C</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com">// دالة رفع صورة اللاعب وتحديث بيانات الطلب
        function uploadPlayerImage(file, requestId) {
            const formData = new FormData();
            formData.append('player_image', file);
            fetch('upload_player_image.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث بيانات الطلب في الواجهة
                    const req = requests.find(r => r.id === requestId);
                    if (req) {
                        req.player_image = data.url;
                        renderRequests();
                        showNotification('تم رفع صورة اللاعب وحفظها ب��جاح!', 'success');
                    }
                } else {
                    showNotification('خطأ في رفع الصورة: ' + data.error, 'error');
                }
            })
            .catch(() => {
                showNotification('حدث خطأ أثناء رفع الصورة!', 'error');
            });
        }

        // مثال: ربط رفع الصورة بحقل input (type=file) في النموذج
        // ضع هذا الكود في حدث onchange لحقل الصورة:
        // <input type="file" id="playerImageInput" accept="image/*" onchange="onPlayerImageChange(event, 'REQ001')">
        function onPlayerImageChange(event, requestId) {
            const file = event.target.files[0];
            if (file) {
                uploadPlayerImage(file, requestId);
            }
        }
        </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0f172a;
            min-height: 100vh;
            color: #e2e8f0;
        }

        .glass {
            background: rgba(30, 41, 59, 0.7);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .action-btn {
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .notification {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            padding: 15px 20px;
            border-radius: 10px;
            color: #e2e8f0;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: linear-gradient(45deg, #10b981, #059669);
        }

        .notification.error {
            background: linear-gradient(45deg, #ef4444, #dc2626);
        }

        .notification.warning {
            background: linear-gradient(45deg, #f59e0b, #d97706);
        }

        .notification.info {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: rgba(251, 191, 36, 0.2);
            color: #fbbf24;
            border: 1px solid rgba(251, 191, 36, 0.3);
        }

        .status-reviewing {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }

        .status-approved {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .status-rejected {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .status-payment {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .plan-badge {
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
        }

        .plan-basic {
            background: rgba(107, 114, 128, 0.2);
            color: #9ca3af;
        }

        .plan-premium {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }

        .plan-vip {
            background: rgba(147, 51, 234, 0.2);
            color: #9333ea;
        }

        .loyalty-badge {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
        }

        .progress-step {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .progress-step.completed .step-icon {
            background: #10b981;
            color: #e2e8f0;
        }

        .progress-step.active .step-icon {
            background: #3b82f6;
            color: #e2e8f0;
        }

        .progress-step .step-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-left: 10px;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: #0f172a;
            border-radius: 20px;
            padding: 30px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .whatsapp-preview {
            background: #25d366;
            border-radius: 15px;
            padding: 15px;
            margin: 10px 0;
            color: #e2e8f0;
            font-size: 14px;
            position: relative;
        }

        .whatsapp-preview::before {
            content: '';
            position: absolute;
            top: -8px;
            right: 20px;
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 8px solid #25d366;
        }

        .ai-analysis {
            background: linear-gradient(45deg, #8b5cf6, #7c3aed);
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border: 1px solid rgba(139, 92, 246, 0.3);
        }

        .subscription-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .subscription-card:hover {
            transform: translateY(-5px);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .subscription-card.popular {
            border-color: #3b82f6;
            position: relative;
        }

        .subscription-card.popular::before {
            content: 'الأكثر شعبية';
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: #3b82f6;
            color: #e2e8f0;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .payment-method {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .payment-method:hover {
            border-color: rgba(255, 255, 255, 0.3);
        }

        .payment-method.selected {
            border-color: #3b82f6;
            background: rgba(59, 130, 246, 0.1);
        }

        .receipt-preview {
            background: white;
            color: #333;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .receipt-header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .receipt-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }

        .receipt-total {
            border-top: 2px solid #333;
            padding-top: 10px;
            margin-top: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="min-h-screen p-6">
        <!-- Header -->
        <div class="glass rounded-2xl p-6 mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-slate-100">نظام طلبات الانضمام المتقدم</h1>
                    <p class="text-slate-100/70 mt-2">إدارة شاملة مع الذكاء الاصطناعي ونظام الولاء والدفع المتكامل</p>
                </div>
                <div class="flex gap-3">
                    <button onclick="openAIAnalysis()" class="action-btn bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg">
                        <i class="fas fa-robot ml-1"></i>تحليل AI
                    </button>
                    <button onclick="exportData()" class="action-btn bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg">
                        <i class="fas fa-download ml-1"></i>تصدير
                    </button>
                    <a href="admin-advanced.html" class="action-btn bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-slate-100 no-underline">
                        <i class="fas fa-arrow-left ml-1"></i>العودة
                    </a>
                </div>
            </div>
        </div>

        <!-- إحصائيات متقدمة -->
        <div class="grid grid-cols-1 md:grid-cols-6 gap-4 mb-6">
            <div class="glass rounded-xl p-4 text-center cursor-pointer hover:scale-105 transition-transform" onclick="filterRequests('pending')">
                <i class="fas fa-clock text-yellow-400 text-2xl mb-2"></i>
                <h3 class="text-2xl font-bold text-slate-100">23</h3>
                <p class="text-slate-100/60 text-sm">في الانتظار</p>
                <div class="text-yellow-400/60 text-xs mt-1">+5 اليوم</div>
            </div>
            
            <div class="glass rounded-xl p-4 text-center cursor-pointer hover:scale-105 transition-transform" onclick="filterRequests('reviewing')">
                <i class="fas fa-eye text-blue-400 text-2xl mb-2"></i>
                <h3 class="text-2xl font-bold text-slate-100">8</h3>
                <p class="text-slate-100/60 text-sm">قيد المراجعة</p>
                <div class="text-blue-400/60 text-xs mt-1">AI تحليل</div>
            </div>
            
            <div class="glass rounded-xl p-4 text-center cursor-pointer hover:scale-105 transition-transform" onclick="filterRequests('approved')">
                <i class="fas fa-check text-green-400 text-2xl mb-2"></i>
                <h3 class="text-2xl font-bold text-slate-100">156</h3>
                <p class="text-slate-100/60 text-sm">مقبولة</p>
                <div class="text-green-400/60 text-xs mt-1">95% معدل</div>
            </div>
            
            <div class="glass rounded-xl p-4 text-center cursor-pointer hover:scale-105 transition-transform" onclick="filterRequests('payment_pending')">
                <i class="fas fa-credit-card text-orange-400 text-2xl mb-2"></i>
                <h3 class="text-2xl font-bold text-slate-100">12</h3>
                <p class="text-slate-100/60 text-sm">انتظار دفع</p>
                <div class="text-orange-400/60 text-xs mt-1">تذكير تلقائي</div>
            </div>
            
            <div class="glass rounded-xl p-4 text-center cursor-pointer hover:scale-105 transition-transform" onclick="filterRequests('rejected')">
                <i class="fas fa-times text-red-400 text-2xl mb-2"></i>
                <h3 class="text-2xl font-bold text-slate-100">7</h3>
                <p class="text-slate-100/60 text-sm">مرفوضة</p>
                <div class="text-red-400/60 text-xs mt-1">أسباب واضحة</div>
            </div>
            
            <div class="glass rounded-xl p-4 text-center cursor-pointer hover:scale-105 transition-transform" onclick="showLoyaltyStats()">
                <i class="fas fa-star text-purple-400 text-2xl mb-2"></i>
                <h3 class="text-2xl font-bold text-slate-100">3,250</h3>
                <p class="text-slate-100/60 text-sm">نقاط الولاء</p>
                <div class="text-purple-400/60 text-xs mt-1">65 دعوة نشطة</div>
            </div>
        </div>

        <!-- خطط الاشتراك -->
        <div class="glass rounded-2xl p-6 mb-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-slate-100">خطط الاشتراك المتاحة</h2>
                <button onclick="manageSubscriptionPlans()" class="action-btn bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg">
                    <i class="fas fa-cog ml-1"></i>إدارة الخطط
                </button>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- الخطة الأساسية -->
                <div class="subscription-card">
                    <div class="text-center">
                        <h3 class="text-xl font-bold text-slate-100 mb-3">الخطة الأساسية</h3>
                        <div class="text-4xl font-bold text-gray-400 mb-2">299 ر.س</div>
                        <div class="text-slate-100/60 text-sm mb-6">شهرياً</div>
                        
                        <div class="space-y-3 text-sm text-slate-100/80 mb-6">
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check text-green-400 ml-2"></i>
                                <span>تدريب 3 مرات أسبوعياً</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check text-green-400 ml-2"></i>
                                <span>متابعة أساسية</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check text-green-400 ml-2"></i>
                                <span>تقارير شهرية</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check text-green-400 ml-2"></i>
                                <span>دعم فني أساسي</span>
                            </div>
                        </div>
                        
                        <div class="text-center mb-4">
                            <span class="plan-badge plan-basic">45 مشترك حالياً</span>
                        </div>
                        
                        <button onclick="selectPlan('basic')" class="action-btn bg-gray-600 hover:bg-gray-700 w-full py-3 rounded-lg">
                            اختيار الخطة
                        </button>
                    </div>
                </div>

                <!-- الخطة المتقدمة -->
                <div class="subscription-card popular">
                    <div class="text-center">
                        <h3 class="text-xl font-bold text-slate-100 mb-3">الخطة المتقدمة</h3>
                        <div class="text-4xl font-bold text-blue-400 mb-2">499 ر.س</div>
                        <div class="text-slate-100/60 text-sm mb-6">شهرياً</div>
                        
                        <div class="space-y-3 text-sm text-slate-100/80 mb-6">
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check text-green-400 ml-2"></i>
                                <span>تدريب 5 مرات أسبوعياً</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check text-green-400 ml-2"></i>
                                <span>متابعة شخصية</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check text-green-400 ml-2"></i>
                                <span>تحليل AI للأداء</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check text-green-400 ml-2"></i>
                                <span>تغذية رياضية</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check text-green-400 ml-2"></i>
                                <span>دعم فني متقدم</span>
                            </div>
                        </div>
                        
                        <div class="text-center mb-4">
                            <span class="plan-badge plan-premium">89 مشترك حالياً</span>
                        </div>
                        
                        <button onclick="selectPlan('premium')" class="action-btn bg-blue-600 hover:bg-blue-700 w-full py-3 rounded-lg">
                            اختيار الخطة
                        </button>
                    </div>
                </div>

                <!-- خطة VIP -->
                <div class="subscription-card">
                    <div class="text-center">
                        <h3 class="text-xl font-bold text-slate-100 mb-3">خطة VIP</h3>
                        <div class="text-4xl font-bold text-purple-400 mb-2">899 ر.س</div>
                        <div class="text-slate-100/60 text-sm mb-6">شهرياً</div>
                        
                        <div class="space-y-3 text-sm text-slate-100/80 mb-6">
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check text-green-400 ml-2"></i>
                                <span>تدريب يومي</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check text-green-400 ml-2"></i>
                                <span>مدرب شخصي</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check text-green-400 ml-2"></i>
                                <span>تحليل متقدم</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check text-green-400 ml-2"></i>
                                <span>برنامج تغذية كامل</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check text-green-400 ml-2"></i>
                                <span>أولوية في المباريات</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check text-green-400 ml-2"></i>
                                <span>دعم فني VIP 24/7</span>
                            </div>
                        </div>
                        
                        <div class="text-center mb-4">
                            <span class="plan-badge plan-vip">22 مشترك حالياً</span>
                        </div>
                        
                        <button onclick="selectPlan('vip')" class="action-btn bg-purple-600 hover:bg-purple-700 w-full py-3 rounded-lg">
                            اختيار الخطة
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث المتقدمة -->
        <div class="glass rounded-2xl p-6 mb-6">
            <h2 class="text-xl font-bold text-slate-100 mb-4">فلاتر البحث والتصفية المتقدمة</h2>
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <label class="block text-slate-100/70 text-sm mb-2">البحث الذكي</label>
                    <input type="text" id="smartSearch" placeholder="اسم، رقم الهاتف، البريد..."
                           class="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-slate-100 placeholder-white/50">
                </div>
                <div>
                    <label class="block text-slate-100/70 text-sm mb-2">الحالة</label>
                    <select id="statusFilter" class="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-slate-100">
                        <option value="">جميع الحالات</option>
                        <option value="pending">في الانتظار</option>
                        <option value="reviewing">قيد المراجعة</option>
                        <option value="approved">مقبولة</option>
                        <option value="payment_pending">انتظار الدفع</option>
                        <option value="rejected">مرفوضة</option>
                    </select>
                </div>
                <div>
                    <label class="block text-slate-100/70 text-sm mb-2">خطة الاشتراك</label>
                    <select id="planFilter" class="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-slate-100">
                        <option value="">جميع الخطط</option>
                        <option value="basic">الأساسية</option>
                        <option value="premium">المتقدمة</option>
                        <option value="vip">VIP</option>
                    </select>
                </div>
                <div>
                    <label class="block text-slate-100/70 text-sm mb-2">التاريخ</label>
                    <input type="date" id="dateFilter"
                           class="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-slate-100">
                </div>
                <div>
                    <label class="block text-slate-100/70 text-sm mb-2">نظام الولاء</label>
                    <select id="loyaltyFilter" class="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-slate-100">
                        <option value="">جميع الطلبات</option>
                        <option value="referral">طلبات بدعوة</option>
                        <option value="direct">طلبات مباشرة</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- قائمة الطلبات المتقدمة -->
        <div class="glass rounded-2xl p-6 mb-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold text-slate-100">طلبات الانضمام الحديثة</h2>
                <div class="flex gap-3">
                    <button onclick="selectAllRequests()" class="action-btn bg-gray-600 hover:bg-gray-700 px-3 py-2 rounded-lg text-sm">
                        <i class="fas fa-check-square ml-1"></i>تحديد الكل
                    </button>
                    <button onclick="bulkActions()" class="action-btn bg-orange-600 hover:bg-orange-700 px-3 py-2 rounded-lg text-sm">
                        <i class="fas fa-cogs ml-1"></i>إجراءات جماعية
                    </button>
                    <button onclick="refreshRequests()" class="action-btn bg-blue-600 hover:bg-blue-700 px-3 py-2 rounded-lg text-sm">
                        <i class="fas fa-sync ml-1"></i>تحديث
                    </button>
                </div>
            </div>

            <div id="requestsList" class="space-y-4">
            <!-- سيتم توليد الطلبات ديناميكياً -->
            </div>
            <script>
            // تحميل الطلبات من قاعدة البيانات
            let requests = [];
            function loadRequests() {
            fetch('join_requests_api.php?action=list')
            .then(res => res.json())
            .then(data => {
            if (data.success) {
            // تحويل بيانات القاعدة إلى نفس بنية الواجهة
            requests = data.data.map(row => ({
            id: row.id,
            name: row.full_name,
            phone: row.phone,
            email: row.national_id || '',
            status: row.status,
            plan: row.subscription_type || '',
            loyalty: row.loyalty_points ? `نقاط: ${row.loyalty_points}` : '',
            time: row.created_at ? new Date(row.created_at).toLocaleDateString('ar-SA') : '',
            steps: ['تم التسجيل'],
            player_image: row.player_image || '',
            player_code: row.player_code || ''
            }));
            renderRequests();
            } else {
            showNotification('فشل تحميل الطلبات من القاعدة', 'error');
            }
            })
            .catch(() => showNotification('خطأ في الاتصال بقاعدة البيانات', 'error'));
            }
            // دالة توليد الطلبات في الواجهة
            function renderRequests() {
            const list = document.getElementById('requestsList');
            list.innerHTML = '';
            requests.forEach(req => {
            let statusBadge = {
            'pending': '<span class="status-badge status-pending">في الانتظار</span>',
            'reviewing': '<span class="status-badge status-reviewing">قيد المراجعة</span>',
            'approved': '<span class="status-badge status-approved">مقبولة</span>',
            'payment_pending': '<span class="status-badge status-payment">انتظار الدفع</span>',
            'rejected': '<span class="status-badge status-rejected">مرفوضة</span>',
            }[req.status] || '';
            let planBadge = {
            'basic': '<span class="plan-badge plan-basic">الأساسية</span>',
            'premium': '<span class="plan-badge plan-premium">المتقدمة</span>',
            'vip': '<span class="plan-badge plan-vip">VIP</span>',
            }[req.plan] || '';
            let loyaltyBadge = req.loyalty ? `<span class="loyalty-badge">${req.loyalty}</span>` : '';
            let stepsHtml = req.steps.map((step, i) => {
            let stepClass = i < req.steps.length - 1 ? 'completed' : 'active';
            let icon = i === 0 ? '<i class="fas fa-check text-xs"></i>' : (step === 'قيد المراجعة' ? '<i class="fas fa-eye text-xs"></i>' : (step === 'انتظار الدفع' ? '<i class="fas fa-credit-card text-xs"></i>' : (step === 'تم القبول' ? '<i class="fas fa-check text-xs"></i>' : (i+1))));
            return `<div class="progress-step ${stepClass}"><div class="step-icon">${icon}</div><span class="text-xs text-slate-100/60">${step}</span></div>`;
            }).join('');
            let actions = '';
            if (req.status === 'pending' || req.status === 'reviewing') {
            actions = `
            <button onclick="viewRequestDetails('${req.id}')" class="action-btn bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm"><i class="fas fa-eye ml-1"></i>عرض</button>
            <button onclick="approveRequest('${req.id}')" class="action-btn bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-sm"><i class="fas fa-check ml-1"></i>قبول</button>
            <button onclick="rejectRequest('${req.id}')" class="action-btn bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm"><i class="fas fa-times ml-1"></i>رفض</button>
            `;
            } else if (req.status === 'payment_pending') {
            actions = `
            <button onclick="viewRequestDetails('${req.id}')" class="action-btn bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm"><i class="fas fa-eye ml-1"></i>عرض</button>
            <button onclick="sendPaymentReminder('${req.id}')" class="action-btn bg-orange-600 hover:bg-orange-700 px-3 py-1 rounded text-sm"><i class="fas fa-bell ml-1"></i>تذكير دفع</button>
            <button onclick="generateReceipt('${req.id}')" class="action-btn bg-purple-600 hover:bg-purple-700 px-3 py-1 rounded text-sm"><i class="fas fa-receipt ml-1"></i>إيصال</button>
            `;
            } else {
            actions = `<button onclick="viewRequestDetails('${req.id}')" class="action-btn bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm"><i class="fas fa-eye ml-1"></i>عرض</button>`;
            }
            list.innerHTML += `
            <div class="bg-white/5 rounded-xl p-4 border border-white/10 hover:border-white/20 transition-all">
            <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
            <input type="checkbox" class="request-checkbox">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
            <span class="text-slate-100 font-bold">${req.name[0]}</span>
            </div>
            <div>
            <h5 class="text-slate-100 font-bold">${req.name}</h5>
            <p class="text-slate-100/60 text-sm">${req.phone} • ${req.email}</p>
            <div class="flex items-center gap-2 mt-2">
            ${statusBadge}
            ${planBadge}
            ${loyaltyBadge}
            <span class="text-slate-100/50 text-xs">${req.time}</span>
            </div>
            <div class="mt-2">${stepsHtml}</div>
            </div>
            </div>
            <div class="flex items-center gap-2">${actions}</div>
            </div>
            </div>
            `;
            });
            }
            // إعادة تحميل الطلبات عند تحميل الصفحة
            document.addEventListener('DOMContentLoaded', loadRequests);
            </script>

            <!-- Pagination -->
            <div class="flex justify-between items-center mt-6 pt-4 border-t border-white/10">
                <div class="text-slate-100/60 text-sm">
                    عرض 1-10 من 199 طلب انضمام
                </div>
                <div class="flex gap-2">
                    <button class="px-3 py-1 bg-white/10 text-slate-100 rounded hover:bg-white/20 transition-colors">السابق</button>
                    <button class="px-3 py-1 bg-blue-600 text-slate-100 rounded">1</button>
                    <button class="px-3 py-1 bg-white/10 text-slate-100 rounded hover:bg-white/20 transition-colors">2</button>
                    <button class="px-3 py-1 bg-white/10 text-slate-100 rounded hover:bg-white/20 transition-colors">3</button>
                    <button class="px-3 py-1 bg-white/10 text-slate-100 rounded hover:bg-white/20 transition-colors">التالي</button>
                </div>
            </div>
        </div>

        <!-- نظام الولاء والدعوات -->
        <div class="glass rounded-2xl p-6 mb-6">
            <h2 class="text-xl font-bold text-slate-100 mb-4">نظام الولاء والدعوات</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white/5 rounded-xl p-4">
                    <h4 class="text-lg font-bold text-purple-400 mb-3">إحصائيات الدعوات</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-slate-100/70">دعوات نشطة:</span>
                            <span class="text-slate-100 font-bold">65</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-slate-100/70">دعوات مكتملة:</span>
                            <span class="text-green-400 font-bold">42</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-slate-100/70">نقاط موزعة:</span>
                            <span class="text-purple-400 font-bold">2,100</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white/5 rounded-xl p-4">
                    <h4 class="text-lg font-bold text-orange-400 mb-3">أفضل الداعين</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-slate-100/70">محمد علي:</span>
                            <span class="text-orange-400 font-bold">8 دعوات</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-slate-100/70">أحمد سالم:</span>
                            <span class="text-orange-400 font-bold">6 دعوات</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-slate-100/70">فاطمة أحمد:</span>
                            <span class="text-orange-400 font-bold">5 دعوات</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white/5 rounded-xl p-4">
                    <h4 class="text-lg font-bold text-blue-400 mb-3">إعدادات النقاط</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-slate-100/70">نقاط الدعوة:</span>
                            <span class="text-blue-400 font-bold">50 نقطة</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-slate-100/70">نقاط الإكمال:</span>
                            <span class="text-blue-400 font-bold">100 نقطة</span>
                        </div>
                        <button onclick="manageLoyaltySettings()" class="action-btn bg-blue-600 hover:bg-blue-700 w-full py-2 rounded-lg mt-3">
                            إدارة النقاط
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal لتفاصيل الطلب -->
    <div id="requestModal" class="modal">
        <div class="modal-content">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-slate-100">تفاصيل طلب الانضمام</h3>
                <button onclick="closeModal('requestModal')" class="text-slate-100/70 hover:text-slate-100">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <div id="requestDetails">
                <!-- سيتم ملء التفاصيل هنا -->
            </div>
        </div>
    </div>

    <!-- Modal للذكاء الاصطناعي -->
    <div id="aiModal" class="modal">
        <div class="modal-content">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-slate-100">تحليل الذكاء الاصطناعي</h3>
                <button onclick="closeModal('aiModal')" class="text-slate-100/70 hover:text-slate-100">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <div class="ai-analysis">
                <h4 class="text-lg font-bold text-slate-100 mb-3">
                    <i class="fas fa-robot ml-2"></i>تحليل شامل للطلبات
                </h4>
                <div class="space-y-3 text-slate-100/80">
                    <p>• معدل القبول الحالي: 87% (ممتاز)</p>
                    <p>• أفضل وقت للتسجيل: المساء (6-9 مساءً)</p>
                    <p>• الخطة الأكثر طلباً: المتقدمة (45%)</p>
                    <p>• معدل إكمال الدفع: 92%</p>
                    <p>• توقع 15 طلب جديد هذا الأسبوع</p>
                </div>

                <div class="mt-4 p-3 bg-white/10 rounded-lg">
                    <h5 class="font-bold text-slate-100 mb-2">توصيات AI:</h5>
                    <ul class="text-slate-100/70 text-sm space-y-1">
                        <li>• تحسين عملية الدفع لتقليل وقت الانتظار</li>
                        <li>• إضافة خصومات للدعوات المتعددة</li>
                        <li>• تفعيل التذكيرات التلقائية للدفع</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // دالة عرض الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: #e2e8f0; cursor: pointer; margin-right: 10px;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }, 5000);
        }

        // دوال الفلترة
        function filterRequests(status) {
            showNotification(`عرض الطلبات: ${getStatusText(status)}`, 'info');
        }

        function getStatusText(status) {
            const statusTexts = {
                'pending': 'في الانتظار',
                'reviewing': 'قيد المراجعة',
                'approved': 'مقبولة',
                'payment_pending': 'انتظار الدفع',
                'rejected': 'مرفوضة'
            };
            return statusTexts[status] || 'غير محدد';
        }

        // دوال الخطط
        function selectPlan(planType) {
            const planNames = {
                'basic': 'الأساسية',
                'premium': 'المتقدمة',
                'vip': 'VIP'
            };
            showNotification(`تم اختيار الخطة ${planNames[planType]}`, 'success');
        }

        function manageSubscriptionPlans() {
            showNotification('فتح إدارة خطط الاشتراك المتقدمة', 'info');
        }

        // دوال إدارة الطلبات المتقدمة
        function viewRequestDetails(requestId) {
            showNotification(`عرض تفاصيل الطلب ${requestId}`, 'info');

            // محاكاة تحميل البيانات
            const requestDetails = `
                <div class="space-y-4">
                    <div class="bg-white/5 rounded-xl p-4">
                        <h4 class="text-lg font-bold text-slate-100 mb-3">المعلومات الشخصية</h4>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div><span class="text-slate-100/70">الاسم:</span> <span class="text-slate-100">أحمد محمد العلي</span></div>
                            <div><span class="text-slate-100/70">العمر:</span> <span class="text-slate-100">16 سنة</span></div>
                            <div><span class="text-slate-100/70">الهاتف:</span> <span class="text-slate-100">+966501234567</span></div>
                            <div><span class="text-slate-100/70">البريد:</span> <span class="text-slate-100"><EMAIL></span></div>
                            <div><span class="text-slate-100/70">المدينة:</span> <span class="text-slate-100">الرياض</span></div>
                            <div><span class="text-slate-100/70">الخطة:</span> <span class="text-blue-400">المتقدمة</span></div>
                        </div>
                    </div>

                    <div class="bg-white/5 rounded-xl p-4">
                        <h4 class="text-lg font-bold text-slate-100 mb-3">معلومات الدعوة</h4>
                        <div class="text-sm">
                            <div><span class="text-slate-100/70">الداعي:</span> <span class="text-orange-400">محمد علي السالم</span></div>
                            <div><span class="text-slate-100/70">هاتف الداعي:</span> <span class="text-slate-100">+966509876543</span></div>
                            <div><span class="text-slate-100/70">نقاط الولاء:</span> <span class="text-purple-400">50 نقطة عند الإكمال</span></div>
                        </div>
                    </div>

                    <div class="bg-white/5 rounded-xl p-4">
                        <h4 class="text-lg font-bold text-slate-100 mb-3">مراحل التقدم</h4>
                        <div class="space-y-2">
                            <div class="progress-step completed">
                                <div class="step-icon"><i class="fas fa-check text-xs"></i></div>
                                <span class="text-sm text-slate-100">تم التسجيل - ${new Date().toLocaleDateString('ar-SA')}</span>
                            </div>
                            <div class="progress-step active">
                                <div class="step-icon">2</div>
                                <span class="text-sm text-slate-100">قيد المراجعة - جاري الآن</span>
                            </div>
                            <div class="progress-step">
                                <div class="step-icon">3</div>
                                <span class="text-sm text-slate-100/60">الموافقة</span>
                            </div>
                            <div class="progress-step">
                                <div class="step-icon">4</div>
                                <span class="text-sm text-slate-100/60">الدفع</span>
                            </div>
                            <div class="progress-step">
                                <div class="step-icon">5</div>
                                <span class="text-sm text-slate-100/60">بدء الاشتراك</span>
                            </div>
                        </div>
                    </div>

                    <div class="flex gap-3 mt-6">
                        <button onclick="approveRequest('${requestId}')" class="action-btn bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg flex-1">
                            <i class="fas fa-check ml-2"></i>موافقة
                        </button>
                        <button onclick="rejectRequest('${requestId}')" class="action-btn bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg flex-1">
                            <i class="fas fa-times ml-2"></i>رفض
                        </button>
                        <button onclick="sendWhatsAppMessage('${requestId}')" class="action-btn bg-green-500 hover:bg-green-600 px-4 py-2 rounded-lg flex-1">
                            <i class="fab fa-whatsapp ml-2"></i>واتساب
                        </button>
                    </div>
                </div>
            `;

            document.getElementById('requestDetails').innerHTML = requestDetails;
            document.getElementById('requestModal').classList.add('show');
        }

        function approveRequest(requestId) {
            showNotification(`جاري الموافقة على الطلب ${requestId}...`, 'info');

            setTimeout(() => {
                showNotification(`تم قبول الطلب ${requestId} بنجاح!`, 'success');

                // إرسال رسالة واتساب تلقائية
                setTimeout(() => {
                    sendWhatsAppApproval(requestId);
                }, 1000);

                // منح نقاط الولاء للداعي
                setTimeout(() => {
                    grantLoyaltyPoints(requestId);
                }, 2000);

                closeModal('requestModal');
            }, 2000);
        }

        function rejectRequest(requestId) {
            const reason = prompt('سبب الرفض (اختياري):');
            showNotification(`جاري رفض الطلب ${requestId}...`, 'warning');

            setTimeout(() => {
                showNotification(`تم رفض الطلب ${requestId}`, 'error');

                // إرسال رسالة واتساب للرفض
                setTimeout(() => {
                    sendWhatsAppRejection(requestId, reason);
                }, 1000);

                closeModal('requestModal');
            }, 1500);
        }

        // دوال واتساب المتقدمة
        function sendWhatsAppApproval(requestId) {
            const message = `
🎉 مبروك! تم قبول طلب انضمامك لأكاديمية 7C الرياضية

📋 رقم الطلب: ${requestId}
✅ الحالة: مقبول
💳 الخطوة التالية: إكمال عملية الدفع

🔗 رابط الدفع: https://7c-academy.com/payment/${requestId}

📞 للاستفسار: +966-XX-XXX-XXXX
🌐 الموقع: www.7c-academy.com

مرحباً بك في عائلة أكاديمية 7C! ⚽
            `;

            showNotification('📱 تم إرسال رسالة القبول عبر واتساب', 'success');

            // محاكاة إرسال الرسالة
            console.log('WhatsApp Approval Message:', message);
        }

        function sendWhatsAppRejection(requestId, reason) {
            const message = `
😔 نأسف لإبلاغك بأنه لم يتم قبول طلب الانضمام

📋 رقم الطلب: ${requestId}
❌ الحالة: مرفوض
${reason ? `📝 السبب: ${reason}` : ''}

🔄 يمكنك التقديم مرة أخرى بعد تحسين المتطلبات

📞 للاستفسار: +966-XX-XXX-XXXX
🌐 الموقع: www.7c-academy.com

شكراً لاهتمامك بأكاديمية 7C ⚽
            `;

            showNotification('📱 تم إرسال رسالة الرفض عبر واتساب', 'info');

            // محاكاة إرسال الرسالة
            console.log('WhatsApp Rejection Message:', message);
        }

        function sendPaymentReminder(requestId) {
            const message = `
⏰ تذكير: إكمال عملية الدفع

📋 رقم الطلب: ${requestId}
💰 المبلغ المطلوب: 499 ر.س
⏳ متبقي على انتهاء الحجز: 48 ساعة

🔗 رابط الدفع: https://7c-academy.com/payment/${requestId}

💳 طرق الدفع المتاحة:
• بطاقة ائتمانية
• تحويل بنكي
• STC Pay
• Apple Pay

📞 للمساعدة: +966-XX-XXX-XXXX

أكاديمية 7C الرياضية ⚽
            `;

            showNotification('📱 تم إرسال تذكير الدفع عبر واتساب', 'warning');
            console.log('WhatsApp Payment Reminder:', message);
        }

        // دوال نظام الولاء المتقدمة
        function grantLoyaltyPoints(requestId) {
            showNotification('🌟 جاري منح نقاط الولاء للداعي...', 'info');

            setTimeout(() => {
                showNotification('✨ تم منح 50 نقطة ولاء للداعي محمد علي!', 'success');

                // إرسال إشعار للداعي
                const loyaltyMessage = `
🎉 تهانينا! حصلت على نقاط ولاء

⭐ النقاط المكتسبة: 50 نقطة
👤 المدعو: أحمد محمد العلي
📋 رقم الطلب: ${requestId}

💰 رصيدك الحالي: 350 نقطة
🎁 اقترب من الجائزة التالية!

شكراً لك على دعوة الأصدقاء لأكاديمية 7C ⚽
                `;

                console.log('Loyalty Points Message:', loyaltyMessage);
                showNotification('📱 تم إشعار الداعي بالنقاط الجديدة', 'info');
            }, 1500);
        }

        function manageLoyaltySettings() {
            showNotification('فتح إعدادات نظام الولاء المتقدمة', 'info');

            const settings = `
                <div class="space-y-4">
                    <h4 class="text-lg font-bold text-slate-100">إعدادات نقاط الولاء</h4>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-slate-100/70 text-sm mb-2">نقاط الدعوة</label>
                            <input type="number" value="50" class="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-slate-100">
                        </div>
                        <div>
                            <label class="block text-slate-100/70 text-sm mb-2">نقاط الإكمال</label>
                            <input type="number" value="100" class="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-slate-100">
                        </div>
                    </div>

                    <div>
                        <label class="block text-slate-100/70 text-sm mb-2">الحد الأدنى للاستبدال</label>
                        <input type="number" value="500" class="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-slate-100">
                    </div>

                    <button onclick="saveLoyaltySettings()" class="action-btn bg-purple-600 hover:bg-purple-700 w-full py-2 rounded-lg">
                        حفظ الإعدادات
                    </button>
                </div>
            `;

            // يمكن عرض هذا في modal منفصل
            console.log('Loyalty Settings:', settings);
        }

        // دوال الذكاء الاصطناعي المتقدمة
        function openAIAnalysis() {
            showNotification('🤖 بدء تحليل الذكاء الاصطناعي المتقدم...', 'info');

            setTimeout(() => {
                document.getElementById('aiModal').classList.add('show');
                showNotification('تم تحليل 199 طلب - معدل القبول 87%', 'success');
            }, 2000);
        }

        // دوال إنشاء الإيصالات
        function generateReceipt(requestId) {
            showNotification('جاري إنشاء إيصال الاشتراك...', 'info');

            setTimeout(() => {
                const receiptContent = `
                    <div class="receipt-preview">
                        <div class="receipt-header">
                            <h3 style="margin: 0; color: #333;">أكاديمية 7C الرياضية</h3>
                            <p style="margin: 5px 0; color: #666;">إيصال اشتراك رقم: ${requestId}</p>
                            <p style="margin: 0; color: #666;">${new Date().toLocaleDateString('ar-SA')}</p>
                        </div>

                        <div style="margin: 15px 0;">
                            <div class="receipt-item">
                                <span>اسم المشترك:</span>
                                <span>أحمد محمد العلي</span>
                            </div>
                            <div class="receipt-item">
                                <span>رقم الهاتف:</span>
                                <span>+966501234567</span>
                            </div>
                            <div class="receipt-item">
                                <span>نوع الاشتراك:</span>
                                <span>الخطة المتقدمة</span>
                            </div>
                            <div class="receipt-item">
                                <span>مدة الاشتراك:</span>
                                <span>شهر واحد</span>
                            </div>
                            <div class="receipt-item">
                                <span>تاريخ البداية:</span>
                                <span>${new Date().toLocaleDateString('ar-SA')}</span>
                            </div>
                            <div class="receipt-item">
                                <span>تاريخ الانتهاء:</span>
                                <span>${new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString('ar-SA')}</span>
                            </div>
                        </div>

                        <div class="receipt-total">
                            <div class="receipt-item">
                                <span>المبلغ الأساسي:</span>
                                <span>499.00 ر.س</span>
                            </div>
                            <div class="receipt-item">
                                <span>ضريبة القيمة المضافة (15%):</span>
                                <span>74.85 ر.س</span>
                            </div>
                            <div class="receipt-item" style="font-size: 18px;">
                                <span>المجموع الكلي:</span>
                                <span>573.85 ر.س</span>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 20px; padding-top: 15px; border-top: 1px solid #ccc;">
                            <p style="margin: 5px 0; color: #666; font-size: 12px;">شكراً لانضمامك لأكاديمية 7C</p>
                            <p style="margin: 5px 0; color: #666; font-size: 12px;">للاستفسار: +966-XX-XXX-XXXX</p>
                            <p style="margin: 5px 0; color: #666; font-size: 12px;">www.7c-academy.com</p>
                        </div>
                    </div>

                    <div class="flex gap-3 mt-4">
                        <button onclick="printReceipt()" class="action-btn bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg flex-1">
                            <i class="fas fa-print ml-2"></i>طباعة
                        </button>
                        <button onclick="downloadReceipt('${requestId}')" class="action-btn bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg flex-1">
                            <i class="fas fa-download ml-2"></i>تحميل PDF
                        </button>
                        <button onclick="emailReceipt('${requestId}')" class="action-btn bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg flex-1">
                            <i class="fas fa-envelope ml-2"></i>إرسال بالبريد
                        </button>
                    </div>
                `;

                document.getElementById('requestDetails').innerHTML = receiptContent;
                document.getElementById('requestModal').classList.add('show');
                showNotification('تم إنشاء الإيصال بنجاح!', 'success');
            }, 1500);
        }

        function printReceipt() {
            window.print();
            showNotification('جاري طباعة الإيصال...', 'info');
        }

        function downloadReceipt(requestId) {
            showNotification(`جاري تحميل إيصال ${requestId} كملف PDF...`, 'info');
            setTimeout(() => {
                showNotification('تم تحميل الإيصال بنجاح!', 'success');
            }, 2000);
        }

        function emailReceipt(requestId) {
            showNotification(`جاري إرسال الإيصال ${requestId} بالبريد الإلكتروني...`, 'info');
            setTimeout(() => {
                showNotification('تم إرسال الإيصال بالبريد الإلكتروني!', 'success');
            }, 2000);
        }

        // دوال إضافية
        function selectAllRequests() {
            const checkboxes = document.querySelectorAll('.request-checkbox');
            const selectAll = document.querySelector('#selectAllCheckbox') || { checked: false };
            selectAll.checked = !selectAll.checked;

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            const count = selectAll.checked ? checkboxes.length : 0;
            showNotification(`تم ${selectAll.checked ? 'اختيار' : 'إلغاء اختيار'} ${count} طلب`, 'info');
        }

        function bulkActions() {
            const selectedCount = document.querySelectorAll('.request-checkbox:checked').length;
            if (selectedCount === 0) {
                showNotification('يرجى اختيار طلب واحد على الأقل', 'warning');
                return;
            }

            showNotification(`إجراءات جماعية على ${selectedCount} طلب`, 'info');
        }

        function refreshRequests() {
            showNotification('جاري تحديث قائمة الطلبات...', 'info');
            setTimeout(() => {
                showNotification('تم تحديث القائمة - 3 طلبات جديدة!', 'success');
            }, 1500);
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }

        // دوال نظام الولاء
        function showLoyaltyStats() {
            showNotification('📊 إحصائيات نظام الولاء: 65 دعوة نشطة، 3,250 نقطة موزعة', 'info');
        }

        // دوال التصدير
        function exportData() {
            showNotification('جاري تصدير بيانات الطلبات...', 'info');
            setTimeout(() => {
                showNotification('تم تصدير 199 طلب انضمام بنجاح!', 'success');
            }, 2000);
        }

        // إغلاق المودال عند النقر خارجه
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.classList.remove('show');
                }
            });
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            showNotification('🚀 نظام طلبات الانضمام المتقدم جاهز!', 'success');

            // تفعيل البحث المباشر
            document.getElementById('smartSearch').addEventListener('input', function() {
                const searchTerm = this.value;
                if (searchTerm.length > 2) {
                    showNotification(`البحث عن: ${searchTerm}`, 'info');
                }
            });
        });
    </script>
<!-- FaceAPI.js: التعرف على الوجه محلياً بدون رفع الصور لأي سيرفر خارجي -->
<script defer src="https://cdn.jsdelivr.net/npm/face-api.js"></script>
<div class="glass rounded-2xl p-6 mb-6" style="margin: 30px auto; max-width: 500px;">
    <h2 class="text-xl font-bold text-slate-100 mb-4">تحقق من الوجه (FaceAPI.js)</h2>
    <input type="file" id="faceImageInput" accept="image/*" class="mb-3 w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-slate-100">
    <button onclick="startCamera()" class="action-btn bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg mb-3 w-full">تشغيل الكاميرا</button>
    <div style="display: flex; gap: 10px; align-items: flex-start;">
        <video id="faceVideo" width="220" height="180" autoplay muted style="display:none; border-radius: 10px;"></video>
        <canvas id="faceCanvas" width="220" height="180" style="display:none; border-radius: 10px;"></canvas>
        <img id="faceImage" style="max-width:220px; max-height:180px; display:none; border-radius: 10px;"/>
    </div>
    <div id="faceResult" class="mt-4 text-slate-100/80 text-sm"></div>
</div>
<script>
// تحميل نماذج FaceAPI.js عند أول استخدام
let faceModelsLoaded = false;
async function loadFaceModels() {
    if (faceModelsLoaded) return;
    await faceapi.nets.tinyFaceDetector.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js/models');
    await faceapi.nets.faceLandmark68Net.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js/models');
    await faceapi.nets.faceRecognitionNet.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js/models');
    faceModelsLoaded = true;
}

// عند رفع صورة
const faceImageInput = document.getElementById('faceImageInput');
faceImageInput.addEventListener('change', async function() {
    const file = this.files[0];
    if (!file) return;
    await loadFaceModels();
    const img = document.getElementById('faceImage');
    img.src = URL.createObjectURL(file);
    img.onload = async () => {
        document.getElementById('faceVideo').style.display = 'none';
        img.style.display = 'block';
        document.getElementById('faceCanvas').style.display = 'block';
        const canvas = document.getElementById('faceCanvas');
        faceapi.matchDimensions(canvas, img);
        const detections = await faceapi.detectAllFaces(img, new faceapi.TinyFaceDetectorOptions()).withFaceLandmarks();
        faceapi.draw.drawDetections(canvas, faceapi.resizeResults(detections, { width: img.width, height: img.height }));
        faceapi.draw.drawFaceLandmarks(canvas, faceapi.resizeResults(detections, { width: img.width, height: img.height }));
        document.getElementById('faceResult').innerText = detections.length ? `تم التعرف على ${detections.length} وجه/وجوه في الصورة.` : 'لم يتم العثور على وجه.';
    };
});

// عند استخدام الكاميرا
let stream = null;
async function startCamera() {
    await loadFaceModels();
    const video = document.getElementById('faceVideo');
    const canvas = document.getElementById('faceCanvas');
    const img = document.getElementById('faceImage');
    img.style.display = 'none';
    canvas.style.display = 'block';
    video.style.display = 'block';
    document.getElementById('faceResult').innerText = 'جاري تشغيل الكاميرا...';
    if (!stream) {
        stream = await navigator.mediaDevices.getUserMedia({ video: true });
        video.srcObject = stream;
    }
    video.onloadedmetadata = () => {
        video.play();
        detectFaceFromVideo();
    };
}
async function detectFaceFromVideo() {
    const video = document.getElementById('faceVideo');
    const canvas = document.getElementById('faceCanvas');
    faceapi.matchDimensions(canvas, video);
    const displaySize = { width: video.width, height: video.height };
    setInterval(async () => {
        const detections = await faceapi.detectAllFaces(video, new faceapi.TinyFaceDetectorOptions()).withFaceLandmarks();
        const resized = faceapi.resizeResults(detections, displaySize);
        canvas.getContext('2d').clearRect(0, 0, canvas.width, canvas.height);
        faceapi.draw.drawDetections(canvas, resized);
        faceapi.draw.drawFaceLandmarks(canvas, resized);
        document.getElementById('faceResult').innerText = detections.length ? `تم التعرف على ${detections.length} وجه/وجوه بالكاميرا.` : 'لم يتم العثور على وجه.';
    }, 1000);
}
</script>
</body>
</html>
