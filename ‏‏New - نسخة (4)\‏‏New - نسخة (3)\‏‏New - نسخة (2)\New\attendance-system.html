<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الحضور والغياب المتقدم - أكاديمية 7C</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: linear-gradient(135deg, #8B4513, #D2691E);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(139, 69, 19, 0.3);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid rgba(139, 69, 19, 0.3);
        }

        .card-header i {
            font-size: 1.5rem;
            margin-left: 1rem;
            color: #D2691E;
        }

        .card-header h3 {
            font-size: 1.3rem;
            font-weight: bold;
        }

        .btn {
            background: linear-gradient(135deg, #8B4513, #D2691E);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(139, 69, 19, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #4B5563, #6B7280);
        }

        .btn-success {
            background: linear-gradient(135deg, #10B981, #34D399);
        }

        .btn-warning {
            background: linear-gradient(135deg, #F59E0B, #FBBF24);
        }

        .btn-danger {
            background: linear-gradient(135deg, #EF4444, #F87171);
        }

        .grid-3 {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
        }

        .grid-4 {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .qr-container {
            background: white;
            padding: 1rem;
            border-radius: 10px;
            display: inline-block;
            margin: 1rem 0;
        }

        .attendance-methods {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .method-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .method-card:hover {
            border-color: #8B4513;
            background: rgba(139, 69, 19, 0.1);
        }

        .method-card.active {
            border-color: #D2691E;
            background: rgba(210, 105, 30, 0.2);
        }

        .method-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #D2691E;
        }

        .player-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .player-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            margin-bottom: 0.5rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .player-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .player-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .player-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-present {
            background: #10B981;
            color: white;
        }

        .status-absent {
            background: #EF4444;
            color: white;
        }

        .status-late {
            background: #F59E0B;
            color: white;
        }

        .status-pending {
            background: #6B7280;
            color: white;
        }

        /* AI Interface Styles */
        .ai-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid rgba(139, 69, 19, 0.3);
        }

        .ai-tab {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px 8px 0 0;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .ai-tab:hover {
            background: rgba(139, 69, 19, 0.3);
        }

        .ai-tab.active {
            background: linear-gradient(135deg, #8B4513, #D2691E);
            box-shadow: 0 4px 8px rgba(139, 69, 19, 0.3);
        }

        .ai-tab-content {
            animation: fadeIn 0.5s ease-in;
        }

        .prediction-card, .pattern-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 1.5rem;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .prediction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            margin-bottom: 0.5rem;
            border-left: 4px solid;
        }

        .prediction-high { border-left-color: #10B981; }
        .prediction-medium { border-left-color: #F59E0B; }
        .prediction-low { border-left-color: #EF4444; }

        .risk-player-card {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }

        .risk-player-card h5 {
            color: #EF4444;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .behavior-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .behavior-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .behavior-type {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .type-excellent { background: #10B981; color: white; }
        .type-good { background: #3B82F6; color: white; }
        .type-average { background: #F59E0B; color: white; }
        .type-needs-attention { background: #EF4444; color: white; }

        .recommendations-container {
            display: grid;
            gap: 1rem;
        }

        .recommendation-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 4px solid;
        }

        .recommendation-high { border-left-color: #EF4444; }
        .recommendation-medium { border-left-color: #F59E0B; }
        .recommendation-low { border-left-color: #10B981; }

        .recommendation-actions {
            list-style: none;
            padding: 0;
            margin: 1rem 0;
        }

        .recommendation-actions li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .recommendation-actions li:before {
            content: "✓";
            color: #10B981;
            font-weight: bold;
            margin-left: 0.5rem;
        }

        .notification-stats .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .stat-box {
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .notifications-list {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 1rem;
        }

        .notification-item {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 1rem 0;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .priority-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .priority-high { background: #EF4444; color: white; }
        .priority-medium { background: #F59E0B; color: white; }
        .priority-normal { background: #3B82F6; color: white; }
        .priority-low { background: #6B7280; color: white; }

        .settings-form label {
            color: white;
            font-size: 0.9rem;
        }

        .settings-form input[type="checkbox"] {
            accent-color: #8B4513;
        }

        .settings-form input[type="number"] {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
        }

        /* Chart Containers */
        .chart-container {
            position: relative;
            height: 200px;
            margin: 1rem 0;
        }

        /* Loading Animation */
        .ai-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            color: #8B4513;
        }

        .ai-loading::after {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #8B4513;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }

            .attendance-methods {
                grid-template-columns: 1fr;
            }

            .grid-3, .grid-4 {
                grid-template-columns: repeat(2, 1fr);
            }

            .ai-tabs {
                flex-wrap: wrap;
            }

            .ai-tab {
                flex: 1;
                min-width: 120px;
            }

            .behavior-grid {
                grid-template-columns: 1fr;
            }

            .prediction-card, .pattern-card {
                padding: 1rem;
            }
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            border-radius: 15px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            border: 1px solid rgba(139, 69, 19, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid rgba(139, 69, 19, 0.3);
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header fade-in">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <button class="btn btn-secondary" onclick="goBackToProfile()" style="background: rgba(255,255,255,0.1);">
                    <i class="fas fa-arrow-right"></i>
                    العودة لملف اللاعب
                </button>
                <div style="text-align: center; flex: 1;">
                    <h1><i class="fas fa-calendar-check"></i> نظام الحضور والغياب المتقدم</h1>
                </div>
                <div style="width: 150px;"></div> <!-- Spacer for centering -->
            </div>
            <p>أكاديمية 7C للتدريب الرياضي - إدارة ذكية للحضور مع تقنيات متطورة</p>
        </div>

        <!-- Main Grid -->
        <div class="main-grid">
            <!-- Attendance Registration -->
            <div class="card fade-in">
                <div class="card-header">
                    <i class="fas fa-user-check"></i>
                    <h3>تسجيل الحضور</h3>
                </div>

                <!-- Attendance Methods -->
                <div class="attendance-methods">
                    <div class="method-card" onclick="selectMethod('face')" id="faceMethod">
                        <div class="method-icon">
                            <i class="fas fa-face-smile"></i>
                        </div>
                        <h4>بصمة الوجه</h4>
                        <p>تسجيل سريع وآمن</p>
                    </div>
                    
                    <div class="method-card" onclick="selectMethod('eye')" id="eyeMethod">
                        <div class="method-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <h4>بصمة العين</h4>
                        <p>دقة عالية</p>
                    </div>
                    
                    <div class="method-card active" onclick="selectMethod('qr')" id="qrMethod">
                        <div class="method-icon">
                            <i class="fas fa-qrcode"></i>
                        </div>
                        <h4>مسح الباركود</h4>
                        <p>سهل ومتاح</p>
                    </div>
                </div>

                <!-- QR Code Section -->
                <div id="qrSection" class="text-center">
                    <h4 style="margin-bottom: 1rem;">باركود الجلسة الحالية</h4>
                    <div class="qr-container" id="sessionQRContainer">
                        <div id="sessionQR"></div>
                    </div>
                    <p style="font-size: 0.9rem; opacity: 0.8;">صالح لمدة 30 دقيقة</p>
                    <button class="btn btn-secondary" onclick="generateNewSessionQR()">
                        <i class="fas fa-sync"></i>
                        إنشاء باركود جديد
                    </button>
                </div>

                <!-- Manual Attendance -->
                <div style="margin-top: 2rem;">
                    <h4 style="margin-bottom: 1rem;">تسجيل يدوي</h4>
                    <div style="display: flex; gap: 1rem; align-items: center;">
                        <input type="text" id="playerSearch" placeholder="ابحث عن اللاعب..." 
                               style="flex: 1; padding: 0.75rem; border-radius: 8px; border: 1px solid rgba(255,255,255,0.3); background: rgba(255,255,255,0.1); color: white;">
                        <button class="btn btn-success" onclick="markAttendance()">
                            <i class="fas fa-check"></i>
                            تسجيل حضور
                        </button>
                    </div>
                </div>
            </div>

            <!-- Live Statistics -->
            <div class="card fade-in">
                <div class="card-header">
                    <i class="fas fa-chart-line"></i>
                    <h3>الإحصائيات المباشرة</h3>
                </div>

                <div class="grid-4" style="margin-bottom: 2rem;">
                    <div class="stat-card">
                        <div class="stat-value" style="color: #10B981;" id="presentCount">0</div>
                        <div class="stat-label">حاضر</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" style="color: #EF4444;" id="absentCount">0</div>
                        <div class="stat-label">غائب</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" style="color: #F59E0B;" id="lateCount">0</div>
                        <div class="stat-label">متأخر</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" style="color: #8B4513;" id="totalPlayers">0</div>
                        <div class="stat-label">إجمالي</div>
                    </div>
                </div>

                <!-- Attendance Chart -->
                <div style="height: 200px;">
                    <canvas id="attendanceChart"></canvas>
                </div>

                <!-- Quick Actions -->
                <div class="grid-3" style="margin-top: 1.5rem;">
                    <button class="btn btn-primary" onclick="openAIAnalysis()">
                        <i class="fas fa-brain"></i>
                        تحليل ذكي
                    </button>
                    <button class="btn btn-warning" onclick="exportReport()">
                        <i class="fas fa-file-export"></i>
                        تصدير تقرير
                    </button>
                    <button class="btn btn-secondary" onclick="sendNotifications()">
                        <i class="fas fa-bell"></i>
                        إرسال تنبيهات
                    </button>
                </div>
            </div>
        </div>

        <!-- Players List Section -->
        <div class="card fade-in" style="margin-top: 2rem;">
            <div class="card-header">
                <i class="fas fa-users"></i>
                <h3>قائمة اللاعبين</h3>
            </div>

            <div class="player-list" id="playersList">
                <!-- Players will be populated by JavaScript -->
            </div>

            <!-- Bulk Actions -->
            <div class="grid-3" style="margin-top: 1.5rem; padding-top: 1.5rem; border-top: 1px solid rgba(255,255,255,0.1);">
                <button class="btn btn-success" onclick="markAllPresent()">
                    <i class="fas fa-check-double"></i>
                    تسجيل الكل حاضر
                </button>
                <button class="btn btn-warning" onclick="markAllLate()">
                    <i class="fas fa-clock"></i>
                    تسجيل الكل متأخر
                </button>
                <button class="btn btn-danger" onclick="resetAllAttendance()">
                    <i class="fas fa-undo"></i>
                    إعادة تعيين
                </button>
            </div>
        </div>

        <!-- AI Analytics Panel -->
        <div class="card fade-in" style="margin-top: 2rem;">
            <div class="card-header">
                <i class="fas fa-brain"></i>
                <h3>التحليل الذكي والتنبؤات</h3>
            </div>

            <!-- AI Insights Tabs -->
            <div class="ai-tabs" style="margin-bottom: 1.5rem;">
                <button class="ai-tab active" onclick="showAITab('predictions')" id="predictionsTabBtn">
                    <i class="fas fa-crystal-ball"></i>
                    التنبؤات
                </button>
                <button class="ai-tab" onclick="showAITab('patterns')" id="patternsTabBtn">
                    <i class="fas fa-chart-line"></i>
                    الأنماط
                </button>
                <button class="ai-tab" onclick="showAITab('recommendations')" id="recommendationsTabBtn">
                    <i class="fas fa-lightbulb"></i>
                    التوصيات
                </button>
                <button class="ai-tab" onclick="showAITab('notifications')" id="notificationsTabBtn">
                    <i class="fas fa-bell"></i>
                    التنبيهات الذكية
                </button>
            </div>

            <!-- Predictions Tab -->
            <div id="predictionsTab" class="ai-tab-content">
                <div class="grid-2 gap-4">
                    <div class="prediction-card">
                        <h4 class="text-lg font-bold mb-3 text-blue-400">
                            <i class="fas fa-user-clock"></i>
                            توقعات الجلسة القادمة
                        </h4>
                        <div id="nextSessionPredictions" class="space-y-2">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>

                    <div class="prediction-card">
                        <h4 class="text-lg font-bold mb-3 text-green-400">
                            <i class="fas fa-calendar-week"></i>
                            توقعات الأسبوع
                        </h4>
                        <div id="weeklyPredictions" class="space-y-2">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <div class="mt-6">
                    <h4 class="text-lg font-bold mb-3 text-yellow-400">
                        <i class="fas fa-exclamation-triangle"></i>
                        اللاعبون المعرضون للخطر
                    </h4>
                    <div id="riskPlayers" class="grid-3 gap-4">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Patterns Tab -->
            <div id="patternsTab" class="ai-tab-content" style="display: none;">
                <div class="grid-2 gap-4">
                    <div class="pattern-card">
                        <h4 class="text-lg font-bold mb-3 text-purple-400">
                            <i class="fas fa-calendar-alt"></i>
                            الأنماط الأسبوعية
                        </h4>
                        <canvas id="weeklyPatternChart" width="400" height="200"></canvas>
                    </div>

                    <div class="pattern-card">
                        <h4 class="text-lg font-bold mb-3 text-orange-400">
                            <i class="fas fa-clock"></i>
                            الأنماط الزمنية
                        </h4>
                        <canvas id="timePatternChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="mt-6">
                    <h4 class="text-lg font-bold mb-3 text-red-400">
                        <i class="fas fa-users"></i>
                        تحليل السلوك الجماعي
                    </h4>
                    <div id="behaviorAnalysis" class="behavior-grid">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Recommendations Tab -->
            <div id="recommendationsTab" class="ai-tab-content" style="display: none;">
                <div id="aiRecommendations" class="recommendations-container">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>

            <!-- Smart Notifications Tab -->
            <div id="notificationsTab" class="ai-tab-content" style="display: none;">
                <div class="grid-2 gap-4 mb-6">
                    <div class="notification-stats">
                        <h4 class="text-lg font-bold mb-3 text-cyan-400">
                            <i class="fas fa-chart-pie"></i>
                            إحصائيات التنبيهات
                        </h4>
                        <div id="notificationStats" class="stats-grid">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>

                    <div class="notification-settings">
                        <h4 class="text-lg font-bold mb-3 text-indigo-400">
                            <i class="fas fa-cog"></i>
                            إعدادات التنبيهات
                        </h4>
                        <div class="settings-form">
                            <label class="flex items-center mb-2">
                                <input type="checkbox" id="enableProactive" checked class="ml-2">
                                تفعيل التنبيهات الاستباقية
                            </label>
                            <label class="flex items-center mb-2">
                                <input type="checkbox" id="enableRealTime" checked class="ml-2">
                                تفعيل التنبيهات الفورية
                            </label>
                            <div class="mb-2">
                                <label class="block mb-1">وقت التذكير (بالدقائق):</label>
                                <input type="number" id="reminderTime" value="30" min="5" max="120" class="w-full p-2 rounded bg-gray-700 text-white">
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 class="text-lg font-bold mb-3 text-pink-400">
                        <i class="fas fa-history"></i>
                        سجل التنبيهات الأخيرة
                    </h4>
                    <div id="notificationsPanel" class="notifications-list max-h-64 overflow-y-auto">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Integration Panel -->
        <div class="card fade-in" style="margin-top: 2rem;">
            <div class="card-header">
                <i class="fas fa-link"></i>
                <h3>التكامل مع الأنظمة</h3>
            </div>

            <div class="grid-4">
                <button class="btn btn-primary" onclick="syncWithDatabase()">
                    <i class="fas fa-database"></i>
                    مزامنة قاعدة البيانات
                </button>
                <button class="btn btn-secondary" onclick="updateLoyaltyPoints()">
                    <i class="fas fa-star"></i>
                    تحديث نقاط الولاء
                </button>
                <button class="btn btn-success" onclick="updatePlayerRatings()">
                    <i class="fas fa-chart-bar"></i>
                    تحديث التقييمات
                </button>
                <button class="btn btn-warning" onclick="generateCertificates()">
                    <i class="fas fa-certificate"></i>
                    إنشاء شهادات
                </button>
            </div>

            <!-- AI Status -->
            <div style="margin-top: 1.5rem; padding: 1rem; background: rgba(139, 69, 19, 0.1); border-radius: 8px; border: 1px solid #8B4513;">
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <div style="width: 10px; height: 10px; background: #8B4513; border-radius: 50%; animation: pulse 2s infinite;"></div>
                    <span style="color: #D2691E; font-weight: bold;">محرك الذكاء الاصطناعي نشط</span>
                    <span style="margin-right: auto; font-size: 0.9rem; opacity: 0.8;">آخر تحليل: <span id="lastAIAnalysis">الآن</span></span>
                </div>
                <div style="margin-top: 0.5rem; font-size: 0.9rem; opacity: 0.8;">
                    <span id="aiStatus">جاري تحليل الأنماط وإنشاء التوصيات...</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global Variables
        let currentSession = {
            id: generateSessionId(),
            startTime: new Date(),
            qrCode: null,
            attendanceData: []
        };

        let players = [
            { id: 1, name: 'أحمد محمد علي', number: '85MID', avatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iMjAiIGZpbGw9IiM4QjQ1MTMiLz4KPHRleHQgeD0iMjAiIHk9IjI1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+N0M8L3RleHQ+Cjwvc3ZnPgo=', status: 'pending' },
            { id: 2, name: 'محمد أحمد سالم', number: '42DEF', avatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iMjAiIGZpbGw9IiM4QjQ1MTMiLz4KPHRleHQgeD0iMjAiIHk9IjI1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+N0M8L3RleHQ+Cjwvc3ZnPgo=', status: 'pending' },
            { id: 3, name: 'سالم عبدالله محمد', number: '19ATT', avatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iMjAiIGZpbGw9IiM4QjQ1MTMiLz4KPHRleHQgeD0iMjAiIHk9IjI1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+N0M8L3RleHQ+Cjwvc3ZnPgo=', status: 'pending' }
        ];

        // Global AI instances
        let attendanceAI;
        let smartNotifications;

        // Initialize System
        document.addEventListener('DOMContentLoaded', function() {
            generateSessionQR();
            updateStatistics();
            initializeChart();
            updatePlayersList();

            // Initialize AI Systems
            initializeAISystems();

            // Auto-refresh every 30 seconds
            setInterval(() => {
                updateStatistics();
                document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString('ar-SA');
                updateAIStatus();
            }, 30000);

            // Initialize search functionality
            const searchInput = document.getElementById('playerSearch');
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        markAttendance();
                    }
                });

                // Add autocomplete functionality
                searchInput.addEventListener('input', function(e) {
                    const value = e.target.value.toLowerCase();
                    if (value.length > 0) {
                        const suggestions = players.filter(p =>
                            p.name.toLowerCase().includes(value) ||
                            p.number.toLowerCase().includes(value)
                        );

                        // You could show suggestions here
                        if (suggestions.length === 1 && value.length > 2) {
                            // Auto-suggest the player
                            const suggestion = suggestions[0];
                            e.target.style.borderColor = '#10B981';
                            e.target.title = `اقتراح: ${suggestion.name} (${suggestion.number})`;
                        } else {
                            e.target.style.borderColor = 'rgba(255,255,255,0.3)';
                            e.target.title = '';
                        }
                    }
                });
            }
        });

        // Initialize AI Systems
        function initializeAISystems() {
            showNotification('جاري تهيئة محرك الذكاء الاصطناعي...', 'info');

            // Initialize AI Engine
            attendanceAI = new AttendanceAI();

            // Initialize Smart Notifications
            smartNotifications = new SmartNotificationSystem(attendanceAI);

            // Update UI with AI data
            setTimeout(() => {
                updateAIInterface();
                showNotification('تم تفعيل محرك الذكاء الاصطناعي بنجاح!', 'success');
            }, 2000);
        }

        // Update AI Interface
        function updateAIInterface() {
            updatePredictionsTab();
            updatePatternsTab();
            updateRecommendationsTab();
            updateNotificationsTab();
            updateAIStatus();
        }

        // AI Tab Management
        function showAITab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.ai-tab-content').forEach(tab => {
                tab.style.display = 'none';
            });

            // Remove active class from all buttons
            document.querySelectorAll('.ai-tab').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName + 'Tab').style.display = 'block';

            // Add active class to clicked button
            document.getElementById(tabName + 'TabBtn').classList.add('active');

            // Update tab content
            switch(tabName) {
                case 'predictions':
                    updatePredictionsTab();
                    break;
                case 'patterns':
                    updatePatternsTab();
                    break;
                case 'recommendations':
                    updateRecommendationsTab();
                    break;
                case 'notifications':
                    updateNotificationsTab();
                    break;
            }
        }

        // Update Predictions Tab
        function updatePredictionsTab() {
            if (!attendanceAI) return;

            const predictions = attendanceAI.predictions;
            const nextSessionContainer = document.getElementById('nextSessionPredictions');
            const weeklyContainer = document.getElementById('weeklyPredictions');
            const riskContainer = document.getElementById('riskPlayers');

            // Next Session Predictions
            let nextSessionHTML = '';
            players.forEach(player => {
                const prediction = predictions[player.number];
                if (prediction) {
                    const statusClass = prediction.nextSession.probability >= 70 ? 'prediction-high' :
                                       prediction.nextSession.probability >= 40 ? 'prediction-medium' : 'prediction-low';

                    nextSessionHTML += `
                        <div class="prediction-item ${statusClass}">
                            <div>
                                <strong>${player.name}</strong>
                                <div style="font-size: 0.8rem; opacity: 0.8;">${prediction.nextSession.status}</div>
                            </div>
                            <div style="text-align: left;">
                                <div style="font-size: 1.2rem; font-weight: bold; color: ${prediction.nextSession.color};">
                                    ${prediction.nextSession.probability}%
                                </div>
                            </div>
                        </div>
                    `;
                }
            });
            nextSessionContainer.innerHTML = nextSessionHTML || '<p class="text-gray-400">لا توجد توقعات متاحة</p>';

            // Weekly Predictions Summary
            const weeklyStats = this.calculateWeeklyStats(predictions);
            weeklyContainer.innerHTML = `
                <div class="grid-2 gap-4">
                    <div class="stat-box">
                        <div class="stat-number text-green-400">${weeklyStats.expectedPresent}</div>
                        <div class="stat-label">متوقع الحضور</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-number text-red-400">${weeklyStats.expectedAbsent}</div>
                        <div class="stat-label">متوقع الغياب</div>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <div class="text-lg font-bold">معدل الحضور المتوقع: ${weeklyStats.expectedRate}%</div>
                </div>
            `;

            // Risk Players
            const riskPlayers = this.identifyRiskPlayers(predictions);
            let riskHTML = '';
            riskPlayers.forEach(player => {
                riskHTML += `
                    <div class="risk-player-card">
                        <h5>${player.name}</h5>
                        <div style="font-size: 0.9rem; margin-bottom: 0.5rem;">
                            احتمالية الغياب: ${100 - player.probability}%
                        </div>
                        <div style="font-size: 0.8rem; opacity: 0.8;">
                            ${player.riskFactors.join(', ')}
                        </div>
                    </div>
                `;
            });
            riskContainer.innerHTML = riskHTML || '<p class="text-gray-400">لا يوجد لاعبون معرضون للخطر حالياً</p>';
        }

        // Update Patterns Tab
        function updatePatternsTab() {
            if (!attendanceAI) return;

            createWeeklyPatternChart();
            createTimePatternChart();
            updateBehaviorAnalysis();
        }

        // Create Weekly Pattern Chart
        function createWeeklyPatternChart() {
            const ctx = document.getElementById('weeklyPatternChart');
            if (!ctx || !attendanceAI.patterns.weeklyPatterns) return;

            const weeklyData = attendanceAI.patterns.weeklyPatterns;
            const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];

            // Calculate average attendance rate for each day
            const avgRates = days.map(day => {
                const dayRates = Object.values(weeklyData).map(playerData =>
                    playerData[day] ? playerData[day].attendanceRate : 0
                );
                return dayRates.length > 0 ? dayRates.reduce((a, b) => a + b, 0) / dayRates.length : 0;
            });

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: days,
                    datasets: [{
                        label: 'معدل الحضور %',
                        data: avgRates,
                        backgroundColor: 'rgba(139, 69, 19, 0.6)',
                        borderColor: 'rgba(139, 69, 19, 1)',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: 'white' }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: { color: 'white' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        x: {
                            ticks: { color: 'white' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        }
                    }
                }
            });
        }

        // Create Time Pattern Chart
        function createTimePatternChart() {
            const ctx = document.getElementById('timePatternChart');
            if (!ctx || !attendanceAI.patterns.timePatterns) return;

            const timeData = attendanceAI.patterns.timePatterns;
            const players = Object.keys(timeData);

            // Calculate average punctuality scores
            const avgPunctuality = players.length > 0 ?
                Object.values(timeData).reduce((sum, player) => sum + (player.punctualityScore || 0), 0) / players.length : 0;

            // Create a simple visualization
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['في الوقت المحدد', 'متأخر', 'غائب'],
                    datasets: [{
                        data: [avgPunctuality, 100 - avgPunctuality, 10],
                        backgroundColor: [
                            '#10B981',
                            '#F59E0B',
                            '#EF4444'
                        ],
                        borderWidth: 2,
                        borderColor: '#1a1a1a'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: { color: 'white' }
                        }
                    }
                }
            });
        }

        // Update Behavior Analysis
        function updateBehaviorAnalysis() {
            if (!attendanceAI) return;

            const behaviorData = attendanceAI.patterns.playerBehavior;
            const container = document.getElementById('behaviorAnalysis');

            let behaviorHTML = '';
            players.forEach(player => {
                const behavior = behaviorData[player.number];
                if (behavior) {
                    const typeClass = this.getBehaviorTypeClass(behavior.type);

                    behaviorHTML += `
                        <div class="behavior-card">
                            <div class="flex justify-between items-start mb-2">
                                <strong>${player.name}</strong>
                                <span class="behavior-type ${typeClass}">${behavior.type}</span>
                            </div>
                            <div class="grid-2 gap-2 text-sm">
                                <div>معدل الحضور: ${behavior.attendanceRate}%</div>
                                <div>الانضباط: ${behavior.punctualityRate}%</div>
                                <div>الاتجاه: ${behavior.trend}</div>
                                <div>مستوى الخطر: ${behavior.riskLevel}</div>
                            </div>
                            <div class="mt-2 text-xs opacity-75">
                                إجمالي الجلسات: ${behavior.totalSessions}
                            </div>
                        </div>
                    `;
                }
            });

            container.innerHTML = behaviorHTML || '<p class="text-gray-400">لا توجد بيانات سلوكية متاحة</p>';
        }

        // Update Recommendations Tab
        function updateRecommendationsTab() {
            if (!attendanceAI) return;

            const recommendations = attendanceAI.recommendations;
            const container = document.getElementById('aiRecommendations');

            let recommendationsHTML = '';
            recommendations.forEach(rec => {
                const priorityClass = `recommendation-${rec.priority === 'عالي' ? 'high' : rec.priority === 'متوسط' ? 'medium' : 'low'}`;

                recommendationsHTML += `
                    <div class="recommendation-card ${priorityClass}">
                        <div class="flex justify-between items-start mb-3">
                            <h4 class="text-lg font-bold">${rec.title}</h4>
                            <span class="priority-badge priority-${rec.priority === 'عالي' ? 'high' : rec.priority === 'متوسط' ? 'medium' : 'low'}">
                                ${rec.priority}
                            </span>
                        </div>

                        <p class="mb-3 opacity-90">${rec.description}</p>

                        ${rec.actions ? `
                            <h5 class="font-bold mb-2">الإجراءات المقترحة:</h5>
                            <ul class="recommendation-actions">
                                ${rec.actions.map(action => `<li>${action}</li>`).join('')}
                            </ul>
                        ` : ''}

                        ${rec.expectedImpact ? `
                            <div class="mt-3 p-2 bg-green-600/20 rounded border border-green-600/30">
                                <strong>التأثير المتوقع:</strong> ${rec.expectedImpact}
                            </div>
                        ` : ''}

                        ${rec.playerName ? `
                            <div class="mt-2 text-sm opacity-75">
                                اللاعب: ${rec.playerName}
                            </div>
                        ` : ''}
                    </div>
                `;
            });

            container.innerHTML = recommendationsHTML || '<p class="text-gray-400">لا توجد توصيات متاحة حالياً</p>';
        }

        // Update Notifications Tab
        function updateNotificationsTab() {
            if (!smartNotifications) return;

            updateNotificationStats();
            updateNotificationSettings();
            updateNotificationsList();
        }

        function updateNotificationStats() {
            const stats = smartNotifications.getNotificationStats();
            const container = document.getElementById('notificationStats');

            container.innerHTML = `
                <div class="stat-box">
                    <div class="stat-number text-blue-400">${stats.totalSent}</div>
                    <div class="stat-label">إجمالي المرسل</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number text-green-400">${stats.last24Hours}</div>
                    <div class="stat-label">آخر 24 ساعة</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number text-yellow-400">${stats.queued}</div>
                    <div class="stat-label">في الانتظار</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number text-purple-400">${Object.keys(stats.byType).length}</div>
                    <div class="stat-label">أنواع التنبيهات</div>
                </div>
            `;
        }

        function updateNotificationSettings() {
            // Load current settings
            const preferences = smartNotifications.preferences;

            document.getElementById('enableProactive').checked = preferences.enableProactive;
            document.getElementById('enableRealTime').checked = preferences.enableRealTime;
            document.getElementById('reminderTime').value = preferences.reminderTime;

            // Add event listeners
            document.getElementById('enableProactive').addEventListener('change', saveNotificationSettings);
            document.getElementById('enableRealTime').addEventListener('change', saveNotificationSettings);
            document.getElementById('reminderTime').addEventListener('change', saveNotificationSettings);
        }

        function saveNotificationSettings() {
            const preferences = {
                enableProactive: document.getElementById('enableProactive').checked,
                enableRealTime: document.getElementById('enableRealTime').checked,
                reminderTime: parseInt(document.getElementById('reminderTime').value),
                channels: ['app', 'whatsapp', 'email'],
                frequency: 'smart'
            };

            localStorage.setItem('notification_preferences', JSON.stringify(preferences));
            smartNotifications.preferences = preferences;

            showNotification('تم حفظ إعدادات التنبيهات', 'success');
        }

        function updateNotificationsList() {
            const recentNotifications = smartNotifications.sentNotifications.slice(-10).reverse();
            const container = document.getElementById('notificationsPanel');

            if (recentNotifications.length === 0) {
                container.innerHTML = '<p class="text-gray-400 text-center">لا توجد تنبيهات حديثة</p>';
                return;
            }

            let notificationsHTML = '';
            recentNotifications.forEach(notification => {
                notificationsHTML += `
                    <div class="notification-item">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <h5 class="font-bold text-sm">${notification.title}</h5>
                                <p class="text-xs text-gray-300 mt-1">${notification.message}</p>
                                <div class="flex items-center gap-2 mt-2">
                                    <span class="text-xs text-gray-500">
                                        ${new Date(notification.sentAt).toLocaleString('ar-SA')}
                                    </span>
                                    ${notification.playerName ? `
                                        <span class="text-xs bg-gray-700 px-2 py-1 rounded">
                                            ${notification.playerName}
                                        </span>
                                    ` : ''}
                                </div>
                            </div>
                            <span class="priority-badge priority-${notification.priority}">
                                ${notification.priority}
                            </span>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = notificationsHTML;
        }

        // Update AI Status
        function updateAIStatus() {
            const statusElement = document.getElementById('aiStatus');
            const lastAnalysisElement = document.getElementById('lastAIAnalysis');

            if (attendanceAI) {
                const insights = attendanceAI.getInsights();
                const analysisTime = new Date(insights.lastAnalysis).toLocaleTimeString('ar-SA');

                statusElement.textContent = `تم تحليل ${players.length} لاعب - ${attendanceAI.recommendations.length} توصية متاحة`;
                lastAnalysisElement.textContent = analysisTime;
            }
        }

        // Utility Functions
        function calculateWeeklyStats(predictions) {
            let expectedPresent = 0;
            let total = 0;

            Object.values(predictions).forEach(prediction => {
                if (prediction.nextSession.probability >= 50) {
                    expectedPresent++;
                }
                total++;
            });

            return {
                expectedPresent,
                expectedAbsent: total - expectedPresent,
                expectedRate: total > 0 ? Math.round((expectedPresent / total) * 100) : 0
            };
        }

        function identifyRiskPlayers(predictions) {
            const riskPlayers = [];

            players.forEach(player => {
                const prediction = predictions[player.number];
                if (prediction && prediction.nextSession.probability < 50) {
                    riskPlayers.push({
                        name: player.name,
                        number: player.number,
                        probability: prediction.nextSession.probability,
                        riskFactors: prediction.riskFactors.map(rf => rf.factor) || []
                    });
                }
            });

            return riskPlayers.sort((a, b) => a.probability - b.probability);
        }

        function getBehaviorTypeClass(type) {
            const mapping = {
                'ممتاز': 'type-excellent',
                'جيد': 'type-good',
                'متوسط': 'type-average',
                'يحتاج متابعة': 'type-needs-attention'
            };
            return mapping[type] || 'type-average';
        }

        // Enhanced AI Analysis Modal
        function openAIAnalysis() {
            if (!attendanceAI) {
                showNotification('محرك الذكاء الاصطناعي غير متاح', 'error');
                return;
            }

            const insights = attendanceAI.getInsights();
            const teamStats = attendanceAI.analyzeTeamPerformance();

            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 800px; max-height: 80vh; overflow-y: auto;">
                    <div class="modal-header">
                        <h3><i class="fas fa-brain"></i> التحليل الذكي المتقدم للحضور</h3>
                        <button class="modal-close" onclick="closeModal(this)">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="grid-2 gap-6">
                            <!-- Team Performance -->
                            <div class="card" style="background: rgba(16, 185, 129, 0.1); border: 1px solid #10B981;">
                                <h4 style="color: #10B981; margin-bottom: 1rem;">
                                    <i class="fas fa-users"></i> أداء الفريق
                                </h4>
                                <div class="grid-2 gap-4">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold">${teamStats.attendanceRate}%</div>
                                        <div class="text-sm opacity-75">معدل الحضور</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold">${teamStats.punctualityRate}%</div>
                                        <div class="text-sm opacity-75">معدل الانضباط</div>
                                    </div>
                                </div>
                                <div class="mt-4 text-sm">
                                    <p>• ${teamStats.presentPlayers} لاعب حاضر</p>
                                    <p>• ${teamStats.lateePlayers} لاعب متأخر</p>
                                    <p>• ${teamStats.absentPlayers} لاعب غائب</p>
                                </div>
                            </div>

                            <!-- AI Predictions -->
                            <div class="card" style="background: rgba(59, 130, 246, 0.1); border: 1px solid #3B82F6;">
                                <h4 style="color: #3B82F6; margin-bottom: 1rem;">
                                    <i class="fas fa-crystal-ball"></i> التنبؤات الذكية
                                </h4>
                                <div class="space-y-2">
                                    ${this.generatePredictionsSummary(insights.predictions)}
                                </div>
                            </div>

                            <!-- Risk Analysis -->
                            <div class="card" style="background: rgba(245, 158, 11, 0.1); border: 1px solid #F59E0B;">
                                <h4 style="color: #F59E0B; margin-bottom: 1rem;">
                                    <i class="fas fa-exclamation-triangle"></i> تحليل المخاطر
                                </h4>
                                ${this.generateRiskAnalysis(insights.patterns.playerBehavior)}
                            </div>

                            <!-- Smart Recommendations -->
                            <div class="card" style="background: rgba(139, 69, 19, 0.1); border: 1px solid #8B4513;">
                                <h4 style="color: #D2691E; margin-bottom: 1rem;">
                                    <i class="fas fa-lightbulb"></i> التوصيات الذكية
                                </h4>
                                <div class="space-y-2">
                                    ${insights.recommendations.slice(0, 3).map(rec => `
                                        <div class="p-2 bg-gray-800/50 rounded">
                                            <strong>${rec.title}</strong>
                                            <p class="text-sm opacity-75">${rec.description}</p>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>

                        <div class="mt-6 text-center">
                            <button class="btn btn-primary" onclick="exportAIReport()">
                                <i class="fas fa-download"></i>
                                تصدير التقرير الذكي
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function generatePredictionsSummary(predictions) {
            const highRisk = Object.values(predictions).filter(p => p.nextSession.probability < 40).length;
            const lowRisk = Object.values(predictions).filter(p => p.nextSession.probability > 80).length;

            return `
                <p>• ${highRisk} لاعب معرض لخطر الغياب العالي</p>
                <p>• ${lowRisk} لاعب متوقع حضوره بثقة عالية</p>
                <p>• متوسط احتمالية الحضور: ${this.calculateAveragePrediction(predictions)}%</p>
            `;
        }

        function generateRiskAnalysis(behaviorData) {
            const highRisk = Object.values(behaviorData).filter(b => b.riskLevel === 'عالي').length;
            const declining = Object.values(behaviorData).filter(b => b.trend === 'متراجع').length;

            return `
                <p>• ${highRisk} لاعب في مستوى خطر عالي</p>
                <p>• ${declining} لاعب يظهر اتجاهاً متراجعاً</p>
                <p>• يُنصح بالتدخل المبكر للاعبين المعرضين للخطر</p>
            `;
        }

        function calculateAveragePrediction(predictions) {
            const values = Object.values(predictions).map(p => p.nextSession.probability);
            return values.length > 0 ? Math.round(values.reduce((a, b) => a + b, 0) / values.length) : 0;
        }

        function exportAIReport() {
            if (!attendanceAI) return;

            const insights = attendanceAI.getInsights();
            const report = {
                timestamp: new Date().toISOString(),
                sessionId: currentSession.id,
                teamPerformance: attendanceAI.analyzeTeamPerformance(),
                predictions: insights.predictions,
                patterns: insights.patterns,
                recommendations: insights.recommendations,
                notificationStats: smartNotifications ? smartNotifications.getNotificationStats() : null
            };

            const dataStr = JSON.stringify(report, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `ai_attendance_report_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            closeModal();
            showNotification('تم تصدير التقرير الذكي بنجاح!', 'success');
        }

        // Generate Session ID
        function generateSessionId() {
            return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        // Generate Session QR Code
        function generateSessionQR() {
            const sessionData = {
                sessionId: currentSession.id,
                timestamp: new Date().toISOString(),
                academy: 'أكاديمية 7C للتدريب الرياضي',
                type: 'attendance_session'
            };

            QRCode.toCanvas(document.createElement('canvas'), JSON.stringify(sessionData), {
                width: 150,
                height: 150,
                color: {
                    dark: '#1a1a1a',
                    light: '#ffffff'
                }
            }, function (error, canvas) {
                if (!error) {
                    const container = document.getElementById('sessionQR');
                    container.innerHTML = '';
                    container.appendChild(canvas);
                    currentSession.qrCode = sessionData;
                }
            });
        }

        // Generate New Session QR
        function generateNewSessionQR() {
            currentSession.id = generateSessionId();
            currentSession.startTime = new Date();
            generateSessionQR();
            showNotification('تم إنشاء باركود جلسة جديد!', 'success');
        }

        // Select Attendance Method
        function selectMethod(method) {
            // Remove active class from all methods
            document.querySelectorAll('.method-card').forEach(card => {
                card.classList.remove('active');
            });

            // Add active class to selected method
            document.getElementById(method + 'Method').classList.add('active');

            // Show appropriate interface
            if (method === 'qr') {
                document.getElementById('qrSection').style.display = 'block';
            } else {
                document.getElementById('qrSection').style.display = 'none';
                showNotification(`تم تفعيل ${method === 'face' ? 'بصمة الوجه' : 'بصمة العين'}`, 'info');
            }
        }

        // Mark Attendance
        function markAttendance() {
            const searchInput = document.getElementById('playerSearch');
            const playerName = searchInput.value.trim();

            if (!playerName) {
                showNotification('يرجى إدخال اسم اللاعب أو رقمه', 'warning');
                return;
            }

            // Find player
            const player = players.find(p =>
                p.name.includes(playerName) ||
                p.number.includes(playerName.toUpperCase())
            );

            if (!player) {
                showNotification('لم يتم العثور على اللاعب', 'error');
                return;
            }

            if (player.status === 'present') {
                showNotification('اللاعب مسجل حضوره مسبقاً', 'info');
                return;
            }

            // Mark as present
            player.status = 'present';
            player.attendanceTime = new Date();

            // Add to session data
            currentSession.attendanceData.push({
                playerId: player.id,
                playerName: player.name,
                playerNumber: player.number,
                status: 'present',
                timestamp: new Date(),
                method: 'manual'
            });

            // Save to localStorage for integration with player profile
            saveAttendanceData();

            // Update displays
            updateStatistics();
            updatePlayersList();
            updateChart();

            // Clear search
            searchInput.value = '';

            showNotification(`تم تسجيل حضور ${player.name}`, 'success');
        }

        // Update Statistics
        function updateStatistics() {
            const present = players.filter(p => p.status === 'present').length;
            const absent = players.filter(p => p.status === 'absent').length;
            const late = players.filter(p => p.status === 'late').length;
            const total = players.length;

            document.getElementById('presentCount').textContent = present;
            document.getElementById('absentCount').textContent = absent;
            document.getElementById('lateCount').textContent = late;
            document.getElementById('totalPlayers').textContent = total;

            // Update attendance percentage
            const attendanceRate = total > 0 ? Math.round((present / total) * 100) : 0;

            // Add visual feedback
            const presentElement = document.getElementById('presentCount');
            if (present > 0) {
                presentElement.classList.add('pulse');
                setTimeout(() => presentElement.classList.remove('pulse'), 2000);
            }
        }

        // Initialize Chart
        function initializeChart() {
            const ctx = document.getElementById('attendanceChart').getContext('2d');

            window.attendanceChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['حاضر', 'غائب', 'متأخر', 'في الانتظار'],
                    datasets: [{
                        data: [0, 0, 0, players.length],
                        backgroundColor: [
                            '#10B981',
                            '#EF4444',
                            '#F59E0B',
                            '#6B7280'
                        ],
                        borderWidth: 2,
                        borderColor: '#1a1a1a'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: 'white',
                                font: {
                                    family: 'Segoe UI'
                                }
                            }
                        }
                    }
                }
            });
        }

        // Update Chart
        function updateChart() {
            const present = players.filter(p => p.status === 'present').length;
            const absent = players.filter(p => p.status === 'absent').length;
            const late = players.filter(p => p.status === 'late').length;
            const pending = players.filter(p => p.status === 'pending').length;

            window.attendanceChart.data.datasets[0].data = [present, absent, late, pending];
            window.attendanceChart.update();
        }

        // Update Players List
        function updatePlayersList() {
            const playersList = document.getElementById('playersList');
            if (!playersList) return;

            playersList.innerHTML = players.map(player => `
                <div class="player-item">
                    <div class="player-info">
                        <img src="${player.avatar}" alt="${player.name}" class="player-avatar">
                        <div>
                            <div style="font-weight: bold;">${player.name}</div>
                            <div style="font-size: 0.9rem; opacity: 0.8;">${player.number}</div>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        ${player.attendanceTime ? `<span style="font-size: 0.8rem; opacity: 0.7;">${player.attendanceTime.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'})}</span>` : ''}
                        <span class="status-badge status-${player.status}">
                            ${getStatusText(player.status)}
                        </span>
                        <div style="display: flex; gap: 0.5rem;">
                            <button class="btn" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" onclick="togglePlayerStatus('${player.id}', 'present')">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-warning" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" onclick="togglePlayerStatus('${player.id}', 'late')">
                                <i class="fas fa-clock"></i>
                            </button>
                            <button class="btn btn-danger" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;" onclick="togglePlayerStatus('${player.id}', 'absent')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            updateStatistics();
        }

        // Get Status Text
        function getStatusText(status) {
            const statusMap = {
                'present': 'حاضر',
                'absent': 'غائب',
                'late': 'متأخر',
                'pending': 'في الانتظار'
            };
            return statusMap[status] || 'غير محدد';
        }

        // Toggle Player Status
        function togglePlayerStatus(playerId, newStatus) {
            const player = players.find(p => p.id == playerId);
            if (!player) return;

            const oldStatus = player.status;
            player.status = newStatus;

            if (newStatus !== 'pending') {
                player.attendanceTime = new Date();
            } else {
                player.attendanceTime = null;
            }

            // Add to session data
            currentSession.attendanceData.push({
                playerId: player.id,
                playerName: player.name,
                playerNumber: player.number,
                status: newStatus,
                previousStatus: oldStatus,
                timestamp: new Date(),
                method: 'manual'
            });

            updatePlayersList();
            updateChart();
            saveAttendanceData();

            showNotification(`تم تغيير حالة ${player.name} إلى ${getStatusText(newStatus)}`, 'success');
        }

        // Bulk Actions
        function markAllPresent() {
            players.forEach(player => {
                if (player.status !== 'present') {
                    player.status = 'present';
                    player.attendanceTime = new Date();
                }
            });
            updatePlayersList();
            updateChart();
            showNotification('تم تسجيل جميع اللاعبين حاضرين', 'success');
        }

        function markAllLate() {
            players.forEach(player => {
                if (player.status === 'pending') {
                    player.status = 'late';
                    player.attendanceTime = new Date();
                }
            });
            updatePlayersList();
            updateChart();
            showNotification('تم تسجيل اللاعبين المتبقين كمتأخرين', 'warning');
        }

        function resetAllAttendance() {
            if (confirm('هل أنت متأكد من إعادة تعيين حضور جميع اللاعبين؟')) {
                players.forEach(player => {
                    player.status = 'pending';
                    player.attendanceTime = null;
                });
                currentSession.attendanceData = [];
                updatePlayersList();
                updateChart();
                showNotification('تم إعادة تعيين حضور جميع اللاعبين', 'info');
            }
        }

        // Integration Functions
        function syncWithDatabase() {
            showNotification('جاري المزامنة مع قاعدة البيانات...', 'info');

            // Simulate database sync
            setTimeout(() => {
                document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString('ar-SA');
                showNotification('تم مزامنة البيانات بنجاح!', 'success');
            }, 2000);
        }

        function updateLoyaltyPoints() {
            const presentPlayers = players.filter(p => p.status === 'present');

            if (presentPlayers.length === 0) {
                showNotification('لا يوجد لاعبين حاضرين لتحديث نقاطهم', 'warning');
                return;
            }

            showNotification(`جاري تحديث نقاط الولاء لـ ${presentPlayers.length} لاعب...`, 'info');

            setTimeout(() => {
                presentPlayers.forEach(player => {
                    // Add loyalty points based on attendance
                    const pointsToAdd = player.status === 'present' ? 10 : player.status === 'late' ? 5 : 0;
                    // This would integrate with the player profile system
                });

                showNotification(`تم تحديث نقاط الولاء لـ ${presentPlayers.length} لاعب`, 'success');
            }, 1500);
        }

        function updatePlayerRatings() {
            showNotification('جاري تحديث تقييمات اللاعبين...', 'info');

            setTimeout(() => {
                // This would integrate with the rating system
                showNotification('تم تحديث تقييمات اللاعبين بناءً على الحضور', 'success');
            }, 2000);
        }

        function generateCertificates() {
            const presentPlayers = players.filter(p => p.status === 'present');

            if (presentPlayers.length === 0) {
                showNotification('لا يوجد لاعبين حاضرين لإنشاء شهادات لهم', 'warning');
                return;
            }

            showNotification(`جاري إنشاء شهادات حضور لـ ${presentPlayers.length} لاعب...`, 'info');

            setTimeout(() => {
                // This would generate attendance certificates
                showNotification(`تم إنشاء ${presentPlayers.length} شهادة حضور`, 'success');
            }, 3000);
        }

        // AI Analysis Modal
        function openAIAnalysis() {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-brain"></i> التحليل الذكي للحضور</h3>
                        <button class="modal-close" onclick="closeModal(this)">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="space-y-4">
                            <div class="card" style="background: rgba(16, 185, 129, 0.1); border: 1px solid #10B981;">
                                <h4 style="color: #10B981; margin-bottom: 1rem;">
                                    <i class="fas fa-chart-line"></i> تحليل الأنماط
                                </h4>
                                <p>• معدل الحضور العام: ${Math.round((players.filter(p => p.status === 'present').length / players.length) * 100)}%</p>
                                <p>• أفضل وقت للحضور: 4:00 - 5:00 مساءً</p>
                                <p>• اللاعبون الأكثر انتظاماً: ${players.filter(p => p.status === 'present').slice(0, 2).map(p => p.name).join(', ')}</p>
                            </div>

                            <div class="card" style="background: rgba(245, 158, 11, 0.1); border: 1px solid #F59E0B;">
                                <h4 style="color: #F59E0B; margin-bottom: 1rem;">
                                    <i class="fas fa-exclamation-triangle"></i> تنبيهات ذكية
                                </h4>
                                <p>• ${players.filter(p => p.status === 'absent').length} لاعب غائب اليوم</p>
                                <p>• يُنصح بمتابعة اللاعبين المتأخرين</p>
                                <p>• إرسال تذكير للجلسة القادمة</p>
                            </div>

                            <div class="card" style="background: rgba(139, 69, 19, 0.1); border: 1px solid #8B4513;">
                                <h4 style="color: #D2691E; margin-bottom: 1rem;">
                                    <i class="fas fa-lightbulb"></i> توصيات التحسين
                                </h4>
                                <p>• تطبيق نظام نقاط الولاء للحضور المنتظم</p>
                                <p>• إرسال تنبيهات قبل الجلسة بـ 30 دقيقة</p>
                                <p>• تحفيز اللاعبين بجوائز شهرية</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // Export Report
        function exportReport() {
            const reportData = {
                sessionId: currentSession.id,
                date: new Date().toLocaleDateString('ar-SA'),
                time: new Date().toLocaleTimeString('ar-SA'),
                totalPlayers: players.length,
                present: players.filter(p => p.status === 'present').length,
                absent: players.filter(p => p.status === 'absent').length,
                late: players.filter(p => p.status === 'late').length,
                attendanceRate: Math.round((players.filter(p => p.status === 'present').length / players.length) * 100),
                players: players.map(p => ({
                    name: p.name,
                    number: p.number,
                    status: p.status,
                    attendanceTime: p.attendanceTime || null
                }))
            };

            // Create downloadable file
            const dataStr = JSON.stringify(reportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `attendance_report_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('تم تصدير التقرير بنجاح!', 'success');
        }

        // Send Notifications
        function sendNotifications() {
            const absentPlayers = players.filter(p => p.status === 'absent');

            if (absentPlayers.length === 0) {
                showNotification('جميع اللاعبين حاضرون!', 'success');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-bell"></i> إرسال التنبيهات</h3>
                        <button class="modal-close" onclick="closeModal(this)">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p style="margin-bottom: 1rem;">سيتم إرسال تنبيهات للاعبين الغائبين (${absentPlayers.length} لاعب):</p>

                        <div style="max-height: 200px; overflow-y: auto; margin-bottom: 1.5rem;">
                            ${absentPlayers.map(p => `
                                <div style="padding: 0.5rem; background: rgba(239, 68, 68, 0.1); border-radius: 5px; margin-bottom: 0.5rem;">
                                    <strong>${p.name}</strong> - ${p.number}
                                </div>
                            `).join('')}
                        </div>

                        <div class="grid-2" style="gap: 1rem;">
                            <button class="btn btn-primary" onclick="sendWhatsAppNotifications()">
                                <i class="fab fa-whatsapp"></i> واتساب
                            </button>
                            <button class="btn btn-secondary" onclick="sendEmailNotifications()">
                                <i class="fas fa-envelope"></i> إيميل
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // Send WhatsApp Notifications
        function sendWhatsAppNotifications() {
            const message = `🏃‍♂️ تذكير من أكاديمية 7C

لم نتمكن من تسجيل حضورك في جلسة التدريب اليوم.
📅 التاريخ: ${new Date().toLocaleDateString('ar-SA')}
⏰ الوقت: ${new Date().toLocaleTimeString('ar-SA')}

يرجى التواصل معنا في حالة وجود أي ظروف خاصة.
نتطلع لرؤيتك في الجلسة القادمة! 💪`;

            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            closeModal();
            showNotification('تم فتح واتساب لإرسال التنبيهات!', 'success');
        }

        // Send Email Notifications
        function sendEmailNotifications() {
            const subject = 'تذكير حضور جلسة التدريب - أكاديمية 7C';
            const body = `عزيزي اللاعب،

لم نتمكن من تسجيل حضورك في جلسة التدريب اليوم.

تفاصيل الجلسة:
📅 التاريخ: ${new Date().toLocaleDateString('ar-SA')}
⏰ الوقت: ${new Date().toLocaleTimeString('ar-SA')}

يرجى التواصل معنا في حالة وجود أي ظروف خاصة.
نتطلع لرؤيتك في الجلسة القادمة!

مع تحيات فريق أكاديمية 7C للتدريب الرياضي`;

            const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
            window.location.href = mailtoUrl;

            closeModal();
            showNotification('تم فتح تطبيق الإيميل!', 'success');
        }

        // Close Modal
        function closeModal(button) {
            const modal = button ? button.closest('.modal-overlay') : document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        }

        // Show Notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : type === 'warning' ? '#F59E0B' : '#3B82F6'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                font-weight: bold;
                max-width: 300px;
                animation: slideIn 0.3s ease-out;
            `;

            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-in forwards';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }

            .grid-2 {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
            }

            .space-y-4 > * + * {
                margin-top: 1rem;
            }
        `;
        document.head.appendChild(style);

        // Simulate some attendance data for demo
        setTimeout(() => {
            // Mark first player as present
            players[0].status = 'present';
            players[0].attendanceTime = new Date();

            updateStatistics();
            updateChart();
            showNotification(`تم تسجيل حضور ${players[0].name} تلقائياً (تجريبي)`, 'success');
        }, 3000);

        // Auto-mark some players as late after 5 seconds
        setTimeout(() => {
            if (players[1].status === 'pending') {
                players[1].status = 'late';
                players[1].attendanceTime = new Date();

                updateStatistics();
                updateChart();
                showNotification(`${players[1].name} متأخر`, 'warning');
            }
        }, 8000);

        // Go back to player profile
        function goBackToProfile() {
            window.close();
            // If window.close() doesn't work (some browsers block it), redirect
            setTimeout(() => {
                window.location.href = './player-profile.html';
            }, 100);
        }

        // Save Attendance Data for Integration
        function saveAttendanceData() {
            const attendanceSessionData = {
                sessionId: currentSession.id,
                startTime: currentSession.startTime,
                lastUpdate: new Date(),
                attendanceData: currentSession.attendanceData,
                statistics: {
                    totalPlayers: players.length,
                    present: players.filter(p => p.status === 'present').length,
                    absent: players.filter(p => p.status === 'absent').length,
                    late: players.filter(p => p.status === 'late').length,
                    attendanceRate: Math.round((players.filter(p => p.status === 'present').length / players.length) * 100)
                }
            };

            // Save to localStorage for integration with other systems
            localStorage.setItem('attendance_session_data', JSON.stringify(attendanceSessionData));
            localStorage.setItem('latest_attendance_update', new Date().toISOString());

            // Also save individual player attendance records
            players.forEach(player => {
                if (player.status !== 'pending') {
                    const playerAttendanceKey = `player_attendance_${player.number}`;
                    const existingData = JSON.parse(localStorage.getItem(playerAttendanceKey) || '[]');

                    existingData.push({
                        sessionId: currentSession.id,
                        date: new Date().toISOString().split('T')[0],
                        status: player.status,
                        timestamp: player.attendanceTime,
                        loyaltyPointsEarned: player.status === 'present' ? 10 : player.status === 'late' ? 5 : 0
                    });

                    // Keep only last 30 records
                    if (existingData.length > 30) {
                        existingData.splice(0, existingData.length - 30);
                    }

                    localStorage.setItem(playerAttendanceKey, JSON.stringify(existingData));
                }
            });
        }

        // Auto-save attendance data every minute
        setInterval(saveAttendanceData, 60000);

        // ==================== AI-Powered Analytics Engine ====================

        class AttendanceAI {
            constructor() {
                this.historicalData = this.loadHistoricalData();
                this.patterns = this.loadPatterns();
                this.predictions = {};
                this.recommendations = [];
                this.init();
            }

            init() {
                this.analyzePatterns();
                this.generatePredictions();
                this.createRecommendations();
                this.setupRealTimeAnalysis();
            }

            // Load Historical Attendance Data
            loadHistoricalData() {
                const data = {};
                players.forEach(player => {
                    const playerKey = `player_attendance_${player.number}`;
                    const playerData = JSON.parse(localStorage.getItem(playerKey) || '[]');
                    data[player.number] = playerData;
                });
                return data;
            }

            // Load Existing Patterns
            loadPatterns() {
                return JSON.parse(localStorage.getItem('attendance_patterns') || '{}');
            }

            // Advanced Pattern Analysis
            analyzePatterns() {
                const patterns = {
                    weeklyPatterns: this.analyzeWeeklyPatterns(),
                    timePatterns: this.analyzeTimePatterns(),
                    seasonalPatterns: this.analyzeSeasonalPatterns(),
                    playerBehavior: this.analyzePlayerBehavior(),
                    correlations: this.analyzeCorrelations()
                };

                this.patterns = patterns;
                localStorage.setItem('attendance_patterns', JSON.stringify(patterns));
                return patterns;
            }

            // Weekly Pattern Analysis
            analyzeWeeklyPatterns() {
                const weeklyData = {};
                const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];

                players.forEach(player => {
                    const playerData = this.historicalData[player.number] || [];
                    const weeklyStats = {};

                    dayNames.forEach((day, index) => {
                        const dayData = playerData.filter(record => {
                            const recordDate = new Date(record.date);
                            return recordDate.getDay() === index;
                        });

                        weeklyStats[day] = {
                            totalSessions: dayData.length,
                            presentCount: dayData.filter(r => r.status === 'present').length,
                            lateCount: dayData.filter(r => r.status === 'late').length,
                            absentCount: dayData.filter(r => r.status === 'absent').length,
                            attendanceRate: dayData.length > 0 ?
                                Math.round((dayData.filter(r => r.status === 'present').length / dayData.length) * 100) : 0
                        };
                    });

                    weeklyData[player.number] = weeklyStats;
                });

                return weeklyData;
            }

            // Time Pattern Analysis
            analyzeTimePatterns() {
                const timeData = {};

                players.forEach(player => {
                    const playerData = this.historicalData[player.number] || [];
                    const timeStats = {
                        morningAttendance: 0,
                        afternoonAttendance: 0,
                        eveningAttendance: 0,
                        averageArrivalTime: null,
                        punctualityScore: 0
                    };

                    const arrivalTimes = [];

                    playerData.forEach(record => {
                        if (record.timestamp) {
                            const time = new Date(record.timestamp);
                            const hour = time.getHours();

                            if (hour < 12) timeStats.morningAttendance++;
                            else if (hour < 17) timeStats.afternoonAttendance++;
                            else timeStats.eveningAttendance++;

                            if (record.status === 'present') {
                                arrivalTimes.push(hour * 60 + time.getMinutes());
                            }
                        }
                    });

                    if (arrivalTimes.length > 0) {
                        const avgMinutes = arrivalTimes.reduce((a, b) => a + b, 0) / arrivalTimes.length;
                        timeStats.averageArrivalTime = Math.floor(avgMinutes / 60) + ':' +
                            String(Math.floor(avgMinutes % 60)).padStart(2, '0');

                        // Calculate punctuality (how often they arrive on time)
                        const onTimeCount = playerData.filter(r => r.status === 'present').length;
                        const lateCount = playerData.filter(r => r.status === 'late').length;
                        timeStats.punctualityScore = onTimeCount + lateCount > 0 ?
                            Math.round((onTimeCount / (onTimeCount + lateCount)) * 100) : 100;
                    }

                    timeData[player.number] = timeStats;
                });

                return timeData;
            }

            // Seasonal Pattern Analysis
            analyzeSeasonalPatterns() {
                const seasonalData = {};
                const seasons = {
                    'الشتاء': [12, 1, 2],
                    'الربيع': [3, 4, 5],
                    'الصيف': [6, 7, 8],
                    'الخريف': [9, 10, 11]
                };

                players.forEach(player => {
                    const playerData = this.historicalData[player.number] || [];
                    const seasonStats = {};

                    Object.keys(seasons).forEach(season => {
                        const seasonMonths = seasons[season];
                        const seasonData = playerData.filter(record => {
                            const month = new Date(record.date).getMonth() + 1;
                            return seasonMonths.includes(month);
                        });

                        seasonStats[season] = {
                            attendanceRate: seasonData.length > 0 ?
                                Math.round((seasonData.filter(r => r.status === 'present').length / seasonData.length) * 100) : 0,
                            totalSessions: seasonData.length
                        };
                    });

                    seasonalData[player.number] = seasonStats;
                });

                return seasonalData;
            }

            // Player Behavior Analysis
            analyzePlayerBehavior() {
                const behaviorData = {};

                players.forEach(player => {
                    const playerData = this.historicalData[player.number] || [];

                    if (playerData.length === 0) {
                        behaviorData[player.number] = {
                            type: 'جديد',
                            reliability: 'غير محدد',
                            trend: 'مستقر',
                            riskLevel: 'منخفض'
                        };
                        return;
                    }

                    const totalSessions = playerData.length;
                    const presentCount = playerData.filter(r => r.status === 'present').length;
                    const lateCount = playerData.filter(r => r.status === 'late').length;
                    const absentCount = playerData.filter(r => r.status === 'absent').length;

                    const attendanceRate = Math.round((presentCount / totalSessions) * 100);
                    const punctualityRate = presentCount + lateCount > 0 ?
                        Math.round((presentCount / (presentCount + lateCount)) * 100) : 100;

                    // Determine player type
                    let type = 'منتظم';
                    if (attendanceRate >= 90) type = 'ممتاز';
                    else if (attendanceRate >= 75) type = 'جيد';
                    else if (attendanceRate >= 60) type = 'متوسط';
                    else type = 'يحتاج متابعة';

                    // Determine reliability
                    let reliability = 'موثوق';
                    if (punctualityRate >= 90) reliability = 'موثوق جداً';
                    else if (punctualityRate >= 75) reliability = 'موثوق';
                    else if (punctualityRate >= 60) reliability = 'متوسط الموثوقية';
                    else reliability = 'غير موثوق';

                    // Analyze trend (last 5 sessions vs previous 5)
                    const recentSessions = playerData.slice(-5);
                    const previousSessions = playerData.slice(-10, -5);

                    const recentRate = recentSessions.length > 0 ?
                        (recentSessions.filter(r => r.status === 'present').length / recentSessions.length) * 100 : 0;
                    const previousRate = previousSessions.length > 0 ?
                        (previousSessions.filter(r => r.status === 'present').length / previousSessions.length) * 100 : 0;

                    let trend = 'مستقر';
                    if (recentRate > previousRate + 10) trend = 'متحسن';
                    else if (recentRate < previousRate - 10) trend = 'متراجع';

                    // Risk assessment
                    let riskLevel = 'منخفض';
                    if (attendanceRate < 60 || trend === 'متراجع') riskLevel = 'عالي';
                    else if (attendanceRate < 75 || punctualityRate < 70) riskLevel = 'متوسط';

                    behaviorData[player.number] = {
                        type,
                        reliability,
                        trend,
                        riskLevel,
                        attendanceRate,
                        punctualityRate,
                        totalSessions,
                        recentPerformance: recentRate
                    };
                });

                return behaviorData;
            }

            // Correlation Analysis
            analyzeCorrelations() {
                // Analyze correlations between different factors
                const correlations = {
                    weatherImpact: this.analyzeWeatherCorrelation(),
                    timeImpact: this.analyzeTimeCorrelation(),
                    groupDynamics: this.analyzeGroupDynamics()
                };

                return correlations;
            }

            analyzeWeatherCorrelation() {
                // Simulate weather impact analysis
                // In real implementation, this would connect to weather API
                return {
                    rainyDays: { attendanceImpact: -15, confidence: 0.7 },
                    hotDays: { attendanceImpact: -8, confidence: 0.6 },
                    pleasantDays: { attendanceImpact: 5, confidence: 0.8 }
                };
            }

            analyzeTimeCorrelation() {
                const timeImpact = {};
                const hours = [16, 17, 18, 19, 20]; // Common training hours

                hours.forEach(hour => {
                    // Simulate analysis of attendance rates by hour
                    const baseRate = 85;
                    const hourImpact = hour === 17 ? 10 : hour === 19 ? -5 : 0;
                    timeImpact[`${hour}:00`] = {
                        attendanceRate: baseRate + hourImpact,
                        preference: hour === 17 ? 'مفضل' : hour === 19 ? 'أقل تفضيلاً' : 'عادي'
                    };
                });

                return timeImpact;
            }

            analyzeGroupDynamics() {
                // Analyze how players influence each other's attendance
                const dynamics = {};

                players.forEach(player => {
                    const influences = [];
                    const influenced = [];

                    // Simulate finding players who often attend together
                    players.forEach(otherPlayer => {
                        if (player.id !== otherPlayer.id) {
                            const correlation = Math.random() * 0.8; // Simulate correlation
                            if (correlation > 0.6) {
                                influences.push({
                                    player: otherPlayer.name,
                                    correlation: Math.round(correlation * 100)
                                });
                            }
                        }
                    });

                    dynamics[player.number] = {
                        influences: influences.slice(0, 2), // Top 2 influences
                        socialImpact: influences.length > 0 ? 'عالي' : 'منخفض'
                    };
                });

                return dynamics;
            }

            // Generate AI Predictions
            generatePredictions() {
                const predictions = {};

                players.forEach(player => {
                    const behavior = this.patterns.playerBehavior[player.number];
                    const weeklyPattern = this.patterns.weeklyPatterns[player.number];
                    const timePattern = this.patterns.timePatterns[player.number];

                    if (!behavior) return;

                    // Predict next session attendance
                    const nextSessionPrediction = this.predictNextSession(player, behavior, weeklyPattern);

                    // Predict weekly attendance
                    const weeklyPrediction = this.predictWeeklyAttendance(player, behavior);

                    // Predict risk factors
                    const riskFactors = this.identifyRiskFactors(player, behavior, timePattern);

                    predictions[player.number] = {
                        nextSession: nextSessionPrediction,
                        weeklyForecast: weeklyPrediction,
                        riskFactors: riskFactors,
                        confidence: this.calculateConfidence(behavior),
                        lastUpdated: new Date().toISOString()
                    };
                });

                this.predictions = predictions;
                localStorage.setItem('attendance_predictions', JSON.stringify(predictions));
                return predictions;
            }

            predictNextSession(player, behavior, weeklyPattern) {
                const today = new Date();
                const dayName = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'][today.getDay()];

                let baseProbability = behavior.attendanceRate / 100;

                // Adjust based on day of week
                if (weeklyPattern && weeklyPattern[dayName]) {
                    const dayRate = weeklyPattern[dayName].attendanceRate / 100;
                    baseProbability = (baseProbability + dayRate) / 2;
                }

                // Adjust based on trend
                if (behavior.trend === 'متحسن') baseProbability += 0.1;
                else if (behavior.trend === 'متراجع') baseProbability -= 0.15;

                // Adjust based on recent performance
                if (behavior.recentPerformance > behavior.attendanceRate) baseProbability += 0.05;
                else if (behavior.recentPerformance < behavior.attendanceRate) baseProbability -= 0.1;

                // Weather simulation (in real app, use weather API)
                const weatherFactor = Math.random() > 0.7 ? -0.1 : 0; // 30% chance of bad weather
                baseProbability += weatherFactor;

                // Ensure probability is between 0 and 1
                baseProbability = Math.max(0, Math.min(1, baseProbability));

                const percentage = Math.round(baseProbability * 100);
                let status = 'متوقع الحضور';
                let color = '#10B981';

                if (percentage < 30) {
                    status = 'متوقع الغياب';
                    color = '#EF4444';
                } else if (percentage < 60) {
                    status = 'غير مؤكد';
                    color = '#F59E0B';
                } else if (percentage < 80) {
                    status = 'محتمل الحضور';
                    color = '#3B82F6';
                }

                return {
                    probability: percentage,
                    status: status,
                    color: color,
                    factors: this.getInfluencingFactors(behavior, weeklyPattern)
                };
            }

            predictWeeklyAttendance(player, behavior) {
                const weekDays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس'];
                const forecast = {};

                weekDays.forEach(day => {
                    const dayProbability = (behavior.attendanceRate / 100) + (Math.random() * 0.2 - 0.1);
                    forecast[day] = {
                        probability: Math.round(Math.max(0, Math.min(1, dayProbability)) * 100),
                        recommended: dayProbability > 0.7
                    };
                });

                return forecast;
            }

            identifyRiskFactors(player, behavior, timePattern) {
                const risks = [];

                if (behavior.attendanceRate < 60) {
                    risks.push({
                        factor: 'معدل حضور منخفض',
                        severity: 'عالي',
                        impact: 'قد يؤثر على الأداء العام',
                        recommendation: 'يحتاج متابعة شخصية'
                    });
                }

                if (behavior.trend === 'متراجع') {
                    risks.push({
                        factor: 'اتجاه متراجع في الحضور',
                        severity: 'متوسط',
                        impact: 'قد يؤدي لتراجع أكبر',
                        recommendation: 'تدخل مبكر مطلوب'
                    });
                }

                if (behavior.punctualityRate < 70) {
                    risks.push({
                        factor: 'تأخير متكرر',
                        severity: 'متوسط',
                        impact: 'يؤثر على انضباط الفريق',
                        recommendation: 'تحسين إدارة الوقت'
                    });
                }

                if (timePattern && timePattern.punctualityScore < 60) {
                    risks.push({
                        factor: 'عدم انتظام في المواعيد',
                        severity: 'منخفض',
                        impact: 'يؤثر على التخطيط',
                        recommendation: 'تذكيرات مسبقة'
                    });
                }

                return risks;
            }

            getInfluencingFactors(behavior, weeklyPattern) {
                const factors = [];

                if (behavior.trend === 'متحسن') {
                    factors.push('تحسن مستمر في الحضور');
                }

                if (behavior.reliability === 'موثوق جداً') {
                    factors.push('سجل موثوقية ممتاز');
                }

                if (weeklyPattern) {
                    const bestDay = Object.keys(weeklyPattern).reduce((a, b) =>
                        weeklyPattern[a].attendanceRate > weeklyPattern[b].attendanceRate ? a : b
                    );
                    factors.push(`أفضل أداء في ${bestDay}`);
                }

                return factors;
            }

            calculateConfidence(behavior) {
                let confidence = 0.5; // Base confidence

                // More data = higher confidence
                if (behavior.totalSessions > 20) confidence += 0.3;
                else if (behavior.totalSessions > 10) confidence += 0.2;
                else if (behavior.totalSessions > 5) confidence += 0.1;

                // Consistent behavior = higher confidence
                if (behavior.trend === 'مستقر') confidence += 0.2;

                // High attendance rate = higher confidence in predictions
                if (behavior.attendanceRate > 80) confidence += 0.1;

                return Math.min(0.95, confidence); // Max 95% confidence
            }

            // Create Smart Recommendations
            createRecommendations() {
                const recommendations = [];

                // Analyze overall team performance
                const teamStats = this.analyzeTeamPerformance();

                // Generate team-level recommendations
                recommendations.push(...this.generateTeamRecommendations(teamStats));

                // Generate individual player recommendations
                players.forEach(player => {
                    const behavior = this.patterns.playerBehavior[player.number];
                    if (behavior) {
                        recommendations.push(...this.generatePlayerRecommendations(player, behavior));
                    }
                });

                // Generate schedule optimization recommendations
                recommendations.push(...this.generateScheduleRecommendations());

                this.recommendations = recommendations;
                localStorage.setItem('attendance_recommendations', JSON.stringify(recommendations));
                return recommendations;
            }

            analyzeTeamPerformance() {
                const totalPlayers = players.length;
                const presentPlayers = players.filter(p => p.status === 'present').length;
                const lateePlayers = players.filter(p => p.status === 'late').length;
                const absentPlayers = players.filter(p => p.status === 'absent').length;

                const attendanceRate = Math.round((presentPlayers / totalPlayers) * 100);
                const punctualityRate = Math.round((presentPlayers / (presentPlayers + lateePlayers || 1)) * 100);

                return {
                    totalPlayers,
                    presentPlayers,
                    lateePlayers,
                    absentPlayers,
                    attendanceRate,
                    punctualityRate
                };
            }

            generateTeamRecommendations(teamStats) {
                const recommendations = [];

                if (teamStats.attendanceRate < 70) {
                    recommendations.push({
                        type: 'team',
                        priority: 'عالي',
                        title: 'تحسين معدل الحضور العام',
                        description: `معدل الحضور الحالي ${teamStats.attendanceRate}% أقل من المطلوب`,
                        actions: [
                            'مراجعة أوقات التدريب',
                            'تحسين التواصل مع اللاعبين',
                            'تطبيق نظام حوافز للحضور'
                        ],
                        expectedImpact: 'زيادة الحضور بنسبة 15-20%'
                    });
                }

                if (teamStats.punctualityRate < 80) {
                    recommendations.push({
                        type: 'team',
                        priority: 'متوسط',
                        title: 'تحسين الانضباط في المواعيد',
                        description: `${teamStats.lateePlayers} لاعب متأخر في الجلسة الحالية`,
                        actions: [
                            'إرسال تذكيرات قبل 30 دقيقة',
                            'تطبيق قواعد واضحة للتأخير',
                            'تحفيز الحضور المبكر'
                        ],
                        expectedImpact: 'تقليل التأخير بنسبة 50%'
                    });
                }

                return recommendations;
            }

            generatePlayerRecommendations(player, behavior) {
                const recommendations = [];

                if (behavior.riskLevel === 'عالي') {
                    recommendations.push({
                        type: 'player',
                        playerId: player.id,
                        playerName: player.name,
                        priority: 'عالي',
                        title: `متابعة خاصة للاعب ${player.name}`,
                        description: `معدل حضور منخفض: ${behavior.attendanceRate}%`,
                        actions: [
                            'اجتماع شخصي مع اللاعب',
                            'فهم الأسباب وراء الغياب',
                            'وضع خطة تحسين مخصصة',
                            'متابعة أسبوعية'
                        ],
                        expectedImpact: 'تحسين الحضور بنسبة 25%'
                    });
                }

                if (behavior.trend === 'متراجع') {
                    recommendations.push({
                        type: 'player',
                        playerId: player.id,
                        playerName: player.name,
                        priority: 'متوسط',
                        title: `تدخل مبكر للاعب ${player.name}`,
                        description: 'اتجاه متراجع في الحضور',
                        actions: [
                            'تحديد أسباب التراجع',
                            'تقديم الدعم المناسب',
                            'مراجعة الأهداف الشخصية'
                        ],
                        expectedImpact: 'منع تراجع إضافي'
                    });
                }

                return recommendations;
            }

            generateScheduleRecommendations() {
                const recommendations = [];

                // Analyze best times for training
                const timeAnalysis = this.patterns.correlations?.timeImpact || {};
                const bestTime = Object.keys(timeAnalysis).reduce((best, time) => {
                    return timeAnalysis[time].attendanceRate > (timeAnalysis[best]?.attendanceRate || 0) ? time : best;
                }, '17:00');

                if (bestTime) {
                    recommendations.push({
                        type: 'schedule',
                        priority: 'منخفض',
                        title: 'تحسين جدولة التدريبات',
                        description: `أفضل وقت للحضور هو ${bestTime}`,
                        actions: [
                            `جدولة المزيد من التدريبات في ${bestTime}`,
                            'تجنب الأوقات ذات الحضور المنخفض',
                            'مرونة في التوقيتات حسب الحاجة'
                        ],
                        expectedImpact: 'تحسين الحضور العام بنسبة 10%'
                    });
                }

                return recommendations;
            }

            // Setup Real-time Analysis
            setupRealTimeAnalysis() {
                // Update predictions every 5 minutes
                setInterval(() => {
                    this.generatePredictions();
                }, 5 * 60 * 1000);

                // Update patterns every hour
                setInterval(() => {
                    this.analyzePatterns();
                    this.createRecommendations();
                }, 60 * 60 * 1000);
            }

            // Get AI Insights for Display
            getInsights() {
                return {
                    patterns: this.patterns,
                    predictions: this.predictions,
                    recommendations: this.recommendations,
                    lastAnalysis: new Date().toISOString()
                };
            }
        }

        // ==================== Smart Notification System ====================

        class SmartNotificationSystem {
            constructor(aiEngine) {
                this.ai = aiEngine;
                this.notificationQueue = [];
                this.sentNotifications = this.loadSentNotifications();
                this.preferences = this.loadNotificationPreferences();
                this.init();
            }

            init() {
                this.setupProactiveNotifications();
                this.setupRealTimeAlerts();
                this.processNotificationQueue();
            }

            loadSentNotifications() {
                return JSON.parse(localStorage.getItem('sent_notifications') || '[]');
            }

            loadNotificationPreferences() {
                return JSON.parse(localStorage.getItem('notification_preferences') || JSON.stringify({
                    enableProactive: true,
                    enableRealTime: true,
                    reminderTime: 30, // minutes before session
                    channels: ['app', 'whatsapp', 'email'],
                    frequency: 'smart' // smart, daily, weekly
                }));
            }

            // Proactive Notification System
            setupProactiveNotifications() {
                // Check for at-risk players every hour
                setInterval(() => {
                    this.checkAtRiskPlayers();
                }, 60 * 60 * 1000);

                // Send session reminders
                this.scheduleSessionReminders();

                // Weekly performance reports
                this.scheduleWeeklyReports();
            }

            checkAtRiskPlayers() {
                const predictions = this.ai.predictions;
                const currentTime = new Date();

                players.forEach(player => {
                    const prediction = predictions[player.number];
                    if (!prediction) return;

                    // Check if player is at high risk of absence
                    if (prediction.nextSession.probability < 40) {
                        this.queueNotification({
                            type: 'risk_alert',
                            priority: 'high',
                            playerId: player.id,
                            playerName: player.name,
                            playerNumber: player.number,
                            title: 'تنبيه: احتمالية غياب عالية',
                            message: `اللاعب ${player.name} لديه احتمالية غياب ${100 - prediction.nextSession.probability}% في الجلسة القادمة`,
                            recommendations: prediction.nextSession.factors,
                            scheduledFor: new Date(currentTime.getTime() + 10 * 60 * 1000), // 10 minutes from now
                            channels: ['app', 'whatsapp']
                        });
                    }

                    // Check for declining trend
                    const behavior = this.ai.patterns.playerBehavior[player.number];
                    if (behavior && behavior.trend === 'متراجع' && !this.wasRecentlySent('trend_alert', player.number)) {
                        this.queueNotification({
                            type: 'trend_alert',
                            priority: 'medium',
                            playerId: player.id,
                            playerName: player.name,
                            playerNumber: player.number,
                            title: 'تنبيه: اتجاه متراجع في الحضور',
                            message: `لوحظ تراجع في حضور اللاعب ${player.name} خلال الفترة الأخيرة`,
                            recommendations: [
                                'التواصل مع اللاعب لفهم الأسباب',
                                'تقديم الدعم والتحفيز المناسب',
                                'مراجعة الأهداف الشخصية'
                            ],
                            scheduledFor: new Date(currentTime.getTime() + 30 * 60 * 1000),
                            channels: ['app']
                        });
                    }
                });
            }

            scheduleSessionReminders() {
                // This would integrate with actual session schedule
                // For demo, we'll simulate upcoming sessions
                const upcomingSessions = [
                    { date: new Date(Date.now() + 24 * 60 * 60 * 1000), time: '17:00' }, // Tomorrow
                    { date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), time: '17:00' } // Day after
                ];

                upcomingSessions.forEach(session => {
                    const reminderTime = new Date(session.date.getTime() - this.preferences.reminderTime * 60 * 1000);

                    players.forEach(player => {
                        const prediction = this.ai.predictions[player.number];

                        // Customize reminder based on prediction
                        let reminderType = 'standard';
                        let message = `تذكير: لديك جلسة تدريب غداً في ${session.time}`;

                        if (prediction && prediction.nextSession.probability < 60) {
                            reminderType = 'encouraging';
                            message = `🌟 نتطلع لرؤيتك غداً! جلسة التدريب في ${session.time} - حضورك مهم للفريق`;
                        } else if (prediction && prediction.nextSession.probability > 90) {
                            reminderType = 'appreciation';
                            message = `شكراً لانتظامك! جلسة التدريب غداً في ${session.time} كالمعتاد`;
                        }

                        this.queueNotification({
                            type: 'session_reminder',
                            subType: reminderType,
                            priority: 'normal',
                            playerId: player.id,
                            playerName: player.name,
                            playerNumber: player.number,
                            title: 'تذكير جلسة التدريب',
                            message: message,
                            sessionDate: session.date.toISOString(),
                            sessionTime: session.time,
                            scheduledFor: reminderTime,
                            channels: this.getPreferredChannels(player)
                        });
                    });
                });
            }

            scheduleWeeklyReports() {
                // Send weekly performance reports every Sunday
                const now = new Date();
                const nextSunday = new Date(now);
                nextSunday.setDate(now.getDate() + (7 - now.getDay()));
                nextSunday.setHours(9, 0, 0, 0); // 9 AM on Sunday

                setTimeout(() => {
                    this.generateWeeklyReports();

                    // Schedule for next week
                    setInterval(() => {
                        this.generateWeeklyReports();
                    }, 7 * 24 * 60 * 60 * 1000); // Weekly
                }, nextSunday.getTime() - now.getTime());
            }

            generateWeeklyReports() {
                players.forEach(player => {
                    const behavior = this.ai.patterns.playerBehavior[player.number];
                    const prediction = this.ai.predictions[player.number];

                    if (!behavior) return;

                    const report = this.createWeeklyReport(player, behavior, prediction);

                    this.queueNotification({
                        type: 'weekly_report',
                        priority: 'low',
                        playerId: player.id,
                        playerName: player.name,
                        playerNumber: player.number,
                        title: 'تقرير الأداء الأسبوعي',
                        message: report.summary,
                        fullReport: report,
                        scheduledFor: new Date(),
                        channels: ['app', 'email']
                    });
                });
            }

            createWeeklyReport(player, behavior, prediction) {
                const report = {
                    playerName: player.name,
                    playerNumber: player.number,
                    period: 'الأسبوع الماضي',
                    summary: '',
                    metrics: {
                        attendanceRate: behavior.attendanceRate,
                        punctualityRate: behavior.punctualityRate,
                        trend: behavior.trend,
                        reliability: behavior.reliability
                    },
                    highlights: [],
                    recommendations: [],
                    nextWeekPrediction: prediction?.weeklyForecast || {}
                };

                // Generate summary
                if (behavior.attendanceRate >= 90) {
                    report.summary = `أداء ممتاز! حافظت على معدل حضور ${behavior.attendanceRate}%`;
                    report.highlights.push('حضور منتظم ومثالي');
                } else if (behavior.attendanceRate >= 75) {
                    report.summary = `أداء جيد بمعدل حضور ${behavior.attendanceRate}%، يمكن التحسين أكثر`;
                    report.recommendations.push('استهدف الوصول لمعدل 90% أو أكثر');
                } else {
                    report.summary = `يحتاج تحسين - معدل الحضور ${behavior.attendanceRate}% أقل من المطلوب`;
                    report.recommendations.push('ضرورة تحسين الانتظام في الحضور');
                }

                // Add trend analysis
                if (behavior.trend === 'متحسن') {
                    report.highlights.push('اتجاه إيجابي في التحسن');
                } else if (behavior.trend === 'متراجع') {
                    report.recommendations.push('مراجعة الأسباب وراء التراجع');
                }

                return report;
            }

            // Real-time Alert System
            setupRealTimeAlerts() {
                // Monitor current session for real-time alerts
                setInterval(() => {
                    this.checkRealTimeAlerts();
                }, 2 * 60 * 1000); // Every 2 minutes
            }

            checkRealTimeAlerts() {
                const currentTime = new Date();
                const sessionStartTime = currentSession.startTime;
                const minutesElapsed = Math.floor((currentTime - sessionStartTime) / (1000 * 60));

                // Alert for players who haven't arrived after 15 minutes
                if (minutesElapsed >= 15) {
                    const absentPlayers = players.filter(p => p.status === 'pending');

                    absentPlayers.forEach(player => {
                        if (!this.wasRecentlySent('late_alert', player.number, 30)) { // Don't spam
                            this.queueNotification({
                                type: 'late_alert',
                                priority: 'high',
                                playerId: player.id,
                                playerName: player.name,
                                playerNumber: player.number,
                                title: 'تنبيه: تأخير في الحضور',
                                message: `اللاعب ${player.name} لم يصل بعد - الجلسة بدأت منذ ${minutesElapsed} دقيقة`,
                                scheduledFor: new Date(),
                                channels: ['app', 'whatsapp']
                            });
                        }
                    });
                }

                // Alert for low attendance rate in current session
                const attendanceRate = Math.round((players.filter(p => p.status === 'present').length / players.length) * 100);
                if (attendanceRate < 60 && minutesElapsed >= 20 && !this.wasRecentlySent('low_attendance', 'session', 60)) {
                    this.queueNotification({
                        type: 'low_attendance',
                        priority: 'medium',
                        title: 'تنبيه: معدل حضور منخفض',
                        message: `معدل الحضور الحالي ${attendanceRate}% - قد تحتاج لإجراءات إضافية`,
                        scheduledFor: new Date(),
                        channels: ['app']
                    });
                }
            }

            // Notification Queue Management
            queueNotification(notification) {
                notification.id = this.generateNotificationId();
                notification.createdAt = new Date().toISOString();
                notification.status = 'queued';

                this.notificationQueue.push(notification);
                this.saveNotificationQueue();
            }

            processNotificationQueue() {
                setInterval(() => {
                    const now = new Date();
                    const readyNotifications = this.notificationQueue.filter(n =>
                        n.status === 'queued' && new Date(n.scheduledFor) <= now
                    );

                    readyNotifications.forEach(notification => {
                        this.sendNotification(notification);
                    });
                }, 30 * 1000); // Check every 30 seconds
            }

            sendNotification(notification) {
                // Mark as sending
                notification.status = 'sending';
                notification.sentAt = new Date().toISOString();

                // Send through different channels
                notification.channels.forEach(channel => {
                    this.sendThroughChannel(notification, channel);
                });

                // Mark as sent
                notification.status = 'sent';
                this.sentNotifications.push(notification);

                // Remove from queue
                this.notificationQueue = this.notificationQueue.filter(n => n.id !== notification.id);

                this.saveNotificationQueue();
                this.saveSentNotifications();

                // Show in UI
                this.displayNotificationInUI(notification);
            }

            sendThroughChannel(notification, channel) {
                switch (channel) {
                    case 'app':
                        this.sendAppNotification(notification);
                        break;
                    case 'whatsapp':
                        this.sendWhatsAppNotification(notification);
                        break;
                    case 'email':
                        this.sendEmailNotification(notification);
                        break;
                }
            }

            sendAppNotification(notification) {
                // Display in-app notification
                showNotification(notification.message, this.getPriorityType(notification.priority));
            }

            sendWhatsAppNotification(notification) {
                // In real implementation, this would use WhatsApp Business API
                const message = `🏃‍♂️ أكاديمية 7C

${notification.title}

${notification.message}

${notification.recommendations ?
    '📋 التوصيات:\n' + notification.recommendations.map(r => `• ${r}`).join('\n') : ''}

تم إرسال هذه الرسالة تلقائياً من نظام إدارة الحضور الذكي.`;

                // Simulate WhatsApp sending
                console.log('WhatsApp notification sent:', message);

                // In real app, you would open WhatsApp with pre-filled message
                // const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
                // window.open(whatsappUrl, '_blank');
            }

            sendEmailNotification(notification) {
                // In real implementation, this would use email service
                const emailContent = {
                    to: `player_${notification.playerNumber}@academy7c.com`,
                    subject: notification.title,
                    body: this.generateEmailBody(notification)
                };

                console.log('Email notification sent:', emailContent);
            }

            generateEmailBody(notification) {
                return `
                    <div style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                        <h2 style="color: #8B4513;">${notification.title}</h2>
                        <p>${notification.message}</p>

                        ${notification.fullReport ? this.generateReportHTML(notification.fullReport) : ''}

                        ${notification.recommendations ? `
                            <h3>التوصيات:</h3>
                            <ul>
                                ${notification.recommendations.map(r => `<li>${r}</li>`).join('')}
                            </ul>
                        ` : ''}

                        <hr>
                        <p style="font-size: 12px; color: #666;">
                            تم إرسال هذه الرسالة تلقائياً من نظام إدارة الحضور الذكي - أكاديمية 7C
                        </p>
                    </div>
                `;
            }

            generateReportHTML(report) {
                return `
                    <div style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0;">
                        <h3>تقرير الأداء التفصيلي</h3>
                        <p><strong>الفترة:</strong> ${report.period}</p>
                        <p><strong>معدل الحضور:</strong> ${report.metrics.attendanceRate}%</p>
                        <p><strong>معدل الانضباط:</strong> ${report.metrics.punctualityRate}%</p>
                        <p><strong>الاتجاه:</strong> ${report.metrics.trend}</p>

                        ${report.highlights.length > 0 ? `
                            <h4>النقاط المميزة:</h4>
                            <ul>${report.highlights.map(h => `<li>${h}</li>`).join('')}</ul>
                        ` : ''}
                    </div>
                `;
            }

            // Utility Functions
            wasRecentlySent(type, identifier, minutesAgo = 60) {
                const cutoff = new Date(Date.now() - minutesAgo * 60 * 1000);
                return this.sentNotifications.some(n =>
                    n.type === type &&
                    (n.playerNumber === identifier || n.sessionId === identifier) &&
                    new Date(n.sentAt) > cutoff
                );
            }

            getPreferredChannels(player) {
                // In real app, this would be based on player preferences
                return this.preferences.channels;
            }

            getPriorityType(priority) {
                const mapping = {
                    'high': 'error',
                    'medium': 'warning',
                    'normal': 'info',
                    'low': 'info'
                };
                return mapping[priority] || 'info';
            }

            generateNotificationId() {
                return 'notif_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            }

            displayNotificationInUI(notification) {
                // Create notification element in UI
                const notificationElement = document.createElement('div');
                notificationElement.className = 'notification-item bg-gray-800 border-l-4 border-orange-500 p-4 mb-2 rounded';
                notificationElement.innerHTML = `
                    <div class="flex justify-between items-start">
                        <div>
                            <h4 class="font-bold text-orange-400">${notification.title}</h4>
                            <p class="text-sm text-gray-300">${notification.message}</p>
                            <span class="text-xs text-gray-500">${new Date(notification.sentAt).toLocaleString('ar-SA')}</span>
                        </div>
                        <span class="priority-badge priority-${notification.priority}">${notification.priority}</span>
                    </div>
                `;

                // Add to notifications panel if exists
                const notificationsPanel = document.getElementById('notificationsPanel');
                if (notificationsPanel) {
                    notificationsPanel.insertBefore(notificationElement, notificationsPanel.firstChild);

                    // Keep only last 10 notifications
                    while (notificationsPanel.children.length > 10) {
                        notificationsPanel.removeChild(notificationsPanel.lastChild);
                    }
                }
            }

            // Storage Functions
            saveNotificationQueue() {
                localStorage.setItem('notification_queue', JSON.stringify(this.notificationQueue));
            }

            saveSentNotifications() {
                // Keep only last 100 sent notifications
                if (this.sentNotifications.length > 100) {
                    this.sentNotifications = this.sentNotifications.slice(-100);
                }
                localStorage.setItem('sent_notifications', JSON.stringify(this.sentNotifications));
            }

            // Public API
            getNotificationStats() {
                const last24h = new Date(Date.now() - 24 * 60 * 60 * 1000);
                const recent = this.sentNotifications.filter(n => new Date(n.sentAt) > last24h);

                return {
                    totalSent: this.sentNotifications.length,
                    last24Hours: recent.length,
                    queued: this.notificationQueue.length,
                    byType: this.groupNotificationsByType(recent),
                    byPriority: this.groupNotificationsByPriority(recent)
                };
            }

            groupNotificationsByType(notifications) {
                return notifications.reduce((acc, n) => {
                    acc[n.type] = (acc[n.type] || 0) + 1;
                    return acc;
                }, {});
            }

            groupNotificationsByPriority(notifications) {
                return notifications.reduce((acc, n) => {
                    acc[n.priority] = (acc[n.priority] || 0) + 1;
                    return acc;
                }, {});
            }
        }
    </script>
</body>
</html>
