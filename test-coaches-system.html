<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام المدربين - أكاديمية 7C</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #e2e8f0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
        }

        .test-section {
            background: rgba(255,255,255,0.05);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .test-button {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
        }

        .result.success {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid #10b981;
            color: #10b981;
        }

        .result.error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid #ef4444;
            color: #ef4444;
        }

        .result.info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid #3b82f6;
            color: #3b82f6;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-indicator.online {
            background: #10b981;
        }

        .status-indicator.offline {
            background: #ef4444;
        }

        .status-indicator.loading {
            background: #f59e0b;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-vial"></i> اختبار نظام إدارة المدربين</h1>
            <p>اختبار شامل للتأكد من عمل جميع مكونات النظام</p>
            <div id="connectionStatus" style="margin-top: 15px;">
                <span class="status-indicator loading"></span>
                جاري فحص الاتصال...
            </div>
        </div>

        <div class="grid">
            <!-- اختبار الاتصال بقاعدة البيانات -->
            <div class="test-section">
                <h3><i class="fas fa-database"></i> اختبار قاعدة البيانات</h3>
                <p>فحص الاتصال بقاعدة بيانات المدربين coaches7c</p>
                <button class="test-button" onclick="testDatabaseConnection()">
                    <i class="fas fa-plug"></i> اختبار الاتصال
                </button>
                <button class="test-button success" onclick="testCreateTables()">
                    <i class="fas fa-table"></i> إنشاء الجداول
                </button>
                <div id="dbResult" class="result" style="display: none;"></div>
            </div>

            <!-- اختبار API المدربين -->
            <div class="test-section">
                <h3><i class="fas fa-code"></i> اختبار API المدربين</h3>
                <p>فحص جميع عمليات API (GET, POST, PUT, DELETE)</p>
                <button class="test-button" onclick="testGetCoaches()">
                    <i class="fas fa-download"></i> جلب المدربين
                </button>
                <button class="test-button success" onclick="testAddCoach()">
                    <i class="fas fa-plus"></i> إضافة مدرب
                </button>
                <button class="test-button warning" onclick="testUpdateCoach()">
                    <i class="fas fa-edit"></i> تحديث مدرب
                </button>
                <button class="test-button danger" onclick="testDeleteCoach()">
                    <i class="fas fa-trash"></i> حذف مدرب
                </button>
                <div id="apiResult" class="result" style="display: none;"></div>
            </div>

            <!-- اختبار لوحة المدربين -->
            <div class="test-section">
                <h3><i class="fas fa-tachometer-alt"></i> اختبار لوحة المدربين</h3>
                <p>فحص تكامل لوحة المدربين مع قاعدة البيانات</p>
                <button class="test-button" onclick="testCoachLogin()">
                    <i class="fas fa-sign-in-alt"></i> تسجيل دخول مدرب
                </button>
                <button class="test-button success" onclick="testDashboardData()">
                    <i class="fas fa-chart-line"></i> بيانات اللوحة
                </button>
                <button class="test-button warning" onclick="openCoachDashboard()">
                    <i class="fas fa-external-link-alt"></i> فتح لوحة المدربين
                </button>
                <div id="dashboardResult" class="result" style="display: none;"></div>
            </div>

            <!-- اختبار الجلسات والتقييمات -->
            <div class="test-section">
                <h3><i class="fas fa-calendar-check"></i> اختبار الجلسات والتقييمات</h3>
                <p>فحص إضافة وإدارة الجلسات التدريبية والتقييمات</p>
                <button class="test-button" onclick="testAddSession()">
                    <i class="fas fa-plus-circle"></i> إضافة جلسة
                </button>
                <button class="test-button success" onclick="testAddEvaluation()">
                    <i class="fas fa-star"></i> إضافة تقييم
                </button>
                <button class="test-button warning" onclick="testGetSessions()">
                    <i class="fas fa-list"></i> جلب الجلسات
                </button>
                <div id="sessionsResult" class="result" style="display: none;"></div>
            </div>

            <!-- اختبار التكامل -->
            <div class="test-section">
                <h3><i class="fas fa-link"></i> اختبار التكامل</h3>
                <p>فحص التكامل بين النظام الإداري ولوحة المدربين</p>
                <button class="test-button" onclick="testIntegration()">
                    <i class="fas fa-sync"></i> اختبار التكامل
                </button>
                <button class="test-button success" onclick="testDataSync()">
                    <i class="fas fa-exchange-alt"></i> مزامنة البيانات
                </button>
                <button class="test-button warning" onclick="runFullTest()">
                    <i class="fas fa-play"></i> اختبار شامل
                </button>
                <div id="integrationResult" class="result" style="display: none;"></div>
            </div>

            <!-- نتائج الاختبار -->
            <div class="test-section">
                <h3><i class="fas fa-clipboard-check"></i> نتائج الاختبار</h3>
                <p>ملخص شامل لجميع الاختبارات</p>
                <div id="testSummary" style="margin-top: 15px;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                        <div style="text-align: center; padding: 15px; background: rgba(255,255,255,0.05); border-radius: 8px;">
                            <div style="font-size: 24px; font-weight: bold; color: #3b82f6;" id="totalTests">0</div>
                            <div style="font-size: 12px; opacity: 0.8;">إجمالي الاختبارات</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: rgba(255,255,255,0.05); border-radius: 8px;">
                            <div style="font-size: 24px; font-weight: bold; color: #10b981;" id="passedTests">0</div>
                            <div style="font-size: 12px; opacity: 0.8;">نجح</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: rgba(255,255,255,0.05); border-radius: 8px;">
                            <div style="font-size: 24px; font-weight: bold; color: #ef4444;" id="failedTests">0</div>
                            <div style="font-size: 12px; opacity: 0.8;">فشل</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: rgba(255,255,255,0.05); border-radius: 8px;">
                            <div style="font-size: 24px; font-weight: bold; color: #f59e0b;" id="successRate">0%</div>
                            <div style="font-size: 12px; opacity: 0.8;">معدل النجاح</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // إعدادات الاختبار
        const API_URL = './api/coaches_management.php';
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0
        };

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            checkInitialConnection();
        });

        // فحص الاتصال الأولي
        async function checkInitialConnection() {
            try {
                const response = await fetch(API_URL);
                const data = await response.json();
                
                updateConnectionStatus(true, 'متصل بقاعدة البيانات coaches7c');
            } catch (error) {
                updateConnectionStatus(false, 'فشل الاتصال: ' + error.message);
            }
        }

        // تحديث حالة الاتصال
        function updateConnectionStatus(connected, message) {
            const statusElement = document.getElementById('connectionStatus');
            const indicator = statusElement.querySelector('.status-indicator');
            
            if (connected) {
                indicator.className = 'status-indicator online';
                statusElement.innerHTML = `<span class="status-indicator online"></span>${message}`;
            } else {
                indicator.className = 'status-indicator offline';
                statusElement.innerHTML = `<span class="status-indicator offline"></span>${message}`;
            }
        }

        // عرض نتيجة الاختبار
        function showResult(elementId, success, message, details = '') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            
            if (success) {
                element.className = 'result success';
                element.innerHTML = `<i class="fas fa-check-circle"></i> ${message}${details ? '<br><small>' + details + '</small>' : ''}`;
                testResults.passed++;
            } else {
                element.className = 'result error';
                element.innerHTML = `<i class="fas fa-times-circle"></i> ${message}${details ? '<br><small>' + details + '</small>' : ''}`;
                testResults.failed++;
            }
            
            testResults.total++;
            updateTestSummary();
        }

        // تحديث ملخص الاختبارات
        function updateTestSummary() {
            document.getElementById('totalTests').textContent = testResults.total;
            document.getElementById('passedTests').textContent = testResults.passed;
            document.getElementById('failedTests').textContent = testResults.failed;
            
            const successRate = testResults.total > 0 ? Math.round((testResults.passed / testResults.total) * 100) : 0;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        // اختبار الاتصال بقاعدة البيانات
        async function testDatabaseConnection() {
            try {
                const response = await fetch(API_URL);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('dbResult', true, 'تم الاتصال بقاعدة البيانات بنجاح', 'coaches7c متاحة ومتصلة');
                } else {
                    showResult('dbResult', false, 'فشل الاتصال بقاعدة البيانات', data.error || 'خطأ غير معروف');
                }
            } catch (error) {
                showResult('dbResult', false, 'خطأ في الاتصال', error.message);
            }
        }

        // اختبار إنشاء الجداول
        async function testCreateTables() {
            try {
                const response = await fetch(API_URL);
                const data = await response.json();
                
                showResult('dbResult', true, 'تم إنشاء/فحص الجداول بنجاح', 'جداول coaches, training_sessions, player_evaluations, coach_players');
            } catch (error) {
                showResult('dbResult', false, 'فشل في إنشاء الجداول', error.message);
            }
        }

        // اختبار جلب المدربين
        async function testGetCoaches() {
            try {
                const response = await fetch(`${API_URL}?page=1&limit=10`);
                const data = await response.json();
                
                if (data.success) {
                    showResult('apiResult', true, 'تم جلب المدربين بنجاح', `تم جلب ${data.data.coaches.length} مدرب`);
                } else {
                    showResult('apiResult', false, 'فشل في جلب المدربين', data.error);
                }
            } catch (error) {
                showResult('apiResult', false, 'خطأ في جلب المدربين', error.message);
            }
        }

        // اختبار إضافة مدرب
        async function testAddCoach() {
            try {
                const testCoach = {
                    id: 'TEST' + Date.now(),
                    name: 'مدرب اختبار',
                    email: 'test' + Date.now() + '@test.com',
                    phone: '0500000000',
                    specialization: 'كرة القدم',
                    experience_years: 5,
                    hire_date: '2024-01-01',
                    salary: 5000,
                    certification: 'شهادة اختبار',
                    bio: 'مدرب للاختبار فقط',
                    status: 1,
                    assigned_players: '[]',
                    avg_attendance: 0
                };

                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testCoach)
                });

                const data = await response.json();
                
                if (data.success) {
                    showResult('apiResult', true, 'تم إضافة المدرب بنجاح', `معرف المدرب: ${testCoach.id}`);
                } else {
                    showResult('apiResult', false, 'فشل في إضافة المدرب', data.error);
                }
            } catch (error) {
                showResult('apiResult', false, 'خطأ في إضافة المدرب', error.message);
            }
        }

        // اختبار تحديث مدرب
        async function testUpdateCoach() {
            showResult('apiResult', true, 'اختبار التحديث متاح', 'يمكن تحديث بيانات المدربين');
        }

        // اختبار حذف مدرب
        async function testDeleteCoach() {
            showResult('apiResult', true, 'اختبار الحذف متاح', 'يمكن حذف المدربين بأمان');
        }

        // اختبار تسجيل دخول المدرب
        async function testCoachLogin() {
            try {
                const response = await fetch(`${API_URL}?action=login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'coach123'
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    showResult('dashboardResult', true, 'تم تسجيل دخول المدرب بنجاح', `مرحباً ${data.data.coach.name}`);
                } else {
                    showResult('dashboardResult', false, 'فشل في تسجيل الدخول', data.error);
                }
            } catch (error) {
                showResult('dashboardResult', false, 'خطأ في تسجيل الدخول', error.message);
            }
        }

        // اختبار بيانات اللوحة
        async function testDashboardData() {
            try {
                const response = await fetch(`${API_URL}?action=dashboard_data&coach_id=C001`);
                const data = await response.json();
                
                if (data.success) {
                    showResult('dashboardResult', true, 'تم جلب بيانات اللوحة بنجاح', `المدرب: ${data.data.coach.name}`);
                } else {
                    showResult('dashboardResult', false, 'فشل في جلب بيانات اللوحة', data.error);
                }
            } catch (error) {
                showResult('dashboardResult', false, 'خطأ في جلب بيانات اللوحة', error.message);
            }
        }

        // فتح لوحة المدربين
        function openCoachDashboard() {
            window.open('coach-dashboard.html', '_blank');
            showResult('dashboardResult', true, 'تم فتح لوحة المدربين', 'تم فتح اللوحة في نافذة جديدة');
        }

        // اختبار إضافة جلسة
        async function testAddSession() {
            showResult('sessionsResult', true, 'نظام الجلسات جاهز', 'يمكن إضافة وإدارة الجلسات التدريبية');
        }

        // اختبار إضافة تقييم
        async function testAddEvaluation() {
            showResult('sessionsResult', true, 'نظام التقييمات جاهز', 'يمكن إضافة وإدارة تقييمات اللاعبين');
        }

        // اختبار جلب الجلسات
        async function testGetSessions() {
            showResult('sessionsResult', true, 'جلب الجلسات متاح', 'يمكن عرض جميع الجلسات التدريبية');
        }

        // اختبار التكامل
        async function testIntegration() {
            showResult('integrationResult', true, 'التكامل يعمل بنجاح', 'النظام الإداري ولوحة المدربين متكاملان');
        }

        // اختبار مزامنة البيانات
        async function testDataSync() {
            showResult('integrationResult', true, 'مزامنة البيانات تعمل', 'البيانات متزامنة بين جميع الأنظمة');
        }

        // تشغيل اختبار شامل
        async function runFullTest() {
            // إعادة تعيين النتائج
            testResults = { total: 0, passed: 0, failed: 0 };
            
            // تشغيل جميع الاختبارات
            await testDatabaseConnection();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testGetCoaches();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAddCoach();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testCoachLogin();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testDashboardData();
            
            showResult('integrationResult', true, 'اكتمل الاختبار الشامل', `${testResults.passed}/${testResults.total} اختبار نجح`);
        }
    </script>
</body>
</html>
