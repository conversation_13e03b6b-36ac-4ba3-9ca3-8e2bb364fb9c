<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم اللاعب - أكاديمية 7C الرياضية</title>
    
    <!-- External Libraries -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.0/dist/sweetalert2.all.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    
    <style>
        /* ==================== متغيرات الألوان الأساسية ==================== */
        :root {
            --primary-bg: #1a1a1a;
            --brand-primary: #8B4513;
            --brand-secondary: #D2691E;
            --accent-dark: #2F4F4F;
            --success: #28a745;
            --danger: #dc3545;
            --warning: #ffc107;
            --info: #17a2b8;
            --glass: rgba(255,255,255,0.1);
            --border: rgba(255,255,255,0.2);
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --shadow-dark: 0 8px 32px rgba(0, 0, 0, 0.3);
            --shadow-glow: 0 0 20px rgba(139, 69, 19, 0.3);
        }

        /* ==================== إعدادات أساسية ==================== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--primary-bg);
            color: var(--text-primary);
            overflow-x: hidden;
        }

        /* ==================== التخطيط الرئيسي ==================== */
        .main-layout {
            display: grid;
            grid-template-columns: 280px 1fr;
            grid-template-rows: 70px 1fr;
            grid-template-areas: 
                "sidebar header"
                "sidebar content";
            min-height: 100vh;
        }

        /* ==================== الشريط الجانبي ==================== */
        .sidebar {
            grid-area: sidebar;
            background: linear-gradient(180deg, var(--brand-primary) 0%, var(--accent-dark) 100%);
            border-left: 1px solid var(--border);
            overflow-y: auto;
            transition: all 0.3s ease;
            position: relative;
        }

        .sidebar-header {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid var(--border);
            background: rgba(0, 0, 0, 0.2);
        }

        .player-avatar {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--brand-secondary), #ff8c42);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            font-size: 2rem;
            color: white;
            box-shadow: var(--shadow-glow);
            animation: pulse 3s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .player-name {
            font-size: 1.1rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.25rem;
        }

        .player-level {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.7);
            background: rgba(255, 255, 255, 0.1);
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            display: inline-block;
        }

        /* ==================== قائمة التنقل ==================== */
        .nav-menu {
            padding: 1rem 0;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
            position: relative;
            cursor: pointer;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-right-color: var(--brand-secondary);
            transform: translateX(-5px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border-right-color: var(--brand-secondary);
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
        }

        .nav-icon {
            width: 20px;
            margin-left: 1rem;
            font-size: 1.1rem;
        }

        .nav-text {
            font-weight: 600;
            font-size: 0.95rem;
        }

        .nav-badge {
            margin-right: auto;
            background: var(--brand-secondary);
            color: white;
            font-size: 0.7rem;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            font-weight: 600;
        }

        /* ==================== رأس الصفحة ==================== */
        .header {
            grid-area: header;
            background: var(--glass);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 2rem;
            box-shadow: var(--shadow-dark);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--brand-primary);
        }

        .breadcrumb {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-btn {
            background: var(--glass);
            border: 1px solid var(--border);
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .header-btn:hover {
            background: var(--brand-primary);
            transform: translateY(-2px);
        }

        .notification-btn {
            position: relative;
        }

        .notification-count {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--danger);
            color: white;
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
            border-radius: 50%;
            min-width: 18px;
            text-align: center;
        }

        /* ==================== المحتوى الرئيسي ==================== */
        .content {
            grid-area: content;
            padding: 2rem;
            overflow-y: auto;
            background: linear-gradient(135deg, var(--primary-bg) 0%, #1e1e2e 100%);
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* ==================== بطاقات الإحصائيات ==================== */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-dark);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--brand-primary), var(--brand-secondary));
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 600;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stat-change {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .stat-change.positive {
            color: var(--success);
        }

        .stat-change.negative {
            color: var(--danger);
        }

        .stat-change.neutral {
            color: var(--text-secondary);
        }

        /* ==================== البطاقات العامة ==================== */
        .card {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .card-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border);
            border-radius: 8px;
            background: var(--glass);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn:hover {
            background: var(--brand-primary);
            transform: translateY(-2px);
        }

        .btn.primary {
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            border-color: var(--brand-primary);
        }

        /* ==================== تصميم متجاوب ==================== */
        @media (max-width: 1024px) {
            .main-layout {
                grid-template-columns: 1fr;
                grid-template-areas: 
                    "header"
                    "content";
            }
            
            .sidebar {
                position: fixed;
                left: -280px;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            
            .sidebar.open {
                left: 0;
            }
        }

        @media (max-width: 768px) {
            .content {
                padding: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .header {
                padding: 0 1rem;
            }
            
            .page-title {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-layout">
        <!-- الشريط الجانبي -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="player-avatar" id="playerAvatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="player-name" id="playerName">محمد اللاعب</div>
                <div class="player-level" id="playerLevel">مستوى متقدم</div>
            </div>

            <nav class="nav-menu">
                <div class="nav-item">
                    <div class="nav-link active" onclick="showSection('overview')">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">نظرة عامة</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('profile')">
                        <i class="fas fa-user-edit nav-icon"></i>
                        <span class="nav-text">الملف الشخصي</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('schedule')">
                        <i class="fas fa-calendar-alt nav-icon"></i>
                        <span class="nav-text">الجدول التدريبي</span>
                        <span class="nav-badge" id="upcomingSessionsCount">3</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('goals')">
                        <i class="fas fa-bullseye nav-icon"></i>
                        <span class="nav-text">الأهداف الشخصية</span>
                        <span class="nav-badge" id="activeGoalsCount">5</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('payments')">
                        <i class="fas fa-credit-card nav-icon"></i>
                        <span class="nav-text">المدفوعات</span>
                        <span class="nav-badge" id="pendingPaymentsCount">1</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('messages')">
                        <i class="fas fa-envelope nav-icon"></i>
                        <span class="nav-text">الرسائل</span>
                        <span class="nav-badge" id="unreadMessagesCount">4</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('achievements')">
                        <i class="fas fa-trophy nav-icon"></i>
                        <span class="nav-text">الإنجازات</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="logout()">
                        <i class="fas fa-sign-out-alt nav-icon"></i>
                        <span class="nav-text">تسجيل الخروج</span>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- رأس الصفحة -->
        <header class="header">
            <div class="header-left">
                <button class="header-btn" onclick="toggleSidebar()" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div>
                    <h1 class="page-title" id="pageTitle">نظرة عامة</h1>
                    <div class="breadcrumb" id="breadcrumb">الرئيسية / نظرة عامة</div>
                </div>
            </div>

            <div class="header-right">
                <button class="header-btn notification-btn" onclick="showNotifications()">
                    <i class="fas fa-bell"></i>
                    <span class="notification-count" id="notificationCount">3</span>
                </button>

                <button class="header-btn" onclick="showQuickActions()">
                    <i class="fas fa-plus"></i>
                    إجراء سريع
                </button>

                <button class="header-btn" onclick="exportPersonalReport()">
                    <i class="fas fa-download"></i>
                    تصدير التقرير
                </button>
            </div>
        </header>

        <!-- المحتوى الرئيسي -->
        <main class="content">
            <!-- قسم النظرة العامة -->
            <div id="overviewSection" class="content-section active">
                <!-- بطاقات الإحصائيات -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">معدل الحضور</div>
                            <div class="stat-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="attendanceRate">85%</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+5% من الشهر الماضي</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">درجة الأداء</div>
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="performanceScore">92</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+8 نقاط هذا الأسبوع</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">الحصص المكتملة</div>
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="completedSessions">24</div>
                        <div class="stat-change neutral">
                            <i class="fas fa-minus"></i>
                            <span>من أصل 28 هذا الشهر</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">الأهداف المحققة</div>
                            <div class="stat-icon">
                                <i class="fas fa-bullseye"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="achievedGoals">8</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>من أصل 12 هدف</span>
                        </div>
                    </div>
                </div>

                <!-- رسم بياني للأداء -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">تطور الأداء (آخر 6 أشهر)</h3>
                        <div class="card-actions">
                            <button class="btn" onclick="changeChartPeriod('3months')">3 أشهر</button>
                            <button class="btn primary" onclick="changeChartPeriod('6months')">6 أشهر</button>
                            <button class="btn" onclick="changeChartPeriod('1year')">سنة</button>
                        </div>
                    </div>
                    <div style="position: relative; height: 300px;">
                        <canvas id="performanceChart"></canvas>
                    </div>
                </div>

                <!-- الحصص القادمة -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">الحصص القادمة</h3>
                        <div class="card-actions">
                            <button class="btn" onclick="viewFullSchedule()">
                                <i class="fas fa-calendar"></i>
                                عرض الجدول الكامل
                            </button>
                        </div>
                    </div>
                    <div id="upcomingSessionsList">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <!-- قسم الملف الشخصي -->
            <div id="profileSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">الملف الشخصي</h3>
                        <div class="card-actions">
                            <button class="btn primary" onclick="editProfile()">
                                <i class="fas fa-edit"></i>
                                تعديل البيانات
                            </button>
                            <button class="btn" onclick="changeProfilePicture()">
                                <i class="fas fa-camera"></i>
                                تغيير الصورة
                            </button>
                        </div>
                    </div>
                    <div id="profileContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">إحصائيات الأداء التفصيلية</h3>
                    </div>
                    <div id="detailedStatsContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <!-- قسم الجدول التدريبي -->
            <div id="scheduleSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">الجدول التدريبي</h3>
                        <div class="card-actions">
                            <button class="btn" onclick="filterSchedule('all')" id="filterAll">الكل</button>
                            <button class="btn primary" onclick="filterSchedule('upcoming')" id="filterUpcoming">القادمة</button>
                            <button class="btn" onclick="filterSchedule('completed')" id="filterCompleted">المكتملة</button>
                            <button class="btn" onclick="filterSchedule('cancelled')" id="filterCancelled">الملغية</button>
                        </div>
                    </div>
                    <div id="scheduleContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">إحصائيات الحضور</h3>
                    </div>
                    <div style="position: relative; height: 250px;">
                        <canvas id="attendanceChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- قسم الأهداف الشخصية -->
            <div id="goalsSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">الأهداف الشخصية</h3>
                        <div class="card-actions">
                            <button class="btn primary" onclick="addNewGoal()">
                                <i class="fas fa-plus"></i>
                                إضافة هدف جديد
                            </button>
                            <button class="btn" onclick="filterGoals('all')" id="goalsFilterAll">الكل</button>
                            <button class="btn" onclick="filterGoals('active')" id="goalsFilterActive">النشطة</button>
                            <button class="btn" onclick="filterGoals('completed')" id="goalsFilterCompleted">المكتملة</button>
                        </div>
                    </div>
                    <div id="goalsContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">تقدم الأهداف</h3>
                    </div>
                    <div style="position: relative; height: 300px;">
                        <canvas id="goalsChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- قسم المدفوعات -->
            <div id="paymentsSection" class="content-section">
                <div class="stats-grid" style="margin-bottom: 2rem;">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">حالة الاشتراك</div>
                            <div class="stat-icon">
                                <i class="fas fa-user-check"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="subscriptionStatus">نشط</div>
                        <div class="stat-change neutral">
                            <i class="fas fa-calendar"></i>
                            <span id="subscriptionEnd">ينتهي في 31 ديسمبر 2024</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">المدفوعات المعلقة</div>
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="pendingAmount">500</div>
                        <div class="stat-change negative">
                            <i class="fas fa-clock"></i>
                            <span>ريال سعودي</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">إجمالي المدفوعات</div>
                            <div class="stat-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="totalPaid">1150</div>
                        <div class="stat-change positive">
                            <i class="fas fa-check"></i>
                            <span>ريال سعودي</span>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">الفواتير والمدفوعات</h3>
                        <div class="card-actions">
                            <button class="btn primary" onclick="downloadAllInvoices()">
                                <i class="fas fa-download"></i>
                                تحميل جميع الفواتير
                            </button>
                            <button class="btn" onclick="filterPayments('all')" id="paymentsFilterAll">الكل</button>
                            <button class="btn" onclick="filterPayments('pending')" id="paymentsFilterPending">المعلقة</button>
                            <button class="btn" onclick="filterPayments('paid')" id="paymentsFilterPaid">المدفوعة</button>
                        </div>
                    </div>
                    <div id="paymentsContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <!-- قسم الرسائل -->
            <div id="messagesSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">صندوق الرسائل</h3>
                        <div class="card-actions">
                            <button class="btn primary" onclick="composeMessage()">
                                <i class="fas fa-pen"></i>
                                رسالة جديدة
                            </button>
                            <button class="btn" onclick="filterMessages('all')" id="messagesFilterAll">الكل</button>
                            <button class="btn" onclick="filterMessages('unread')" id="messagesFilterUnread">غير مقروءة</button>
                            <button class="btn" onclick="filterMessages('read')" id="messagesFilterRead">مقروءة</button>
                            <button class="btn" onclick="markAllAsRead()">
                                <i class="fas fa-check-double"></i>
                                تحديد الكل كمقروء
                            </button>
                        </div>
                    </div>
                    <div id="messagesContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <!-- قسم الإنجازات -->
            <div id="achievementsSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">الإنجازات والشهادات</h3>
                        <div class="card-actions">
                            <button class="btn primary" onclick="shareAchievement()">
                                <i class="fas fa-share"></i>
                                مشاركة الإنجاز
                            </button>
                            <button class="btn" onclick="downloadCertificates()">
                                <i class="fas fa-download"></i>
                                تحميل الشهادات
                            </button>
                            <button class="btn" onclick="viewAchievementTimeline()">
                                <i class="fas fa-timeline"></i>
                                الخط الزمني
                            </button>
                        </div>
                    </div>
                    <div id="achievementsContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">إحصائيات الإنجازات</h3>
                    </div>
                    <div style="position: relative; height: 250px;">
                        <canvas id="achievementsChart"></canvas>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // ==================== متغيرات عامة ====================
        let currentPlayer = null;
        let playerData = {};
        let performanceChart = null;

        // ==================== بيانات اللاعبين التجريبية ====================
        const playersDatabase = {
            'P001': {
                id: 'P001',
                name: 'محمد اللاعب',
                email: '<EMAIL>',
                phone: '0551122334',
                age: 18,
                gender: 'ذكر',
                level: 'متقدم',
                coach: 'كابتن أحمد المدرب',
                joinDate: '2024-01-15',
                avatar: null,
                attendanceRate: 85,
                performanceScore: 92,
                completedSessions: 24,
                totalSessions: 28,
                achievedGoals: 8,
                totalGoals: 12,
                subscriptionStatus: 'نشط',
                subscriptionEnd: '2024-12-31',
                monthlyFee: 500
            }
        };

        // ==================== بيانات الحصص التدريبية ====================
        const sessionsDatabase = [
            {
                id: 'S001',
                playerId: 'P001',
                title: 'تدريب كرة القدم - تقنيات الهجوم',
                coach: 'كابتن أحمد المدرب',
                date: '2024-06-25',
                time: '16:00',
                duration: 90,
                location: 'الملعب الرئيسي',
                status: 'قادمة',
                type: 'جماعي',
                description: 'تدريب على تقنيات الهجوم والتسديد'
            },
            {
                id: 'S002',
                playerId: 'P001',
                title: 'تدريب اللياقة البدنية',
                coach: 'كابتن أحمد المدرب',
                date: '2024-06-23',
                time: '17:00',
                duration: 60,
                location: 'صالة اللياقة',
                status: 'مكتملة',
                type: 'فردي',
                description: 'تدريب على تحسين اللياقة البدنية والقوة',
                rating: 9,
                notes: 'أداء ممتاز، تحسن ملحوظ في القوة'
            },
            {
                id: 'S003',
                playerId: 'P001',
                title: 'تدريب المهارات الفنية',
                coach: 'كابتن أحمد المدرب',
                date: '2024-06-27',
                time: '15:30',
                duration: 75,
                location: 'الملعب الفرعي',
                status: 'قادمة',
                type: 'جماعي',
                description: 'تدريب على المهارات الفنية والتحكم بالكرة'
            }
        ];

        // ==================== بيانات الأهداف الشخصية ====================
        const goalsDatabase = [
            {
                id: 'G001',
                playerId: 'P001',
                title: 'تحسين دقة التسديد',
                description: 'الوصول لدقة تسديد 80% في التدريبات',
                targetDate: '2024-08-01',
                progress: 65,
                status: 'نشط',
                category: 'مهارات فنية',
                createdDate: '2024-05-01'
            },
            {
                id: 'G002',
                playerId: 'P001',
                title: 'زيادة السرعة',
                description: 'تحسين زمن الجري 100 متر إلى أقل من 12 ثانية',
                targetDate: '2024-07-15',
                progress: 40,
                status: 'نشط',
                category: 'لياقة بدنية',
                createdDate: '2024-04-15'
            },
            {
                id: 'G003',
                playerId: 'P001',
                title: 'تطوير مهارة المراوغة',
                description: 'إتقان 5 حركات مراوغة جديدة',
                targetDate: '2024-09-01',
                progress: 80,
                status: 'نشط',
                category: 'مهارات فنية',
                createdDate: '2024-03-01'
            },
            {
                id: 'G004',
                playerId: 'P001',
                title: 'تحسين معدل الحضور',
                description: 'الوصول لمعدل حضور 95% في الشهر',
                targetDate: '2024-07-31',
                progress: 90,
                status: 'نشط',
                category: 'انضباط',
                createdDate: '2024-06-01'
            },
            {
                id: 'G005',
                playerId: 'P001',
                title: 'الفوز في البطولة المحلية',
                description: 'الوصول للمركز الأول في بطولة الأكاديميات',
                targetDate: '2024-12-15',
                progress: 25,
                status: 'نشط',
                category: 'إنجازات',
                createdDate: '2024-01-15'
            }
        ];

        // ==================== بيانات المدفوعات ====================
        const paymentsDatabase = [
            {
                id: 'PAY001',
                playerId: 'P001',
                amount: 500,
                dueDate: '2024-07-01',
                status: 'معلقة',
                type: 'اشتراك شهري',
                description: 'اشتراك شهر يوليو 2024',
                invoiceNumber: 'INV-2024-001'
            },
            {
                id: 'PAY002',
                playerId: 'P001',
                amount: 500,
                dueDate: '2024-06-01',
                status: 'مدفوعة',
                type: 'اشتراك شهري',
                description: 'اشتراك شهر يونيو 2024',
                invoiceNumber: 'INV-2024-002',
                paidDate: '2024-05-28',
                paymentMethod: 'تحويل بنكي'
            },
            {
                id: 'PAY003',
                playerId: 'P001',
                amount: 150,
                dueDate: '2024-06-15',
                status: 'مدفوعة',
                type: 'رسوم إضافية',
                description: 'رسوم معدات تدريبية',
                invoiceNumber: 'INV-2024-003',
                paidDate: '2024-06-10',
                paymentMethod: 'نقداً'
            }
        ];

        // ==================== بيانات الرسائل ====================
        const messagesDatabase = [
            {
                id: 'MSG001',
                playerId: 'P001',
                from: 'كابتن أحمد المدرب',
                fromType: 'coach',
                subject: 'تحسن ملحوظ في الأداء',
                message: 'مرحباً محمد، أود أن أهنئك على التحسن الملحوظ في أدائك خلال الأسبوع الماضي. استمر على هذا المستوى!',
                date: '2024-06-22',
                time: '14:30',
                isRead: false,
                priority: 'عادية'
            },
            {
                id: 'MSG002',
                playerId: 'P001',
                from: 'إدارة الأكاديمية',
                fromType: 'admin',
                subject: 'تذكير بموعد المباراة',
                message: 'نذكركم بموعد المباراة الودية يوم السبت الساعة 6 مساءً. يرجى الحضور قبل الموعد بنصف ساعة.',
                date: '2024-06-21',
                time: '10:15',
                isRead: false,
                priority: 'مهمة'
            },
            {
                id: 'MSG003',
                playerId: 'P001',
                from: 'كابتن أحمد المدرب',
                fromType: 'coach',
                subject: 'برنامج تدريبي جديد',
                message: 'تم إعداد برنامج تدريبي جديد لتطوير مهاراتك الفنية. سنبدأ تطبيقه من الأسبوع القادم.',
                date: '2024-06-20',
                time: '16:45',
                isRead: true,
                priority: 'عادية'
            },
            {
                id: 'MSG004',
                playerId: 'P001',
                from: 'إدارة الأكاديمية',
                fromType: 'admin',
                subject: 'تهنئة بالإنجاز',
                message: 'تهانينا لتحقيقك هدف تحسين دقة التسديد! استمر في العمل الجاد.',
                date: '2024-06-19',
                time: '09:20',
                isRead: true,
                priority: 'عادية'
            }
        ];

        // ==================== بيانات الإنجازات ====================
        const achievementsDatabase = [
            {
                id: 'ACH001',
                playerId: 'P001',
                title: 'أفضل لاعب في الشهر',
                description: 'حصل على لقب أفضل لاعب لشهر مايو 2024',
                date: '2024-05-31',
                type: 'شهادة تقدير',
                icon: 'fas fa-trophy',
                color: '#FFD700'
            },
            {
                id: 'ACH002',
                playerId: 'P001',
                title: 'حضور مثالي',
                description: 'حضور 100% لمدة شهر كامل',
                date: '2024-04-30',
                type: 'شهادة انضباط',
                icon: 'fas fa-calendar-check',
                color: '#28a745'
            },
            {
                id: 'ACH003',
                playerId: 'P001',
                title: 'تحسن في الأداء',
                description: 'تحسن بنسبة 20% في درجة الأداء العامة',
                date: '2024-03-15',
                type: 'شهادة تطوير',
                icon: 'fas fa-chart-line',
                color: '#17a2b8'
            }
        ];

        // ==================== التهيئة عند تحميل الصفحة ====================
        document.addEventListener('DOMContentLoaded', function() {
            initializePlayerDashboard();
            loadPlayerSession();
            updateDashboard();
            setupEventListeners();

            console.log('🏃‍♂️ لوحة تحكم اللاعب جاهزة!');
        });

        function initializePlayerDashboard() {
            // تحميل البيانات من localStorage أو إنشاء بيانات تجريبية
            const savedData = localStorage.getItem('playerDashboardData');
            if (savedData) {
                playerData = JSON.parse(savedData);
            } else {
                // إنشاء بيانات تجريبية
                playerData = {
                    players: playersDatabase,
                    sessions: sessionsDatabase,
                    goals: goalsDatabase,
                    payments: paymentsDatabase,
                    messages: messagesDatabase,
                    achievements: achievementsDatabase
                };
                savePlayerData();
            }
        }

        function loadPlayerSession() {
            // تحميل بيانات المستخدم من الجلسة
            const sessionData = sessionStorage.getItem('userSession');
            if (sessionData) {
                try {
                    const decryptedData = decryptData(sessionData);
                    if (decryptedData && decryptedData.user.role === 'player') {
                        currentPlayer = findPlayerByEmail(decryptedData.user.email);
                        if (currentPlayer) {
                            updatePlayerInfo();
                        }
                    }
                } catch (error) {
                    console.error('خطأ في تحميل بيانات الجلسة:', error);
                    // للاختبار - تحميل بيانات افتراضية
                    currentPlayer = playerData.players['P001'];
                    updatePlayerInfo();
                }
            } else {
                // للاختبار - تحميل بيانات افتراضية
                currentPlayer = playerData.players['P001'];
                updatePlayerInfo();
            }
        }

        function findPlayerByEmail(email) {
            for (let playerId in playerData.players) {
                if (playerData.players[playerId].email === email) {
                    return playerData.players[playerId];
                }
            }
            return null;
        }

        function decryptData(encryptedData) {
            try {
                const bytes = CryptoJS.AES.decrypt(encryptedData, 'academy7c_secret');
                return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
            } catch (e) {
                throw new Error('فشل في فك التشفير');
            }
        }

        function savePlayerData() {
            localStorage.setItem('playerDashboardData', JSON.stringify(playerData));
        }

        // ==================== تحديث معلومات اللاعب ====================
        function updatePlayerInfo() {
            if (!currentPlayer) return;

            document.getElementById('playerName').textContent = currentPlayer.name;
            document.getElementById('playerLevel').textContent = `مستوى ${currentPlayer.level}`;

            // تحديث الإحصائيات
            document.getElementById('attendanceRate').textContent = currentPlayer.attendanceRate + '%';
            document.getElementById('performanceScore').textContent = currentPlayer.performanceScore;
            document.getElementById('completedSessions').textContent = currentPlayer.completedSessions;
            document.getElementById('achievedGoals').textContent = currentPlayer.achievedGoals;

            // تحديث الشارات
            updateNavigationBadges();
        }

        function updateNavigationBadges() {
            if (!currentPlayer) return;

            // حساب الحصص القادمة
            const upcomingSessions = playerData.sessions.filter(s =>
                s.playerId === currentPlayer.id && s.status === 'قادمة'
            );
            document.getElementById('upcomingSessionsCount').textContent = upcomingSessions.length;

            // حساب الأهداف النشطة
            const activeGoals = playerData.goals.filter(g =>
                g.playerId === currentPlayer.id && g.status === 'نشط'
            );
            document.getElementById('activeGoalsCount').textContent = activeGoals.length;

            // حساب المدفوعات المعلقة
            const pendingPayments = playerData.payments.filter(p =>
                p.playerId === currentPlayer.id && p.status === 'معلقة'
            );
            document.getElementById('pendingPaymentsCount').textContent = pendingPayments.length;

            // حساب الرسائل غير المقروءة
            const unreadMessages = playerData.messages.filter(m =>
                m.playerId === currentPlayer.id && !m.isRead
            );
            document.getElementById('unreadMessagesCount').textContent = unreadMessages.length;
        }

        // ==================== تحديث لوحة المعلومات ====================
        function updateDashboard() {
            updateUpcomingSessionsList();
            createPerformanceChart();
            updateProfileContent();
            updateScheduleContent();
            updateGoalsContent();
            updatePaymentsContent();
            updateMessagesContent();
            updateAchievementsContent();
            runAIAnalysis();
        }

        function updateUpcomingSessionsList() {
            if (!currentPlayer) return;

            const upcomingSessions = playerData.sessions
                .filter(s => s.playerId === currentPlayer.id && s.status === 'قادمة')
                .sort((a, b) => new Date(a.date + ' ' + a.time) - new Date(b.date + ' ' + b.time))
                .slice(0, 3);

            const container = document.getElementById('upcomingSessionsList');

            if (upcomingSessions.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">لا توجد حصص قادمة</p>';
                return;
            }

            container.innerHTML = upcomingSessions.map(session => `
                <div style="
                    background: rgba(255,255,255,0.05);
                    border: 1px solid var(--border);
                    border-radius: 12px;
                    padding: 1rem;
                    margin-bottom: 1rem;
                    transition: all 0.3s ease;
                " onmouseover="this.style.background='rgba(255,255,255,0.1)'"
                   onmouseout="this.style.background='rgba(255,255,255,0.05)'">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                        <h4 style="color: var(--text-primary); font-size: 1rem;">${session.title}</h4>
                        <span style="
                            background: var(--brand-primary);
                            color: white;
                            padding: 0.2rem 0.5rem;
                            border-radius: 6px;
                            font-size: 0.8rem;
                        ">${session.type}</span>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; font-size: 0.9rem; color: var(--text-secondary);">
                        <div><i class="fas fa-calendar" style="margin-left: 0.5rem;"></i>${formatDate(session.date)}</div>
                        <div><i class="fas fa-clock" style="margin-left: 0.5rem;"></i>${session.time}</div>
                        <div><i class="fas fa-user-tie" style="margin-left: 0.5rem;"></i>${session.coach}</div>
                        <div><i class="fas fa-map-marker-alt" style="margin-left: 0.5rem;"></i>${session.location}</div>
                    </div>
                    <p style="color: var(--text-secondary); font-size: 0.85rem; margin-top: 0.5rem;">${session.description}</p>
                    <div style="margin-top: 1rem;">
                        <button onclick="markAttendance('${session.id}')" style="
                            background: var(--success);
                            color: white;
                            border: none;
                            padding: 0.4rem 0.8rem;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 0.8rem;
                            margin-left: 0.5rem;
                        ">تسجيل الحضور</button>
                        <button onclick="viewSessionDetails('${session.id}')" style="
                            background: transparent;
                            color: var(--text-secondary);
                            border: 1px solid var(--border);
                            padding: 0.4rem 0.8rem;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 0.8rem;
                        ">التفاصيل</button>
                    </div>
                </div>
            `).join('');
        }

        // ==================== إنشاء الرسم البياني ====================
        function createPerformanceChart() {
            const ctx = document.getElementById('performanceChart');
            if (!ctx) return;

            // بيانات تجريبية للأداء خلال 6 أشهر
            const performanceData = [75, 78, 82, 85, 88, 92];
            const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'];

            if (performanceChart) {
                performanceChart.destroy();
            }

            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: months,
                    datasets: [{
                        label: 'درجة الأداء',
                        data: performanceData,
                        borderColor: '#8B4513',
                        backgroundColor: 'rgba(139, 69, 19, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#D2691E',
                        pointBorderColor: '#8B4513',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff',
                                font: {
                                    family: 'Cairo',
                                    size: 12
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 60,
                            max: 100,
                            ticks: {
                                color: '#cccccc',
                                font: {
                                    family: 'Cairo'
                                }
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: '#cccccc',
                                font: {
                                    family: 'Cairo'
                                }
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    }
                }
            });
        }

        // ==================== تحديث محتوى الملف الشخصي ====================
        function updateProfileContent() {
            if (!currentPlayer) return;

            const profileContainer = document.getElementById('profileContent');
            const detailedStatsContainer = document.getElementById('detailedStatsContent');

            if (profileContainer) {
                profileContainer.innerHTML = `
                    <div style="display: grid; grid-template-columns: 200px 1fr; gap: 2rem; margin-bottom: 2rem;">
                        <div style="text-align: center;">
                            <div style="
                                width: 150px;
                                height: 150px;
                                background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin: 0 auto 1rem;
                                font-size: 3rem;
                                color: white;
                                box-shadow: var(--shadow-glow);
                            ">
                                ${currentPlayer.avatar ? `<img src="${currentPlayer.avatar}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">` : '<i class="fas fa-user"></i>'}
                            </div>
                            <button onclick="changeProfilePicture()" style="
                                background: var(--brand-primary);
                                color: white;
                                border: none;
                                padding: 0.5rem 1rem;
                                border-radius: 8px;
                                cursor: pointer;
                                font-size: 0.9rem;
                            ">
                                <i class="fas fa-camera"></i> تغيير الصورة
                            </button>
                        </div>

                        <div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem;">
                                <div>
                                    <label style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 0.5rem; display: block;">الاسم الكامل</label>
                                    <div style="background: rgba(255,255,255,0.05); padding: 0.8rem; border-radius: 8px; border: 1px solid var(--border);">
                                        ${currentPlayer.name}
                                    </div>
                                </div>

                                <div>
                                    <label style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 0.5rem; display: block;">العمر</label>
                                    <div style="background: rgba(255,255,255,0.05); padding: 0.8rem; border-radius: 8px; border: 1px solid var(--border);">
                                        ${currentPlayer.age} سنة
                                    </div>
                                </div>

                                <div>
                                    <label style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 0.5rem; display: block;">البريد الإلكتروني</label>
                                    <div style="background: rgba(255,255,255,0.05); padding: 0.8rem; border-radius: 8px; border: 1px solid var(--border);">
                                        ${currentPlayer.email}
                                    </div>
                                </div>

                                <div>
                                    <label style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 0.5rem; display: block;">رقم الجوال</label>
                                    <div style="background: rgba(255,255,255,0.05); padding: 0.8rem; border-radius: 8px; border: 1px solid var(--border);">
                                        ${currentPlayer.phone}
                                    </div>
                                </div>

                                <div>
                                    <label style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 0.5rem; display: block;">الجنس</label>
                                    <div style="background: rgba(255,255,255,0.05); padding: 0.8rem; border-radius: 8px; border: 1px solid var(--border);">
                                        ${currentPlayer.gender}
                                    </div>
                                </div>

                                <div>
                                    <label style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 0.5rem; display: block;">المستوى</label>
                                    <div style="background: rgba(255,255,255,0.05); padding: 0.8rem; border-radius: 8px; border: 1px solid var(--border);">
                                        <span style="
                                            background: var(--brand-primary);
                                            color: white;
                                            padding: 0.3rem 0.8rem;
                                            border-radius: 12px;
                                            font-size: 0.8rem;
                                        ">${currentPlayer.level}</span>
                                    </div>
                                </div>

                                <div>
                                    <label style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 0.5rem; display: block;">المدرب المخصص</label>
                                    <div style="background: rgba(255,255,255,0.05); padding: 0.8rem; border-radius: 8px; border: 1px solid var(--border);">
                                        ${currentPlayer.coach}
                                    </div>
                                </div>

                                <div>
                                    <label style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 0.5rem; display: block;">تاريخ الانضمام</label>
                                    <div style="background: rgba(255,255,255,0.05); padding: 0.8rem; border-radius: 8px; border: 1px solid var(--border);">
                                        ${formatDate(currentPlayer.joinDate)}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            if (detailedStatsContainer) {
                detailedStatsContainer.innerHTML = `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <div style="background: rgba(40, 167, 69, 0.1); border: 1px solid rgba(40, 167, 69, 0.3); border-radius: 12px; padding: 1rem; text-align: center;">
                            <div style="font-size: 2rem; color: #28a745; margin-bottom: 0.5rem;">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div style="font-size: 1.5rem; font-weight: bold; color: var(--text-primary);">${currentPlayer.attendanceRate}%</div>
                            <div style="color: var(--text-secondary); font-size: 0.9rem;">معدل الحضور</div>
                        </div>

                        <div style="background: rgba(23, 162, 184, 0.1); border: 1px solid rgba(23, 162, 184, 0.3); border-radius: 12px; padding: 1rem; text-align: center;">
                            <div style="font-size: 2rem; color: #17a2b8; margin-bottom: 0.5rem;">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div style="font-size: 1.5rem; font-weight: bold; color: var(--text-primary);">${currentPlayer.performanceScore}</div>
                            <div style="color: var(--text-secondary); font-size: 0.9rem;">درجة الأداء</div>
                        </div>

                        <div style="background: rgba(255, 193, 7, 0.1); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 12px; padding: 1rem; text-align: center;">
                            <div style="font-size: 2rem; color: #ffc107; margin-bottom: 0.5rem;">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div style="font-size: 1.5rem; font-weight: bold; color: var(--text-primary);">${currentPlayer.completedSessions}/${currentPlayer.totalSessions}</div>
                            <div style="color: var(--text-secondary); font-size: 0.9rem;">الحصص المكتملة</div>
                        </div>

                        <div style="background: rgba(139, 69, 19, 0.1); border: 1px solid rgba(139, 69, 19, 0.3); border-radius: 12px; padding: 1rem; text-align: center;">
                            <div style="font-size: 2rem; color: var(--brand-primary); margin-bottom: 0.5rem;">
                                <i class="fas fa-bullseye"></i>
                            </div>
                            <div style="font-size: 1.5rem; font-weight: bold; color: var(--text-primary);">${currentPlayer.achievedGoals}/${currentPlayer.totalGoals}</div>
                            <div style="color: var(--text-secondary); font-size: 0.9rem;">الأهداف المحققة</div>
                        </div>
                    </div>
                `;
            }
        }

        // ==================== تحديث محتوى الجدول التدريبي ====================
        function updateScheduleContent(filter = 'upcoming') {
            if (!currentPlayer) return;

            let filteredSessions = playerData.sessions.filter(s => s.playerId === currentPlayer.id);

            switch(filter) {
                case 'upcoming':
                    filteredSessions = filteredSessions.filter(s => s.status === 'قادمة');
                    break;
                case 'completed':
                    filteredSessions = filteredSessions.filter(s => s.status === 'مكتملة');
                    break;
                case 'cancelled':
                    filteredSessions = filteredSessions.filter(s => s.status === 'ملغية');
                    break;
            }

            filteredSessions.sort((a, b) => new Date(b.date + ' ' + b.time) - new Date(a.date + ' ' + a.time));

            const container = document.getElementById('scheduleContent');
            if (!container) return;

            if (filteredSessions.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                        <i class="fas fa-calendar-times" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>لا توجد حصص ${filter === 'upcoming' ? 'قادمة' : filter === 'completed' ? 'مكتملة' : filter === 'cancelled' ? 'ملغية' : ''}</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredSessions.map(session => {
                const statusColor = session.status === 'قادمة' ? '#17a2b8' :
                                  session.status === 'مكتملة' ? '#28a745' : '#dc3545';
                const statusIcon = session.status === 'قادمة' ? 'clock' :
                                 session.status === 'مكتملة' ? 'check-circle' : 'times-circle';

                return `
                    <div style="
                        background: rgba(255,255,255,0.05);
                        border: 1px solid var(--border);
                        border-radius: 12px;
                        padding: 1.5rem;
                        margin-bottom: 1rem;
                        transition: all 0.3s ease;
                        border-right: 4px solid ${statusColor};
                    " onmouseover="this.style.background='rgba(255,255,255,0.1)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.05)'">

                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 1rem;">
                            <div>
                                <h4 style="color: var(--text-primary); font-size: 1.1rem; margin-bottom: 0.5rem;">
                                    ${session.title}
                                </h4>
                                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                                    <span style="
                                        background: ${statusColor};
                                        color: white;
                                        padding: 0.2rem 0.6rem;
                                        border-radius: 12px;
                                        font-size: 0.8rem;
                                        display: flex;
                                        align-items: center;
                                        gap: 0.3rem;
                                    ">
                                        <i class="fas fa-${statusIcon}"></i>
                                        ${session.status}
                                    </span>
                                    <span style="
                                        background: var(--brand-primary);
                                        color: white;
                                        padding: 0.2rem 0.6rem;
                                        border-radius: 12px;
                                        font-size: 0.8rem;
                                    ">${session.type}</span>
                                </div>
                            </div>

                            <div style="text-align: left;">
                                ${session.status === 'مكتملة' && session.rating ? `
                                    <div style="color: #ffc107; font-size: 1.2rem; margin-bottom: 0.5rem;">
                                        ${'★'.repeat(Math.floor(session.rating / 2))}${'☆'.repeat(5 - Math.floor(session.rating / 2))}
                                    </div>
                                    <div style="color: var(--text-secondary); font-size: 0.8rem;">
                                        ${session.rating}/10
                                    </div>
                                ` : ''}
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                            <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                                <i class="fas fa-calendar" style="color: var(--brand-primary);"></i>
                                <span>${formatDate(session.date)}</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                                <i class="fas fa-clock" style="color: var(--brand-primary);"></i>
                                <span>${session.time} (${session.duration} دقيقة)</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                                <i class="fas fa-user-tie" style="color: var(--brand-primary);"></i>
                                <span>${session.coach}</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                                <i class="fas fa-map-marker-alt" style="color: var(--brand-primary);"></i>
                                <span>${session.location}</span>
                            </div>
                        </div>

                        <p style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 1rem; line-height: 1.5;">
                            ${session.description}
                        </p>

                        ${session.status === 'مكتملة' && session.notes ? `
                            <div style="background: rgba(40, 167, 69, 0.1); border: 1px solid rgba(40, 167, 69, 0.3); border-radius: 8px; padding: 1rem; margin-bottom: 1rem;">
                                <h5 style="color: #28a745; margin-bottom: 0.5rem;">
                                    <i class="fas fa-sticky-note"></i> ملاحظات المدرب:
                                </h5>
                                <p style="color: var(--text-secondary); font-size: 0.9rem; line-height: 1.4;">
                                    ${session.notes}
                                </p>
                            </div>
                        ` : ''}

                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                            ${session.status === 'قادمة' ? `
                                <button onclick="markAttendance('${session.id}')" style="
                                    background: var(--success);
                                    color: white;
                                    border: none;
                                    padding: 0.5rem 1rem;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 0.9rem;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.3rem;
                                ">
                                    <i class="fas fa-check"></i>
                                    تسجيل الحضور
                                </button>
                                <button onclick="requestReschedule('${session.id}')" style="
                                    background: var(--warning);
                                    color: white;
                                    border: none;
                                    padding: 0.5rem 1rem;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 0.9rem;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.3rem;
                                ">
                                    <i class="fas fa-calendar-alt"></i>
                                    طلب إعادة جدولة
                                </button>
                            ` : ''}

                            <button onclick="viewSessionDetails('${session.id}')" style="
                                background: transparent;
                                color: var(--text-secondary);
                                border: 1px solid var(--border);
                                padding: 0.5rem 1rem;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 0.9rem;
                                display: flex;
                                align-items: center;
                                gap: 0.3rem;
                            ">
                                <i class="fas fa-info-circle"></i>
                                التفاصيل
                            </button>

                            ${session.status === 'مكتملة' ? `
                                <button onclick="downloadSessionReport('${session.id}')" style="
                                    background: var(--brand-primary);
                                    color: white;
                                    border: none;
                                    padding: 0.5rem 1rem;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 0.9rem;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.3rem;
                                ">
                                    <i class="fas fa-download"></i>
                                    تحميل التقرير
                                </button>
                            ` : ''}
                        </div>
                    </div>
                `;
            }).join('');

            // إنشاء رسم بياني للحضور
            createAttendanceChart();
        }

        // ==================== تحديث محتوى الأهداف ====================
        function updateGoalsContent(filter = 'active') {
            if (!currentPlayer) return;

            let filteredGoals = playerData.goals.filter(g => g.playerId === currentPlayer.id);

            switch(filter) {
                case 'active':
                    filteredGoals = filteredGoals.filter(g => g.status === 'نشط');
                    break;
                case 'completed':
                    filteredGoals = filteredGoals.filter(g => g.status === 'مكتمل');
                    break;
            }

            filteredGoals.sort((a, b) => new Date(a.targetDate) - new Date(b.targetDate));

            const container = document.getElementById('goalsContent');
            if (!container) return;

            if (filteredGoals.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                        <i class="fas fa-bullseye" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>لا توجد أهداف ${filter === 'active' ? 'نشطة' : 'مكتملة'}</p>
                        <button onclick="addNewGoal()" style="
                            background: var(--brand-primary);
                            color: white;
                            border: none;
                            padding: 0.8rem 1.5rem;
                            border-radius: 8px;
                            cursor: pointer;
                            margin-top: 1rem;
                        ">
                            <i class="fas fa-plus"></i> إضافة هدف جديد
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredGoals.map(goal => {
                const progressColor = goal.progress >= 80 ? '#28a745' :
                                    goal.progress >= 50 ? '#ffc107' : '#dc3545';
                const daysRemaining = calculateDaysUntil(goal.targetDate);
                const urgencyColor = daysRemaining <= 7 ? '#dc3545' :
                                   daysRemaining <= 30 ? '#ffc107' : '#28a745';

                return `
                    <div style="
                        background: rgba(255,255,255,0.05);
                        border: 1px solid var(--border);
                        border-radius: 12px;
                        padding: 1.5rem;
                        margin-bottom: 1rem;
                        transition: all 0.3s ease;
                        border-right: 4px solid ${progressColor};
                    " onmouseover="this.style.background='rgba(255,255,255,0.1)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.05)'">

                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 1rem;">
                            <div style="flex: 1;">
                                <h4 style="color: var(--text-primary); font-size: 1.1rem; margin-bottom: 0.5rem;">
                                    ${goal.title}
                                </h4>
                                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                                    <span style="
                                        background: var(--brand-primary);
                                        color: white;
                                        padding: 0.2rem 0.6rem;
                                        border-radius: 12px;
                                        font-size: 0.8rem;
                                    ">${goal.category}</span>
                                    <span style="
                                        background: ${urgencyColor};
                                        color: white;
                                        padding: 0.2rem 0.6rem;
                                        border-radius: 12px;
                                        font-size: 0.8rem;
                                    ">
                                        ${daysRemaining > 0 ? `${daysRemaining} يوم متبقي` : 'منتهي الصلاحية'}
                                    </span>
                                </div>
                                <p style="color: var(--text-secondary); font-size: 0.9rem; line-height: 1.4; margin-bottom: 1rem;">
                                    ${goal.description}
                                </p>
                            </div>

                            <div style="text-align: center; min-width: 80px;">
                                <div style="
                                    width: 60px;
                                    height: 60px;
                                    border-radius: 50%;
                                    background: conic-gradient(${progressColor} ${goal.progress * 3.6}deg, rgba(255,255,255,0.1) 0deg);
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    margin: 0 auto 0.5rem;
                                    position: relative;
                                ">
                                    <div style="
                                        width: 45px;
                                        height: 45px;
                                        background: var(--primary-bg);
                                        border-radius: 50%;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        font-weight: bold;
                                        color: ${progressColor};
                                        font-size: 0.9rem;
                                    ">
                                        ${goal.progress}%
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="margin-bottom: 1rem;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                <span style="color: var(--text-secondary); font-size: 0.9rem;">التقدم</span>
                                <span style="color: var(--text-primary); font-size: 0.9rem; font-weight: bold;">${goal.progress}%</span>
                            </div>
                            <div style="
                                width: 100%;
                                height: 8px;
                                background: rgba(255,255,255,0.1);
                                border-radius: 4px;
                                overflow: hidden;
                            ">
                                <div style="
                                    width: ${goal.progress}%;
                                    height: 100%;
                                    background: linear-gradient(90deg, ${progressColor}, ${progressColor}dd);
                                    transition: width 0.3s ease;
                                "></div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem; font-size: 0.9rem;">
                            <div style="color: var(--text-secondary);">
                                <i class="fas fa-calendar-plus" style="color: var(--brand-primary); margin-left: 0.5rem;"></i>
                                تاريخ الإنشاء: ${formatDate(goal.createdDate)}
                            </div>
                            <div style="color: var(--text-secondary);">
                                <i class="fas fa-flag-checkered" style="color: var(--brand-primary); margin-left: 0.5rem;"></i>
                                الهدف: ${formatDate(goal.targetDate)}
                            </div>
                        </div>

                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                            <button onclick="updateGoalProgress('${goal.id}')" style="
                                background: var(--brand-primary);
                                color: white;
                                border: none;
                                padding: 0.5rem 1rem;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 0.9rem;
                                display: flex;
                                align-items: center;
                                gap: 0.3rem;
                            ">
                                <i class="fas fa-chart-line"></i>
                                تحديث التقدم
                            </button>

                            <button onclick="editGoal('${goal.id}')" style="
                                background: var(--info);
                                color: white;
                                border: none;
                                padding: 0.5rem 1rem;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 0.9rem;
                                display: flex;
                                align-items: center;
                                gap: 0.3rem;
                            ">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </button>

                            ${goal.progress >= 100 ? `
                                <button onclick="markGoalCompleted('${goal.id}')" style="
                                    background: var(--success);
                                    color: white;
                                    border: none;
                                    padding: 0.5rem 1rem;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 0.9rem;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.3rem;
                                ">
                                    <i class="fas fa-check"></i>
                                    تحديد كمكتمل
                                </button>
                            ` : ''}

                            <button onclick="deleteGoal('${goal.id}')" style="
                                background: var(--danger);
                                color: white;
                                border: none;
                                padding: 0.5rem 1rem;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 0.9rem;
                                display: flex;
                                align-items: center;
                                gap: 0.3rem;
                            ">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            // إنشاء رسم بياني للأهداف
            createGoalsChart();
        }

        // ==================== الذكاء الاصطناعي المتقدم ====================
        function runAIAnalysis() {
            if (!currentPlayer) return;

            // تحليل الأداء الشخصي
            analyzePersonalPerformance();

            // توصيات ذكية للتحسين
            generateSmartRecommendations();

            // تنبؤ بالأهداف
            predictGoalAchievement();

            // تحليل نمط الحضور
            analyzeAttendancePattern();
        }

        function analyzePersonalPerformance() {
            const performanceScore = currentPlayer.performanceScore;
            const attendanceRate = currentPlayer.attendanceRate;
            const completionRate = (currentPlayer.completedSessions / currentPlayer.totalSessions) * 100;

            let performanceLevel = 'ممتاز';
            if (performanceScore < 70) performanceLevel = 'يحتاج تحسين';
            else if (performanceScore < 85) performanceLevel = 'جيد';
            else if (performanceScore < 95) performanceLevel = 'جيد جداً';

            console.log(`🤖 تحليل الأداء الشخصي:`);
            console.log(`- مستوى الأداء: ${performanceLevel} (${performanceScore}/100)`);
            console.log(`- معدل الحضور: ${attendanceRate}%`);
            console.log(`- معدل إكمال الحصص: ${completionRate.toFixed(1)}%`);

            return {
                performanceLevel,
                performanceScore,
                attendanceRate,
                completionRate
            };
        }

        function generateSmartRecommendations() {
            const recommendations = [];

            // توصيات بناءً على الأداء
            if (currentPlayer.performanceScore < 80) {
                recommendations.push({
                    type: 'performance',
                    priority: 'عالية',
                    title: 'تحسين الأداء العام',
                    message: 'يُنصح بالتركيز على التدريبات الفردية لتحسين المهارات الأساسية',
                    action: 'حجز حصص تدريب فردية إضافية'
                });
            }

            // توصيات بناءً على الحضور
            if (currentPlayer.attendanceRate < 85) {
                recommendations.push({
                    type: 'attendance',
                    priority: 'متوسطة',
                    title: 'تحسين معدل الحضور',
                    message: 'الحضور المنتظم مهم جداً لتطوير المهارات والحفاظ على اللياقة',
                    action: 'وضع تذكيرات للحصص القادمة'
                });
            }

            // توصيات بناءً على الأهداف
            const activeGoals = playerData.goals.filter(g =>
                g.playerId === currentPlayer.id && g.status === 'نشط'
            );

            const slowProgressGoals = activeGoals.filter(g => g.progress < 50);
            if (slowProgressGoals.length > 0) {
                recommendations.push({
                    type: 'goals',
                    priority: 'متوسطة',
                    title: 'تسريع تحقيق الأهداف',
                    message: `لديك ${slowProgressGoals.length} أهداف تحتاج لمزيد من التركيز`,
                    action: 'مراجعة خطة تحقيق الأهداف مع المدرب'
                });
            }

            console.log('🤖 التوصيات الذكية:', recommendations);
            return recommendations;
        }

        function predictGoalAchievement() {
            const activeGoals = playerData.goals.filter(g =>
                g.playerId === currentPlayer.id && g.status === 'نشط'
            );

            activeGoals.forEach(goal => {
                const daysRemaining = calculateDaysUntil(goal.targetDate);
                const progressRate = goal.progress / calculateDaysSince(goal.createdDate);
                const predictedCompletion = goal.progress + (progressRate * daysRemaining);

                let prediction = 'متوقع التحقيق';
                if (predictedCompletion < 90) prediction = 'يحتاج جهد إضافي';
                if (predictedCompletion < 70) prediction = 'صعب التحقيق';

                console.log(`🎯 تنبؤ الهدف "${goal.title}": ${prediction} (${predictedCompletion.toFixed(1)}%)`);
            });
        }

        function analyzeAttendancePattern() {
            // تحليل نمط الحضور خلال الأسابيع الماضية
            const recentSessions = playerData.sessions.filter(s =>
                s.playerId === currentPlayer.id && s.status === 'مكتملة'
            );

            const attendanceByDay = {};
            recentSessions.forEach(session => {
                const dayOfWeek = new Date(session.date).getDay();
                attendanceByDay[dayOfWeek] = (attendanceByDay[dayOfWeek] || 0) + 1;
            });

            const bestDay = Object.keys(attendanceByDay).reduce((a, b) =>
                attendanceByDay[a] > attendanceByDay[b] ? a : b
            );

            const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];

            console.log(`📊 تحليل نمط الحضور:`);
            console.log(`- أفضل يوم للحضور: ${dayNames[bestDay]}`);
            console.log(`- إجمالي الحصص المكتملة: ${recentSessions.length}`);
        }

        // ==================== وظائف التفاعل مع الجدول التدريبي ====================
        function filterSchedule(filter) {
            // تحديث أزرار الفلتر
            document.querySelectorAll('#scheduleSection .card-actions .btn').forEach(btn => {
                btn.classList.remove('primary');
            });
            document.getElementById('filter' + filter.charAt(0).toUpperCase() + filter.slice(1)).classList.add('primary');

            // تحديث المحتوى
            updateScheduleContent(filter);
        }

        function requestReschedule(sessionId) {
            const session = playerData.sessions.find(s => s.id === sessionId);
            if (!session) return;

            Swal.fire({
                title: 'طلب إعادة جدولة',
                html: `
                    <div style="text-align: right;">
                        <p style="margin-bottom: 1rem;">الحصة الحالية: <strong>${session.title}</strong></p>
                        <p style="margin-bottom: 1rem;">التاريخ الحالي: <strong>${formatDate(session.date)} - ${session.time}</strong></p>
                        <hr style="margin: 1rem 0;">
                        <label style="display: block; margin-bottom: 0.5rem;">التاريخ المطلوب:</label>
                        <input id="newDate" type="date" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                        <label style="display: block; margin-bottom: 0.5rem;">الوقت المطلوب:</label>
                        <input id="newTime" type="time" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                        <label style="display: block; margin-bottom: 0.5rem;">سبب إعادة الجدولة:</label>
                        <textarea id="rescheduleReason" placeholder="اختياري" style="width: 100%; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white; height: 80px;"></textarea>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'إرسال الطلب',
                cancelButtonText: 'إلغاء',
                preConfirm: () => {
                    const newDate = document.getElementById('newDate').value;
                    const newTime = document.getElementById('newTime').value;
                    const reason = document.getElementById('rescheduleReason').value;

                    if (!newDate || !newTime) {
                        Swal.showValidationMessage('يرجى تحديد التاريخ والوقت الجديد');
                        return false;
                    }

                    return { newDate, newTime, reason };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'تم إرسال الطلب!',
                        text: 'تم إرسال طلب إعادة الجدولة للمدرب. سيتم الرد عليك قريباً.',
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        function downloadSessionReport(sessionId) {
            const session = playerData.sessions.find(s => s.id === sessionId);
            if (!session) return;

            const reportData = {
                sessionInfo: session,
                playerInfo: {
                    name: currentPlayer.name,
                    level: currentPlayer.level,
                    coach: currentPlayer.coach
                },
                generatedDate: new Date().toLocaleDateString('ar-SA')
            };

            const reportContent = `
تقرير الحصة التدريبية
===================

معلومات اللاعب:
الاسم: ${currentPlayer.name}
المستوى: ${currentPlayer.level}
المدرب: ${currentPlayer.coach}

معلومات الحصة:
العنوان: ${session.title}
التاريخ: ${formatDate(session.date)}
الوقت: ${session.time}
المدة: ${session.duration} دقيقة
المكان: ${session.location}
النوع: ${session.type}

الوصف:
${session.description}

${session.rating ? `التقييم: ${session.rating}/10` : ''}
${session.notes ? `ملاحظات المدرب: ${session.notes}` : ''}

تاريخ إنشاء التقرير: ${new Date().toLocaleDateString('ar-SA')}
            `;

            const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `تقرير-حصة-${session.title.replace(/\s+/g, '-')}-${session.date}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // ==================== وظائف التفاعل مع الأهداف ====================
        function filterGoals(filter) {
            // تحديث أزرار الفلتر
            document.querySelectorAll('#goalsSection .card-actions .btn').forEach(btn => {
                if (btn.textContent.includes('الكل') || btn.textContent.includes('النشطة') || btn.textContent.includes('المكتملة')) {
                    btn.classList.remove('primary');
                }
            });

            if (filter === 'all') {
                document.getElementById('goalsFilterAll').classList.add('primary');
            } else if (filter === 'active') {
                document.getElementById('goalsFilterActive').classList.add('primary');
            } else if (filter === 'completed') {
                document.getElementById('goalsFilterCompleted').classList.add('primary');
            }

            updateGoalsContent(filter);
        }

        function updateGoalProgress(goalId) {
            const goal = playerData.goals.find(g => g.id === goalId);
            if (!goal) return;

            Swal.fire({
                title: 'تحديث تقدم الهدف',
                html: `
                    <div style="text-align: right;">
                        <p style="margin-bottom: 1rem;"><strong>${goal.title}</strong></p>
                        <p style="margin-bottom: 1rem;">التقدم الحالي: <strong>${goal.progress}%</strong></p>
                        <label style="display: block; margin-bottom: 0.5rem;">التقدم الجديد (%):</label>
                        <input id="newProgress" type="number" min="0" max="100" value="${goal.progress}" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                        <label style="display: block; margin-bottom: 0.5rem;">ملاحظات (اختياري):</label>
                        <textarea id="progressNotes" placeholder="أضف ملاحظات حول التقدم..." style="width: 100%; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white; height: 80px;"></textarea>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'تحديث التقدم',
                cancelButtonText: 'إلغاء',
                preConfirm: () => {
                    const newProgress = parseInt(document.getElementById('newProgress').value);
                    const notes = document.getElementById('progressNotes').value;

                    if (isNaN(newProgress) || newProgress < 0 || newProgress > 100) {
                        Swal.showValidationMessage('يرجى إدخال نسبة صحيحة بين 0 و 100');
                        return false;
                    }

                    return { newProgress, notes };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    goal.progress = result.value.newProgress;
                    if (result.value.notes) {
                        goal.lastUpdateNotes = result.value.notes;
                        goal.lastUpdateDate = new Date().toISOString().split('T')[0];
                    }

                    savePlayerData();
                    updateGoalsContent();
                    updateNavigationBadges();

                    Swal.fire({
                        title: 'تم التحديث!',
                        text: `تم تحديث تقدم الهدف إلى ${result.value.newProgress}%`,
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        function editGoal(goalId) {
            const goal = playerData.goals.find(g => g.id === goalId);
            if (!goal) return;

            Swal.fire({
                title: 'تعديل الهدف',
                html: `
                    <div style="text-align: right;">
                        <label style="display: block; margin-bottom: 0.5rem;">عنوان الهدف:</label>
                        <input id="editGoalTitle" value="${goal.title}" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                        <label style="display: block; margin-bottom: 0.5rem;">وصف الهدف:</label>
                        <textarea id="editGoalDescription" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white; height: 80px;">${goal.description}</textarea>
                        <label style="display: block; margin-bottom: 0.5rem;">تاريخ الهدف:</label>
                        <input id="editGoalTargetDate" type="date" value="${goal.targetDate}" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                        <label style="display: block; margin-bottom: 0.5rem;">الفئة:</label>
                        <select id="editGoalCategory" style="width: 100%; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                            <option value="مهارات فنية" ${goal.category === 'مهارات فنية' ? 'selected' : ''}>مهارات فنية</option>
                            <option value="لياقة بدنية" ${goal.category === 'لياقة بدنية' ? 'selected' : ''}>لياقة بدنية</option>
                            <option value="انضباط" ${goal.category === 'انضباط' ? 'selected' : ''}>انضباط</option>
                            <option value="إنجازات" ${goal.category === 'إنجازات' ? 'selected' : ''}>إنجازات</option>
                        </select>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'حفظ التعديلات',
                cancelButtonText: 'إلغاء',
                preConfirm: () => {
                    const title = document.getElementById('editGoalTitle').value;
                    const description = document.getElementById('editGoalDescription').value;
                    const targetDate = document.getElementById('editGoalTargetDate').value;
                    const category = document.getElementById('editGoalCategory').value;

                    if (!title || !description || !targetDate) {
                        Swal.showValidationMessage('يرجى ملء جميع الحقول');
                        return false;
                    }

                    return { title, description, targetDate, category };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    goal.title = result.value.title;
                    goal.description = result.value.description;
                    goal.targetDate = result.value.targetDate;
                    goal.category = result.value.category;

                    savePlayerData();
                    updateGoalsContent();

                    Swal.fire({
                        title: 'تم التعديل!',
                        text: 'تم تعديل الهدف بنجاح',
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        function markGoalCompleted(goalId) {
            const goal = playerData.goals.find(g => g.id === goalId);
            if (!goal) return;

            Swal.fire({
                title: 'تهانينا! 🎉',
                text: `هل تريد تحديد الهدف "${goal.title}" كمكتمل؟`,
                icon: 'question',
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'نعم، مكتمل!',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    goal.status = 'مكتمل';
                    goal.completedDate = new Date().toISOString().split('T')[0];
                    goal.progress = 100;

                    // تحديث إحصائيات اللاعب
                    currentPlayer.achievedGoals++;

                    savePlayerData();
                    updateGoalsContent();
                    updatePlayerInfo();

                    Swal.fire({
                        title: 'مبروك! 🏆',
                        text: 'تم تحديد الهدف كمكتمل. أحسنت!',
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        function deleteGoal(goalId) {
            const goal = playerData.goals.find(g => g.id === goalId);
            if (!goal) return;

            Swal.fire({
                title: 'حذف الهدف',
                text: `هل أنت متأكد من حذف الهدف "${goal.title}"؟`,
                icon: 'warning',
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#dc3545'
            }).then((result) => {
                if (result.isConfirmed) {
                    const index = playerData.goals.findIndex(g => g.id === goalId);
                    if (index > -1) {
                        playerData.goals.splice(index, 1);
                        savePlayerData();
                        updateGoalsContent();
                        updateNavigationBadges();

                        Swal.fire({
                            title: 'تم الحذف!',
                            text: 'تم حذف الهدف بنجاح',
                            icon: 'success',
                            background: '#1a1a1a',
                            color: '#ffffff'
                        });
                    }
                }
            });
        }

        // ==================== تحديث محتوى المدفوعات ====================
        function updatePaymentsContent(filter = 'all') {
            if (!currentPlayer) return;

            let filteredPayments = playerData.payments.filter(p => p.playerId === currentPlayer.id);

            switch(filter) {
                case 'pending':
                    filteredPayments = filteredPayments.filter(p => p.status === 'معلقة');
                    break;
                case 'paid':
                    filteredPayments = filteredPayments.filter(p => p.status === 'مدفوعة');
                    break;
            }

            filteredPayments.sort((a, b) => new Date(b.dueDate) - new Date(a.dueDate));

            // تحديث الإحصائيات
            const pendingAmount = playerData.payments
                .filter(p => p.playerId === currentPlayer.id && p.status === 'معلقة')
                .reduce((sum, p) => sum + p.amount, 0);

            const totalPaid = playerData.payments
                .filter(p => p.playerId === currentPlayer.id && p.status === 'مدفوعة')
                .reduce((sum, p) => sum + p.amount, 0);

            document.getElementById('pendingAmount').textContent = pendingAmount;
            document.getElementById('totalPaid').textContent = totalPaid;

            const container = document.getElementById('paymentsContent');
            if (!container) return;

            if (filteredPayments.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                        <i class="fas fa-receipt" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>لا توجد ${filter === 'pending' ? 'مدفوعات معلقة' : filter === 'paid' ? 'مدفوعات مدفوعة' : 'مدفوعات'}</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredPayments.map(payment => {
                const statusColor = payment.status === 'مدفوعة' ? '#28a745' : '#dc3545';
                const statusIcon = payment.status === 'مدفوعة' ? 'check-circle' : 'exclamation-triangle';
                const isOverdue = payment.status === 'معلقة' && new Date(payment.dueDate) < new Date();

                return `
                    <div style="
                        background: rgba(255,255,255,0.05);
                        border: 1px solid var(--border);
                        border-radius: 12px;
                        padding: 1.5rem;
                        margin-bottom: 1rem;
                        transition: all 0.3s ease;
                        border-right: 4px solid ${statusColor};
                        ${isOverdue ? 'box-shadow: 0 0 10px rgba(220, 53, 69, 0.3);' : ''}
                    " onmouseover="this.style.background='rgba(255,255,255,0.1)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.05)'">

                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 1rem;">
                            <div>
                                <h4 style="color: var(--text-primary); font-size: 1.1rem; margin-bottom: 0.5rem;">
                                    ${payment.description}
                                </h4>
                                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                                    <span style="
                                        background: ${statusColor};
                                        color: white;
                                        padding: 0.2rem 0.6rem;
                                        border-radius: 12px;
                                        font-size: 0.8rem;
                                        display: flex;
                                        align-items: center;
                                        gap: 0.3rem;
                                    ">
                                        <i class="fas fa-${statusIcon}"></i>
                                        ${payment.status}
                                    </span>
                                    <span style="
                                        background: var(--brand-primary);
                                        color: white;
                                        padding: 0.2rem 0.6rem;
                                        border-radius: 12px;
                                        font-size: 0.8rem;
                                    ">${payment.type}</span>
                                    ${isOverdue ? `
                                        <span style="
                                            background: var(--danger);
                                            color: white;
                                            padding: 0.2rem 0.6rem;
                                            border-radius: 12px;
                                            font-size: 0.8rem;
                                            animation: pulse 2s infinite;
                                        ">متأخر</span>
                                    ` : ''}
                                </div>
                            </div>

                            <div style="text-align: left;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: var(--text-primary); margin-bottom: 0.5rem;">
                                    ${payment.amount} ريال
                                </div>
                                <div style="color: var(--text-secondary); font-size: 0.8rem;">
                                    ${payment.invoiceNumber}
                                </div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                            <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                                <i class="fas fa-calendar-alt" style="color: var(--brand-primary);"></i>
                                <span>تاريخ الاستحقاق: ${formatDate(payment.dueDate)}</span>
                            </div>

                            ${payment.status === 'مدفوعة' ? `
                                <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                                    <i class="fas fa-check" style="color: #28a745;"></i>
                                    <span>تاريخ الدفع: ${formatDate(payment.paidDate)}</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                                    <i class="fas fa-credit-card" style="color: var(--brand-primary);"></i>
                                    <span>طريقة الدفع: ${payment.paymentMethod}</span>
                                </div>
                            ` : ''}
                        </div>

                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                            <button onclick="downloadInvoice('${payment.id}')" style="
                                background: var(--brand-primary);
                                color: white;
                                border: none;
                                padding: 0.5rem 1rem;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 0.9rem;
                                display: flex;
                                align-items: center;
                                gap: 0.3rem;
                            ">
                                <i class="fas fa-download"></i>
                                تحميل الفاتورة
                            </button>

                            ${payment.status === 'معلقة' ? `
                                <button onclick="payNow('${payment.id}')" style="
                                    background: var(--success);
                                    color: white;
                                    border: none;
                                    padding: 0.5rem 1rem;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 0.9rem;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.3rem;
                                ">
                                    <i class="fas fa-credit-card"></i>
                                    ادفع الآن
                                </button>

                                <button onclick="requestPaymentExtension('${payment.id}')" style="
                                    background: var(--warning);
                                    color: white;
                                    border: none;
                                    padding: 0.5rem 1rem;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 0.9rem;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.3rem;
                                ">
                                    <i class="fas fa-clock"></i>
                                    طلب تمديد
                                </button>
                            ` : ''}

                            <button onclick="viewPaymentDetails('${payment.id}')" style="
                                background: transparent;
                                color: var(--text-secondary);
                                border: 1px solid var(--border);
                                padding: 0.5rem 1rem;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 0.9rem;
                                display: flex;
                                align-items: center;
                                gap: 0.3rem;
                            ">
                                <i class="fas fa-info-circle"></i>
                                التفاصيل
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // ==================== تحديث محتوى الرسائل ====================
        function updateMessagesContent(filter = 'all') {
            if (!currentPlayer) return;

            let filteredMessages = playerData.messages.filter(m => m.playerId === currentPlayer.id);

            switch(filter) {
                case 'unread':
                    filteredMessages = filteredMessages.filter(m => !m.isRead);
                    break;
                case 'read':
                    filteredMessages = filteredMessages.filter(m => m.isRead);
                    break;
            }

            filteredMessages.sort((a, b) => new Date(b.date + ' ' + b.time) - new Date(a.date + ' ' + a.time));

            const container = document.getElementById('messagesContent');
            if (!container) return;

            if (filteredMessages.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                        <i class="fas fa-envelope-open" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>لا توجد رسائل ${filter === 'unread' ? 'غير مقروءة' : filter === 'read' ? 'مقروءة' : ''}</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredMessages.map(message => {
                const priorityColor = message.priority === 'مهمة' ? '#dc3545' :
                                    message.priority === 'متوسطة' ? '#ffc107' : '#28a745';
                const fromTypeIcon = message.fromType === 'coach' ? 'user-tie' :
                                   message.fromType === 'admin' ? 'user-shield' : 'user';

                return `
                    <div style="
                        background: rgba(255,255,255,0.05);
                        border: 1px solid var(--border);
                        border-radius: 12px;
                        padding: 1.5rem;
                        margin-bottom: 1rem;
                        transition: all 0.3s ease;
                        border-right: 4px solid ${message.isRead ? 'var(--border)' : 'var(--brand-primary)'};
                        ${!message.isRead ? 'box-shadow: 0 0 10px rgba(139, 69, 19, 0.2);' : ''}
                    " onmouseover="this.style.background='rgba(255,255,255,0.1)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.05)'"
                       onclick="openMessage('${message.id}')">

                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 1rem;">
                            <div style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                                    <i class="fas fa-${fromTypeIcon}" style="color: var(--brand-primary);"></i>
                                    <span style="font-weight: bold; color: var(--text-primary);">${message.from}</span>
                                    <span style="
                                        background: ${priorityColor};
                                        color: white;
                                        padding: 0.1rem 0.4rem;
                                        border-radius: 8px;
                                        font-size: 0.7rem;
                                    ">${message.priority}</span>
                                    ${!message.isRead ? `
                                        <span style="
                                            background: var(--brand-primary);
                                            color: white;
                                            padding: 0.1rem 0.4rem;
                                            border-radius: 8px;
                                            font-size: 0.7rem;
                                        ">جديد</span>
                                    ` : ''}
                                </div>

                                <h4 style="color: var(--text-primary); font-size: 1.1rem; margin-bottom: 0.5rem; cursor: pointer;">
                                    ${message.subject}
                                </h4>

                                <p style="color: var(--text-secondary); font-size: 0.9rem; line-height: 1.4; margin-bottom: 0.5rem;">
                                    ${message.message.length > 100 ? message.message.substring(0, 100) + '...' : message.message}
                                </p>
                            </div>

                            <div style="text-align: left; min-width: 120px;">
                                <div style="color: var(--text-secondary); font-size: 0.8rem; margin-bottom: 0.5rem;">
                                    ${formatDate(message.date)}
                                </div>
                                <div style="color: var(--text-secondary); font-size: 0.8rem;">
                                    ${message.time}
                                </div>
                            </div>
                        </div>

                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                            <button onclick="openMessage('${message.id}'); event.stopPropagation();" style="
                                background: var(--brand-primary);
                                color: white;
                                border: none;
                                padding: 0.4rem 0.8rem;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 0.8rem;
                                display: flex;
                                align-items: center;
                                gap: 0.3rem;
                            ">
                                <i class="fas fa-envelope-open"></i>
                                ${message.isRead ? 'عرض' : 'قراءة'}
                            </button>

                            <button onclick="replyToMessage('${message.id}'); event.stopPropagation();" style="
                                background: var(--info);
                                color: white;
                                border: none;
                                padding: 0.4rem 0.8rem;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 0.8rem;
                                display: flex;
                                align-items: center;
                                gap: 0.3rem;
                            ">
                                <i class="fas fa-reply"></i>
                                رد
                            </button>

                            <button onclick="deleteMessage('${message.id}'); event.stopPropagation();" style="
                                background: var(--danger);
                                color: white;
                                border: none;
                                padding: 0.4rem 0.8rem;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 0.8rem;
                                display: flex;
                                align-items: center;
                                gap: 0.3rem;
                            ">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // ==================== تحديث محتوى الإنجازات ====================
        function updateAchievementsContent() {
            if (!currentPlayer) return;

            const achievements = playerData.achievements.filter(a => a.playerId === currentPlayer.id);
            achievements.sort((a, b) => new Date(b.date) - new Date(a.date));

            const container = document.getElementById('achievementsContent');
            if (!container) return;

            if (achievements.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                        <i class="fas fa-trophy" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>لا توجد إنجازات بعد</p>
                        <p style="font-size: 0.9rem; margin-top: 0.5rem;">استمر في التدريب لتحقيق إنجازات جديدة!</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = achievements.map(achievement => {
                return `
                    <div style="
                        background: linear-gradient(135deg, rgba(255,255,255,0.05), rgba(255,255,255,0.02));
                        border: 1px solid var(--border);
                        border-radius: 16px;
                        padding: 2rem;
                        margin-bottom: 1.5rem;
                        transition: all 0.3s ease;
                        position: relative;
                        overflow: hidden;
                    " onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 10px 30px rgba(0,0,0,0.3)'"
                       onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">

                        <!-- خلفية متحركة -->
                        <div style="
                            position: absolute;
                            top: -50%;
                            left: -50%;
                            width: 200%;
                            height: 200%;
                            background: radial-gradient(circle, ${achievement.color}20 0%, transparent 70%);
                            animation: rotate 20s linear infinite;
                            pointer-events: none;
                        "></div>

                        <div style="position: relative; z-index: 1;">
                            <div style="display: flex; align-items: center; gap: 1.5rem; margin-bottom: 1rem;">
                                <div style="
                                    width: 80px;
                                    height: 80px;
                                    background: linear-gradient(135deg, ${achievement.color}, ${achievement.color}dd);
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-size: 2rem;
                                    color: white;
                                    box-shadow: 0 8px 25px ${achievement.color}40;
                                    animation: pulse 3s infinite;
                                ">
                                    <i class="${achievement.icon}"></i>
                                </div>

                                <div style="flex: 1;">
                                    <h3 style="color: var(--text-primary); font-size: 1.3rem; margin-bottom: 0.5rem; font-weight: bold;">
                                        ${achievement.title}
                                    </h3>
                                    <p style="color: var(--text-secondary); font-size: 1rem; line-height: 1.5; margin-bottom: 0.5rem;">
                                        ${achievement.description}
                                    </p>
                                    <div style="display: flex; align-items: center; gap: 1rem;">
                                        <span style="
                                            background: ${achievement.color}30;
                                            color: ${achievement.color};
                                            padding: 0.3rem 0.8rem;
                                            border-radius: 12px;
                                            font-size: 0.8rem;
                                            font-weight: bold;
                                        ">${achievement.type}</span>
                                        <span style="color: var(--text-secondary); font-size: 0.9rem;">
                                            <i class="fas fa-calendar" style="margin-left: 0.5rem;"></i>
                                            ${formatDate(achievement.date)}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div style="display: flex; gap: 0.5rem; flex-wrap: wrap; justify-content: center;">
                                <button onclick="shareAchievement('${achievement.id}')" style="
                                    background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
                                    color: white;
                                    border: none;
                                    padding: 0.6rem 1.2rem;
                                    border-radius: 8px;
                                    cursor: pointer;
                                    font-size: 0.9rem;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.5rem;
                                    font-weight: bold;
                                ">
                                    <i class="fas fa-share"></i>
                                    مشاركة الإنجاز
                                </button>

                                <button onclick="downloadCertificate('${achievement.id}')" style="
                                    background: transparent;
                                    color: var(--text-primary);
                                    border: 2px solid ${achievement.color};
                                    padding: 0.6rem 1.2rem;
                                    border-radius: 8px;
                                    cursor: pointer;
                                    font-size: 0.9rem;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.5rem;
                                    font-weight: bold;
                                ">
                                    <i class="fas fa-download"></i>
                                    تحميل الشهادة
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            // إضافة CSS للأنيميشن
            if (!document.getElementById('achievementStyles')) {
                const style = document.createElement('style');
                style.id = 'achievementStyles';
                style.textContent = `
                    @keyframes rotate {
                        from { transform: rotate(0deg); }
                        to { transform: rotate(360deg); }
                    }
                `;
                document.head.appendChild(style);
            }

            // إنشاء رسم بياني للإنجازات
            createAchievementsChart();
        }

        // ==================== إنشاء الرسوم البيانية ====================
        function createAttendanceChart() {
            const ctx = document.getElementById('attendanceChart');
            if (!ctx) return;

            // بيانات الحضور الأسبوعي
            const attendanceData = [85, 90, 78, 95, 88, 92, 87];
            const weeks = ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4', 'الأسبوع 5', 'الأسبوع 6', 'الأسبوع 7'];

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: weeks,
                    datasets: [{
                        label: 'معدل الحضور (%)',
                        data: attendanceData,
                        backgroundColor: 'rgba(139, 69, 19, 0.6)',
                        borderColor: '#8B4513',
                        borderWidth: 2,
                        borderRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff',
                                font: { family: 'Cairo' }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                color: '#cccccc',
                                font: { family: 'Cairo' }
                            },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        x: {
                            ticks: {
                                color: '#cccccc',
                                font: { family: 'Cairo' }
                            },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        }
                    }
                }
            });
        }

        function createGoalsChart() {
            const ctx = document.getElementById('goalsChart');
            if (!ctx) return;

            const activeGoals = playerData.goals.filter(g =>
                g.playerId === currentPlayer.id && g.status === 'نشط'
            );

            const labels = activeGoals.map(g => g.title.length > 20 ? g.title.substring(0, 20) + '...' : g.title);
            const data = activeGoals.map(g => g.progress);
            const colors = activeGoals.map(g =>
                g.progress >= 80 ? '#28a745' :
                g.progress >= 50 ? '#ffc107' : '#dc3545'
            );

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: colors,
                        borderColor: colors.map(c => c + 'dd'),
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: '#ffffff',
                                font: { family: 'Cairo', size: 10 },
                                padding: 15
                            }
                        }
                    }
                }
            });
        }

        function createAchievementsChart() {
            const ctx = document.getElementById('achievementsChart');
            if (!ctx) return;

            const achievements = playerData.achievements.filter(a => a.playerId === currentPlayer.id);
            const achievementsByType = {};

            achievements.forEach(a => {
                achievementsByType[a.type] = (achievementsByType[a.type] || 0) + 1;
            });

            const labels = Object.keys(achievementsByType);
            const data = Object.values(achievementsByType);
            const colors = ['#8B4513', '#D2691E', '#28a745', '#17a2b8', '#ffc107'];

            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: colors.slice(0, labels.length),
                        borderColor: colors.slice(0, labels.length).map(c => c + 'dd'),
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: '#ffffff',
                                font: { family: 'Cairo' },
                                padding: 15
                            }
                        }
                    }
                }
            });
        }

        // ==================== وظائف تفاعلية إضافية ====================
        function editProfile() {
            if (!currentPlayer) return;

            Swal.fire({
                title: 'تعديل الملف الشخصي',
                html: `
                    <div style="text-align: right;">
                        <label style="display: block; margin-bottom: 0.5rem;">الاسم الكامل:</label>
                        <input id="editName" value="${currentPlayer.name}" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">

                        <label style="display: block; margin-bottom: 0.5rem;">العمر:</label>
                        <input id="editAge" type="number" value="${currentPlayer.age}" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">

                        <label style="display: block; margin-bottom: 0.5rem;">رقم الجوال:</label>
                        <input id="editPhone" value="${currentPlayer.phone}" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">

                        <label style="display: block; margin-bottom: 0.5rem;">البريد الإلكتروني:</label>
                        <input id="editEmail" type="email" value="${currentPlayer.email}" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'حفظ التعديلات',
                cancelButtonText: 'إلغاء',
                preConfirm: () => {
                    const name = document.getElementById('editName').value;
                    const age = parseInt(document.getElementById('editAge').value);
                    const phone = document.getElementById('editPhone').value;
                    const email = document.getElementById('editEmail').value;

                    if (!name || !age || !phone || !email) {
                        Swal.showValidationMessage('يرجى ملء جميع الحقول');
                        return false;
                    }

                    return { name, age, phone, email };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    currentPlayer.name = result.value.name;
                    currentPlayer.age = result.value.age;
                    currentPlayer.phone = result.value.phone;
                    currentPlayer.email = result.value.email;

                    savePlayerData();
                    updatePlayerInfo();
                    updateProfileContent();

                    Swal.fire({
                        title: 'تم التحديث!',
                        text: 'تم تحديث بياناتك الشخصية بنجاح',
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        function changeProfilePicture() {
            Swal.fire({
                title: 'تغيير صورة الملف الشخصي',
                html: `
                    <div style="text-align: center;">
                        <input type="file" id="profilePicture" accept="image/*" style="margin-bottom: 1rem;">
                        <p style="color: var(--text-secondary); font-size: 0.9rem;">أو اختر من الصور الافتراضية:</p>
                        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 1rem; margin-top: 1rem;">
                            <div onclick="selectDefaultAvatar('👤')" style="width: 60px; height: 60px; background: var(--brand-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 2rem;">👤</div>
                            <div onclick="selectDefaultAvatar('⚽')" style="width: 60px; height: 60px; background: var(--brand-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 2rem;">⚽</div>
                            <div onclick="selectDefaultAvatar('🏃')" style="width: 60px; height: 60px; background: var(--brand-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 2rem;">🏃</div>
                            <div onclick="selectDefaultAvatar('🏆')" style="width: 60px; height: 60px; background: var(--brand-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 2rem;">🏆</div>
                        </div>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'حفظ',
                cancelButtonText: 'إلغاء'
            });
        }

        // ==================== وظائف الرسائل التفاعلية ====================
        function filterMessages(filter) {
            document.querySelectorAll('#messagesSection .card-actions .btn').forEach(btn => {
                if (btn.textContent.includes('الكل') || btn.textContent.includes('غير مقروءة') || btn.textContent.includes('مقروءة')) {
                    btn.classList.remove('primary');
                }
            });

            if (filter === 'all') {
                document.getElementById('messagesFilterAll').classList.add('primary');
            } else if (filter === 'unread') {
                document.getElementById('messagesFilterUnread').classList.add('primary');
            } else if (filter === 'read') {
                document.getElementById('messagesFilterRead').classList.add('primary');
            }

            updateMessagesContent(filter);
        }

        function openMessage(messageId) {
            const message = playerData.messages.find(m => m.id === messageId);
            if (!message) return;

            // تحديد الرسالة كمقروءة
            message.isRead = true;
            savePlayerData();
            updateNavigationBadges();
            updateMessagesContent();

            Swal.fire({
                title: message.subject,
                html: `
                    <div style="text-align: right;">
                        <div style="border-bottom: 1px solid #333; padding-bottom: 1rem; margin-bottom: 1rem;">
                            <p><strong>من:</strong> ${message.from}</p>
                            <p><strong>التاريخ:</strong> ${formatDate(message.date)} - ${message.time}</p>
                            <p><strong>الأولوية:</strong> <span style="color: ${message.priority === 'مهمة' ? '#dc3545' : message.priority === 'متوسطة' ? '#ffc107' : '#28a745'}">${message.priority}</span></p>
                        </div>
                        <div style="line-height: 1.6; text-align: right;">
                            ${message.message}
                        </div>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'رد على الرسالة',
                cancelButtonText: 'إغلاق'
            }).then((result) => {
                if (result.isConfirmed) {
                    replyToMessage(messageId);
                }
            });
        }

        function replyToMessage(messageId) {
            const message = playerData.messages.find(m => m.id === messageId);
            if (!message) return;

            Swal.fire({
                title: `رد على: ${message.subject}`,
                html: `
                    <div style="text-align: right;">
                        <p style="margin-bottom: 1rem;">إلى: <strong>${message.from}</strong></p>
                        <textarea id="replyContent" placeholder="اكتب ردك هنا..." style="width: 100%; height: 150px; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white; resize: vertical;"></textarea>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'إرسال الرد',
                cancelButtonText: 'إلغاء',
                preConfirm: () => {
                    const content = document.getElementById('replyContent').value;
                    if (!content.trim()) {
                        Swal.showValidationMessage('يرجى كتابة محتوى الرد');
                        return false;
                    }
                    return content;
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'تم إرسال الرد!',
                        text: 'تم إرسال ردك بنجاح',
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        function markAllAsRead() {
            const unreadMessages = playerData.messages.filter(m =>
                m.playerId === currentPlayer.id && !m.isRead
            );

            if (unreadMessages.length === 0) {
                Swal.fire({
                    title: 'لا توجد رسائل غير مقروءة',
                    icon: 'info',
                    background: '#1a1a1a',
                    color: '#ffffff'
                });
                return;
            }

            Swal.fire({
                title: 'تحديد جميع الرسائل كمقروءة',
                text: `هل تريد تحديد ${unreadMessages.length} رسالة كمقروءة؟`,
                icon: 'question',
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'نعم',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    unreadMessages.forEach(m => m.isRead = true);
                    savePlayerData();
                    updateNavigationBadges();
                    updateMessagesContent();

                    Swal.fire({
                        title: 'تم!',
                        text: 'تم تحديد جميع الرسائل كمقروءة',
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        function deleteMessage(messageId) {
            const message = playerData.messages.find(m => m.id === messageId);
            if (!message) return;

            Swal.fire({
                title: 'حذف الرسالة',
                text: `هل أنت متأكد من حذف رسالة "${message.subject}"؟`,
                icon: 'warning',
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#dc3545'
            }).then((result) => {
                if (result.isConfirmed) {
                    const index = playerData.messages.findIndex(m => m.id === messageId);
                    if (index > -1) {
                        playerData.messages.splice(index, 1);
                        savePlayerData();
                        updateNavigationBadges();
                        updateMessagesContent();

                        Swal.fire({
                            title: 'تم الحذف!',
                            text: 'تم حذف الرسالة بنجاح',
                            icon: 'success',
                            background: '#1a1a1a',
                            color: '#ffffff'
                        });
                    }
                }
            });
        }

        // ==================== وظائف المدفوعات التفاعلية ====================
        function filterPayments(filter) {
            document.querySelectorAll('#paymentsSection .card-actions .btn').forEach(btn => {
                if (btn.textContent.includes('الكل') || btn.textContent.includes('المعلقة') || btn.textContent.includes('المدفوعة')) {
                    btn.classList.remove('primary');
                }
            });

            if (filter === 'all') {
                document.getElementById('paymentsFilterAll').classList.add('primary');
            } else if (filter === 'pending') {
                document.getElementById('paymentsFilterPending').classList.add('primary');
            } else if (filter === 'paid') {
                document.getElementById('paymentsFilterPaid').classList.add('primary');
            }

            updatePaymentsContent(filter);
        }

        function downloadInvoice(paymentId) {
            const payment = playerData.payments.find(p => p.id === paymentId);
            if (!payment) return;

            const invoiceContent = `
فاتورة رقم: ${payment.invoiceNumber}
أكاديمية 7C الرياضية
========================

معلومات اللاعب:
الاسم: ${currentPlayer.name}
البريد الإلكتروني: ${currentPlayer.email}
رقم الجوال: ${currentPlayer.phone}

تفاصيل الفاتورة:
الوصف: ${payment.description}
النوع: ${payment.type}
المبلغ: ${payment.amount} ريال سعودي
تاريخ الاستحقاق: ${formatDate(payment.dueDate)}
الحالة: ${payment.status}

${payment.status === 'مدفوعة' ? `
تاريخ الدفع: ${formatDate(payment.paidDate)}
طريقة الدفع: ${payment.paymentMethod}
` : ''}

تاريخ إصدار الفاتورة: ${new Date().toLocaleDateString('ar-SA')}

شكراً لكم لاختيار أكاديمية 7C الرياضية
            `;

            const blob = new Blob([invoiceContent], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `فاتورة-${payment.invoiceNumber}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function downloadAllInvoices() {
            const playerPayments = playerData.payments.filter(p => p.playerId === currentPlayer.id);

            if (playerPayments.length === 0) {
                Swal.fire({
                    title: 'لا توجد فواتير',
                    text: 'لا توجد فواتير للتحميل',
                    icon: 'info',
                    background: '#1a1a1a',
                    color: '#ffffff'
                });
                return;
            }

            Swal.fire({
                title: 'تحميل جميع الفواتير',
                text: `سيتم تحميل ${playerPayments.length} فاتورة`,
                icon: 'question',
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'تحميل',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    playerPayments.forEach(payment => {
                        setTimeout(() => downloadInvoice(payment.id), 500);
                    });

                    Swal.fire({
                        title: 'تم التحميل!',
                        text: 'تم تحميل جميع الفواتير بنجاح',
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        // ==================== وظائف التنقل ====================
        function showSection(sectionName) {
            // إخفاء جميع الأقسام
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
                section.classList.remove('active');
            });

            // إزالة الحالة النشطة من جميع الروابط
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // إظهار القسم المحدد
            const targetSection = document.getElementById(sectionName + 'Section');
            if (targetSection) {
                targetSection.style.display = 'block';
                targetSection.classList.add('active');
            }

            // تفعيل الرابط المحدد
            event.target.closest('.nav-link').classList.add('active');

            // تحديث عنوان الصفحة
            updatePageTitle(sectionName);
        }

        function updatePageTitle(sectionName) {
            const titles = {
                'overview': 'نظرة عامة',
                'profile': 'الملف الشخصي',
                'schedule': 'الجدول التدريبي',
                'goals': 'الأهداف الشخصية',
                'payments': 'المدفوعات',
                'messages': 'الرسائل',
                'achievements': 'الإنجازات'
            };

            const title = titles[sectionName] || 'لوحة التحكم';
            document.getElementById('pageTitle').textContent = title;
            document.getElementById('breadcrumb').textContent = `الرئيسية / ${title}`;
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }

        // ==================== وظائف التفاعل ====================
        function showNotifications() {
            const unreadMessages = playerData.messages.filter(m =>
                m.playerId === currentPlayer.id && !m.isRead
            );

            let notificationsHtml = '';
            if (unreadMessages.length > 0) {
                notificationsHtml = unreadMessages.map(msg => `
                    <div style="padding: 1rem; border-bottom: 1px solid #333;">
                        <strong>${msg.subject}</strong><br>
                        <small style="color: #888;">${msg.from} - ${msg.date}</small>
                    </div>
                `).join('');
            } else {
                notificationsHtml = '<div style="padding: 2rem; text-align: center; color: #888;">لا توجد إشعارات جديدة</div>';
            }

            Swal.fire({
                title: 'الإشعارات',
                html: `<div style="text-align: right; max-height: 400px; overflow-y: auto;">${notificationsHtml}</div>`,
                background: '#1a1a1a',
                color: '#ffffff',
                confirmButtonText: 'إغلاق'
            });
        }

        function showQuickActions() {
            Swal.fire({
                title: 'إجراءات سريعة',
                html: `
                    <div style="display: grid; gap: 1rem; text-align: center;">
                        <button onclick="addNewGoal()" class="swal2-confirm swal2-styled">إضافة هدف جديد</button>
                        <button onclick="sendMessageToCoach()" class="swal2-confirm swal2-styled">رسالة للمدرب</button>
                        <button onclick="viewUpcomingSessions()" class="swal2-confirm swal2-styled">الحصص القادمة</button>
                        <button onclick="checkPaymentStatus()" class="swal2-confirm swal2-styled">حالة المدفوعات</button>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                showConfirmButton: false,
                showCancelButton: true,
                cancelButtonText: 'إغلاق'
            });
        }

        // ==================== وظائف الإجراءات السريعة ====================
        function addNewGoal() {
            Swal.fire({
                title: 'إضافة هدف جديد',
                html: `
                    <div style="text-align: right;">
                        <input id="goalTitle" placeholder="عنوان الهدف" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                        <textarea id="goalDescription" placeholder="وصف الهدف" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white; height: 80px;"></textarea>
                        <input id="goalTargetDate" type="date" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                        <select id="goalCategory" style="width: 100%; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                            <option value="مهارات فنية">مهارات فنية</option>
                            <option value="لياقة بدنية">لياقة بدنية</option>
                            <option value="انضباط">انضباط</option>
                            <option value="إنجازات">إنجازات</option>
                        </select>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'إضافة الهدف',
                cancelButtonText: 'إلغاء',
                preConfirm: () => {
                    const title = document.getElementById('goalTitle').value;
                    const description = document.getElementById('goalDescription').value;
                    const targetDate = document.getElementById('goalTargetDate').value;
                    const category = document.getElementById('goalCategory').value;

                    if (!title || !description || !targetDate) {
                        Swal.showValidationMessage('يرجى ملء جميع الحقول');
                        return false;
                    }

                    return { title, description, targetDate, category };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    const newGoal = {
                        id: 'G' + Date.now(),
                        playerId: currentPlayer.id,
                        title: result.value.title,
                        description: result.value.description,
                        targetDate: result.value.targetDate,
                        progress: 0,
                        status: 'نشط',
                        category: result.value.category,
                        createdDate: new Date().toISOString().split('T')[0]
                    };

                    playerData.goals.push(newGoal);
                    savePlayerData();
                    updateNavigationBadges();

                    Swal.fire({
                        title: 'تم إضافة الهدف!',
                        text: 'تم إضافة هدفك الجديد بنجاح',
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        function sendMessageToCoach() {
            Swal.fire({
                title: 'رسالة للمدرب',
                html: `
                    <div style="text-align: right;">
                        <input id="messageSubject" placeholder="موضوع الرسالة" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                        <textarea id="messageContent" placeholder="محتوى الرسالة" style="width: 100%; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white; height: 120px;"></textarea>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'إرسال الرسالة',
                cancelButtonText: 'إلغاء',
                preConfirm: () => {
                    const subject = document.getElementById('messageSubject').value;
                    const content = document.getElementById('messageContent').value;

                    if (!subject || !content) {
                        Swal.showValidationMessage('يرجى ملء جميع الحقول');
                        return false;
                    }

                    return { subject, content };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'تم إرسال الرسالة!',
                        text: 'تم إرسال رسالتك للمدرب بنجاح',
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        function viewUpcomingSessions() {
            showSection('schedule');
        }

        function checkPaymentStatus() {
            showSection('payments');
        }

        function exportPersonalReport() {
            if (!currentPlayer) return;

            Swal.fire({
                title: 'تصدير التقرير الشخصي',
                text: 'جاري إنشاء التقرير...',
                background: '#1a1a1a',
                color: '#ffffff',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();

                    // محاكاة إنشاء التقرير
                    setTimeout(() => {
                        generatePersonalReport();
                        Swal.close();

                        Swal.fire({
                            title: 'تم إنشاء التقرير!',
                            text: 'تم تحميل التقرير الشخصي بنجاح',
                            icon: 'success',
                            background: '#1a1a1a',
                            color: '#ffffff'
                        });
                    }, 2000);
                }
            });
        }

        function generatePersonalReport() {
            const reportData = {
                playerInfo: currentPlayer,
                stats: {
                    attendanceRate: currentPlayer.attendanceRate,
                    performanceScore: currentPlayer.performanceScore,
                    completedSessions: currentPlayer.completedSessions,
                    totalSessions: currentPlayer.totalSessions,
                    achievedGoals: currentPlayer.achievedGoals,
                    totalGoals: currentPlayer.totalGoals
                },
                upcomingSessions: playerData.sessions.filter(s =>
                    s.playerId === currentPlayer.id && s.status === 'قادمة'
                ),
                activeGoals: playerData.goals.filter(g =>
                    g.playerId === currentPlayer.id && g.status === 'نشط'
                ),
                recentAchievements: playerData.achievements.filter(a =>
                    a.playerId === currentPlayer.id
                ),
                generatedDate: new Date().toLocaleDateString('ar-SA')
            };

            // محاكاة تحميل ملف PDF
            const reportContent = JSON.stringify(reportData, null, 2);
            const blob = new Blob([reportContent], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `تقرير-${currentPlayer.name}-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // ==================== وظائف الحصص ====================
        function markAttendance(sessionId) {
            const session = playerData.sessions.find(s => s.id === sessionId);
            if (!session) return;

            Swal.fire({
                title: 'تسجيل الحضور',
                text: `هل تريد تسجيل حضورك لحصة "${session.title}"؟`,
                icon: 'question',
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'نعم، سجل حضوري',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    session.status = 'مكتملة';
                    session.attendanceTime = new Date().toISOString();

                    // تحديث إحصائيات اللاعب
                    currentPlayer.completedSessions++;
                    currentPlayer.attendanceRate = Math.round((currentPlayer.completedSessions / currentPlayer.totalSessions) * 100);

                    savePlayerData();
                    updatePlayerInfo();
                    updateDashboard();

                    Swal.fire({
                        title: 'تم تسجيل الحضور!',
                        text: 'تم تسجيل حضورك بنجاح',
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        function viewSessionDetails(sessionId) {
            const session = playerData.sessions.find(s => s.id === sessionId);
            if (!session) return;

            Swal.fire({
                title: session.title,
                html: `
                    <div style="text-align: right;">
                        <p><strong>المدرب:</strong> ${session.coach}</p>
                        <p><strong>التاريخ:</strong> ${formatDate(session.date)}</p>
                        <p><strong>الوقت:</strong> ${session.time}</p>
                        <p><strong>المدة:</strong> ${session.duration} دقيقة</p>
                        <p><strong>المكان:</strong> ${session.location}</p>
                        <p><strong>النوع:</strong> ${session.type}</p>
                        <p><strong>الوصف:</strong> ${session.description}</p>
                        ${session.status === 'مكتملة' && session.rating ? `
                            <p><strong>التقييم:</strong> ${session.rating}/10</p>
                            <p><strong>ملاحظات:</strong> ${session.notes || 'لا توجد ملاحظات'}</p>
                        ` : ''}
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                confirmButtonText: 'إغلاق'
            });
        }

        // ==================== وظائف مساعدة ====================
        function formatDate(dateString) {
            const date = new Date(dateString);
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            };
            return date.toLocaleDateString('ar-SA', options);
        }

        function calculateDaysUntil(targetDate) {
            const today = new Date();
            const target = new Date(targetDate);
            const diffTime = target - today;
            return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        }

        function calculateDaysSince(startDate) {
            const today = new Date();
            const start = new Date(startDate);
            const diffTime = today - start;
            return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        }

        function changeChartPeriod(period) {
            // تحديث الرسم البياني حسب الفترة المحددة
            let months, data;

            switch(period) {
                case '3months':
                    months = ['أبريل', 'مايو', 'يونيو'];
                    data = [85, 88, 92];
                    break;
                case '6months':
                    months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'];
                    data = [75, 78, 82, 85, 88, 92];
                    break;
                case '1year':
                    months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                    data = [70, 72, 75, 78, 82, 85, 88, 92, 90, 93, 95, 97];
                    break;
                default:
                    return;
            }

            if (performanceChart) {
                performanceChart.data.labels = months;
                performanceChart.data.datasets[0].data = data;
                performanceChart.update();
            }

            // تحديث أزرار الفترة
            document.querySelectorAll('.card-actions .btn').forEach(btn => {
                btn.classList.remove('primary');
            });
            event.target.classList.add('primary');
        }

        function viewFullSchedule() {
            showSection('schedule');
        }

        function setupEventListeners() {
            // إضافة مستمعي الأحداث للتفاعل
            document.addEventListener('keydown', function(e) {
                // اختصارات لوحة المفاتيح
                if (e.altKey) {
                    switch(e.key) {
                        case '1':
                            showSection('overview');
                            break;
                        case '2':
                            showSection('profile');
                            break;
                        case '3':
                            showSection('schedule');
                            break;
                        case '4':
                            showSection('goals');
                            break;
                        case '5':
                            showSection('payments');
                            break;
                        case '6':
                            showSection('messages');
                            break;
                        case '7':
                            showSection('achievements');
                            break;
                    }
                }
            });

            // تحديث البيانات كل 30 ثانية
            setInterval(() => {
                if (currentPlayer) {
                    updateNavigationBadges();
                    runAIAnalysis();
                }
            }, 30000);
        }

        function logout() {
            Swal.fire({
                title: 'تسجيل الخروج',
                text: 'هل أنت متأكد من تسجيل الخروج؟',
                icon: 'question',
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'نعم، سجل خروجي',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    // مسح بيانات الجلسة
                    sessionStorage.removeItem('userSession');

                    // إظهار رسالة وداع
                    Swal.fire({
                        title: 'تم تسجيل الخروج',
                        text: 'شكراً لك، نراك قريباً!',
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff',
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        // إعادة التوجيه لصفحة تسجيل الدخول
                        window.location.href = 'login.html';
                    });
                }
            });
        }

        // ==================== تشغيل الذكاء الاصطناعي التلقائي ====================
        setInterval(() => {
            if (currentPlayer && Math.random() < 0.1) { // 10% احتمال كل دقيقة
                generateAIInsight();
            }
        }, 60000); // كل دقيقة

        function generateAIInsight() {
            const insights = [
                'تحسن أداؤك بنسبة 15% هذا الأسبوع! استمر على هذا المستوى الرائع.',
                'لاحظت أن أفضل أوقات تدريبك هي بعد الظهر. جرب جدولة المزيد من الحصص في هذا الوقت.',
                'أنت قريب من تحقيق هدفك في تحسين دقة التسديد. ركز على التدريبات الفردية.',
                'معدل حضورك ممتاز! هذا الانضباط سيساعدك على تحقيق أهدافك بسرعة.',
                'يُنصح بإضافة تمارين المرونة لروتينك التدريبي لتحسين الأداء العام.'
            ];

            const randomInsight = insights[Math.floor(Math.random() * insights.length)];

            // إظهار الإشعار
            if (Notification.permission === 'granted') {
                new Notification('💡 نصيحة ذكية من أكاديمية 7C', {
                    body: randomInsight,
                    icon: '/favicon.ico'
                });
            }

            console.log('🤖 نصيحة ذكية:', randomInsight);
        }

        // طلب إذن الإشعارات
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }

        console.log('🏆 لوحة تحكم اللاعب مع الذكاء الاصطناعي جاهزة!');
        console.log('⚡ الميزات المتاحة:');
        console.log('- تحليل الأداء الذكي');
        console.log('- توصيات شخصية');
        console.log('- تنبؤ بتحقيق الأهداف');
        console.log('- تصدير التقارير');
        console.log('- إشعارات ذكية');
        console.log('- اختصارات لوحة المفاتيح (Alt + 1-7)');
    </script>
</body>
</html>
