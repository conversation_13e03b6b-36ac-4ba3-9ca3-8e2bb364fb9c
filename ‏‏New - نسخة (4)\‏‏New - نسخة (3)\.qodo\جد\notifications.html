<?php
session_start();
if (!isset($_SESSION['user'])) {
  header("Location: index.html");
  exit;
}

$user = $_SESSION['user'];

$host = "localhost";
$db = "academy7c";
$db_user = "root";
$db_pass = "";
$conn = new mysqli($host, $db_user, $db_pass, $db);
if ($conn->connect_error) {
  die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
}

// إضافة إشعار يدوي من الإدارة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['target_user'], $_POST['message'], $_POST['type'])) {
  $target = intval($_POST['target_user']);
  $msg = $conn->real_escape_string($_POST['message']);
  $type = $conn->real_escape_string($_POST['type']);
  $conn->query("INSERT INTO notifications (user_id, message, type) VALUES ($target, '$msg', '$type')");
}

$type_filter = isset($_GET['type']) ? $_GET['type'] : '';
$sql = "SELECT message, created_at, type FROM notifications WHERE user_id = ?";
if (!empty($type_filter)) {
  $sql .= " AND type = ?";
}
$sql .= " ORDER BY created_at DESC";
$stmt = !empty($type_filter) ? $conn->prepare($sql) : $conn->prepare(str_replace(" AND type = ?", "", $sql));
if (!empty($type_filter)) {
  $stmt->bind_param("is", $user['id'], $type_filter);
} else {
  $stmt->bind_param("i", $user['id']);
}
$stmt->execute();
$result = $stmt->get_result();

$users = $conn->query("SELECT id, fullname FROM users WHERE role = 'player'");
?>

<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8">
  <title>الإشعارات - أكاديمية 7C</title>
  <style>
    body { font-family: Arial; background: #f0f2f5; direction: rtl; padding: 30px; text-align: center; }
    .notifications-box { max-width: 800px; margin: auto; background: #fff; padding: 25px; border-radius: 15px; box-shadow: 0 0 12px rgba(0,0,0,0.1); }
    h2 { color: #007BFF; margin-bottom: 20px; }
    .notification { background: #e9ecef; padding: 15px; border-radius: 10px; margin: 10px 0; text-align: right; }
    .timestamp { color: #888; font-size: 12px; margin-top: 5px; }
    .back-link, .home-link {
      margin-top: 20px; display: inline-block; padding: 10px 20px;
      background: #007BFF; color: white; text-decoration: none;
      border-radius: 6px; margin: 10px;
    }
    .back-link:hover, .home-link:hover { background: #0056b3; }
    .filter-box, .form-box { margin-bottom: 20px; }
    select, textarea { padding: 8px; border-radius: 5px; border: 1px solid #ccc; width: 100%; max-width: 300px; }
    button { padding: 10px 20px; margin-top: 10px; border: none; border-radius: 6px; background: #28a745; color: white; cursor: pointer; }
    button:hover { background: #218838; }
  </style>
</head>
<body>
  <div class="notifications-box">
    <h2>الإشعارات</h2>
    <div class="form-box">
      <h4>إرسال إشعار يدوي</h4>
      <form method="post">
        <select name="target_user" required>
          <option value="">اختر اللاعب</option>
          <?php while($u = $users->fetch_assoc()): ?>
            <option value="<?= $u['id'] ?>"><?= htmlspecialchars($u['fullname']) ?></option>
          <?php endwhile; ?>
        </select>
        <select name="type" required>
          <option value="payment">مدفوعات</option>
          <option value="attendance">الحضور</option>
          <option value="violation">مخالفة</option>
        </select>
        <textarea name="message" rows="3" placeholder="نص الإشعار" required></textarea>
        <button type="submit">إرسال الإشعار</button>
      </form>
    </div>

    <div class="filter-box">
      <form method="get">
        <label for="type">تصفية حسب النوع:</label>
        <select name="type" onchange="this.form.submit()">
          <option value="">الكل</option>
          <option value="payment" <?= $type_filter === 'payment' ? 'selected' : '' ?>>مدفوعات</option>
          <option value="attendance" <?= $type_filter === 'attendance' ? 'selected' : '' ?>>الحضور</option>
          <option value="violation" <?= $type_filter === 'violation' ? 'selected' : '' ?>>المخالفات</option>
        </select>
      </form>
    </div>

    <?php if ($result->num_rows > 0): ?>
      <?php while ($row = $result->fetch_assoc()): ?>
        <div class="notification">
          <strong>
            <?php
              if ($row['type'] === 'payment') echo '💳 '; 
              elseif ($row['type'] === 'attendance') echo '🕒 ';
              elseif ($row['type'] === 'violation') echo '⚠️ ';
              else echo '🔔 ';
            ?>
          </strong>
          <?= htmlspecialchars($row['message']) ?>
          <div class="timestamp">📅 <?= $row['created_at'] ?></div>
        </div>
      <?php endwhile; ?>
    <?php else: ?>
      <p>لا توجد إشعارات حالياً.</p>
    <?php endif; ?>

    <a href="home.php" class="home-link">⬅️ الرجوع للوحة التحكم</a>
    <a href="index.html" class="back-link">🔐 تسجيل الخروج</a>
  </div>
</body>
</html>
