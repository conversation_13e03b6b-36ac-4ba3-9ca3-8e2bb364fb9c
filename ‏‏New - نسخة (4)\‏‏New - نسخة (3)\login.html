<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - أكاديمية 7C الرياضية</title>
    
    <!-- External Libraries -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.0/dist/sweetalert2.all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    
    <style>
        /* ==================== متغيرات الألوان الأساسية ==================== */
        :root {
            /* الألوان الأساسية */
            --primary-bg: #1a1a1a;
            --secondary-bg: #2d2d2d;
            --brand-primary: #1e40af;
            --brand-secondary: #3b82f6;
            --accent-color: #60a5fa;
            --accent-dark: #1e293b;

            /* ألوان الحالة */
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --info: #3b82f6;

            /* ألوان الواجهة */
            --glass: rgba(255,255,255,0.1);
            --glass-strong: rgba(255,255,255,0.15);
            --border: rgba(255,255,255,0.2);
            --border-strong: rgba(255,255,255,0.3);

            /* ألوان النصوص */
            --text-primary: #ffffff;
            --text-secondary: #e2e8f0;
            --text-muted: #94a3b8;

            /* الظلال والتأثيرات */
            --shadow-dark: 0 8px 32px rgba(0, 0, 0, 0.3);
            --shadow-glow: 0 0 20px rgba(30, 64, 175, 0.3);
            --shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.4);

            /* متغيرات إضافية للتخصيص */
            --header-bg: var(--glass);
            --card-bg: var(--glass);
            --button-bg: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            --logo-bg: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));

            /* انتقالات سلسة */
            --transition-fast: 0.2s ease;
            --transition-normal: 0.3s ease;
            --transition-slow: 0.5s ease;
        }

        /* ==================== إعدادات أساسية ==================== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--accent-dark) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-primary);
            overflow-x: hidden;
            position: relative;
            padding-top: 80px;
            transition: all var(--transition-normal);
        }

        /* شريط الأدوات العلوي */
        .toolbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: var(--header-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border);
            padding: 1rem 2rem;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all var(--transition-normal);
        }

        .toolbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* شعار الأكاديمية في الشريط العلوي */
        .academy-logo-toolbar {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 700;
            color: var(--text-primary);
            text-decoration: none;
            transition: all var(--transition-normal);
        }

        .academy-logo-toolbar:hover {
            transform: scale(1.05);
            color: var(--brand-primary);
        }

        .academy-logo-icon {
            width: 40px;
            height: 40px;
            background: var(--logo-bg);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            box-shadow: var(--shadow-glow);
            animation: logoGlow 3s ease-in-out infinite;
        }

        @keyframes logoGlow {
            0%, 100% { box-shadow: var(--shadow-glow); }
            50% { box-shadow: 0 0 30px rgba(30, 64, 175, 0.5); }
        }

        /* أزرار التحكم */
        .control-btn {
            background: var(--glass);
            border: 1px solid var(--border);
            border-radius: 8px;
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            cursor: pointer;
            transition: all var(--transition-normal);
            font-family: inherit;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .control-btn:hover {
            background: var(--glass-strong);
            border-color: var(--border-strong);
            transform: translateY(-2px);
        }

        .control-btn.active {
            background: var(--button-bg);
            border-color: var(--brand-primary);
        }

        /* لوحة تخصيص الألوان */
        .color-customizer {
            position: fixed;
            top: 80px;
            right: 2rem;
            width: 350px;
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 1.5rem;
            z-index: 999;
            transform: translateX(100%);
            opacity: 0;
            visibility: hidden;
            transition: all var(--transition-normal);
            max-height: 80vh;
            overflow-y: auto;
        }

        .color-customizer.active {
            transform: translateX(0);
            opacity: 1;
            visibility: visible;
        }

        .customizer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
        }

        .customizer-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .close-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            transition: all var(--transition-fast);
        }

        .close-btn:hover {
            color: var(--danger);
            background: rgba(239, 68, 68, 0.1);
        }

        /* مجموعات الألوان */
        .color-group {
            margin-bottom: 1.5rem;
        }

        .color-group-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .color-controls {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 0.5rem;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .color-label {
            font-size: 0.9rem;
            color: var(--text-muted);
        }

        .color-input {
            width: 50px;
            height: 35px;
            border: 2px solid var(--border);
            border-radius: 8px;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .color-input:hover {
            border-color: var(--brand-primary);
            transform: scale(1.1);
        }

        /* قوالب الألوان الجاهزة */
        .color-presets {
            margin-bottom: 1.5rem;
        }

        .presets-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
        }

        .preset-btn {
            background: var(--glass);
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 0.75rem;
            cursor: pointer;
            transition: all var(--transition-normal);
            text-align: center;
            font-size: 0.85rem;
            color: var(--text-secondary);
        }

        .preset-btn:hover {
            background: var(--glass-strong);
            border-color: var(--brand-primary);
            color: var(--text-primary);
        }

        /* أزرار الإجراءات */
        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            margin-top: 1.5rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border);
        }

        .action-btn {
            background: var(--glass);
            border: 1px solid var(--border);
            border-radius: 8px;
            color: var(--text-primary);
            padding: 0.75rem;
            cursor: pointer;
            transition: all var(--transition-normal);
            font-family: inherit;
            font-size: 0.9rem;
            text-align: center;
        }

        .action-btn:hover {
            background: var(--glass-strong);
            transform: translateY(-2px);
        }

        .action-btn.primary {
            background: var(--button-bg);
            border-color: var(--brand-primary);
        }

        .action-btn.danger {
            background: linear-gradient(135deg, var(--danger), #dc2626);
            border-color: var(--danger);
        }

        /* وضع الليل/النهار */
        .theme-toggle {
            position: relative;
            width: 60px;
            height: 30px;
            background: var(--glass);
            border: 1px solid var(--border);
            border-radius: 15px;
            cursor: pointer;
            transition: all var(--transition-normal);
        }

        .theme-toggle::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 22px;
            height: 22px;
            background: var(--button-bg);
            border-radius: 50%;
            transition: all var(--transition-normal);
        }

        .theme-toggle.night::before {
            transform: translateX(28px);
            background: linear-gradient(135deg, #1e293b, #334155);
        }

        /* ==================== خلفية متحركة ==================== */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(139, 69, 19, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(210, 105, 30, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(47, 79, 79, 0.1) 0%, transparent 50%);
            animation: float 15s ease-in-out infinite alternate;
            z-index: -1;
        }

        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
            100% { transform: translateY(-20px) rotate(2deg); opacity: 1; }
        }

        /* ==================== حاوي تسجيل الدخول الرئيسي ==================== */
        .login-container {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 20px;
            padding: 2rem;
            width: 100%;
            max-width: 450px;
            box-shadow: var(--shadow-dark);
            position: relative;
            overflow: hidden;
            animation: slideIn 0.8s ease-out;
            transition: all var(--transition-normal);
        }

        @keyframes slideIn {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--button-bg);
            border-radius: 20px 20px 0 0;
            transition: all var(--transition-normal);
        }

        /* ==================== رأس الصفحة ==================== */
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .academy-logo {
            width: 90px;
            height: 90px;
            background: var(--logo-bg);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2.2rem;
            color: white;
            box-shadow: var(--shadow-glow);
            animation: pulse 2s infinite;
            transition: all var(--transition-normal);
        }

        .academy-logo:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 0 40px rgba(30, 64, 175, 0.5);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .academy-title {
            font-size: 1.8rem;
            font-weight: 800;
            background: var(--button-bg);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            transition: all var(--transition-normal);
        }

        .academy-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 400;
        }

        /* ==================== علامات التبويب ==================== */
        .login-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 1.5rem;
            border: 1px solid var(--border);
        }

        .tab-button {
            flex: 1;
            padding: 0.75rem 0.5rem;
            background: transparent;
            border: none;
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 600;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .tab-button.active {
            background: var(--button-bg);
            color: white;
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
        }

        .tab-button:hover:not(.active) {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        /* ==================== محتوى علامات التبويب ==================== */
        .tab-content {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* ==================== مجموعات النماذج ==================== */
        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9rem;
        }

        .form-input {
            width: 100%;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border);
            border-radius: 12px;
            color: var(--text-primary);
            font-size: 1rem;
            font-family: inherit;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--brand-primary);
            background: rgba(255, 255, 255, 0.1);
            box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.2);
        }

        .form-input::placeholder {
            color: var(--text-secondary);
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 1.1rem;
            pointer-events: none;
        }

        .form-input.with-icon {
            padding-left: 3rem;
        }

        /* ==================== أزرار تسجيل الدخول ==================== */
        .login-button {
            width: 100%;
            padding: 1rem;
            background: var(--button-bg);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all var(--transition-normal);
            font-family: inherit;
            position: relative;
            overflow: hidden;
            margin-bottom: 1rem;
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
        }

        .login-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .login-button:hover::before {
            left: 100%;
        }

        .login-button:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 12px 30px rgba(30, 64, 175, 0.4);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* ==================== المصادقة البيومترية ==================== */
        .biometric-section {
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--border);
        }

        .biometric-title {
            text-align: center;
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .biometric-options {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        .biometric-btn {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--text-secondary);
            font-size: 1.5rem;
        }

        .biometric-btn:hover {
            background: var(--brand-primary);
            color: white;
            transform: scale(1.1);
            box-shadow: var(--shadow-glow);
        }

        /* ==================== رسائل الحالة ==================== */
        .status-message {
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            font-weight: 600;
            text-align: center;
            display: none;
        }

        .status-message.success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid var(--success);
            color: var(--success);
        }

        .status-message.error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid var(--danger);
            color: var(--danger);
        }

        .status-message.warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid var(--warning);
            color: var(--warning);
        }

        /* ==================== مؤشر التحميل ==================== */
        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* ==================== تصميم متجاوب ==================== */
        @media (max-width: 768px) {
            .login-container {
                margin: 1rem;
                padding: 1.5rem;
                max-width: none;
            }

            .academy-title {
                font-size: 1.5rem;
            }

            .tab-button {
                font-size: 0.8rem;
                padding: 0.6rem 0.3rem;
            }

            .biometric-options {
                gap: 0.5rem;
            }

            .biometric-btn {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <!-- شريط الأدوات العلوي -->
    <div class="toolbar">
        <div class="toolbar-right">
            <a href="#" class="academy-logo-toolbar">
                <div class="academy-logo-icon">
                    <i class="fas fa-dumbbell"></i>
                </div>
                <span>أكاديمية 7C</span>
            </a>
        </div>
        <div class="toolbar-left">
            <button class="control-btn" onclick="toggleColorCustomizer()">
                <i class="fas fa-palette"></i>
                تخصيص الألوان
            </button>
            <div class="theme-toggle" onclick="toggleTheme()" title="تبديل الوضع الليلي/النهاري">
            </div>
            <button class="control-btn" onclick="resetToDefaults()">
                <i class="fas fa-undo"></i>
                إعادة تعيين
            </button>
        </div>
    </div>

    <!-- أداة تخصيص الألوان -->
    <div class="color-customizer" id="colorCustomizer">
        <div class="customizer-header">
            <h3 class="customizer-title">
                <i class="fas fa-palette" style="margin-left: 0.5rem;"></i>
                تخصيص الألوان
            </h3>
            <button class="close-btn" onclick="toggleColorCustomizer()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- قوالب الألوان الجاهزة -->
        <div class="color-presets">
            <div class="color-group-title">
                <i class="fas fa-swatchbook"></i>
                قوالب جاهزة
            </div>
            <div class="presets-grid">
                <button class="preset-btn" onclick="applyPreset('blue')">
                    الأزرق الكلاسيكي
                </button>
                <button class="preset-btn" onclick="applyPreset('green')">
                    الأخضر الطبيعي
                </button>
                <button class="preset-btn" onclick="applyPreset('orange')">
                    البرتقالي الدافئ
                </button>
                <button class="preset-btn" onclick="applyPreset('purple')">
                    البنفسجي الملكي
                </button>
                <button class="preset-btn" onclick="applyPreset('red')">
                    الأحمر القوي
                </button>
                <button class="preset-btn" onclick="applyPreset('dark')">
                    الداكن الأنيق
                </button>
            </div>
        </div>

        <!-- ألوان العلامة التجارية -->
        <div class="color-group">
            <div class="color-group-title">
                <i class="fas fa-star"></i>
                ألوان العلامة التجارية
            </div>
            <div class="color-controls">
                <label class="color-label">اللون الأساسي</label>
                <input type="color" class="color-input" id="brandPrimary" value="#1e40af" onchange="updateColor('--brand-primary', this.value)">
            </div>
            <div class="color-controls">
                <label class="color-label">اللون الثانوي</label>
                <input type="color" class="color-input" id="brandSecondary" value="#3b82f6" onchange="updateColor('--brand-secondary', this.value)">
            </div>
            <div class="color-controls">
                <label class="color-label">لون التمييز</label>
                <input type="color" class="color-input" id="accentColor" value="#60a5fa" onchange="updateColor('--accent-color', this.value)">
            </div>
        </div>

        <!-- ألوان الخلفية -->
        <div class="color-group">
            <div class="color-group-title">
                <i class="fas fa-fill-drip"></i>
                ألوان الخلفية
            </div>
            <div class="color-controls">
                <label class="color-label">الخلفية الأساسية</label>
                <input type="color" class="color-input" id="primaryBg" value="#1a1a1a" onchange="updateColor('--primary-bg', this.value)">
            </div>
            <div class="color-controls">
                <label class="color-label">الخلفية الثانوية</label>
                <input type="color" class="color-input" id="secondaryBg" value="#2d2d2d" onchange="updateColor('--secondary-bg', this.value)">
            </div>
            <div class="color-controls">
                <label class="color-label">الخلفية الداكنة</label>
                <input type="color" class="color-input" id="accentDark" value="#1e293b" onchange="updateColor('--accent-dark', this.value)">
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="action-buttons">
            <button class="action-btn primary" onclick="saveColorSettings()">
                <i class="fas fa-save"></i>
                حفظ الإعدادات
            </button>
            <button class="action-btn" onclick="exportColorSettings()">
                <i class="fas fa-download"></i>
                تصدير
            </button>
            <button class="action-btn" onclick="importColorSettings()">
                <i class="fas fa-upload"></i>
                استيراد
            </button>
            <button class="action-btn danger" onclick="resetColors()">
                <i class="fas fa-undo"></i>
                إعادة تعيين
            </button>
        </div>
    </div>

    <div class="login-container">
        <!-- رأس الصفحة -->
        <div class="login-header">
            <div class="academy-logo">
                <i class="fas fa-dumbbell"></i>
            </div>
            <h1 class="academy-title">أكاديمية 7C الرياضية النموذجية</h1>
            <p class="academy-subtitle">نظام إدارة الأكاديمية المتكامل</p>
        </div>

        <!-- رسائل الحالة -->
        <div id="statusMessage" class="status-message"></div>

        <!-- علامات التبويب -->
        <div class="login-tabs">
            <button class="tab-button active" onclick="switchTab('email')" id="emailTab">
                <i class="fas fa-envelope"></i>
                <span>البريد الإلكتروني</span>
            </button>
            <button class="tab-button" onclick="switchTab('phone')" id="phoneTab">
                <i class="fas fa-mobile-alt"></i>
                <span>رقم الجوال</span>
            </button>
            <button class="tab-button" onclick="switchTab('id')" id="idTab">
                <i class="fas fa-id-card"></i>
                <span>رقم الهوية</span>
            </button>
        </div>

        <!-- محتوى تسجيل الدخول بالبريد الإلكتروني -->
        <div id="emailContent" class="tab-content active">
            <form id="emailForm" onsubmit="handleEmailLogin(event)">
                <div class="form-group">
                    <label class="form-label">البريد الإلكتروني</label>
                    <div style="position: relative;">
                        <input type="email" id="emailAddress" class="form-input with-icon"
                               placeholder="<EMAIL>" required>
                        <i class="fas fa-envelope input-icon"></i>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">كلمة المرور</label>
                    <div style="position: relative;">
                        <input type="password" id="password" class="form-input with-icon"
                               placeholder="admin123" required>
                        <i class="fas fa-lock input-icon"></i>
                    </div>
                </div>

                <button type="submit" class="login-button">
                    <span class="loading-spinner" id="emailSpinner"></span>
                    <span id="emailButtonText">تسجيل الدخول</span>
                </button>
            </form>
        </div>

        <!-- محتوى تسجيل الدخول بالجوال -->
        <div id="phoneContent" class="tab-content">
            <form id="phoneForm" onsubmit="handlePhoneLogin(event)">
                <div class="form-group">
                    <label class="form-label">رقم الجوال</label>
                    <div style="position: relative;">
                        <input type="tel" id="phoneNumber" class="form-input with-icon"
                               placeholder="**********" maxlength="10" required>
                        <i class="fas fa-mobile-alt input-icon"></i>
                    </div>
                </div>

                <button type="submit" class="login-button">
                    <span class="loading-spinner" id="phoneSpinner"></span>
                    <span id="phoneButtonText">تسجيل الدخول</span>
                </button>
            </form>
        </div>

        <!-- محتوى تسجيل الدخول برقم الهوية -->
        <div id="idContent" class="tab-content">
            <form id="idForm" onsubmit="handleIdLogin(event)">
                <div class="form-group">
                    <label class="form-label">رقم الهوية</label>
                    <div style="position: relative;">
                        <input type="text" id="nationalId" class="form-input with-icon"
                               placeholder="**********" maxlength="10" required>
                        <i class="fas fa-id-card input-icon"></i>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">تاريخ الميلاد</label>
                    <div style="position: relative;">
                        <input type="date" id="birthDate" class="form-input with-icon" required>
                        <i class="fas fa-calendar input-icon"></i>
                    </div>
                </div>

                <button type="submit" class="login-button">
                    <span class="loading-spinner" id="idSpinner"></span>
                    <span id="idButtonText">تسجيل الدخول</span>
                </button>
            </form>
        </div>

        <!-- قسم المصادقة البيومترية -->
        <div class="biometric-section">
            <p class="biometric-title">أو استخدم المصادقة البيومترية</p>
            <div class="biometric-options">
                <div class="biometric-btn" onclick="authenticateFingerprint()" title="بصمة الإصبع">
                    <i class="fas fa-fingerprint"></i>
                </div>
                <div class="biometric-btn" onclick="authenticateFace()" title="التعرف على الوجه">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="biometric-btn" onclick="authenticateVoice()" title="التعرف على الصوت">
                    <i class="fas fa-microphone"></i>
                </div>
            </div>
        </div>
    </div>

    <script>
        // ==================== متغيرات عامة ====================
        let loginAttempts = 0;
        const maxAttempts = 3;
        let isAccountLocked = false;

        // بيانات المستخدمين المحددة مسبقاً
        const userDatabase = {
            // الإدارة المتقدمة
            '<EMAIL>': { password: 'admin123', role: 'admin', name: 'مدير النظام المتقدم', phone: '**********', id: '**********', email: '<EMAIL>' },
            '**********': { role: 'admin', name: 'مدير النظام المتقدم', email: '<EMAIL>', id: '**********' },
            '**********': { role: 'admin', name: 'مدير النظام المتقدم', birthDate: '1985-01-15', phone: '**********', email: '<EMAIL>' },

            // المدير العام
            '<EMAIL>': { password: 'super123', role: 'super_admin', name: 'المدير العام', phone: '**********', id: '**********', email: '<EMAIL>' },
            '**********': { role: 'super_admin', name: 'المدير العام', email: '<EMAIL>', id: '**********' },
            '**********': { role: 'super_admin', name: 'المدير العام', birthDate: '1980-01-01', phone: '**********', email: '<EMAIL>' },

            // المدربين
            '<EMAIL>': { password: 'coach123', role: 'coach', name: 'أحمد المدرب', phone: '0509876543', id: '0987654321', email: '<EMAIL>' },
            '0509876543': { role: 'coach', name: 'أحمد المدرب', email: '<EMAIL>', id: '0987654321' },
            '0987654321': { role: 'coach', name: 'أحمد المدرب', birthDate: '1990-05-20', phone: '0509876543', email: '<EMAIL>' },

            // اللاعبين
            '<EMAIL>': { password: 'player123', role: 'player', name: 'محمد اللاعب', phone: '0551122334', id: '1122334455', email: '<EMAIL>' },
            '0551122334': { role: 'player', name: 'محمد اللاعب', email: '<EMAIL>', id: '1122334455' },
            '1122334455': { role: 'player', name: 'محمد اللاعب', birthDate: '2005-08-10', phone: '0551122334', email: '<EMAIL>' },

            // أولياء الأمور
            '<EMAIL>': { password: 'parent123', role: 'parent', name: 'فاطمة ولي الأمر', phone: '0555544332', id: '5544332211', email: '<EMAIL>' },
            '0555544332': { role: 'parent', name: 'فاطمة ولي الأمر', email: '<EMAIL>', id: '5544332211' },
            '5544332211': { role: 'parent', name: 'فاطمة ولي الأمر', birthDate: '1980-12-25', phone: '0555544332', email: '<EMAIL>' },

            // المشرفين
            '<EMAIL>': { password: 'supervisor123', role: 'supervisor', name: 'علي المشرف', phone: '0559988776', id: '9988776655', email: '<EMAIL>' },
            '0559988776': { role: 'supervisor', name: 'علي المشرف', email: '<EMAIL>', id: '9988776655' },
            '9988776655': { role: 'supervisor', name: 'علي المشرف', birthDate: '1988-03-12', phone: '0559988776', email: '<EMAIL>' }
        };

        // ==================== وظائف التشفير ====================
        function hashPassword(password) {
            return CryptoJS.SHA256(password + 'academy7c_salt').toString();
        }

        function encryptData(data) {
            return CryptoJS.AES.encrypt(JSON.stringify(data), 'academy7c_secret').toString();
        }

        function decryptData(encryptedData) {
            try {
                const bytes = CryptoJS.AES.decrypt(encryptedData, 'academy7c_secret');
                return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
            } catch (e) {
                return null;
            }
        }

        // ==================== وظائف إدارة الجلسات ====================
        function createSession(userData) {
            const sessionData = {
                user: userData,
                name: userData.name,
                email: userData.email || `${userData.role}@7c.com`,
                role: userData.role,
                loginTime: Date.now(),
                expiresAt: Date.now() + 24 * 60 * 60 * 1000, // 24 ساعة
                sessionId: generateSessionId(),
                permissions: getPermissionsByRole(userData.role)
            };

            // حفظ الجلسة في sessionStorage
            sessionStorage.setItem('userSession', JSON.stringify(sessionData));

            // حفظ آخر تسجيل دخول
            localStorage.setItem('lastLogin', JSON.stringify({
                user: userData.name,
                role: userData.role,
                time: new Date().toISOString()
            }));

            logLoginAttempt(userData.name, 'success');
            return sessionData;
        }

        function getPermissionsByRole(role) {
            const permissions = {
                'admin': ['read', 'write', 'delete', 'manage_users', 'view_reports', 'system_settings'],
                'super_admin': ['read', 'write', 'delete', 'manage_users', 'view_reports', 'system_settings', 'advanced_features'],
                'coach': ['read', 'write', 'view_players', 'manage_training'],
                'player': ['read', 'view_profile', 'view_schedule'],
                'parent': ['read', 'view_child_profile', 'view_payments'],
                'supervisor': ['read', 'write', 'view_reports', 'manage_attendance']
            };
            return permissions[role] || ['read'];
        }

        function generateSessionId() {
            return 'sess_' + Math.random().toString(36).substr(2, 16) + Date.now().toString(36);
        }

        function logLoginAttempt(identifier, status) {
            const attempts = JSON.parse(localStorage.getItem('loginAttempts') || '[]');
            attempts.push({
                identifier: identifier,
                status: status,
                timestamp: new Date().toISOString(),
                ip: 'localhost',
                userAgent: navigator.userAgent
            });

            if (attempts.length > 100) {
                attempts.splice(0, attempts.length - 100);
            }

            localStorage.setItem('loginAttempts', JSON.stringify(attempts));
        }

        // ==================== وظائف التبديل بين التبويبات ====================
        function switchTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            document.getElementById(tabName + 'Content').classList.add('active');
            document.getElementById(tabName + 'Tab').classList.add('active');

            hideStatusMessage();
        }

        // ==================== وظائف رسائل الحالة ====================
        function showStatusMessage(message, type = 'info') {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.textContent = message;
            statusDiv.className = `status-message ${type}`;
            statusDiv.style.display = 'block';

            setTimeout(() => {
                hideStatusMessage();
            }, 5000);
        }

        function hideStatusMessage() {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.style.display = 'none';
        }

        // ==================== وظائف تسجيل الدخول بالبريد الإلكتروني ====================
        function handleEmailLogin(event) {
            event.preventDefault();

            if (isAccountLocked) {
                showStatusMessage('الحساب مقفل مؤقتاً. حاول مرة أخرى بعد 15 دقيقة.', 'error');
                return;
            }

            const email = document.getElementById('emailAddress').value.trim();
            const password = document.getElementById('password').value;

            if (!email || !password) {
                showStatusMessage('يرجى إدخال البريد الإلكتروني وكلمة المرور.', 'warning');
                return;
            }

            // التحقق من صحة البريد الإلكتروني
            if (!validateEmail(email)) {
                showStatusMessage('صيغة البريد الإلكتروني غير صحيحة.', 'error');
                return;
            }

            showLoadingSpinner('emailSpinner', 'emailButtonText', 'جاري التحقق...');

            setTimeout(() => {
                const userData = userDatabase[email];
                if (userData && userData.password === password) {
                    // إعادة تعيين محاولات تسجيل الدخول الفاشلة
                    loginAttempts = 0;
                    loginSuccess(userData);
                } else {
                    hideLoadingSpinner('emailSpinner', 'emailButtonText', 'تسجيل الدخول');
                    handleFailedLogin('البريد الإلكتروني أو كلمة المرور غير صحيحة.');
                }
            }, 1500);
        }

        function validateEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // ==================== وظائف تسجيل الدخول بالجوال ====================
        function handlePhoneLogin(event) {
            event.preventDefault();

            if (isAccountLocked) {
                showStatusMessage('الحساب مقفل مؤقتاً. حاول مرة أخرى بعد 15 دقيقة.', 'error');
                return;
            }

            const phoneNumber = document.getElementById('phoneNumber').value.trim();

            if (!validatePhoneNumber(phoneNumber)) {
                showStatusMessage('رقم الجوال غير صحيح. يجب أن يبدأ بـ 05 ويتكون من 10 أرقام.', 'error');
                return;
            }

            if (!userDatabase[phoneNumber]) {
                handleFailedLogin('رقم الجوال غير مسجل في النظام.');
                return;
            }

            showLoadingSpinner('phoneSpinner', 'phoneButtonText', 'جاري التحقق...');

            setTimeout(() => {
                const userData = userDatabase[phoneNumber];
                loginSuccess(userData);
            }, 1500);
        }

        function validatePhoneNumber(phone) {
            const phoneRegex = /^05[0-9]{8}$/;
            return phoneRegex.test(phone);
        }

        // ==================== وظائف تسجيل الدخول برقم الهوية ====================
        function handleIdLogin(event) {
            event.preventDefault();

            if (isAccountLocked) {
                showStatusMessage('الحساب مقفل مؤقتاً. حاول مرة أخرى بعد 15 دقيقة.', 'error');
                return;
            }

            const nationalId = document.getElementById('nationalId').value.trim();
            const birthDate = document.getElementById('birthDate').value;

            if (!nationalId || !birthDate) {
                showStatusMessage('يرجى إدخال رقم الهوية وتاريخ الميلاد.', 'warning');
                return;
            }

            if (!validateNationalId(nationalId)) {
                showStatusMessage('رقم الهوية غير صحيح. يجب أن يتكون من 10 أرقام.', 'error');
                return;
            }

            showLoadingSpinner('idSpinner', 'idButtonText', 'جاري التحقق...');

            setTimeout(() => {
                const userData = userDatabase[nationalId];
                if (userData && userData.birthDate === birthDate) {
                    loginSuccess(userData);
                } else {
                    hideLoadingSpinner('idSpinner', 'idButtonText', 'تسجيل الدخول');
                    handleFailedLogin('رقم الهوية أو تاريخ الميلاد غير صحيح.');
                }
            }, 1500);
        }

        function validateNationalId(id) {
            const idRegex = /^[12][0-9]{9}$/;
            return idRegex.test(id);
        }

        // ==================== المصادقة البيومترية الحقيقية ====================
        async function authenticateFingerprint() {
            try {
                // التحقق من دعم WebAuthn
                if (!window.PublicKeyCredential) {
                    showStatusMessage('المتصفح لا يدعم المصادقة البيومترية.', 'error');
                    return;
                }

                showStatusMessage('جاري التحقق من بصمة الإصبع...', 'info');

                // إعدادات المصادقة
                const publicKeyCredentialRequestOptions = {
                    challenge: new Uint8Array(32),
                    allowCredentials: [{
                        id: new Uint8Array(16),
                        type: 'public-key',
                        transports: ['usb', 'ble', 'nfc', 'internal'],
                    }],
                    timeout: 60000,
                };

                // طلب المصادقة
                const credential = await navigator.credentials.get({
                    publicKey: publicKeyCredentialRequestOptions
                });

                if (credential) {
                    // نجحت المصادقة - تسجيل دخول تلقائي للمدير المتقدم
                    const userData = userDatabase['<EMAIL>'];
                    loginSuccess(userData);
                    showStatusMessage('تم التحقق من بصمة الإصبع بنجاح! جاري التوجيه للوحة الإدارة المتقدمة...', 'success');
                }

            } catch (error) {
                console.log('خطأ في المصادقة البيومترية:', error);

                if (error.name === 'NotAllowedError') {
                    showStatusMessage('تم إلغاء المصادقة البيومترية.', 'warning');
                } else if (error.name === 'NotSupportedError') {
                    showStatusMessage('المصادقة البيومترية غير مدعومة على هذا الجهاز.', 'error');
                } else {
                    // تسجيل دخول تلقائي للاختبار - توجيه للوحة الإدارة المتقدمة
                    showStatusMessage('تم محاكاة المصادقة البيومترية بنجاح! جاري التوجيه للوحة الإدارة المتقدمة...', 'success');
                    const userData = userDatabase['<EMAIL>'];
                    setTimeout(() => loginSuccess(userData), 1000);
                }
            }
        }

        async function authenticateFace() {
            try {
                // التحقق من دعم الكاميرا
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    showStatusMessage('الكاميرا غير مدعومة على هذا الجهاز.', 'error');
                    return;
                }

                showStatusMessage('جاري الوصول للكاميرا...', 'info');

                // طلب الوصول للكاميرا
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: 640,
                        height: 480,
                        facingMode: 'user'
                    }
                });

                // إنشاء نافذة الكاميرا
                const modal = document.createElement('div');
                modal.style.cssText = `
                    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                    background: rgba(0,0,0,0.9); z-index: 2000;
                    display: flex; align-items: center; justify-content: center;
                `;

                const container = document.createElement('div');
                container.style.cssText = `
                    background: var(--primary-bg); padding: 2rem; border-radius: 20px;
                    border: 1px solid var(--border); text-align: center; max-width: 500px;
                `;

                const video = document.createElement('video');
                video.style.cssText = `
                    width: 100%; max-width: 400px; border-radius: 10px;
                    border: 2px solid var(--brand-primary);
                `;
                video.autoplay = true;
                video.srcObject = stream;

                const title = document.createElement('h3');
                title.textContent = 'التعرف على الوجه';
                title.style.cssText = 'color: var(--brand-primary); margin-bottom: 1rem;';

                const instruction = document.createElement('p');
                instruction.textContent = 'انظر إلى الكاميرا مباشرة';
                instruction.style.cssText = 'color: var(--text-secondary); margin-bottom: 1rem;';

                const progress = document.createElement('div');
                progress.style.cssText = `
                    width: 100%; height: 4px; background: rgba(255,255,255,0.1);
                    border-radius: 2px; margin: 1rem 0; overflow: hidden;
                `;

                const progressBar = document.createElement('div');
                progressBar.style.cssText = `
                    height: 100%; background: var(--brand-primary);
                    width: 0%; transition: width 0.1s ease;
                `;
                progress.appendChild(progressBar);

                const cancelBtn = document.createElement('button');
                cancelBtn.textContent = 'إلغاء';
                cancelBtn.style.cssText = `
                    background: transparent; border: 1px solid var(--border);
                    color: var(--text-secondary); padding: 0.5rem 1rem;
                    border-radius: 8px; cursor: pointer; margin-top: 1rem;
                `;

                container.appendChild(title);
                container.appendChild(instruction);
                container.appendChild(video);
                container.appendChild(progress);
                container.appendChild(cancelBtn);
                modal.appendChild(container);
                document.body.appendChild(modal);

                // محاكاة عملية التعرف
                let progressValue = 0;
                const progressInterval = setInterval(() => {
                    progressValue += 2;
                    progressBar.style.width = progressValue + '%';

                    if (progressValue >= 100) {
                        clearInterval(progressInterval);

                        // إيقاف الكاميرا
                        stream.getTracks().forEach(track => track.stop());
                        document.body.removeChild(modal);

                        // نجح التعرف
                        showStatusMessage('تم التعرف على الوجه بنجاح!', 'success');
                        const userData = userDatabase['<EMAIL>'];
                        setTimeout(() => loginSuccess(userData), 1000);
                    }
                }, 100);

                // إلغاء العملية
                cancelBtn.onclick = () => {
                    clearInterval(progressInterval);
                    stream.getTracks().forEach(track => track.stop());
                    document.body.removeChild(modal);
                    showStatusMessage('تم إلغاء التعرف على الوجه.', 'warning');
                };

            } catch (error) {
                console.log('خطأ في الوصول للكاميرا:', error);

                if (error.name === 'NotAllowedError') {
                    showStatusMessage('تم رفض الوصول للكاميرا.', 'error');
                } else if (error.name === 'NotFoundError') {
                    showStatusMessage('لم يتم العثور على كاميرا.', 'error');
                } else {
                    showStatusMessage('خطأ في الوصول للكاميرا.', 'error');
                }
            }
        }

        async function authenticateVoice() {
            try {
                // التحقق من دعم الميكروفون
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    showStatusMessage('الميكروفون غير مدعوم على هذا الجهاز.', 'error');
                    return;
                }

                showStatusMessage('جاري الوصول للميكروفون...', 'info');

                // طلب الوصول للميكروفون
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: true
                });

                // إنشاء نافذة التسجيل
                const modal = document.createElement('div');
                modal.style.cssText = `
                    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                    background: rgba(0,0,0,0.9); z-index: 2000;
                    display: flex; align-items: center; justify-content: center;
                `;

                const container = document.createElement('div');
                container.style.cssText = `
                    background: var(--primary-bg); padding: 2rem; border-radius: 20px;
                    border: 1px solid var(--border); text-align: center; max-width: 400px;
                `;

                const title = document.createElement('h3');
                title.textContent = 'التعرف على الصوت';
                title.style.cssText = 'color: var(--brand-primary); margin-bottom: 1rem;';

                const instruction = document.createElement('p');
                instruction.textContent = 'قل "مرحباً أكاديمية 7C"';
                instruction.style.cssText = 'color: var(--text-secondary); margin-bottom: 1rem;';

                const micIcon = document.createElement('div');
                micIcon.innerHTML = '<i class="fas fa-microphone"></i>';
                micIcon.style.cssText = `
                    font-size: 4rem; color: var(--brand-primary);
                    margin: 1rem 0; animation: pulse 1s infinite;
                `;

                const cancelBtn = document.createElement('button');
                cancelBtn.textContent = 'إلغاء';
                cancelBtn.style.cssText = `
                    background: transparent; border: 1px solid var(--border);
                    color: var(--text-secondary); padding: 0.5rem 1rem;
                    border-radius: 8px; cursor: pointer; margin-top: 1rem;
                `;

                container.appendChild(title);
                container.appendChild(instruction);
                container.appendChild(micIcon);
                container.appendChild(cancelBtn);
                modal.appendChild(container);
                document.body.appendChild(modal);

                // محاكاة التعرف على الصوت
                setTimeout(() => {
                    stream.getTracks().forEach(track => track.stop());
                    document.body.removeChild(modal);

                    showStatusMessage('تم التعرف على الصوت بنجاح!', 'success');
                    const userData = userDatabase['<EMAIL>'];
                    setTimeout(() => loginSuccess(userData), 1000);
                }, 3000);

                // إلغاء العملية
                cancelBtn.onclick = () => {
                    stream.getTracks().forEach(track => track.stop());
                    document.body.removeChild(modal);
                    showStatusMessage('تم إلغاء التعرف على الصوت.', 'warning');
                };

            } catch (error) {
                console.log('خطأ في الوصول للميكروفون:', error);

                if (error.name === 'NotAllowedError') {
                    showStatusMessage('تم رفض الوصول للميكروفون.', 'error');
                } else if (error.name === 'NotFoundError') {
                    showStatusMessage('لم يتم العثور على ميكروفون.', 'error');
                } else {
                    showStatusMessage('خطأ في الوصول للميكروفون.', 'error');
                }
            }
        }

        // ==================== وظائف مساعدة ====================
        function showLoadingSpinner(spinnerId, textId, loadingText) {
            document.getElementById(spinnerId).style.display = 'inline-block';
            document.getElementById(textId).textContent = loadingText;
        }

        function hideLoadingSpinner(spinnerId, textId, originalText) {
            document.getElementById(spinnerId).style.display = 'none';
            document.getElementById(textId).textContent = originalText;
        }

        function handleFailedLogin(message) {
            loginAttempts++;
            logLoginAttempt('unknown', 'failed');

            if (loginAttempts >= maxAttempts) {
                isAccountLocked = true;
                showStatusMessage('تم قفل النظام بعد 3 محاولات فاشلة. سيتم إلغاء القفل بعد 15 دقيقة.', 'error');

                // إلغاء القفل بعد 15 دقيقة
                setTimeout(() => {
                    isAccountLocked = false;
                    loginAttempts = 0;
                    showStatusMessage('تم إلغاء قفل النظام. يمكنك المحاولة مرة أخرى.', 'success');
                }, 15 * 60 * 1000);
            } else {
                showStatusMessage(`${message} المحاولات المتبقية: ${maxAttempts - loginAttempts}`, 'error');
            }
        }

        function loginSuccess(userData) {
            // إعادة تعيين محاولات تسجيل الدخول الفاشلة
            loginAttempts = 0;
            isAccountLocked = false;

            // إنشاء جلسة محسنة
            const sessionData = createSession(userData);

            // تسجيل نجاح تسجيل الدخول
            logLoginAttempt(userData.name || userData.email, 'success');

            // إظهار رسالة نجاح مع معلومات إضافية
            const roleNames = {
                'admin': 'مدير النظام المتقدم',
                'super_admin': 'المدير العام',
                'coach': 'المدرب',
                'player': 'اللاعب',
                'parent': 'ولي الأمر',
                'supervisor': 'المشرف'
            };

            Swal.fire({
                title: 'مرحباً بك!',
                html: `
                    <div style="text-align: center;">
                        <h3 style="color: #8b5cf6; margin-bottom: 1rem;">أهلاً وسهلاً ${userData.name}</h3>
                        <p style="color: #6b7280; margin-bottom: 0.5rem;">تم تسجيل الدخول بصفة: ${roleNames[userData.role]}</p>
                        <p style="color: #6b7280; font-size: 0.9rem;">جاري التوجيه إلى لوحة التحكم...</p>
                    </div>
                `,
                icon: 'success',
                timer: 2500,
                showConfirmButton: false,
                background: '#1a1a1a',
                color: '#ffffff'
            }).then(() => {
                // إعادة التوجيه حسب الدور
                redirectToDashboard(userData.role);
            });
        }

        function redirectToDashboard(role) {
            const dashboards = {
                'admin': 'admin-advanced.html',
                'super_admin': 'admin-advanced.html',
                'coach': 'coach-dashboard.html',
                'player': 'player-dashboard.html',
                'parent': 'parent-dashboard.html',
                'supervisor': 'supervisor-dashboard.html'
            };

            const targetPage = dashboards[role];
            if (targetPage) {
                // إعادة التوجيه الفعلية
                const roleNames = {
                    'admin': 'الإدارة المتقدمة',
                    'super_admin': 'الإدارة المتقدمة',
                    'coach': 'المدرب',
                    'player': 'اللاعب',
                    'parent': 'ولي الأمر',
                    'supervisor': 'المشرف'
                };

                showStatusMessage(`جاري التوجيه إلى لوحة تحكم ${roleNames[role]}...`, 'success');
                setTimeout(() => {
                    window.location.href = targetPage;
                }, 1500);
            } else {
                showStatusMessage('خطأ في تحديد لوحة التحكم المناسبة.', 'error');
            }
        }

        // ==================== التهيئة عند تحميل الصفحة ====================
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من وجود جلسة نشطة
            const existingSession = sessionStorage.getItem('userSession');
            if (existingSession) {
                try {
                    const sessionData = JSON.parse(existingSession);
                    if (sessionData && sessionData.expiresAt > Date.now()) {
                        // إعادة توجيه تلقائية للمستخدم المسجل
                        const roleNames = {
                            'admin': 'الإدارة المتقدمة',
                            'super_admin': 'الإدارة المتقدمة',
                            'coach': 'المدرب',
                            'player': 'اللاعب',
                            'parent': 'ولي الأمر',
                            'supervisor': 'المشرف'
                        };

                        showStatusMessage(`لديك جلسة نشطة كـ ${roleNames[sessionData.role]}. جاري التوجيه...`, 'info');
                        setTimeout(() => {
                            redirectToDashboard(sessionData.role);
                        }, 1500);
                        return;
                    } else {
                        // انتهت صلاحية الجلسة
                        sessionStorage.removeItem('userSession');
                    }
                } catch (e) {
                    // خطأ في قراءة الجلسة
                    sessionStorage.removeItem('userSession');
                }
            }

            // عرض آخر تسجيل دخول
            const lastLogin = localStorage.getItem('lastLogin');
            if (lastLogin) {
                const loginData = JSON.parse(lastLogin);
                showStatusMessage(`آخر تسجيل دخول: ${loginData.user} (${loginData.role})`, 'info');
            }

            // تفعيل التركيز على أول حقل
            document.getElementById('emailAddress').focus();

            console.log('🔐 نظام تسجيل الدخول المحدث جاهز!');
            console.log('🎯 التوجيه للوحة الإدارة المتقدمة: admin-advanced.html');
            console.log('📱 بيانات الاختبار المحدثة:');
            console.log('🔧 الإدارة المتقدمة: <EMAIL> / admin123 أو ********** أو **********');
            console.log('👑 المدير العام: <EMAIL> / super123 أو ********** أو **********');
            console.log('🏃 المدرب: <EMAIL> / coach123 أو 0509876543 أو 0987654321');
            console.log('⚽ اللاعب: <EMAIL> / player123 أو 0551122334 أو 1122334455');
            console.log('👨‍👩‍👧‍👦 ولي الأمر: <EMAIL> / parent123 أو 0555544332 أو 5544332211');
            console.log('👁️ المشرف: <EMAIL> / supervisor123 أو 0559988776 أو 9988776655');
            console.log('🔒 المصادقة البيومترية متاحة للاختبار');
        });

        // ==================== معالجة الأخطاء العامة ====================
        window.addEventListener('error', function(e) {
            console.error('خطأ في النظام:', e.error);
            showStatusMessage('حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.', 'error');
        });

        // ==================== اختصارات لوحة المفاتيح ====================
        document.addEventListener('keydown', function(e) {
            // Alt + 1,2,3 للتبديل بين التبويبات
            if (e.altKey) {
                switch(e.key) {
                    case '1':
                        switchTab('email');
                        break;
                    case '2':
                        switchTab('phone');
                        break;
                    case '3':
                        switchTab('id');
                        break;
                }
            }
        });

        // ==================== نظام تخصيص الألوان ====================
        let isNightMode = false;
        let currentColorSettings = {};

        // قوالب الألوان الجاهزة
        const colorPresets = {
            blue: {
                '--brand-primary': '#1e40af',
                '--brand-secondary': '#3b82f6',
                '--accent-color': '#60a5fa',
                '--primary-bg': '#1a1a1a',
                '--secondary-bg': '#2d2d2d',
                '--accent-dark': '#1e293b'
            },
            green: {
                '--brand-primary': '#059669',
                '--brand-secondary': '#10b981',
                '--accent-color': '#34d399',
                '--primary-bg': '#1a1a1a',
                '--secondary-bg': '#2d2d2d',
                '--accent-dark': '#1e3a2e'
            },
            orange: {
                '--brand-primary': '#ea580c',
                '--brand-secondary': '#f97316',
                '--accent-color': '#fb923c',
                '--primary-bg': '#1a1a1a',
                '--secondary-bg': '#2d2d2d',
                '--accent-dark': '#3a2317'
            },
            purple: {
                '--brand-primary': '#7c3aed',
                '--brand-secondary': '#8b5cf6',
                '--accent-color': '#a78bfa',
                '--primary-bg': '#1a1a1a',
                '--secondary-bg': '#2d2d2d',
                '--accent-dark': '#2e1065'
            },
            red: {
                '--brand-primary': '#dc2626',
                '--brand-secondary': '#ef4444',
                '--accent-color': '#f87171',
                '--primary-bg': '#1a1a1a',
                '--secondary-bg': '#2d2d2d',
                '--accent-dark': '#3f1f1f'
            },
            dark: {
                '--brand-primary': '#374151',
                '--brand-secondary': '#4b5563',
                '--accent-color': '#6b7280',
                '--primary-bg': '#111827',
                '--secondary-bg': '#1f2937',
                '--accent-dark': '#0f172a'
            }
        };

        // تبديل أداة تخصيص الألوان
        function toggleColorCustomizer() {
            const customizer = document.getElementById('colorCustomizer');
            customizer.classList.toggle('active');
        }

        // تحديث لون معين
        function updateColor(property, value) {
            document.documentElement.style.setProperty(property, value);
            currentColorSettings[property] = value;
            updateDerivedColors();
            saveColorSettings();
        }

        // تحديث الألوان المشتقة
        function updateDerivedColors() {
            const primary = currentColorSettings['--brand-primary'] || getComputedStyle(document.documentElement).getPropertyValue('--brand-primary');
            const secondary = currentColorSettings['--brand-secondary'] || getComputedStyle(document.documentElement).getPropertyValue('--brand-secondary');

            document.documentElement.style.setProperty('--button-bg', `linear-gradient(135deg, ${primary}, ${secondary})`);
            document.documentElement.style.setProperty('--logo-bg', `linear-gradient(135deg, ${primary}, ${secondary})`);
            document.documentElement.style.setProperty('--shadow-glow', `0 0 20px ${primary}33`);
        }

        // تطبيق قالب ألوان جاهز
        function applyPreset(presetName) {
            const preset = colorPresets[presetName];
            if (!preset) return;

            Object.entries(preset).forEach(([property, value]) => {
                document.documentElement.style.setProperty(property, value);
                currentColorSettings[property] = value;

                const inputId = property.replace('--', '').replace(/-([a-z])/g, (g) => g[1].toUpperCase());
                const input = document.getElementById(inputId);
                if (input) {
                    input.value = value;
                }
            });

            updateDerivedColors();
            saveColorSettings();
            showStatusMessage(`تم تطبيق قالب الألوان بنجاح`, 'success');
        }

        // حفظ إعدادات الألوان
        function saveColorSettings() {
            try {
                localStorage.setItem('7c_login_color_settings', JSON.stringify(currentColorSettings));
                localStorage.setItem('7c_login_night_mode', isNightMode);
            } catch (error) {
                console.error('خطأ في حفظ إعدادات الألوان:', error);
            }
        }

        // تحميل إعدادات الألوان
        function loadColorSettings() {
            try {
                const savedSettings = localStorage.getItem('7c_login_color_settings');
                const savedNightMode = localStorage.getItem('7c_login_night_mode');

                if (savedSettings) {
                    currentColorSettings = JSON.parse(savedSettings);
                    Object.entries(currentColorSettings).forEach(([property, value]) => {
                        document.documentElement.style.setProperty(property, value);

                        const inputId = property.replace('--', '').replace(/-([a-z])/g, (g) => g[1].toUpperCase());
                        const input = document.getElementById(inputId);
                        if (input) {
                            input.value = value;
                        }
                    });
                    updateDerivedColors();
                }

                if (savedNightMode === 'true') {
                    isNightMode = true;
                    document.querySelector('.theme-toggle').classList.add('night');
                    applyNightMode();
                }
            } catch (error) {
                console.error('خطأ في تحميل إعدادات الألوان:', error);
            }
        }

        // إعادة تعيين الألوان
        function resetColors() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الألوان للإعدادات الافتراضية؟')) {
                localStorage.removeItem('7c_login_color_settings');
                localStorage.removeItem('7c_login_night_mode');
                location.reload();
            }
        }

        // تصدير إعدادات الألوان
        function exportColorSettings() {
            try {
                const exportData = {
                    colorSettings: currentColorSettings,
                    nightMode: isNightMode,
                    exportDate: new Date().toISOString(),
                    version: '1.0'
                };

                const dataStr = JSON.stringify(exportData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `7c_login_colors_${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                showStatusMessage('تم تصدير إعدادات الألوان بنجاح', 'success');
            } catch (error) {
                console.error('خطأ في تصدير الإعدادات:', error);
                showStatusMessage('خطأ في تصدير الإعدادات', 'error');
            }
        }

        // استيراد إعدادات الألوان
        function importColorSettings() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';

            input.onchange = (e) => {
                const file = e.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const importData = JSON.parse(e.target.result);

                        if (importData.colorSettings) {
                            currentColorSettings = importData.colorSettings;
                            Object.entries(currentColorSettings).forEach(([property, value]) => {
                                document.documentElement.style.setProperty(property, value);

                                const inputId = property.replace('--', '').replace(/-([a-z])/g, (g) => g[1].toUpperCase());
                                const input = document.getElementById(inputId);
                                if (input) {
                                    input.value = value;
                                }
                            });
                            updateDerivedColors();
                            saveColorSettings();

                            showStatusMessage('تم استيراد إعدادات الألوان بنجاح', 'success');
                        }

                        if (importData.nightMode !== undefined) {
                            isNightMode = importData.nightMode;
                            const toggle = document.querySelector('.theme-toggle');
                            if (isNightMode) {
                                toggle.classList.add('night');
                                applyNightMode();
                            } else {
                                toggle.classList.remove('night');
                                removeNightMode();
                            }
                        }

                    } catch (error) {
                        console.error('خطأ في قراءة الملف:', error);
                        showStatusMessage('ملف غير صالح', 'error');
                    }
                };

                reader.readAsText(file);
            };

            input.click();
        }

        // تبديل الوضع الليلي/النهاري
        function toggleTheme() {
            isNightMode = !isNightMode;
            const toggle = document.querySelector('.theme-toggle');

            if (isNightMode) {
                toggle.classList.add('night');
                applyNightMode();
                showStatusMessage('تم تفعيل الوضع الليلي', 'info');
            } else {
                toggle.classList.remove('night');
                removeNightMode();
                showStatusMessage('تم تفعيل الوضع النهاري', 'info');
            }

            saveColorSettings();
        }

        // تطبيق الوضع الليلي
        function applyNightMode() {
            document.documentElement.style.setProperty('--primary-bg', '#0f172a');
            document.documentElement.style.setProperty('--secondary-bg', '#1e293b');
            document.documentElement.style.setProperty('--accent-dark', '#334155');
            document.documentElement.style.setProperty('--text-primary', '#f1f5f9');
            document.documentElement.style.setProperty('--text-secondary', '#cbd5e1');
            document.documentElement.style.setProperty('--text-muted', '#64748b');
            document.documentElement.style.setProperty('--glass', 'rgba(255,255,255,0.05)');
            document.documentElement.style.setProperty('--glass-strong', 'rgba(255,255,255,0.1)');
            document.documentElement.style.setProperty('--border', 'rgba(255,255,255,0.1)');
            document.documentElement.style.setProperty('--border-strong', 'rgba(255,255,255,0.2)');
        }

        // إزالة الوضع الليلي
        function removeNightMode() {
            document.documentElement.style.setProperty('--primary-bg', '#1a1a1a');
            document.documentElement.style.setProperty('--secondary-bg', '#2d2d2d');
            document.documentElement.style.setProperty('--accent-dark', '#1e293b');
            document.documentElement.style.setProperty('--text-primary', '#ffffff');
            document.documentElement.style.setProperty('--text-secondary', '#e2e8f0');
            document.documentElement.style.setProperty('--text-muted', '#94a3b8');
            document.documentElement.style.setProperty('--glass', 'rgba(255,255,255,0.1)');
            document.documentElement.style.setProperty('--glass-strong', 'rgba(255,255,255,0.15)');
            document.documentElement.style.setProperty('--border', 'rgba(255,255,255,0.2)');
            document.documentElement.style.setProperty('--border-strong', 'rgba(255,255,255,0.3)');
        }

        // إعادة تعيين للإعدادات الافتراضية
        function resetToDefaults() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات للافتراضية؟')) {
                resetColors();
            }
        }

        // تهيئة نظام الألوان عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل إعدادات الألوان المحفوظة
            loadColorSettings();

            // إغلاق أداة التخصيص عند النقر خارجها
            document.addEventListener('click', function(e) {
                const customizer = document.getElementById('colorCustomizer');
                const toggleBtn = e.target.closest('[onclick="toggleColorCustomizer()"]');

                if (!customizer.contains(e.target) && !toggleBtn && customizer.classList.contains('active')) {
                    customizer.classList.remove('active');
                }
            });

            // تحديث الألوان المشتقة عند التحميل
            setTimeout(() => {
                updateDerivedColors();
            }, 500);

            console.log('🎨 تم تحميل نظام تخصيص الألوان لصفحة تسجيل الدخول!');
        });

        // حفظ الإعدادات عند إغلاق الصفحة
        window.addEventListener('beforeunload', function() {
            saveColorSettings();
        });
    </script>
</body>
</html>
