// ==================== Plugin: إدارة المستخدمين ====================
export const UsersManagementPlugin = {
    id: 'users',
    name: 'إدارة المستخدمين',
    init() {
        if (!document.getElementById('users-plugin-style')) {
            const style = document.createElement('style');
            style.id = 'users-plugin-style';
            style.innerHTML = `
                .plugin-section {background: #181f2a; color: #fff; border-radius: 18px; box-shadow: 0 2px 16px #0002; max-width: 900px; margin: 32px auto 0; padding: 32px 24px 24px; font-family: 'Cairo',sans-serif;}
                .plugin-header {display: flex; align-items: center; justify-content: space-between; margin-bottom: 18px;}
                .plugin-header h2 {font-size: 1.5rem; font-weight: bold; margin: 0;}
                .plugin-btn {background: linear-gradient(90deg,#4f8cff,#6f6bff); color: #fff; border: none; border-radius: 8px; padding: 10px 22px; font-size: 1rem; font-weight: 600; cursor: pointer; transition: background 0.2s;}
                .plugin-btn:hover {background: linear-gradient(90deg,#6f6bff,#4f8cff);}
                .plugin-table {width: 100%; border-collapse: collapse; background: #232b3b; border-radius: 12px; overflow: hidden; margin-top: 10px;}
                .plugin-table th, .plugin-table td {padding: 12px 8px; text-align: center;}
                .plugin-table th {background: #232b3b; color: #8bb4ff; font-weight: 700; border-bottom: 2px solid #2d3950;}
                .plugin-table tr {transition: background 0.2s;}
                .plugin-table tr:hover {background: #232b3bcc;}
                .plugin-table td {border-bottom: 1px solid #232b3b;}
                .plugin-action-btn {background: #2d3950; color: #8bb4ff; border: none; border-radius: 6px; padding: 6px 16px; margin: 0 2px; font-size: 0.95rem; cursor: pointer; transition: background 0.2s;}
                .plugin-action-btn:hover {background: #4f8cff; color: #fff;}
                .plugin-modal-bg {position: fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.45); z-index: 9999; display: flex; align-items: center; justify-content: center;}
                .plugin-modal {background: #232b3b; border-radius: 16px; padding: 32px 24px 24px; min-width: 320px; max-width: 95vw; box-shadow: 0 4px 32px #0005;}
                .plugin-modal h3 {margin-top:0; color:#8bb4ff; font-size:1.2rem; margin-bottom:18px;}
                .plugin-modal label {display:block; margin-bottom:6px; color:#b6cfff; font-size:1rem;}
                .plugin-modal input {width:100%; padding:8px 10px; border-radius:6px; border:none; background:#181f2a; color:#fff; margin-bottom:16px; font-size:1rem;}
                .plugin-modal .modal-actions {display:flex; justify-content:flex-end; gap:10px;}
                .plugin-modal .plugin-btn {padding:8px 18px; font-size:1rem;}
            `;
            document.head.appendChild(style);
        }
        if (!document.getElementById('users-plugin-container')) {
            const container = document.createElement('section');
            container.id = 'users-plugin-container';
            container.className = 'plugin-section';
            container.innerHTML = `
                <div class="plugin-header">
                    <h2>إدارة المستخدمين</h2>
                    <button id="add-user-btn" class="plugin-btn">إضافة مستخدم</button>
                </div>
                <table class="plugin-table" id="users-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الدور</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="users-table-body">
                        <!-- سيتم تعبئة المستخدمين هنا -->
                    </tbody>
                </table>
            `;
            document.body.prepend(container);
        }
        this.renderUsers();
        document.getElementById('add-user-btn').onclick = () => this.openUserModal();
    },
    destroy() {
        const container = document.getElementById('users-plugin-container');
        if (container) container.remove();
        const style = document.getElementById('users-plugin-style');
        if (style) style.remove();
        this.closeModal();
    },
    renderUsers() {
        const users = JSON.parse(localStorage.getItem('plugin_users') || '[]');
        const tbody = document.getElementById('users-table-body');
        if (!tbody) return;
        tbody.innerHTML = users.length ? users.map((u, i) => `
            <tr>
                <td>${u.name || ''}</td>
                <td>${u.email || ''}</td>
                <td>${u.role || ''}</td>
                <td>
                    <button class="plugin-action-btn" onclick="window.PluginManager.plugins.users.openUserModal(${i})">تعديل</button>
                    <button class="plugin-action-btn" onclick="window.PluginManager.plugins.users.deleteUser(${i})">حذف</button>
                </td>
            </tr>
        `).join('') : '<tr><td colspan="4">لا يوجد مستخدمين</td></tr>';
    },
    openUserModal(index = null) {
        this.closeModal();
        const users = JSON.parse(localStorage.getItem('plugin_users') || '[]');
        const user = index !== null ? users[index] : { name: '', email: '', role: '' };
        const modalBg = document.createElement('div');
        modalBg.className = 'plugin-modal-bg';
        modalBg.id = 'users-plugin-modal-bg';
        modalBg.innerHTML = `
            <div class="plugin-modal">
                <h3>${index !== null ? 'تعديل مستخدم' : 'إضافة مستخدم جديد'}</h3>
                <label>اسم المستخدم</label>
                <input id="modal-user-name" type="text" value="${user.name || ''}" placeholder="مثال: خالد محمد" />
                <label>البريد الإلكتروني</label>
                <input id="modal-user-email" type="email" value="${user.email || ''}" placeholder="<EMAIL>" />
                <label>الدور</label>
                <input id="modal-user-role" type="text" value="${user.role || ''}" placeholder="مدير/مدرب/لاعب..." />
                <div class="modal-actions">
                    <button class="plugin-btn" id="save-user-btn">${index !== null ? 'حفظ التعديلات' : 'إضافة'}</button>
                    <button class="plugin-btn" id="cancel-user-btn" style="background:#2d3950;">إلغاء</button>
                </div>
            </div>
        `;
        document.body.appendChild(modalBg);
        document.getElementById('cancel-user-btn').onclick = () => this.closeModal();
        document.getElementById('save-user-btn').onclick = () => {
            const name = document.getElementById('modal-user-name').value.trim();
            const email = document.getElementById('modal-user-email').value.trim();
            const role = document.getElementById('modal-user-role').value.trim();
            if (!name || !email || !role) {
                alert('يرجى تعبئة جميع الحقول');
                return;
            }
            if (index !== null) {
                users[index] = { name, email, role };
            } else {
                users.push({ name, email, role });
            }
            localStorage.setItem('plugin_users', JSON.stringify(users));
            this.closeModal();
            this.renderUsers();
        };
        setTimeout(() => {
            document.getElementById('modal-user-name').focus();
        }, 100);
    },
    closeModal() {
        const modalBg = document.getElementById('users-plugin-modal-bg');
        if (modalBg) modalBg.remove();
    },
    deleteUser(index) {
        if (!confirm('هل أنت متأكد من حذف المستخدم؟')) return;
        const users = JSON.parse(localStorage.getItem('plugin_users') || '[]');
        users.splice(index, 1);
        localStorage.setItem('plugin_users', JSON.stringify(users));
        this.renderUsers();
    }
};
