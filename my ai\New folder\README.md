# تعليمات تشغيل مشروع المساعد الذكي

## 1. تشغيل الواجهة الخلفية (backend)
1. افتح الطرفية (Terminal) وانتقل إلى مجلد backend:
   ```sh
   cd backend
   npm install
   node index.js
   ```
2. يجب أن تظهر رسالة: AI Assistant backend running on http://localhost:5000

## 2. تشغيل الواجهة الأمامية (frontend)
1. افتح طرفية جديدة وانتقل إلى مجلد frontend:
   ```sh
   cd frontend
   npm install
   npm start
   ```
2. سيتم تشغيل الواجهة على رابط محلي (عادة http://localhost:3000 أو حسب أداة serve)

## 3. استخدام المشروع
- افتح المتصفح وادخل على رابط الواجهة الأمامية.
- اسأل أي سؤال وسيتم الرد عليك من المساعد الذكي.

---

### ملاحظات للنشر على استضافة:
- يجب أن تدعم الاستضافة Node.js لتشغيل backend.
- يمكنك بناء الواجهة الأمامية كملفات ثابتة (static) ورفعها مع backend أو على استضافة ملفات ثابتة.
- استخدم أدوات مثل pm2 لإدارة تشغيل الخادم على السيرفر.

لأي استفسار أو مشكلة، انسخ رسالة الخطأ هنا وسأساعدك فورًا.
