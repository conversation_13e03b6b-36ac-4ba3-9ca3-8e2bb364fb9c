<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة طلبات الانضمام - أكاديمية 7C</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #A0522D;
            --accent-color: #CD853F;
            --success-color: #228B22;
            --warning-color: #DAA520;
            --danger-color: #B22222;
            --text-dark: #2D1B0E;
            --text-light: #8B4513;
            --bg-light: #ffffff;
            --bg-dark: #1a1a1a;
            --card-light: #ffffff;
            --card-dark: #2d2d2d;
        }

        /* الوضع المظلم كافتراضي */
        :root {
            --bg-light: #1a1a1a;
            --card-light: #2d2d2d;
            --text-dark: #ffffff;
            --text-light: #cccccc;
            --input-bg: #3d3d3d;
            --border-color: #4d4d4d;
            --hover-bg: #404040;
        }

        [data-theme="light"] {
            --bg-light: #ffffff;
            --card-light: #ffffff;
            --text-dark: #2D1B0E;
            --text-light: #8B4513;
            --input-bg: #ffffff;
            --border-color: rgba(139, 69, 19, 0.1);
            --hover-bg: #f8fafc;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--bg-light);
            color: var(--text-dark);
            transition: all 0.3s ease;
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card {
            background: var(--card-light);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .status-pending { background: linear-gradient(135deg, #fbbf24, #f59e0b); }
        .status-reviewing { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }
        .status-approved { background: linear-gradient(135deg, #10b981, #059669); }
        .status-rejected { background: linear-gradient(135deg, #ef4444, #dc2626); }
        .status-active { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }

        .notification-dot {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .ai-glow {
            box-shadow: 0 0 20px rgba(139, 69, 19, 0.3);
            animation: aiGlow 3s ease-in-out infinite alternate;
        }

        @keyframes aiGlow {
            from { box-shadow: 0 0 20px rgba(139, 69, 19, 0.3); }
            to { box-shadow: 0 0 30px rgba(139, 69, 19, 0.6); }
        }

        .progress-bar {
            background: linear-gradient(90deg, #10b981, #059669);
            height: 8px;
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            min-height: 100vh;
        }

        .nav-item {
            transition: all 0.3s ease;
            border-radius: 12px;
            margin: 5px 0;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .stat-card {
            background: linear-gradient(135deg, var(--card-light), #f8fafc);
            border-radius: 20px;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        }

        .player-card {
            background: var(--card-light);
            border-radius: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid rgba(139, 69, 19, 0.1);
        }

        .player-card:hover {
            transform: scale(1.02);
            box-shadow: 0 15px 35px rgba(139, 69, 19, 0.2);
        }

        .badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            color: white;
        }

        .theme-toggle {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            transform: rotate(180deg);
        }

        .ai-assistant {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            z-index: 1000;
        }

        .ai-assistant:hover {
            transform: scale(1.1);
        }

        .modal {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
        }

        .modal-content {
            background: var(--card-light);
            border-radius: 20px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, var(--primary-color), var(--accent-color));
        }

        .timeline-item {
            position: relative;
            margin-bottom: 30px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -22px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--primary-color);
            border: 3px solid var(--card-light);
        }

        .search-box {
            background: var(--input-bg);
            border: 2px solid var(--border-color);
            border-radius: 25px;
            padding: 12px 20px;
            transition: all 0.3s ease;
            color: var(--text-dark);
            outline: none;
        }

        .search-box:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
        }

        .search-box::placeholder {
            color: var(--text-light);
            opacity: 0.7;
        }

        /* تحسينات إمكانية الوصول */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* تحسينات التباين للوضع المظلم */
        [data-theme="dark"] .stat-card {
            background: linear-gradient(135deg, var(--card-light), #333333);
        }

        [data-theme="dark"] .bg-blue-50 {
            background-color: rgba(59, 130, 246, 0.1) !important;
        }

        [data-theme="dark"] .bg-yellow-50 {
            background-color: rgba(245, 158, 11, 0.1) !important;
        }

        [data-theme="dark"] .bg-green-50 {
            background-color: rgba(16, 185, 129, 0.1) !important;
        }

        [data-theme="dark"] .text-blue-700,
        [data-theme="dark"] .text-yellow-700,
        [data-theme="dark"] .text-green-700 {
            color: var(--text-dark) !important;
        }

        /* تحسينات الاستجابة */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                position: fixed;
                top: 0;
                left: -100%;
                z-index: 1000;
                transition: left 0.3s ease;
            }

            .sidebar.open {
                left: 0;
            }

            .main-content {
                margin-right: 0;
            }

            .mobile-menu-btn {
                display: block;
            }

            .stat-card {
                padding: 1.5rem;
            }

            .grid-cols-4 {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .grid-cols-4,
            .grid-cols-2 {
                grid-template-columns: 1fr;
            }

            .flex-col-reverse {
                flex-direction: column-reverse;
            }
        }

        /* تحسينات الأداء */
        .lazy-load {
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .lazy-load.loaded {
            opacity: 1;
        }

        /* تحسينات التحميل */
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        [data-theme="dark"] .loading-skeleton {
            background: linear-gradient(90deg, #2d2d2d 25%, #3d3d3d 50%, #2d2d2d 75%);
            background-size: 200% 100%;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Switch Toggle Styles */
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        /* Pagination Styles */
        .pagination-btn {
            padding: 8px 12px;
            margin: 0 2px;
            border: 1px solid var(--border-color);
            background: var(--card-light);
            color: var(--text-dark);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Table Styles */
        .table-row:hover {
            background: var(--hover-bg);
        }

        .table-checkbox {
            width: 16px;
            height: 16px;
            accent-color: var(--primary-color);
        }

        /* Mobile Optimizations */
        .mobile-menu-btn {
            display: none;
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }

            .sidebar {
                transform: translateX(100%);
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                padding: 1rem;
            }

            .flex-col-reverse {
                flex-direction: column-reverse;
            }

            .space-x-reverse > * + * {
                margin-right: 0;
                margin-left: 0;
                margin-bottom: 0.5rem;
            }
        }

        /* Chart Container Styles */
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }

        /* Loading States */
        .btn-loading {
            position: relative;
            pointer-events: none;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            top: 50%;
            left: 50%;
            margin-left: -8px;
            margin-top: -8px;
            border: 2px solid transparent;
            border-top-color: currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Tooltip Styles */
        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 120px;
            background-color: #555;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        /* Success/Error States */
        .success-state {
            border-left: 4px solid #10b981;
            background-color: rgba(16, 185, 129, 0.1);
        }

        .error-state {
            border-left: 4px solid #ef4444;
            background-color: rgba(239, 68, 68, 0.1);
        }

        .warning-state {
            border-left: 4px solid #f59e0b;
            background-color: rgba(245, 158, 11, 0.1);
        }

        /* Focus States for Accessibility */
        button:focus,
        input:focus,
        select:focus,
        textarea:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* High Contrast Mode Support */
        @media (prefers-contrast: high) {
            :root {
                --primary-color: #000000;
                --text-dark: #000000;
                --text-light: #333333;
                --border-color: #000000;
            }

            [data-theme="dark"] {
                --primary-color: #ffffff;
                --text-dark: #ffffff;
                --text-light: #cccccc;
                --border-color: #ffffff;
            }
        }

        /* Reduced Motion Support */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        .filter-chip {
            background: rgba(139, 69, 19, 0.1);
            color: var(--primary-color);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-chip:hover, .filter-chip.active {
            background: var(--primary-color);
            color: white;
        }

        .notification-panel {
            background: var(--card-light);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            max-height: 400px;
            overflow-y: auto;
        }

        .notification-item {
            padding: 15px;
            border-bottom: 1px solid rgba(139, 69, 19, 0.1);
            transition: all 0.3s ease;
        }

        .notification-item:hover {
            background: rgba(139, 69, 19, 0.05);
        }

        .notification-item.unread {
            background: rgba(59, 130, 246, 0.05);
            border-left: 4px solid #3b82f6;
        }
    </style>
</head>
<body>
    <!-- AI Assistant -->
    <div class="ai-assistant ai-glow" onclick="toggleAIAssistant()">
        <i class="fas fa-robot"></i>
    </div>

    <!-- Mobile Menu Button -->
    <button class="mobile-menu-btn fixed top-4 right-4 z-50 md:hidden bg-white p-3 rounded-xl shadow-lg" onclick="toggleMobileMenu()" aria-label="فتح القائمة">
        <i class="fas fa-bars text-gray-800"></i>
    </button>

    <!-- Theme Toggle -->
    <button class="theme-toggle fixed top-4 left-4 z-50" onclick="toggleTheme()" aria-label="تبديل الوضع المظلم">
        <i class="fas fa-sun" id="theme-icon"></i>
    </button>

    <!-- Main Container -->
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="sidebar w-64 p-6 text-white">
            <div class="flex items-center mb-8">
                <div class="w-12 h-12 bg-white rounded-xl flex items-center justify-center text-2xl font-bold text-gray-800 mr-3">
                    7C
                </div>
                <div>
                    <h1 class="text-xl font-bold">أكاديمية 7C</h1>
                    <p class="text-sm opacity-80">نظام إدارة الطلبات</p>
                </div>
            </div>

            <nav class="space-y-2" role="navigation" aria-label="القائمة الرئيسية">
                <a href="#" class="nav-item active flex items-center p-3 text-white" onclick="showSection('dashboard')" role="menuitem" aria-current="page">
                    <i class="fas fa-tachometer-alt ml-3" aria-hidden="true"></i>
                    لوحة التحكم
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-white" onclick="showSection('applications')" role="menuitem">
                    <i class="fas fa-file-alt ml-3" aria-hidden="true"></i>
                    طلبات الانضمام
                    <span class="notification-dot bg-red-500 w-2 h-2 rounded-full mr-auto" aria-label="إشعارات جديدة"></span>
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-white" onclick="showSection('players')" role="menuitem">
                    <i class="fas fa-users ml-3" aria-hidden="true"></i>
                    اللاعبين المسجلين
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-white" onclick="showSection('training-plans')" role="menuitem">
                    <i class="fas fa-dumbbell ml-3" aria-hidden="true"></i>
                    خطط التدريب
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-white" onclick="showSection('certificates')" role="menuitem">
                    <i class="fas fa-certificate ml-3" aria-hidden="true"></i>
                    سندات الاشتراك
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-white" onclick="showSection('notifications')" role="menuitem">
                    <i class="fas fa-bell ml-3" aria-hidden="true"></i>
                    الإشعارات
                    <span class="bg-blue-500 text-white text-xs px-2 py-1 rounded-full mr-auto" id="notification-badge">5</span>
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-white" onclick="showSection('analytics')" role="menuitem">
                    <i class="fas fa-chart-line ml-3" aria-hidden="true"></i>
                    التحليلات والإحصائيات
                </a>
                <a href="#" class="nav-item flex items-center p-3 text-white" onclick="showSection('settings')" role="menuitem">
                    <i class="fas fa-cog ml-3" aria-hidden="true"></i>
                    الإعدادات
                </a>
            </nav>

            <div class="mt-8 p-4 bg-white bg-opacity-10 rounded-xl">
                <div class="flex items-center mb-2">
                    <i class="fas fa-robot text-yellow-300 ml-2"></i>
                    <span class="text-sm font-semibold">مساعد الذكاء الاصطناعي</span>
                </div>
                <p class="text-xs opacity-80">نظام ذكي لتحليل الطلبات وتقديم التوصيات</p>
                <div class="mt-2 bg-green-500 h-1 rounded-full"></div>
                <p class="text-xs mt-1">متصل ونشط</p>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6 main-content" style="background: var(--bg-light);">
            <!-- Dashboard Section -->
            <div id="dashboard-section">
                <!-- Header -->
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h2 class="text-3xl font-bold" style="color: var(--text-dark);">لوحة التحكم الرئيسية</h2>
                        <p style="color: var(--text-light);">إدارة شاملة لطلبات الانضمام والمتابعة الذكية</p>
                    </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <div class="relative">
                        <button class="p-3 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all" onclick="toggleNotifications()">
                            <i class="fas fa-bell text-gray-600"></i>
                            <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center">3</span>
                        </button>
                        <div id="notification-dropdown" class="notification-panel absolute left-0 top-full mt-2 w-80 hidden z-50">
                            <div class="p-4 border-b">
                                <h3 class="font-semibold text-gray-800">الإشعارات الحديثة</h3>
                            </div>
                            <div id="notifications-list">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3 space-x-reverse bg-white p-3 rounded-xl shadow-lg">
                        <img src="https://via.placeholder.com/40x40/8B4513/FFFFFF?text=Admin" alt="Admin" class="w-10 h-10 rounded-full">
                        <div>
                            <p class="font-semibold text-gray-800">مدير النظام</p>
                            <p class="text-sm text-gray-500">متصل الآن</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="stat-card">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 mb-1">طلبات جديدة</p>
                            <p class="text-3xl font-bold text-gray-800" id="new-applications">12</p>
                            <p class="text-sm text-green-600 mt-1">
                                <i class="fas fa-arrow-up"></i> +15% من الأسبوع الماضي
                            </p>
                        </div>
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-file-alt text-2xl text-blue-600"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 mb-1">قيد المراجعة</p>
                            <p class="text-3xl font-bold text-gray-800" id="reviewing-applications">8</p>
                            <p class="text-sm text-yellow-600 mt-1">
                                <i class="fas fa-clock"></i> متوسط الوقت: 2 أيام
                            </p>
                        </div>
                        <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-search text-2xl text-yellow-600"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 mb-1">تم القبول</p>
                            <p class="text-3xl font-bold text-gray-800" id="approved-applications">45</p>
                            <p class="text-sm text-green-600 mt-1">
                                <i class="fas fa-check"></i> معدل القبول: 85%
                            </p>
                        </div>
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-check-circle text-2xl text-green-600"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 mb-1">لاعبين نشطين</p>
                            <p class="text-3xl font-bold text-gray-800" id="active-players">127</p>
                            <p class="text-sm text-purple-600 mt-1">
                                <i class="fas fa-users"></i> +8 هذا الشهر
                            </p>
                        </div>
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-users text-2xl text-purple-600"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Insights Panel -->
            <div class="card p-6 mb-8 ai-glow">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <i class="fas fa-brain text-2xl text-purple-600 ml-3"></i>
                        <div>
                            <h3 class="text-xl font-bold text-gray-800">رؤى الذكاء الاصطناعي</h3>
                            <p class="text-sm text-gray-600">تحليل ذكي للطلبات والتوصيات</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <span class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></span>
                        <span class="text-sm text-green-600 font-semibold">نشط</span>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-blue-50 p-4 rounded-xl">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-lightbulb text-blue-600 ml-2"></i>
                            <span class="font-semibold text-blue-800">توصية ذكية</span>
                        </div>
                        <p class="text-sm text-blue-700">يُنصح بمراجعة طلبات فئة الناشئين - معدل قبول عالي متوقع</p>
                    </div>
                    <div class="bg-yellow-50 p-4 rounded-xl">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-exclamation-triangle text-yellow-600 ml-2"></i>
                            <span class="font-semibold text-yellow-800">تنبيه</span>
                        </div>
                        <p class="text-sm text-yellow-700">5 طلبات تحتاج مراجعة عاجلة - تجاوزت المدة المحددة</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-xl">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-chart-line text-green-600 ml-2"></i>
                            <span class="font-semibold text-green-800">إحصائية</span>
                        </div>
                        <p class="text-sm text-green-700">ارتفاع في طلبات التسجيل بنسبة 23% هذا الأسبوع</p>
                    </div>
                </div>
            </div>

            <!-- Applications List -->
            <div class="card p-6 mb-8">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-gray-800">أحدث الطلبات</h3>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <input type="text" placeholder="البحث في الطلبات..." class="search-box" id="search-applications">
                        <button class="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- Filter Chips -->
                <div class="flex flex-wrap gap-2 mb-6">
                    <span class="filter-chip active" data-filter="all">الكل</span>
                    <span class="filter-chip" data-filter="pending">قيد الانتظار</span>
                    <span class="filter-chip" data-filter="reviewing">قيد المراجعة</span>
                    <span class="filter-chip" data-filter="approved">مقبول</span>
                    <span class="filter-chip" data-filter="rejected">مرفوض</span>
                </div>

                <!-- Applications List -->
                <div class="space-y-4" id="applications-list">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
            </div> <!-- End Dashboard Section -->

            <!-- Applications Management Section -->
            <div id="applications-section" class="hidden">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h2 class="text-3xl font-bold" style="color: var(--text-dark);">إدارة طلبات الانضمام</h2>
                        <p style="color: var(--text-light);">مراجعة وإدارة جميع طلبات الانضمام للأكاديمية</p>
                    </div>
                    <div class="flex space-x-4 space-x-reverse">
                        <button class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-all" onclick="openAddApplicationModal()">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة طلب جديد
                        </button>
                        <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all" onclick="exportApplications()">
                            <i class="fas fa-file-export ml-2"></i>
                            تصدير البيانات
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all" onclick="bulkActions()">
                            <i class="fas fa-tasks ml-2"></i>
                            إجراءات مجمعة
                        </button>
                    </div>
                </div>

                <div class="card p-6">
                    <div class="overflow-x-auto">
                        <table class="w-full" id="applications-table-full">
                            <thead>
                                <tr class="border-b" style="border-color: var(--border-color);">
                                    <th class="text-right p-4">
                                        <input type="checkbox" id="select-all" onchange="toggleSelectAll()">
                                    </th>
                                    <th class="text-right p-4">الصورة</th>
                                    <th class="text-right p-4">الاسم</th>
                                    <th class="text-right p-4">العمر</th>
                                    <th class="text-right p-4">الفئة</th>
                                    <th class="text-right p-4">الحالة</th>
                                    <th class="text-right p-4">تاريخ التقديم</th>
                                    <th class="text-right p-4">المصدر</th>
                                    <th class="text-right p-4">نقاط الذكاء الاصطناعي</th>
                                    <th class="text-right p-4">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="applications-table-body">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="flex justify-between items-center mt-6">
                        <div class="text-sm" style="color: var(--text-light);">
                            عرض <span id="showing-from">1</span> إلى <span id="showing-to">10</span> من <span id="total-applications">0</span> طلب
                        </div>
                        <div class="flex space-x-2 space-x-reverse" id="pagination-controls">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Players Management Section -->
            <div id="players-section" class="hidden">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h2 class="text-3xl font-bold" style="color: var(--text-dark);">إدارة اللاعبين المسجلين</h2>
                        <p style="color: var(--text-light);">عرض وإدارة ملفات اللاعبين وتتبع أدائهم</p>
                    </div>
                    <div class="flex space-x-4 space-x-reverse">
                        <button class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-all" onclick="addNewPlayer()">
                            <i class="fas fa-user-plus ml-2"></i>
                            إضافة لاعب جديد
                        </button>
                        <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all" onclick="exportPlayers()">
                            <i class="fas fa-file-export ml-2"></i>
                            تصدير قائمة اللاعبين
                        </button>
                    </div>
                </div>

                <!-- Filter and Search -->
                <div class="card p-6 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <input type="text" placeholder="البحث بالاسم..." class="search-box" id="players-search">
                        <select class="search-box" id="category-filter">
                            <option value="">جميع الفئات</option>
                            <option value="براعم">براعم</option>
                            <option value="ناشئين">ناشئين</option>
                            <option value="شباب">شباب</option>
                        </select>
                        <select class="search-box" id="status-filter">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="suspended">موقوف</option>
                        </select>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all" onclick="filterPlayers()">
                            <i class="fas fa-filter ml-2"></i>
                            تطبيق الفلتر
                        </button>
                    </div>
                </div>

                <!-- Players Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" id="players-grid">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- Training Plans Section -->
            <div id="training-plans-section" class="hidden">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h2 class="text-3xl font-bold" style="color: var(--text-dark);">إدارة خطط التدريب</h2>
                        <p style="color: var(--text-light);">إنشاء وإدارة خطط التدريب المختلفة للفئات العمرية</p>
                    </div>
                    <div class="flex space-x-4 space-x-reverse">
                        <button class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-all" onclick="createTrainingPlan()">
                            <i class="fas fa-plus ml-2"></i>
                            إنشاء خطة جديدة
                        </button>
                        <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all" onclick="importTrainingPlan()">
                            <i class="fas fa-file-import ml-2"></i>
                            استيراد خطة
                        </button>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Training Plans List -->
                    <div class="lg:col-span-2">
                        <div class="card p-6">
                            <h3 class="text-xl font-bold mb-4" style="color: var(--text-dark);">خطط التدريب المتاحة</h3>
                            <div class="space-y-4" id="training-plans-list">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Training Calendar -->
                    <div>
                        <div class="card p-6">
                            <h3 class="text-xl font-bold mb-4" style="color: var(--text-dark);">تقويم التدريب</h3>
                            <div id="training-calendar">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Certificates Section -->
            <div id="certificates-section" class="hidden">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h2 class="text-3xl font-bold" style="color: var(--text-dark);">إدارة سندات الاشتراك</h2>
                        <p style="color: var(--text-light);">إنشاء وإدارة سندات الاشتراك والشهادات</p>
                    </div>
                    <div class="flex space-x-4 space-x-reverse">
                        <button class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-all" onclick="bulkGenerateCertificates()">
                            <i class="fas fa-certificate ml-2"></i>
                            إنتاج مجمع
                        </button>
                        <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all" onclick="editCertificateTemplate()">
                            <i class="fas fa-edit ml-2"></i>
                            تحرير القالب
                        </button>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Certificate Template -->
                    <div class="lg:col-span-2">
                        <div class="card p-6">
                            <h3 class="text-xl font-bold mb-4" style="color: var(--text-dark);">معاينة قالب السند</h3>
                            <div id="certificate-preview" class="border-2 border-dashed border-gray-300 p-8 text-center min-h-96">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Certificate Settings -->
                    <div>
                        <div class="card p-6">
                            <h3 class="text-xl font-bold mb-4" style="color: var(--text-dark);">إعدادات السند</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">عنوان السند</label>
                                    <input type="text" class="search-box w-full" value="سند اشتراك" id="certificate-title">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">اسم المؤسسة</label>
                                    <input type="text" class="search-box w-full" value="أكاديمية 7C الرياضية" id="institution-name">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">توقيع المدير</label>
                                    <input type="file" class="search-box w-full" accept="image/*" id="signature-upload">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">ختم المؤسسة</label>
                                    <input type="file" class="search-box w-full" accept="image/*" id="stamp-upload">
                                </div>
                                <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all" onclick="updateCertificateTemplate()">
                                    <i class="fas fa-save ml-2"></i>
                                    حفظ التغييرات
                                </button>
                            </div>
                        </div>

                        <!-- Recent Certificates -->
                        <div class="card p-6 mt-6">
                            <h3 class="text-xl font-bold mb-4" style="color: var(--text-dark);">السندات الحديثة</h3>
                            <div class="space-y-3" id="recent-certificates">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications Section -->
            <div id="notifications-section" class="hidden">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h2 class="text-3xl font-bold" style="color: var(--text-dark);">مركز الإشعارات</h2>
                        <p style="color: var(--text-light);">إدارة وإرسال الإشعارات للمستخدمين</p>
                    </div>
                    <div class="flex space-x-4 space-x-reverse">
                        <button class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-all" onclick="createNotification()">
                            <i class="fas fa-plus ml-2"></i>
                            إنشاء إشعار
                        </button>
                        <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all" onclick="sendBulkNotification()">
                            <i class="fas fa-bullhorn ml-2"></i>
                            إرسال جماعي
                        </button>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Notifications List -->
                    <div class="lg:col-span-2">
                        <div class="card p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-xl font-bold" style="color: var(--text-dark);">سجل الإشعارات</h3>
                                <div class="flex space-x-2 space-x-reverse">
                                    <button class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-full" onclick="filterNotifications('all')">الكل</button>
                                    <button class="px-3 py-1 text-sm bg-gray-100 text-gray-800 rounded-full" onclick="filterNotifications('sent')">مرسل</button>
                                    <button class="px-3 py-1 text-sm bg-gray-100 text-gray-800 rounded-full" onclick="filterNotifications('pending')">معلق</button>
                                    <button class="px-3 py-1 text-sm bg-gray-100 text-gray-800 rounded-full" onclick="filterNotifications('failed')">فشل</button>
                                </div>
                            </div>
                            <div class="space-y-4" id="notifications-history">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Notification Stats -->
                    <div>
                        <div class="card p-6 mb-6">
                            <h3 class="text-xl font-bold mb-4" style="color: var(--text-dark);">إحصائيات الإشعارات</h3>
                            <div class="space-y-4">
                                <div class="flex justify-between">
                                    <span style="color: var(--text-light);">إجمالي المرسل</span>
                                    <span class="font-bold" style="color: var(--text-dark);" id="total-sent">0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span style="color: var(--text-light);">معدل الفتح</span>
                                    <span class="font-bold text-green-600" id="open-rate">0%</span>
                                </div>
                                <div class="flex justify-between">
                                    <span style="color: var(--text-light);">معدل النقر</span>
                                    <span class="font-bold text-blue-600" id="click-rate">0%</span>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Templates -->
                        <div class="card p-6">
                            <h3 class="text-xl font-bold mb-4" style="color: var(--text-dark);">قوالب سريعة</h3>
                            <div class="space-y-2">
                                <button class="w-full text-right p-2 hover:bg-gray-100 rounded" onclick="useTemplate('welcome')">
                                    <i class="fas fa-hand-wave ml-2"></i>
                                    رسالة ترحيب
                                </button>
                                <button class="w-full text-right p-2 hover:bg-gray-100 rounded" onclick="useTemplate('approval')">
                                    <i class="fas fa-check-circle ml-2"></i>
                                    إشعار القبول
                                </button>
                                <button class="w-full text-right p-2 hover:bg-gray-100 rounded" onclick="useTemplate('rejection')">
                                    <i class="fas fa-times-circle ml-2"></i>
                                    إشعار الرفض
                                </button>
                                <button class="w-full text-right p-2 hover:bg-gray-100 rounded" onclick="useTemplate('reminder')">
                                    <i class="fas fa-bell ml-2"></i>
                                    تذكير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analytics Section -->
            <div id="analytics-section" class="hidden">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h2 class="text-3xl font-bold" style="color: var(--text-dark);">التحليلات والإحصائيات</h2>
                        <p style="color: var(--text-light);">تحليل شامل لأداء الأكاديمية والاتجاهات</p>
                    </div>
                    <div class="flex space-x-4 space-x-reverse">
                        <select class="search-box" id="analytics-period">
                            <option value="today">اليوم</option>
                            <option value="week">هذا الأسبوع</option>
                            <option value="month" selected>هذا الشهر</option>
                            <option value="year">هذا العام</option>
                        </select>
                        <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all" onclick="exportAnalytics()">
                            <i class="fas fa-file-export ml-2"></i>
                            تصدير التقرير
                        </button>
                    </div>
                </div>

                <!-- KPI Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="stat-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm mb-1" style="color: var(--text-light);">إجمالي الطلبات</p>
                                <p class="text-3xl font-bold" style="color: var(--text-dark);" id="analytics-total-applications">0</p>
                                <p class="text-sm text-green-600 mt-1">
                                    <i class="fas fa-arrow-up"></i> <span id="applications-growth">0%</span>
                                </p>
                            </div>
                            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-file-alt text-2xl text-blue-600"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm mb-1" style="color: var(--text-light);">معدل القبول</p>
                                <p class="text-3xl font-bold" style="color: var(--text-dark);" id="analytics-acceptance-rate">0%</p>
                                <p class="text-sm text-blue-600 mt-1">
                                    <i class="fas fa-chart-line"></i> متوسط الصناعة: 75%
                                </p>
                            </div>
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-check-circle text-2xl text-green-600"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm mb-1" style="color: var(--text-light);">الإيرادات المتوقعة</p>
                                <p class="text-3xl font-bold" style="color: var(--text-dark);" id="analytics-revenue">0 ر.س</p>
                                <p class="text-sm text-purple-600 mt-1">
                                    <i class="fas fa-coins"></i> <span id="revenue-growth">0%</span>
                                </p>
                            </div>
                            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-chart-line text-2xl text-purple-600"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm mb-1" style="color: var(--text-light);">متوسط وقت المراجعة</p>
                                <p class="text-3xl font-bold" style="color: var(--text-dark);" id="analytics-review-time">0 يوم</p>
                                <p class="text-sm text-orange-600 mt-1">
                                    <i class="fas fa-clock"></i> الهدف: 2 أيام
                                </p>
                            </div>
                            <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-hourglass-half text-2xl text-orange-600"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Grid -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Applications Trend Chart -->
                    <div class="card p-6">
                        <h3 class="text-xl font-bold mb-4" style="color: var(--text-dark);">اتجاه الطلبات</h3>
                        <canvas id="applications-trend-chart" height="300"></canvas>
                    </div>

                    <!-- Category Distribution Chart -->
                    <div class="card p-6">
                        <h3 class="text-xl font-bold mb-4" style="color: var(--text-dark);">توزيع الفئات</h3>
                        <canvas id="category-distribution-chart" height="300"></canvas>
                    </div>

                    <!-- Source Analysis Chart -->
                    <div class="card p-6">
                        <h3 class="text-xl font-bold mb-4" style="color: var(--text-dark);">تحليل مصادر التسجيل</h3>
                        <canvas id="source-analysis-chart" height="300"></canvas>
                    </div>

                    <!-- Performance Metrics Chart -->
                    <div class="card p-6">
                        <h3 class="text-xl font-bold mb-4" style="color: var(--text-dark);">مؤشرات الأداء</h3>
                        <canvas id="performance-metrics-chart" height="300"></canvas>
                    </div>
                </div>

                <!-- Detailed Analytics Table -->
                <div class="card p-6">
                    <h3 class="text-xl font-bold mb-4" style="color: var(--text-dark);">تحليل تفصيلي</h3>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b" style="border-color: var(--border-color);">
                                    <th class="text-right p-4">الفترة</th>
                                    <th class="text-right p-4">الطلبات الجديدة</th>
                                    <th class="text-right p-4">المقبولة</th>
                                    <th class="text-right p-4">المرفوضة</th>
                                    <th class="text-right p-4">معدل القبول</th>
                                    <th class="text-right p-4">متوسط وقت المراجعة</th>
                                </tr>
                            </thead>
                            <tbody id="analytics-detailed-table">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Settings Section -->
            <div id="settings-section" class="hidden">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h2 class="text-3xl font-bold" style="color: var(--text-dark);">إعدادات النظام</h2>
                        <p style="color: var(--text-light);">تكوين وإدارة إعدادات النظام والمستخدمين</p>
                    </div>
                    <div class="flex space-x-4 space-x-reverse">
                        <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all" onclick="saveAllSettings()">
                            <i class="fas fa-save ml-2"></i>
                            حفظ جميع الإعدادات
                        </button>
                        <button class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-all" onclick="resetSettings()">
                            <i class="fas fa-undo ml-2"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- General Settings -->
                    <div class="card p-6">
                        <h3 class="text-xl font-bold mb-4" style="color: var(--text-dark);">الإعدادات العامة</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">اسم الأكاديمية</label>
                                <input type="text" class="search-box w-full" value="أكاديمية 7C الرياضية" id="academy-name">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">البريد الإلكتروني</label>
                                <input type="email" class="search-box w-full" value="<EMAIL>" id="academy-email">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">رقم الهاتف</label>
                                <input type="tel" class="search-box w-full" value="+966501234567" id="academy-phone">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">العنوان</label>
                                <textarea class="search-box w-full" rows="3" id="academy-address">الرياض، المملكة العربية السعودية</textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Database Settings -->
                    <div class="card p-6">
                        <h3 class="text-xl font-bold mb-4" style="color: var(--text-dark);">إعدادات قاعدة البيانات</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">اسم قاعدة البيانات</label>
                                <input type="text" class="search-box w-full" value="players7c" id="db-name" readonly>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">اسم المستخدم</label>
                                <input type="text" class="search-box w-full" value="komaro" id="db-username" readonly>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">حالة الاتصال</label>
                                <div class="flex items-center">
                                    <span class="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                                    <span class="text-green-600">متصل</span>
                                </div>
                            </div>
                            <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all" onclick="testDatabaseConnection()">
                                <i class="fas fa-database ml-2"></i>
                                اختبار الاتصال
                            </button>
                        </div>
                    </div>

                    <!-- Notification Settings -->
                    <div class="card p-6">
                        <h3 class="text-xl font-bold mb-4" style="color: var(--text-dark);">إعدادات الإشعارات</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span style="color: var(--text-dark);">إشعارات البريد الإلكتروني</span>
                                <label class="switch">
                                    <input type="checkbox" checked id="email-notifications">
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="flex items-center justify-between">
                                <span style="color: var(--text-dark);">إشعارات الواتساب</span>
                                <label class="switch">
                                    <input type="checkbox" checked id="whatsapp-notifications">
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="flex items-center justify-between">
                                <span style="color: var(--text-dark);">الإشعارات الصوتية</span>
                                <label class="switch">
                                    <input type="checkbox" id="sound-notifications">
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">مفتاح API للواتساب</label>
                                <input type="password" class="search-box w-full" placeholder="أدخل مفتاح API" id="whatsapp-api-key">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Management -->
                <div class="card p-6 mt-6">
                    <h3 class="text-xl font-bold mb-4" style="color: var(--text-dark);">إدارة المستخدمين</h3>
                    <div class="flex justify-between items-center mb-4">
                        <div class="flex space-x-4 space-x-reverse">
                            <button class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-all" onclick="addNewUser()">
                                <i class="fas fa-user-plus ml-2"></i>
                                إضافة مستخدم
                            </button>
                            <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all" onclick="exportUsers()">
                                <i class="fas fa-file-export ml-2"></i>
                                تصدير قائمة المستخدمين
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b" style="border-color: var(--border-color);">
                                    <th class="text-right p-4">الاسم</th>
                                    <th class="text-right p-4">البريد الإلكتروني</th>
                                    <th class="text-right p-4">الدور</th>
                                    <th class="text-right p-4">آخر دخول</th>
                                    <th class="text-right p-4">الحالة</th>
                                    <th class="text-right p-4">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="users-table">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Application Details Modal -->
    <div id="application-modal" class="modal fixed inset-0 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-content w-full max-w-6xl p-6 max-h-screen overflow-y-auto">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold" style="color: var(--text-dark);">تفاصيل طلب الانضمام</h3>
                    <button onclick="closeModal('application-modal')" class="text-gray-500 hover:text-gray-700" aria-label="إغلاق">
                        <i class="fas fa-times text-2xl"></i>
                    </button>
                </div>
                <div id="application-details">
                    <!-- Application details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Application Modal -->
    <div id="edit-application-modal" class="modal fixed inset-0 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-content w-full max-w-4xl p-6 max-h-screen overflow-y-auto">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold" style="color: var(--text-dark);">تحرير طلب الانضمام</h3>
                    <button onclick="closeModal('edit-application-modal')" class="text-gray-500 hover:text-gray-700" aria-label="إغلاق">
                        <i class="fas fa-times text-2xl"></i>
                    </button>
                </div>
                <form id="edit-application-form" onsubmit="updateApplication(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Personal Information -->
                        <div class="space-y-4">
                            <h4 class="text-lg font-semibold" style="color: var(--text-dark);">المعلومات الشخصية</h4>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">الاسم الكامل *</label>
                                <input type="text" id="edit-name" name="name" required
                                       class="search-box w-full" placeholder="أدخل الاسم الكامل">
                                <div class="error-message text-red-500 text-xs mt-1 hidden" id="name-error"></div>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">العمر *</label>
                                    <input type="number" id="edit-age" name="age" required min="12" max="25"
                                           class="search-box w-full" placeholder="العمر">
                                    <div class="error-message text-red-500 text-xs mt-1 hidden" id="age-error"></div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">الفئة *</label>
                                    <select id="edit-category" name="category" required class="search-box w-full">
                                        <option value="">اختر الفئة</option>
                                        <option value="براعم">براعم (12-14 سنة)</option>
                                        <option value="ناشئين">ناشئين (15-17 سنة)</option>
                                        <option value="شباب">شباب (18+ سنة)</option>
                                    </select>
                                    <div class="error-message text-red-500 text-xs mt-1 hidden" id="category-error"></div>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">رقم الهاتف *</label>
                                <input type="tel" id="edit-phone" name="phone" required
                                       class="search-box w-full" placeholder="05xxxxxxxx" pattern="^05[0-9]{8}$">
                                <div class="error-message text-red-500 text-xs mt-1 hidden" id="phone-error"></div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">البريد الإلكتروني *</label>
                                <input type="email" id="edit-email" name="email" required
                                       class="search-box w-full" placeholder="<EMAIL>">
                                <div class="error-message text-red-500 text-xs mt-1 hidden" id="email-error"></div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">صورة شخصية</label>
                                <input type="file" id="edit-photo" name="photo" accept="image/*"
                                       class="search-box w-full">
                                <div class="mt-2" id="current-photo-preview"></div>
                            </div>
                        </div>

                        <!-- Training Information -->
                        <div class="space-y-4">
                            <h4 class="text-lg font-semibold" style="color: var(--text-dark);">معلومات التدريب</h4>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">خطة التدريب *</label>
                                <select id="edit-training-plan" name="trainingPlan" required class="search-box w-full">
                                    <option value="">اختر خطة التدريب</option>
                                    <option value="خطة البراعم الأساسية">خطة البراعم الأساسية</option>
                                    <option value="خطة البراعم المتقدمة">خطة البراعم المتقدمة</option>
                                    <option value="خطة الناشئين الأساسية">خطة الناشئين الأساسية</option>
                                    <option value="خطة الناشئين المتقدمة">خطة الناشئين المتقدمة</option>
                                    <option value="خطة الشباب الاحترافية">خطة الشباب الاحترافية</option>
                                </select>
                                <div class="error-message text-red-500 text-xs mt-1 hidden" id="training-plan-error"></div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">حالة الطلب *</label>
                                <select id="edit-status" name="status" required class="search-box w-full">
                                    <option value="pending">قيد الانتظار</option>
                                    <option value="reviewing">قيد المراجعة</option>
                                    <option value="approved">مقبول</option>
                                    <option value="rejected">مرفوض</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">مصدر التسجيل</label>
                                <select id="edit-source" name="source" class="search-box w-full">
                                    <option value="mobile">جوال</option>
                                    <option value="desktop">كمبيوتر</option>
                                </select>
                            </div>

                            <!-- Documents -->
                            <div class="space-y-3">
                                <h5 class="font-medium" style="color: var(--text-dark);">المستندات المطلوبة</h5>

                                <div class="flex items-center">
                                    <input type="checkbox" id="edit-medical-certificate" name="medicalCertificate"
                                           class="ml-2 accent-color: var(--primary-color);">
                                    <label for="edit-medical-certificate" style="color: var(--text-dark);">الشهادة الطبية</label>
                                </div>

                                <div class="flex items-center">
                                    <input type="checkbox" id="edit-parent-consent" name="parentConsent"
                                           class="ml-2 accent-color: var(--primary-color);">
                                    <label for="edit-parent-consent" style="color: var(--text-dark);">موافقة ولي الأمر</label>
                                </div>
                            </div>

                            <!-- AI Assessment -->
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h5 class="font-medium text-purple-800 mb-2">تقييم الذكاء الاصطناعي</h5>
                                <div class="space-y-2">
                                    <div>
                                        <label class="block text-sm font-medium mb-1 text-purple-700">النقاط (0-100)</label>
                                        <input type="range" id="edit-ai-score" name="aiScore" min="0" max="100"
                                               class="w-full" oninput="updateAIScoreDisplay(this.value)">
                                        <div class="flex justify-between text-xs text-purple-600">
                                            <span>0</span>
                                            <span id="ai-score-display" class="font-bold">50</span>
                                            <span>100</span>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium mb-1 text-purple-700">التوصية</label>
                                        <textarea id="edit-ai-recommendation" name="aiRecommendation" rows="3"
                                                  class="search-box w-full text-sm"
                                                  placeholder="توصية الذكاء الاصطناعي..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end space-x-4 space-x-reverse mt-8 pt-6 border-t" style="border-color: var(--border-color);">
                        <button type="button" onclick="closeModal('edit-application-modal')"
                                class="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all">
                            إلغاء
                        </button>
                        <button type="submit"
                                class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all">
                            <i class="fas fa-save ml-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>

                    <input type="hidden" id="edit-application-id" name="id">
                </form>
            </div>
        </div>
    </div>

    <!-- Add New Application Modal -->
    <div id="add-application-modal" class="modal fixed inset-0 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-content w-full max-w-4xl p-6 max-h-screen overflow-y-auto">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold" style="color: var(--text-dark);">إضافة طلب انضمام جديد</h3>
                    <button onclick="closeModal('add-application-modal')" class="text-gray-500 hover:text-gray-700" aria-label="إغلاق">
                        <i class="fas fa-times text-2xl"></i>
                    </button>
                </div>
                <form id="add-application-form" onsubmit="createApplication(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Personal Information -->
                        <div class="space-y-4">
                            <h4 class="text-lg font-semibold" style="color: var(--text-dark);">المعلومات الشخصية</h4>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">الاسم الكامل *</label>
                                <input type="text" id="add-name" name="name" required
                                       class="search-box w-full" placeholder="أدخل الاسم الكامل">
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">العمر *</label>
                                    <input type="number" id="add-age" name="age" required min="12" max="25"
                                           class="search-box w-full" placeholder="العمر" onchange="suggestCategory()">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">الفئة *</label>
                                    <select id="add-category" name="category" required class="search-box w-full">
                                        <option value="">اختر الفئة</option>
                                        <option value="براعم">براعم (12-14 سنة)</option>
                                        <option value="ناشئين">ناشئين (15-17 سنة)</option>
                                        <option value="شباب">شباب (18+ سنة)</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">رقم الهاتف *</label>
                                <input type="tel" id="add-phone" name="phone" required
                                       class="search-box w-full" placeholder="05xxxxxxxx" pattern="^05[0-9]{8}$">
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">البريد الإلكتروني *</label>
                                <input type="email" id="add-email" name="email" required
                                       class="search-box w-full" placeholder="<EMAIL>">
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">صورة شخصية</label>
                                <input type="file" id="add-photo" name="photo" accept="image/*"
                                       class="search-box w-full" onchange="previewPhoto(this, 'add-photo-preview')">
                                <div class="mt-2" id="add-photo-preview"></div>
                            </div>
                        </div>

                        <!-- Training Information -->
                        <div class="space-y-4">
                            <h4 class="text-lg font-semibold" style="color: var(--text-dark);">معلومات التدريب</h4>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">خطة التدريب *</label>
                                <select id="add-training-plan" name="trainingPlan" required class="search-box w-full">
                                    <option value="">اختر خطة التدريب</option>
                                    <option value="خطة البراعم الأساسية">خطة البراعم الأساسية</option>
                                    <option value="خطة البراعم المتقدمة">خطة البراعم المتقدمة</option>
                                    <option value="خطة الناشئين الأساسية">خطة الناشئين الأساسية</option>
                                    <option value="خطة الناشئين المتقدمة">خطة الناشئين المتقدمة</option>
                                    <option value="خطة الشباب الاحترافية">خطة الشباب الاحترافية</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">مصدر التسجيل</label>
                                <select id="add-source" name="source" class="search-box w-full">
                                    <option value="desktop">كمبيوتر</option>
                                    <option value="mobile">جوال</option>
                                </select>
                            </div>

                            <!-- Documents -->
                            <div class="space-y-3">
                                <h5 class="font-medium" style="color: var(--text-dark);">المستندات المطلوبة</h5>

                                <div class="flex items-center">
                                    <input type="checkbox" id="add-medical-certificate" name="medicalCertificate"
                                           class="ml-2 accent-color: var(--primary-color);">
                                    <label for="add-medical-certificate" style="color: var(--text-dark);">الشهادة الطبية</label>
                                </div>

                                <div class="flex items-center">
                                    <input type="checkbox" id="add-parent-consent" name="parentConsent"
                                           class="ml-2 accent-color: var(--primary-color);">
                                    <label for="add-parent-consent" style="color: var(--text-dark);">موافقة ولي الأمر</label>
                                </div>
                            </div>

                            <!-- AI Assessment Preview -->
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h5 class="font-medium text-purple-800 mb-2">معاينة تقييم الذكاء الاصطناعي</h5>
                                <div class="text-sm text-purple-600">
                                    سيتم تقييم الطلب تلقائياً بواسطة الذكاء الاصطناعي بعد الإرسال
                                </div>
                                <div class="mt-2">
                                    <div class="w-full bg-purple-200 rounded-full h-2">
                                        <div class="bg-purple-600 h-2 rounded-full" style="width: 0%" id="ai-preview-bar"></div>
                                    </div>
                                    <div class="text-center mt-1 text-sm font-bold text-purple-700" id="ai-preview-score">جاري التحليل...</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end space-x-4 space-x-reverse mt-8 pt-6 border-t" style="border-color: var(--border-color);">
                        <button type="button" onclick="closeModal('add-application-modal')"
                                class="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all">
                            إلغاء
                        </button>
                        <button type="submit"
                                class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة الطلب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bulk Actions Modal -->
    <div id="bulk-actions-modal" class="modal fixed inset-0 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-content w-full max-w-2xl p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold" style="color: var(--text-dark);">الإجراءات المجمعة</h3>
                    <button onclick="closeModal('bulk-actions-modal')" class="text-gray-500 hover:text-gray-700" aria-label="إغلاق">
                        <i class="fas fa-times text-2xl"></i>
                    </button>
                </div>

                <div class="mb-6">
                    <p class="text-lg mb-4" style="color: var(--text-dark);">
                        تم اختيار <span id="selected-count" class="font-bold text-blue-600">0</span> طلب
                    </p>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">اختر الإجراء:</label>
                            <select id="bulk-action-type" class="search-box w-full">
                                <option value="">اختر الإجراء</option>
                                <option value="approve">قبول جميع الطلبات المحددة</option>
                                <option value="reject">رفض جميع الطلبات المحددة</option>
                                <option value="review">تحويل للمراجعة</option>
                                <option value="delete">حذف الطلبات المحددة</option>
                                <option value="export">تصدير الطلبات المحددة</option>
                                <option value="assign-plan">تعيين خطة تدريب</option>
                            </select>
                        </div>

                        <div id="bulk-training-plan-section" class="hidden">
                            <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">خطة التدريب:</label>
                            <select id="bulk-training-plan" class="search-box w-full">
                                <option value="">اختر خطة التدريب</option>
                                <option value="خطة البراعم الأساسية">خطة البراعم الأساسية</option>
                                <option value="خطة البراعم المتقدمة">خطة البراعم المتقدمة</option>
                                <option value="خطة الناشئين الأساسية">خطة الناشئين الأساسية</option>
                                <option value="خطة الناشئين المتقدمة">خطة الناشئين المتقدمة</option>
                                <option value="خطة الشباب الاحترافية">خطة الشباب الاحترافية</option>
                            </select>
                        </div>

                        <div id="bulk-reason-section" class="hidden">
                            <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">سبب الإجراء (اختياري):</label>
                            <textarea id="bulk-reason" class="search-box w-full" rows="3"
                                      placeholder="أدخل سبب الإجراء..."></textarea>
                        </div>

                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <div class="flex items-start">
                                <i class="fas fa-exclamation-triangle text-yellow-600 mt-1 ml-2"></i>
                                <div>
                                    <h4 class="font-medium text-yellow-800">تنبيه مهم</h4>
                                    <p class="text-sm text-yellow-700 mt-1">
                                        سيتم تطبيق الإجراء على جميع الطلبات المحددة. هذا الإجراء لا يمكن التراجع عنه.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-4 space-x-reverse">
                    <button type="button" onclick="closeModal('bulk-actions-modal')"
                            class="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all">
                        إلغاء
                    </button>
                    <button type="button" onclick="executeBulkAction()"
                            class="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-all">
                        <i class="fas fa-bolt ml-2"></i>
                        تنفيذ الإجراء
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Certificate Modal -->
    <div id="certificate-modal" class="modal fixed inset-0 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-content w-full max-w-3xl p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-800">سند الاشتراك</h3>
                    <button onclick="closeModal('certificate-modal')" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times text-2xl"></i>
                    </button>
                </div>
                <div id="certificate-content" class="bg-white p-8 border-2 border-gray-300 rounded-lg">
                    <!-- Certificate content will be generated here -->
                </div>
                <div class="mt-6 flex justify-end space-x-4 space-x-reverse">
                    <button onclick="printCertificate()" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        <i class="fas fa-print ml-2"></i>
                        طباعة
                    </button>
                    <button onclick="downloadCertificate()" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                        <i class="fas fa-download ml-2"></i>
                        تحميل PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Player Profile Modal -->
    <div id="player-profile-modal" class="modal fixed inset-0 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-content w-full max-w-6xl p-6 max-h-screen overflow-y-auto">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold" style="color: var(--text-dark);">الملف الشخصي للاعب</h3>
                    <button onclick="closeModal('player-profile-modal')" class="text-gray-500 hover:text-gray-700" aria-label="إغلاق">
                        <i class="fas fa-times text-2xl"></i>
                    </button>
                </div>
                <div id="player-profile-content">
                    <!-- Player profile content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Player Modal -->
    <div id="edit-player-modal" class="modal fixed inset-0 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-content w-full max-w-4xl p-6 max-h-screen overflow-y-auto">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold" style="color: var(--text-dark);">تحرير بيانات اللاعب</h3>
                    <button onclick="closeModal('edit-player-modal')" class="text-gray-500 hover:text-gray-700" aria-label="إغلاق">
                        <i class="fas fa-times text-2xl"></i>
                    </button>
                </div>
                <form id="edit-player-form" onsubmit="updatePlayer(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Personal Information -->
                        <div class="space-y-4">
                            <h4 class="text-lg font-semibold" style="color: var(--text-dark);">المعلومات الشخصية</h4>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">الاسم الكامل *</label>
                                <input type="text" id="edit-player-name" name="name" required
                                       class="search-box w-full" placeholder="أدخل الاسم الكامل">
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">العمر *</label>
                                    <input type="number" id="edit-player-age" name="age" required min="12" max="25"
                                           class="search-box w-full" placeholder="العمر">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">الفئة *</label>
                                    <select id="edit-player-category" name="category" required class="search-box w-full">
                                        <option value="">اختر الفئة</option>
                                        <option value="براعم">براعم (12-14 سنة)</option>
                                        <option value="ناشئين">ناشئين (15-17 سنة)</option>
                                        <option value="شباب">شباب (18+ سنة)</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">رقم الهاتف *</label>
                                <input type="tel" id="edit-player-phone" name="phone" required
                                       class="search-box w-full" placeholder="05xxxxxxxx" pattern="^05[0-9]{8}$">
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">البريد الإلكتروني *</label>
                                <input type="email" id="edit-player-email" name="email" required
                                       class="search-box w-full" placeholder="<EMAIL>">
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">الصورة الشخصية</label>
                                <input type="file" id="edit-player-photo" name="photo" accept="image/*"
                                       class="search-box w-full" onchange="previewPhoto(this, 'edit-player-photo-preview')">
                                <div class="mt-2" id="edit-player-photo-preview"></div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">حالة اللاعب</label>
                                <select id="edit-player-status" name="status" class="search-box w-full">
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                    <option value="suspended">موقوف</option>
                                    <option value="graduated">متخرج</option>
                                </select>
                            </div>
                        </div>

                        <!-- Training Information -->
                        <div class="space-y-4">
                            <h4 class="text-lg font-semibold" style="color: var(--text-dark);">معلومات التدريب</h4>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">خطة التدريب الحالية</label>
                                <select id="edit-player-training-plan" name="trainingPlan" class="search-box w-full">
                                    <option value="">اختر خطة التدريب</option>
                                    <option value="خطة البراعم الأساسية">خطة البراعم الأساسية</option>
                                    <option value="خطة البراعم المتقدمة">خطة البراعم المتقدمة</option>
                                    <option value="خطة الناشئين الأساسية">خطة الناشئين الأساسية</option>
                                    <option value="خطة الناشئين المتقدمة">خطة الناشئين المتقدمة</option>
                                    <option value="خطة الشباب الاحترافية">خطة الشباب الاحترافية</option>
                                </select>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">نقاط الأداء (0-100)</label>
                                    <input type="range" id="edit-player-performance" name="performance" min="0" max="100"
                                           class="w-full" oninput="updatePerformanceDisplay(this.value)">
                                    <div class="flex justify-between text-xs" style="color: var(--text-light);">
                                        <span>0</span>
                                        <span id="performance-display" class="font-bold">50</span>
                                        <span>100</span>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">نسبة الحضور (0-100%)</label>
                                    <input type="range" id="edit-player-attendance" name="attendance" min="0" max="100"
                                           class="w-full" oninput="updateAttendanceDisplay(this.value)">
                                    <div class="flex justify-between text-xs" style="color: var(--text-light);">
                                        <span>0%</span>
                                        <span id="attendance-display" class="font-bold">50%</span>
                                        <span>100%</span>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">تاريخ الانضمام</label>
                                <input type="date" id="edit-player-join-date" name="joinDate"
                                       class="search-box w-full">
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">آخر تدريب</label>
                                <input type="date" id="edit-player-last-training" name="lastTraining"
                                       class="search-box w-full">
                            </div>

                            <!-- Goals and Achievements -->
                            <div class="space-y-3">
                                <h5 class="font-medium" style="color: var(--text-dark);">الأهداف والإنجازات</h5>

                                <div>
                                    <label class="block text-sm font-medium mb-1" style="color: var(--text-dark);">الأهداف الحالية</label>
                                    <textarea id="edit-player-goals" name="goals" rows="3"
                                              class="search-box w-full text-sm"
                                              placeholder="أدخل أهداف اللاعب..."></textarea>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium mb-1" style="color: var(--text-dark);">الإنجازات</label>
                                    <textarea id="edit-player-achievements" name="achievements" rows="3"
                                              class="search-box w-full text-sm"
                                              placeholder="أدخل إنجازات اللاعب..."></textarea>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium mb-1" style="color: var(--text-dark);">ملاحظات المدرب</label>
                                    <textarea id="edit-player-notes" name="notes" rows="3"
                                              class="search-box w-full text-sm"
                                              placeholder="ملاحظات حول أداء اللاعب..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end space-x-4 space-x-reverse mt-8 pt-6 border-t" style="border-color: var(--border-color);">
                        <button type="button" onclick="closeModal('edit-player-modal')"
                                class="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all">
                            إلغاء
                        </button>
                        <button type="submit"
                                class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all">
                            <i class="fas fa-save ml-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>

                    <input type="hidden" id="edit-player-id" name="id">
                </form>
            </div>
        </div>
    </div>

    <!-- Add New Player Modal -->
    <div id="add-player-modal" class="modal fixed inset-0 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-content w-full max-w-4xl p-6 max-h-screen overflow-y-auto">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold" style="color: var(--text-dark);">إضافة لاعب جديد</h3>
                    <button onclick="closeModal('add-player-modal')" class="text-gray-500 hover:text-gray-700" aria-label="إغلاق">
                        <i class="fas fa-times text-2xl"></i>
                    </button>
                </div>
                <form id="add-player-form" onsubmit="createPlayer(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Personal Information -->
                        <div class="space-y-4">
                            <h4 class="text-lg font-semibold" style="color: var(--text-dark);">المعلومات الشخصية</h4>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">الاسم الكامل *</label>
                                <input type="text" id="add-player-name" name="name" required
                                       class="search-box w-full" placeholder="أدخل الاسم الكامل">
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">العمر *</label>
                                    <input type="number" id="add-player-age" name="age" required min="12" max="25"
                                           class="search-box w-full" placeholder="العمر" onchange="suggestPlayerCategory()">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">الفئة *</label>
                                    <select id="add-player-category" name="category" required class="search-box w-full">
                                        <option value="">اختر الفئة</option>
                                        <option value="براعم">براعم (12-14 سنة)</option>
                                        <option value="ناشئين">ناشئين (15-17 سنة)</option>
                                        <option value="شباب">شباب (18+ سنة)</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">رقم الهاتف *</label>
                                <input type="tel" id="add-player-phone" name="phone" required
                                       class="search-box w-full" placeholder="05xxxxxxxx" pattern="^05[0-9]{8}$">
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">البريد الإلكتروني *</label>
                                <input type="email" id="add-player-email" name="email" required
                                       class="search-box w-full" placeholder="<EMAIL>">
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">الصورة الشخصية</label>
                                <input type="file" id="add-player-photo" name="photo" accept="image/*"
                                       class="search-box w-full" onchange="previewPhoto(this, 'add-player-photo-preview')">
                                <div class="mt-2" id="add-player-photo-preview"></div>
                            </div>
                        </div>

                        <!-- Training Information -->
                        <div class="space-y-4">
                            <h4 class="text-lg font-semibold" style="color: var(--text-dark);">معلومات التدريب</h4>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">خطة التدريب *</label>
                                <select id="add-player-training-plan" name="trainingPlan" required class="search-box w-full">
                                    <option value="">اختر خطة التدريب</option>
                                    <option value="خطة البراعم الأساسية">خطة البراعم الأساسية</option>
                                    <option value="خطة البراعم المتقدمة">خطة البراعم المتقدمة</option>
                                    <option value="خطة الناشئين الأساسية">خطة الناشئين الأساسية</option>
                                    <option value="خطة الناشئين المتقدمة">خطة الناشئين المتقدمة</option>
                                    <option value="خطة الشباب الاحترافية">خطة الشباب الاحترافية</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">تاريخ الانضمام</label>
                                <input type="date" id="add-player-join-date" name="joinDate"
                                       class="search-box w-full" value="">
                            </div>

                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h5 class="font-medium text-blue-800 mb-2">معلومات الأداء الأولية</h5>
                                <div class="text-sm text-blue-600">
                                    سيتم تعيين نقاط الأداء والحضور الأولية بناءً على التقييم الأولي
                                </div>
                                <div class="mt-2 grid grid-cols-2 gap-4">
                                    <div>
                                        <div class="text-xs text-blue-700">الأداء المتوقع</div>
                                        <div class="w-full bg-blue-200 rounded-full h-2">
                                            <div class="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
                                        </div>
                                        <div class="text-center mt-1 text-sm font-bold text-blue-700">75%</div>
                                    </div>
                                    <div>
                                        <div class="text-xs text-blue-700">الحضور المتوقع</div>
                                        <div class="w-full bg-blue-200 rounded-full h-2">
                                            <div class="bg-blue-600 h-2 rounded-full" style="width: 85%"></div>
                                        </div>
                                        <div class="text-center mt-1 text-sm font-bold text-blue-700">85%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end space-x-4 space-x-reverse mt-8 pt-6 border-t" style="border-color: var(--border-color);">
                        <button type="button" onclick="closeModal('add-player-modal')"
                                class="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all">
                            إلغاء
                        </button>
                        <button type="submit"
                                class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة اللاعب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Training Plan Modal -->
    <div id="training-plan-modal" class="modal fixed inset-0 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-content w-full max-w-5xl p-6 max-h-screen overflow-y-auto">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold" style="color: var(--text-dark);">إدارة خطة التدريب</h3>
                    <button onclick="closeModal('training-plan-modal')" class="text-gray-500 hover:text-gray-700" aria-label="إغلاق">
                        <i class="fas fa-times text-2xl"></i>
                    </button>
                </div>
                <div id="training-plan-content">
                    <!-- Training plan content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Training Plan Modal -->
    <div id="edit-training-plan-modal" class="modal fixed inset-0 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-content w-full max-w-4xl p-6 max-h-screen overflow-y-auto">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold" style="color: var(--text-dark);" id="training-plan-modal-title">إضافة خطة تدريب جديدة</h3>
                    <button onclick="closeModal('edit-training-plan-modal')" class="text-gray-500 hover:text-gray-700" aria-label="إغلاق">
                        <i class="fas fa-times text-2xl"></i>
                    </button>
                </div>
                <form id="training-plan-form" onsubmit="saveTrainingPlan(event)">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Basic Information -->
                        <div class="space-y-4">
                            <h4 class="text-lg font-semibold" style="color: var(--text-dark);">المعلومات الأساسية</h4>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">اسم الخطة *</label>
                                <input type="text" id="plan-name" name="name" required
                                       class="search-box w-full" placeholder="أدخل اسم خطة التدريب">
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">الفئة المستهدفة *</label>
                                    <select id="plan-category" name="category" required class="search-box w-full">
                                        <option value="">اختر الفئة</option>
                                        <option value="براعم">براعم (12-14 سنة)</option>
                                        <option value="ناشئين">ناشئين (15-17 سنة)</option>
                                        <option value="شباب">شباب (18+ سنة)</option>
                                        <option value="جميع الفئات">جميع الفئات</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">مدة الخطة *</label>
                                    <select id="plan-duration" name="duration" required class="search-box w-full">
                                        <option value="">اختر المدة</option>
                                        <option value="شهر واحد">شهر واحد</option>
                                        <option value="3 أشهر">3 أشهر</option>
                                        <option value="6 أشهر">6 أشهر</option>
                                        <option value="سنة كاملة">سنة كاملة</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">وصف الخطة</label>
                                <textarea id="plan-description" name="description" rows="4"
                                          class="search-box w-full"
                                          placeholder="وصف مفصل لخطة التدريب وأهدافها..."></textarea>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">عدد الجلسات</label>
                                    <input type="number" id="plan-sessions" name="sessions" min="1" max="100"
                                           class="search-box w-full" placeholder="عدد الجلسات">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">السعر (ر.س)</label>
                                    <input type="number" id="plan-price" name="price" min="0" step="50"
                                           class="search-box w-full" placeholder="سعر الخطة">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2" style="color: var(--text-dark);">حالة الخطة</label>
                                <select id="plan-status" name="status" class="search-box w-full">
                                    <option value="active">نشطة</option>
                                    <option value="inactive">غير نشطة</option>
                                    <option value="draft">مسودة</option>
                                </select>
                            </div>
                        </div>

                        <!-- Loyalty Points System -->
                        <div class="space-y-4">
                            <h4 class="text-lg font-semibold" style="color: var(--text-dark);">نظام نقاط الولاء</h4>

                            <div class="bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-lg border border-yellow-200">
                                <h5 class="font-medium text-yellow-800 mb-3">إعدادات النقاط</h5>

                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium mb-1 text-yellow-700">نقاط الحضور</label>
                                        <input type="number" id="plan-attendance-points" name="attendancePoints"
                                               value="10" min="1" max="100" class="search-box w-full">
                                        <p class="text-xs text-yellow-600 mt-1">نقاط لكل جلسة حضور</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium mb-1 text-yellow-700">نقاط التقييم</label>
                                        <input type="number" id="plan-evaluation-points" name="evaluationPoints"
                                               value="50" min="1" max="500" class="search-box w-full">
                                        <p class="text-xs text-yellow-600 mt-1">نقاط للتقييم الممتاز</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium mb-1 text-yellow-700">نقاط الدعوات</label>
                                        <input type="number" id="plan-referral-points" name="referralPoints"
                                               value="100" min="1" max="1000" class="search-box w-full">
                                        <p class="text-xs text-yellow-600 mt-1">نقاط لكل دعوة ناجحة</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium mb-1 text-yellow-700">نقاط المكافآت</label>
                                        <input type="number" id="plan-bonus-points" name="bonusPoints"
                                               value="25" min="1" max="200" class="search-box w-full">
                                        <p class="text-xs text-yellow-600 mt-1">نقاط إضافية للإنجازات</p>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <h6 class="font-medium text-yellow-800 mb-2">مستويات الولاء</h6>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between p-2 bg-white rounded border">
                                            <span class="text-sm">🥉 برونزي</span>
                                            <span class="text-sm font-bold">0 - 500 نقطة</span>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-white rounded border">
                                            <span class="text-sm">🥈 فضي</span>
                                            <span class="text-sm font-bold">501 - 1500 نقطة</span>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-white rounded border">
                                            <span class="text-sm">🥇 ذهبي</span>
                                            <span class="text-sm font-bold">1501 - 3000 نقطة</span>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-white rounded border">
                                            <span class="text-sm">💎 ماسي</span>
                                            <span class="text-sm font-bold">3000+ نقطة</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Training Schedule -->
                            <div>
                                <h5 class="font-medium mb-3" style="color: var(--text-dark);">جدول التدريب</h5>
                                <div class="space-y-2">
                                    <div class="grid grid-cols-3 gap-2">
                                        <div>
                                            <label class="block text-xs font-medium mb-1" style="color: var(--text-dark);">اليوم</label>
                                            <select class="search-box w-full text-sm">
                                                <option value="">اختر اليوم</option>
                                                <option value="sunday">الأحد</option>
                                                <option value="monday">الاثنين</option>
                                                <option value="tuesday">الثلاثاء</option>
                                                <option value="wednesday">الأربعاء</option>
                                                <option value="thursday">الخميس</option>
                                                <option value="friday">الجمعة</option>
                                                <option value="saturday">السبت</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-xs font-medium mb-1" style="color: var(--text-dark);">من</label>
                                            <input type="time" class="search-box w-full text-sm">
                                        </div>
                                        <div>
                                            <label class="block text-xs font-medium mb-1" style="color: var(--text-dark);">إلى</label>
                                            <input type="time" class="search-box w-full text-sm">
                                        </div>
                                    </div>
                                    <button type="button" onclick="addTrainingSession()"
                                            class="w-full px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-all text-sm">
                                        <i class="fas fa-plus ml-1"></i>
                                        إضافة جلسة تدريب
                                    </button>
                                </div>
                                <div id="training-sessions-list" class="mt-3 space-y-2">
                                    <!-- Training sessions will be added here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end space-x-4 space-x-reverse mt-8 pt-6 border-t" style="border-color: var(--border-color);">
                        <button type="button" onclick="closeModal('edit-training-plan-modal')"
                                class="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all">
                            إلغاء
                        </button>
                        <button type="submit"
                                class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all">
                            <i class="fas fa-save ml-2"></i>
                            حفظ الخطة
                        </button>
                    </div>

                    <input type="hidden" id="plan-id" name="id">
                </form>
            </div>
        </div>
    </div>

    <!-- Loyalty Points Dashboard Modal -->
    <div id="loyalty-points-modal" class="modal fixed inset-0 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-content w-full max-w-6xl p-6 max-h-screen overflow-y-auto">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold" style="color: var(--text-dark);">لوحة نقاط الولاء</h3>
                    <button onclick="closeModal('loyalty-points-modal')" class="text-gray-500 hover:text-gray-700" aria-label="إغلاق">
                        <i class="fas fa-times text-2xl"></i>
                    </button>
                </div>
                <div id="loyalty-points-content">
                    <!-- Loyalty points content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- AI Assistant Chat -->
    <div id="ai-chat" class="fixed bottom-24 right-8 w-80 bg-white rounded-2xl shadow-2xl hidden z-50">
        <div class="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-4 rounded-t-2xl">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas fa-robot ml-2"></i>
                    <span class="font-semibold">مساعد الذكاء الاصطناعي</span>
                </div>
                <button onclick="toggleAIAssistant()" class="text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="p-4 h-64 overflow-y-auto" id="ai-chat-messages">
            <div class="bg-gray-100 p-3 rounded-lg mb-3">
                <p class="text-sm">مرحباً! أنا مساعدك الذكي. كيف يمكنني مساعدتك في إدارة طلبات الانضمام؟</p>
            </div>
        </div>
        <div class="p-4 border-t">
            <div class="flex">
                <input type="text" id="ai-input" placeholder="اكتب رسالتك..." class="flex-1 p-2 border rounded-l-lg">
                <button onclick="sendAIMessage()" class="bg-blue-600 text-white px-4 py-2 rounded-r-lg hover:bg-blue-700">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Database Configuration
        const DB_CONFIG = {
            host: 'localhost',
            database: 'players7c',
            username: 'komaro',
            password: 'ZdShaker@14'
        };

        // Global Variables
        let currentTheme = 'dark'; // الوضع المظلم كافتراضي
        let applications = [];
        let players = [];
        let notifications = [];
        let trainingPlans = [];
        let certificates = [];
        let users = [];
        let realtimeChart;
        let currentSection = 'dashboard';
        let currentPage = 1;
        let itemsPerPage = 10;
        let searchTimeout;
        let charts = {};

        // Initialize the system
        document.addEventListener('DOMContentLoaded', function() {
            initializeSystem();
            loadAllData();
            initializeCharts();
            startRealtimeUpdates();
            setupEventListeners();
            setupKeyboardShortcuts();
        });

        // System Initialization
        function initializeSystem() {
            console.log('🚀 تم تشغيل نظام إدارة طلبات الانضمام - أكاديمية 7C');
            console.log('🤖 مساعد الذكاء الاصطناعي جاهز');
            console.log('📊 نظام الإحصائيات اللحظية نشط');
            console.log('🌙 الوضع المظلم مفعل كافتراضي');

            // Set dark theme as default
            const savedTheme = localStorage.getItem('theme') || 'dark';
            currentTheme = savedTheme;

            if (savedTheme === 'light') {
                document.body.setAttribute('data-theme', 'light');
                document.getElementById('theme-icon').className = 'fas fa-moon';
            } else {
                // Dark theme is default, no need to set attribute
                document.getElementById('theme-icon').className = 'fas fa-sun';
            }

            // Show loading state
            showLoadingState();

            // Initialize mobile menu
            setupMobileMenu();

            console.log('✅ تم تهيئة النظام بنجاح');
        }

        // Setup Event Listeners
        function setupEventListeners() {
            // Filter chips
            document.querySelectorAll('.filter-chip').forEach(chip => {
                chip.addEventListener('click', function() {
                    document.querySelectorAll('.filter-chip').forEach(c => c.classList.remove('active'));
                    this.classList.add('active');
                    filterApplications(this.dataset.filter);
                });
            });

            // Search with debounce
            const searchInput = document.getElementById('search-applications');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        searchApplications(this.value);
                    }, 300);
                });
            }

            // AI input
            const aiInput = document.getElementById('ai-input');
            if (aiInput) {
                aiInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendAIMessage();
                    }
                });
            }

            // Analytics period change
            const analyticsPeriod = document.getElementById('analytics-period');
            if (analyticsPeriod) {
                analyticsPeriod.addEventListener('change', function() {
                    updateAnalytics(this.value);
                });
            }

            // Close modals on outside click
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('modal')) {
                    e.target.classList.add('hidden');
                }
            });
        }

        // Setup Keyboard Shortcuts
        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                // Ctrl+S: Save
                if (e.ctrlKey && e.key === 's') {
                    e.preventDefault();
                    saveCurrentSection();
                }

                // Escape: Close modals
                if (e.key === 'Escape') {
                    closeAllModals();
                }

                // Ctrl+F: Focus search
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    focusSearch();
                }

                // Ctrl+D: Toggle dark mode
                if (e.ctrlKey && e.key === 'd') {
                    e.preventDefault();
                    toggleTheme();
                }
            });
        }

        // Mobile Menu Setup
        function setupMobileMenu() {
            const mobileBtn = document.querySelector('.mobile-menu-btn');
            const sidebar = document.querySelector('.sidebar');

            if (mobileBtn && sidebar) {
                mobileBtn.addEventListener('click', function() {
                    sidebar.classList.toggle('open');
                    const icon = this.querySelector('i');
                    if (sidebar.classList.contains('open')) {
                        icon.className = 'fas fa-times text-gray-800';
                    } else {
                        icon.className = 'fas fa-bars text-gray-800';
                    }
                });
            }
        }

        // Load All Data
        async function loadAllData() {
            try {
                showLoadingState();

                await Promise.all([
                    loadApplications(),
                    loadPlayers(),
                    loadNotifications(),
                    loadTrainingPlans(),
                    loadCertificates(),
                    loadUsers()
                ]);

                hideLoadingState();
                console.log('✅ تم تحميل جميع البيانات بنجاح');

            } catch (error) {
                console.error('❌ خطأ في تحميل البيانات:', error);
                showNotification('خطأ في تحميل البيانات', 'error');
                hideLoadingState();
            }
        }

        // Show/Hide Loading State
        function showLoadingState() {
            const loadingElements = document.querySelectorAll('[id$="-list"], [id$="-grid"], [id$="-table"]');
            loadingElements.forEach(element => {
                if (element) {
                    element.innerHTML = createLoadingSkeleton();
                }
            });
        }

        function hideLoadingState() {
            // Loading will be replaced by actual content
        }

        function createLoadingSkeleton() {
            return `
                <div class="loading-skeleton h-16 rounded-lg mb-4"></div>
                <div class="loading-skeleton h-16 rounded-lg mb-4"></div>
                <div class="loading-skeleton h-16 rounded-lg mb-4"></div>
            `;
        }

        // Theme Toggle
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('theme-icon');

            if (currentTheme === 'dark') {
                body.setAttribute('data-theme', 'light');
                themeIcon.className = 'fas fa-moon';
                currentTheme = 'light';
                console.log('🌞 تم التبديل إلى الوضع النهاري');
            } else {
                body.removeAttribute('data-theme');
                themeIcon.className = 'fas fa-sun';
                currentTheme = 'dark';
                console.log('🌙 تم التبديل إلى الوضع المظلم');
            }

            localStorage.setItem('theme', currentTheme);

            // Update charts colors if they exist
            updateChartsTheme();

            // Show notification
            showNotification(`تم التبديل إلى ${currentTheme === 'dark' ? 'الوضع المظلم' : 'الوضع النهاري'}`, 'success');
        }

        // Mobile Menu Toggle
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.sidebar');
            const mobileBtn = document.querySelector('.mobile-menu-btn');

            if (sidebar && mobileBtn) {
                sidebar.classList.toggle('open');
                const icon = mobileBtn.querySelector('i');

                if (sidebar.classList.contains('open')) {
                    icon.className = 'fas fa-times text-gray-800';
                    mobileBtn.setAttribute('aria-label', 'إغلاق القائمة');
                } else {
                    icon.className = 'fas fa-bars text-gray-800';
                    mobileBtn.setAttribute('aria-label', 'فتح القائمة');
                }
            }
        }

        // Load Applications from Database
        async function loadApplications() {
            try {
                console.log('📋 جاري تحميل طلبات الانضمام...');

                // Simulated database call with more realistic data
                applications = [
                    {
                        id: 1, name: 'أحمد محمد علي الزهراني', age: 16, category: 'ناشئين', status: 'pending',
                        submitDate: '2024-01-15', source: 'mobile', phone: '**********', email: '<EMAIL>',
                        photo: 'https://via.placeholder.com/100x100/8B4513/FFFFFF?text=أحمد', trainingPlan: 'خطة الناشئين المتقدمة',
                        medicalCertificate: true, parentConsent: true, aiScore: 85, aiRecommendation: 'مرشح ممتاز - يُنصح بالقبول الفوري'
                    },
                    {
                        id: 2, name: 'فاطمة أحمد السالم', age: 14, category: 'براعم', status: 'reviewing',
                        submitDate: '2024-01-14', source: 'desktop', phone: '**********', email: '<EMAIL>',
                        photo: 'https://via.placeholder.com/100x100/8B4513/FFFFFF?text=فاطمة', trainingPlan: 'خطة البراعم الأساسية',
                        medicalCertificate: true, parentConsent: true, aiScore: 92, aiRecommendation: 'مرشحة متميزة - موهبة واعدة'
                    },
                    {
                        id: 3, name: 'خالد عبدالله النصر', age: 18, category: 'شباب', status: 'approved',
                        submitDate: '2024-01-13', source: 'mobile', phone: '**********', email: '<EMAIL>',
                        photo: 'https://via.placeholder.com/100x100/8B4513/FFFFFF?text=خالد', trainingPlan: 'خطة الشباب الاحترافية',
                        medicalCertificate: true, parentConsent: false, aiScore: 78, aiRecommendation: 'مقبول - يحتاج متابعة إضافية'
                    },
                    {
                        id: 4, name: 'سارة محمد القحطاني', age: 15, category: 'ناشئين', status: 'approved',
                        submitDate: '2024-01-12', source: 'mobile', phone: '**********', email: '<EMAIL>',
                        photo: 'https://via.placeholder.com/100x100/8B4513/FFFFFF?text=سارة', trainingPlan: 'خطة الناشئين الأساسية',
                        medicalCertificate: true, parentConsent: true, aiScore: 88, aiRecommendation: 'مرشحة ممتازة - قدرات عالية'
                    },
                    {
                        id: 5, name: 'عبدالرحمن سعد الغامدي', age: 13, category: 'براعم', status: 'pending',
                        submitDate: '2024-01-11', source: 'desktop', phone: '**********', email: '<EMAIL>',
                        photo: 'https://via.placeholder.com/100x100/8B4513/FFFFFF?text=عبدالرحمن', trainingPlan: 'خطة البراعم المتقدمة',
                        medicalCertificate: true, parentConsent: true, aiScore: 79, aiRecommendation: 'مرشح جيد - يحتاج تقييم إضافي'
                    },
                    {
                        id: 6, name: 'نورا علي الشهري', age: 17, category: 'ناشئين', status: 'reviewing',
                        submitDate: '2024-01-10', source: 'mobile', phone: '**********', email: '<EMAIL>',
                        photo: 'https://via.placeholder.com/100x100/8B4513/FFFFFF?text=نورا', trainingPlan: 'خطة الناشئين المتقدمة',
                        medicalCertificate: true, parentConsent: true, aiScore: 91, aiRecommendation: 'مرشحة متفوقة - إمكانيات عالية'
                    },
                    {
                        id: 7, name: 'محمد عبدالعزيز الدوسري', age: 19, category: 'شباب', status: 'rejected',
                        submitDate: '2024-01-09', source: 'desktop', phone: '**********', email: '<EMAIL>',
                        photo: 'https://via.placeholder.com/100x100/8B4513/FFFFFF?text=محمد', trainingPlan: 'خطة الشباب الاحترافية',
                        medicalCertificate: false, parentConsent: true, aiScore: 45, aiRecommendation: 'غير مناسب - نقص في المستندات'
                    },
                    {
                        id: 8, name: 'ريم فهد العتيبي', age: 16, category: 'ناشئين', status: 'approved',
                        submitDate: '2024-01-08', source: 'mobile', phone: '**********', email: '<EMAIL>',
                        photo: 'https://via.placeholder.com/100x100/8B4513/FFFFFF?text=ريم', trainingPlan: 'خطة الناشئين الأساسية',
                        medicalCertificate: true, parentConsent: true, aiScore: 86, aiRecommendation: 'مرشحة ممتازة - التزام عالي'
                    },
                    {
                        id: 9, name: 'يوسف أحمد الحربي', age: 14, category: 'براعم', status: 'pending',
                        submitDate: '2024-01-07', source: 'desktop', phone: '**********', email: '<EMAIL>',
                        photo: 'https://via.placeholder.com/100x100/8B4513/FFFFFF?text=يوسف', trainingPlan: 'خطة البراعم الأساسية',
                        medicalCertificate: true, parentConsent: true, aiScore: 82, aiRecommendation: 'مرشح جيد - يظهر إمكانيات'
                    },
                    {
                        id: 10, name: 'لينا سالم المطيري', age: 15, category: 'ناشئين', status: 'reviewing',
                        submitDate: '2024-01-06', source: 'mobile', phone: '**********', email: '<EMAIL>',
                        photo: 'https://via.placeholder.com/100x100/8B4513/FFFFFF?text=لينا', trainingPlan: 'خطة الناشئين المتقدمة',
                        medicalCertificate: true, parentConsent: true, aiScore: 89, aiRecommendation: 'مرشحة متميزة - مهارات قيادية'
                    }
                ];

                renderApplications();
                renderApplicationsTable();
                updateStatistics();
                console.log('✅ تم تحميل طلبات الانضمام بنجاح');

            } catch (error) {
                console.error('❌ خطأ في تحميل الطلبات:', error);
                showNotification('خطأ في تحميل طلبات الانضمام', 'error');
            }
        }

        // Render Applications
        function renderApplications() {
            const applicationsList = document.getElementById('applications-list');

            applicationsList.innerHTML = applications.slice(0, 10).map(app => `
                <div class="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all cursor-pointer" onclick="viewApplicationDetails(${app.id})">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <img src="${app.photo}" alt="${app.name}" class="w-12 h-12 rounded-full object-cover">
                        <div>
                            <h4 class="font-semibold text-gray-800">${app.name}</h4>
                            <p class="text-sm text-gray-600">${app.category} • ${app.age} سنة</p>
                            <div class="flex items-center mt-1">
                                <i class="fas fa-${app.source === 'mobile' ? 'mobile-alt' : 'desktop'} text-xs text-gray-400 ml-1"></i>
                                <span class="text-xs text-gray-500">${app.source === 'mobile' ? 'جوال' : 'كمبيوتر'}</span>
                                <span class="text-xs text-gray-400 mx-2">•</span>
                                <span class="text-xs text-gray-500">${formatDate(app.submitDate)}</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="text-center">
                            <div class="text-sm font-semibold text-purple-600">AI Score</div>
                            <div class="text-lg font-bold text-purple-800">${app.aiScore}%</div>
                        </div>
                        <span class="badge status-${app.status}">
                            ${getStatusText(app.status)}
                        </span>
                        <div class="flex space-x-1 space-x-reverse">
                            <button onclick="event.stopPropagation(); approveApplication(${app.id})" class="p-2 bg-green-600 text-white rounded hover:bg-green-700" title="قبول">
                                <i class="fas fa-check text-xs"></i>
                            </button>
                            <button onclick="event.stopPropagation(); rejectApplication(${app.id})" class="p-2 bg-red-600 text-white rounded hover:bg-red-700" title="رفض">
                                <i class="fas fa-times text-xs"></i>
                            </button>
                            <button onclick="event.stopPropagation(); generateCertificate(${app.id})" class="p-2 bg-blue-600 text-white rounded hover:bg-blue-700" title="إصدار سند">
                                <i class="fas fa-certificate text-xs"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Helper Functions
        function getStatusText(status) {
            const statusMap = {
                'pending': 'قيد الانتظار',
                'reviewing': 'قيد المراجعة',
                'approved': 'مقبول',
                'rejected': 'مرفوض',
                'active': 'نشط'
            };
            return statusMap[status] || status;
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // Application Actions
        function viewApplicationDetails(id) {
            const app = applications.find(a => a.id === id);
            if (!app) return;

            document.getElementById('application-details').innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div class="text-center">
                            <img src="${app.photo}" alt="${app.name}" class="w-32 h-32 rounded-full mx-auto mb-4 object-cover">
                            <h3 class="text-2xl font-bold text-gray-800">${app.name}</h3>
                            <p class="text-gray-600">${app.category} • ${app.age} سنة</p>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-800 mb-2">معلومات الاتصال</h4>
                            <p><i class="fas fa-phone ml-2"></i>${app.phone}</p>
                            <p><i class="fas fa-envelope ml-2"></i>${app.email}</p>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-800 mb-2">تفاصيل التقديم</h4>
                            <p><i class="fas fa-calendar ml-2"></i>تاريخ التقديم: ${formatDate(app.submitDate)}</p>
                            <p><i class="fas fa-${app.source === 'mobile' ? 'mobile-alt' : 'desktop'} ml-2"></i>المصدر: ${app.source === 'mobile' ? 'جوال' : 'كمبيوتر'}</p>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-purple-800 mb-2">تقييم الذكاء الاصطناعي</h4>
                            <div class="flex items-center mb-2">
                                <span class="text-3xl font-bold text-purple-600">${app.aiScore}%</span>
                                <div class="mr-4 flex-1">
                                    <div class="bg-gray-200 rounded-full h-2">
                                        <div class="progress-bar rounded-full h-2" style="width: ${app.aiScore}%"></div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-sm text-purple-700">${app.aiRecommendation}</p>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-800 mb-2">خطة التدريب المقترحة</h4>
                            <p class="text-gray-700">${app.trainingPlan}</p>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-800 mb-2">المستندات المطلوبة</h4>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <i class="fas fa-${app.medicalCertificate ? 'check-circle text-green-600' : 'times-circle text-red-600'} ml-2"></i>
                                    <span>الشهادة الطبية</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-${app.parentConsent ? 'check-circle text-green-600' : 'times-circle text-red-600'} ml-2"></i>
                                    <span>موافقة ولي الأمر</span>
                                </div>
                            </div>
                        </div>

                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="bg-white p-3 rounded-lg shadow-sm">
                                    <h5 class="font-semibold">تم استلام الطلب</h5>
                                    <p class="text-sm text-gray-600">${formatDate(app.submitDate)}</p>
                                </div>
                            </div>
                            ${app.status !== 'pending' ? `
                            <div class="timeline-item">
                                <div class="bg-white p-3 rounded-lg shadow-sm">
                                    <h5 class="font-semibold">بدء المراجعة</h5>
                                    <p class="text-sm text-gray-600">تم تحويل الطلب للمراجعة</p>
                                </div>
                            </div>
                            ` : ''}
                            ${app.status === 'approved' ? `
                            <div class="timeline-item">
                                <div class="bg-white p-3 rounded-lg shadow-sm">
                                    <h5 class="font-semibold">تم القبول</h5>
                                    <p class="text-sm text-gray-600">تم قبول الطلب وإرسال الإشعار</p>
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                </div>

                <div class="mt-6 flex justify-end space-x-4 space-x-reverse flex-wrap gap-2">
                    <button onclick="openEditApplicationModal(${app.id})" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all">
                        <i class="fas fa-edit ml-2"></i>
                        تحرير الطلب
                    </button>
                    <button onclick="deleteApplication(${app.id})" class="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-all">
                        <i class="fas fa-trash ml-2"></i>
                        حذف الطلب
                    </button>
                    <button onclick="approveApplication(${app.id})" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all">
                        <i class="fas fa-check ml-2"></i>
                        قبول الطلب
                    </button>
                    <button onclick="rejectApplication(${app.id})" class="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-all">
                        <i class="fas fa-times ml-2"></i>
                        رفض الطلب
                    </button>
                    <button onclick="generateCertificate(${app.id})" class="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-all">
                        <i class="fas fa-certificate ml-2"></i>
                        إصدار سند الاشتراك
                    </button>
                    <button onclick="sendNotification(${app.id})" class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-all">
                        <i class="fas fa-bell ml-2"></i>
                        إرسال إشعار
                    </button>
                </div>
            `;

            document.getElementById('application-modal').classList.remove('hidden');
        }

        function approveApplication(id) {
            const app = applications.find(a => a.id === id);
            if (!app) return;

            Swal.fire({
                title: 'تأكيد القبول',
                text: `هل أنت متأكد من قبول طلب ${app.name}؟`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#10b981',
                cancelButtonColor: '#ef4444',
                confirmButtonText: 'نعم، قبول',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    app.status = 'approved';
                    renderApplications();
                    updateStatistics();

                    // Send notification
                    sendAutoNotification(app, 'approved');

                    Swal.fire({
                        title: 'تم القبول!',
                        text: 'تم قبول الطلب بنجاح وإرسال الإشعار للمتقدم',
                        icon: 'success',
                        timer: 3000
                    });

                    closeModal('application-modal');
                }
            });
        }

        function rejectApplication(id) {
            const app = applications.find(a => a.id === id);
            if (!app) return;

            Swal.fire({
                title: 'تأكيد الرفض',
                text: `هل أنت متأكد من رفض طلب ${app.name}؟`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'نعم، رفض',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    app.status = 'rejected';
                    renderApplications();
                    updateStatistics();

                    // Send notification
                    sendAutoNotification(app, 'rejected');

                    Swal.fire({
                        title: 'تم الرفض!',
                        text: 'تم رفض الطلب وإرسال الإشعار للمتقدم',
                        icon: 'success',
                        timer: 3000
                    });

                    closeModal('application-modal');
                }
            });
        }

        // Generate Certificate
        function generateCertificate(id) {
            const app = applications.find(a => a.id === id);
            if (!app) return;

            const certificateContent = `
                <div style="text-align: center; padding: 40px; border: 3px solid #8B4513; background: linear-gradient(135deg, #f8f9fa, #ffffff);">
                    <div style="border: 2px solid #CD853F; padding: 30px; margin: 20px;">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjOEI0NTEzIiByeD0iMTAiLz4KPHRleHQgeD0iNTAiIHk9IjU1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMzYiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+N0M8L3RleHQ+Cjwvc3ZnPgo=" alt="شعار الأكاديمية" style="width: 80px; height: 80px; margin-bottom: 20px;">

                        <h1 style="color: #8B4513; font-size: 32px; font-weight: bold; margin: 20px 0;">أكاديمية 7C الرياضية</h1>
                        <h2 style="color: #A0522D; font-size: 24px; margin: 15px 0;">سند اشتراك</h2>

                        <div style="margin: 30px 0; padding: 20px; background: rgba(139, 69, 19, 0.1); border-radius: 10px;">
                            <p style="font-size: 18px; margin: 10px 0;">نشهد بأن</p>
                            <h3 style="color: #8B4513; font-size: 28px; font-weight: bold; margin: 15px 0; text-decoration: underline;">${app.name}</h3>
                            <p style="font-size: 18px; margin: 10px 0;">قد انضم رسمياً إلى أكاديمية 7C الرياضية</p>
                            <p style="font-size: 16px; margin: 10px 0;">في فئة: <strong>${app.category}</strong></p>
                            <p style="font-size: 16px; margin: 10px 0;">خطة التدريب: <strong>${app.trainingPlan}</strong></p>
                        </div>

                        <div style="display: flex; justify-content: space-between; margin-top: 40px; padding: 0 20px;">
                            <div style="text-align: center;">
                                <div style="border-top: 2px solid #8B4513; width: 150px; margin-bottom: 5px;"></div>
                                <p style="font-size: 14px; color: #666;">توقيع المدير</p>
                                <p style="font-size: 12px; color: #999;">أكاديمية 7C</p>
                            </div>
                            <div style="text-align: center;">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDAiIGN5PSI0MCIgcj0iMzgiIHN0cm9rZT0iIzhCNDUxMyIgc3Ryb2tlLXdpZHRoPSI0IiBmaWxsPSJub25lIi8+Cjx0ZXh0IHg9IjQwIiB5PSI0NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iIzhCNDUxMyIgdGV4dC1hbmNob3I9Im1pZGRsZSI+ختم الأكاديمية</dGV4dD4KPC9zdmc+Cg==" alt="ختم الأكاديمية" style="width: 80px; height: 80px;">
                            </div>
                        </div>

                        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
                            <p style="font-size: 14px; color: #666;">تاريخ الإصدار: ${new Date().toLocaleDateString('ar-SA')}</p>
                            <p style="font-size: 12px; color: #999;">رقم السند: 7C-${app.id}-${new Date().getFullYear()}</p>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('certificate-content').innerHTML = certificateContent;
            document.getElementById('certificate-modal').classList.remove('hidden');
        }

        // Print Certificate
        function printCertificate() {
            const printContent = document.getElementById('certificate-content').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>سند الاشتراك</title>
                    <style>
                        body { font-family: 'Arial', sans-serif; direction: rtl; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>${printContent}</body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        // Download Certificate as PDF
        function downloadCertificate() {
            // This would typically use a library like jsPDF
            alert('سيتم تحميل السند كملف PDF');
        }

        // Send Notifications
        function sendAutoNotification(app, type) {
            const messages = {
                approved: {
                    title: 'تم قبول طلبك!',
                    message: `مرحباً ${app.name}، تم قبول طلب انضمامك لأكاديمية 7C. مرحباً بك في عائلتنا الرياضية!`,
                    whatsapp: `مرحباً ${app.name}! 🎉\n\nنحن سعداء لإبلاغك بأنه تم قبول طلب انضمامك لأكاديمية 7C الرياضية.\n\nتفاصيل الاشتراك:\n- الفئة: ${app.category}\n- خطة التدريب: ${app.trainingPlan}\n\nسنتواصل معك قريباً لتحديد موعد البدء.\n\nأكاديمية 7C 🏆`
                },
                rejected: {
                    title: 'تحديث حول طلبك',
                    message: `عذراً ${app.name}، لم نتمكن من قبول طلبك في الوقت الحالي. يمكنك التقديم مرة أخرى لاحقاً.`,
                    whatsapp: `مرحباً ${app.name},\n\nشكراً لاهتمامك بالانضمام لأكاديمية 7C.\n\nللأسف، لم نتمكن من قبول طلبك في الوقت الحالي، لكن يمكنك التقديم مرة أخرى في المستقبل.\n\nأكاديمية 7C`
                }
            };

            const notification = messages[type];

            // Simulate sending email
            console.log(`📧 إرسال بريد إلكتروني إلى ${app.email}:`);
            console.log(`العنوان: ${notification.title}`);
            console.log(`الرسالة: ${notification.message}`);

            // Simulate sending WhatsApp
            console.log(`📱 إرسال رسالة واتساب إلى ${app.phone}:`);
            console.log(notification.whatsapp);

            // Add to notifications list
            notifications.unshift({
                id: Date.now(),
                title: `تم إرسال إشعار ${type === 'approved' ? 'القبول' : 'الرفض'}`,
                message: `تم إرسال إشعار إلى ${app.name} عبر البريد الإلكتروني والواتساب`,
                time: new Date().toISOString(),
                read: false,
                type: type === 'approved' ? 'success' : 'info'
            });

            updateNotificationBadge();
        }

        function sendNotification(id) {
            const app = applications.find(a => a.id === id);
            if (!app) return;

            Swal.fire({
                title: 'إرسال إشعار',
                html: `
                    <div style="text-align: right;">
                        <p>اختر طريقة الإرسال لـ <strong>${app.name}</strong>:</p>
                        <div style="margin: 20px 0;">
                            <label style="display: block; margin: 10px 0;">
                                <input type="checkbox" checked> البريد الإلكتروني (${app.email})
                            </label>
                            <label style="display: block; margin: 10px 0;">
                                <input type="checkbox" checked> رسالة واتساب (${app.phone})
                            </label>
                            <label style="display: block; margin: 10px 0;">
                                <input type="checkbox"> رسالة نصية (SMS)
                            </label>
                        </div>
                        <textarea placeholder="اكتب رسالتك هنا..." style="width: 100%; height: 100px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; resize: vertical;"></textarea>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'إرسال',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#8b5cf6'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'تم الإرسال!',
                        text: 'تم إرسال الإشعار بنجاح',
                        icon: 'success',
                        timer: 2000
                    });
                }
            });
        }

        // Update Statistics
        function updateStatistics() {
            const stats = {
                new: applications.filter(app => app.status === 'pending').length,
                reviewing: applications.filter(app => app.status === 'reviewing').length,
                approved: applications.filter(app => app.status === 'approved').length,
                active: applications.filter(app => app.status === 'active').length
            };

            document.getElementById('new-applications').textContent = stats.new;
            document.getElementById('reviewing-applications').textContent = stats.reviewing;
            document.getElementById('approved-applications').textContent = stats.approved;
            document.getElementById('active-players').textContent = stats.active;
        }

        // Filter and Search Functions
        function filterApplications(filter) {
            let filtered = applications;

            if (filter !== 'all') {
                filtered = applications.filter(app => app.status === filter);
            }

            const applicationsList = document.getElementById('applications-list');
            applicationsList.innerHTML = filtered.slice(0, 10).map(app => `
                <div class="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all cursor-pointer" onclick="viewApplicationDetails(${app.id})">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <img src="${app.photo}" alt="${app.name}" class="w-12 h-12 rounded-full object-cover">
                        <div>
                            <h4 class="font-semibold text-gray-800">${app.name}</h4>
                            <p class="text-sm text-gray-600">${app.category} • ${app.age} سنة</p>
                            <div class="flex items-center mt-1">
                                <i class="fas fa-${app.source === 'mobile' ? 'mobile-alt' : 'desktop'} text-xs text-gray-400 ml-1"></i>
                                <span class="text-xs text-gray-500">${app.source === 'mobile' ? 'جوال' : 'كمبيوتر'}</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="text-center">
                            <div class="text-sm font-semibold text-purple-600">AI Score</div>
                            <div class="text-lg font-bold text-purple-800">${app.aiScore}%</div>
                        </div>
                        <span class="badge status-${app.status}">
                            ${getStatusText(app.status)}
                        </span>
                    </div>
                </div>
            `).join('');
        }

        function searchApplications(query) {
            if (!query.trim()) {
                renderApplications();
                return;
            }

            const filtered = applications.filter(app =>
                app.name.toLowerCase().includes(query.toLowerCase()) ||
                app.category.toLowerCase().includes(query.toLowerCase()) ||
                app.email.toLowerCase().includes(query.toLowerCase()) ||
                app.phone.includes(query)
            );

            const applicationsList = document.getElementById('applications-list');
            applicationsList.innerHTML = filtered.map(app => `
                <div class="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all cursor-pointer" onclick="viewApplicationDetails(${app.id})">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <img src="${app.photo}" alt="${app.name}" class="w-12 h-12 rounded-full object-cover">
                        <div>
                            <h4 class="font-semibold text-gray-800">${app.name}</h4>
                            <p class="text-sm text-gray-600">${app.category} • ${app.age} سنة</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <span class="badge status-${app.status}">
                            ${getStatusText(app.status)}
                        </span>
                    </div>
                </div>
            `).join('');
        }

        // Notifications
        function loadNotifications() {
            notifications = [
                {
                    id: 1,
                    title: 'طلب جديد',
                    message: 'تم استلام طلب انضمام جديد من أحمد محمد',
                    time: new Date().toISOString(),
                    read: false,
                    type: 'info'
                },
                {
                    id: 2,
                    title: 'تم القبول',
                    message: 'تم قبول طلب فاطمة أحمد وإرسال الإشعار',
                    time: new Date(Date.now() - 3600000).toISOString(),
                    read: false,
                    type: 'success'
                },
                {
                    id: 3,
                    title: 'تذكير',
                    message: '5 طلبات تحتاج مراجعة عاجلة',
                    time: new Date(Date.now() - 7200000).toISOString(),
                    read: true,
                    type: 'warning'
                }
            ];

            renderNotifications();
            updateNotificationBadge();
        }

        function renderNotifications() {
            const notificationsList = document.getElementById('notifications-list');

            notificationsList.innerHTML = notifications.slice(0, 5).map(notif => `
                <div class="notification-item ${!notif.read ? 'unread' : ''}" onclick="markAsRead(${notif.id})">
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="w-8 h-8 rounded-full flex items-center justify-center ${
                            notif.type === 'success' ? 'bg-green-100 text-green-600' :
                            notif.type === 'warning' ? 'bg-yellow-100 text-yellow-600' :
                            'bg-blue-100 text-blue-600'
                        }">
                            <i class="fas fa-${
                                notif.type === 'success' ? 'check' :
                                notif.type === 'warning' ? 'exclamation-triangle' :
                                'info'
                            } text-xs"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-800 text-sm">${notif.title}</h4>
                            <p class="text-gray-600 text-xs mt-1">${notif.message}</p>
                            <p class="text-gray-400 text-xs mt-1">${formatTimeAgo(notif.time)}</p>
                        </div>
                        ${!notif.read ? '<div class="w-2 h-2 bg-blue-500 rounded-full"></div>' : ''}
                    </div>
                </div>
            `).join('');
        }

        function toggleNotifications() {
            const dropdown = document.getElementById('notification-dropdown');
            dropdown.classList.toggle('hidden');
        }

        function updateNotificationBadge() {
            const unreadCount = notifications.filter(n => !n.read).length;
            const badge = document.querySelector('.absolute.-top-2.-right-2');
            if (badge) {
                badge.textContent = unreadCount;
                badge.style.display = unreadCount > 0 ? 'flex' : 'none';
            }
        }

        function markAsRead(id) {
            const notification = notifications.find(n => n.id === id);
            if (notification) {
                notification.read = true;
                renderNotifications();
                updateNotificationBadge();
            }
        }

        function formatTimeAgo(timeString) {
            const time = new Date(timeString);
            const now = new Date();
            const diffInMinutes = Math.floor((now - time) / (1000 * 60));

            if (diffInMinutes < 1) return 'الآن';
            if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;
            if (diffInMinutes < 1440) return `منذ ${Math.floor(diffInMinutes / 60)} ساعة`;
            return `منذ ${Math.floor(diffInMinutes / 1440)} يوم`;
        }

        // AI Assistant
        function toggleAIAssistant() {
            const aiChat = document.getElementById('ai-chat');
            aiChat.classList.toggle('hidden');
        }

        function sendAIMessage() {
            const input = document.getElementById('ai-input');
            const message = input.value.trim();

            if (!message) return;

            const messagesContainer = document.getElementById('ai-chat-messages');

            // Add user message
            messagesContainer.innerHTML += `
                <div class="text-left mb-3">
                    <div class="bg-blue-600 text-white p-3 rounded-lg inline-block max-w-xs">
                        <p class="text-sm">${message}</p>
                    </div>
                </div>
            `;

            input.value = '';

            // Simulate AI response
            setTimeout(() => {
                const responses = [
                    'يمكنني مساعدتك في تحليل الطلبات وتقديم التوصيات المناسبة.',
                    'بناءً على البيانات المتاحة، أنصح بمراجعة الطلبات ذات النقاط العالية أولاً.',
                    'هناك 3 طلبات تحتاج انتباه عاجل بسبب تجاوز المدة المحددة.',
                    'يمكنني إنشاء تقرير مفصل عن أداء عملية القبول هذا الشهر.',
                    'أقترح إرسال رسائل تذكير للمتقدمين الذين لم يكملوا مستنداتهم.'
                ];

                const randomResponse = responses[Math.floor(Math.random() * responses.length)];

                messagesContainer.innerHTML += `
                    <div class="text-right mb-3">
                        <div class="bg-gray-100 p-3 rounded-lg inline-block max-w-xs">
                            <p class="text-sm">${randomResponse}</p>
                        </div>
                    </div>
                `;

                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }, 1000);

            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Section Management
        function showSection(section) {
            console.log(`🔄 التبديل إلى قسم: ${section}`);

            // Hide all sections
            document.querySelectorAll('[id$="-section"]').forEach(el => {
                el.classList.add('hidden');
            });

            // Show selected section
            const sectionElement = document.getElementById(section + '-section');
            if (sectionElement) {
                sectionElement.classList.remove('hidden');
                currentSection = section;

                // Load section-specific data
                loadSectionData(section);

                // Update page title
                updatePageTitle(section);

                // Close mobile menu if open
                const sidebar = document.querySelector('.sidebar');
                if (sidebar && sidebar.classList.contains('open')) {
                    toggleMobileMenu();
                }
            }

            // Update navigation
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
                item.removeAttribute('aria-current');
            });

            // Find and activate the clicked nav item
            const clickedItem = event?.target?.closest('.nav-item');
            if (clickedItem) {
                clickedItem.classList.add('active');
                clickedItem.setAttribute('aria-current', 'page');
            }
        }

        function loadSectionData(section) {
            switch(section) {
                case 'applications':
                    renderApplicationsTable();
                    break;
                case 'players':
                    renderPlayersGrid();
                    break;
                case 'training-plans':
                    renderTrainingPlans();
                    break;
                case 'certificates':
                    renderCertificates();
                    break;
                case 'notifications':
                    renderNotificationsHistory();
                    break;
                case 'analytics':
                    renderAnalytics();
                    break;
                case 'settings':
                    renderSettings();
                    break;
                default:
                    // Dashboard is already loaded
                    break;
            }
        }

        function updatePageTitle(section) {
            const titles = {
                'dashboard': 'لوحة التحكم الرئيسية',
                'applications': 'إدارة طلبات الانضمام',
                'players': 'إدارة اللاعبين المسجلين',
                'training-plans': 'إدارة خطط التدريب',
                'certificates': 'إدارة سندات الاشتراك',
                'notifications': 'مركز الإشعارات',
                'analytics': 'التحليلات والإحصائيات',
                'settings': 'إعدادات النظام'
            };

            const title = titles[section] || 'أكاديمية 7C';
            document.title = `${title} - أكاديمية 7C`;
        }

        // Utility Functions
        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                // Remove focus trap
                document.body.style.overflow = '';
            }
        }

        function closeAllModals() {
            document.querySelectorAll('.modal').forEach(modal => {
                modal.classList.add('hidden');
            });
            document.body.style.overflow = '';
        }

        function saveCurrentSection() {
            switch(currentSection) {
                case 'settings':
                    saveAllSettings();
                    break;
                case 'certificates':
                    updateCertificateTemplate();
                    break;
                default:
                    showNotification('تم الحفظ', 'success');
                    break;
            }
        }

        function focusSearch() {
            const searchInputs = [
                document.getElementById('search-applications'),
                document.getElementById('players-search')
            ];

            const activeSearch = searchInputs.find(input =>
                input && !input.closest('[id$="-section"]').classList.contains('hidden')
            );

            if (activeSearch) {
                activeSearch.focus();
                activeSearch.select();
            }
        }

        // Initialize Charts
        function initializeCharts() {
            initializeRealtimeChart();
            initializeAnalyticsCharts();
        }

        function initializeRealtimeChart() {
            const ctx = document.getElementById('realtime-chart');
            if (!ctx) return;

            const stats = getApplicationStats();

            realtimeChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['قيد الانتظار', 'قيد المراجعة', 'مقبول', 'مرفوض'],
                    datasets: [{
                        data: [stats.pending, stats.reviewing, stats.approved, stats.rejected],
                        backgroundColor: ['#fbbf24', '#3b82f6', '#10b981', '#ef4444'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    size: 12,
                                    family: 'Cairo'
                                },
                                padding: 15,
                                color: currentTheme === 'dark' ? '#ffffff' : '#374151'
                            }
                        }
                    }
                }
            });

            charts.realtime = realtimeChart;
        }

        function initializeAnalyticsCharts() {
            // Applications Trend Chart
            const trendCtx = document.getElementById('applications-trend-chart');
            if (trendCtx) {
                charts.trend = new Chart(trendCtx, {
                    type: 'line',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                        datasets: [{
                            label: 'الطلبات الجديدة',
                            data: [12, 19, 15, 25, 22, 30],
                            borderColor: '#8B4513',
                            backgroundColor: 'rgba(139, 69, 19, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                labels: {
                                    color: currentTheme === 'dark' ? '#ffffff' : '#374151',
                                    font: { family: 'Cairo' }
                                }
                            }
                        },
                        scales: {
                            y: {
                                ticks: { color: currentTheme === 'dark' ? '#ffffff' : '#374151' },
                                grid: { color: currentTheme === 'dark' ? '#4d4d4d' : '#e5e7eb' }
                            },
                            x: {
                                ticks: { color: currentTheme === 'dark' ? '#ffffff' : '#374151' },
                                grid: { color: currentTheme === 'dark' ? '#4d4d4d' : '#e5e7eb' }
                            }
                        }
                    }
                });
            }

            // Category Distribution Chart
            const categoryCtx = document.getElementById('category-distribution-chart');
            if (categoryCtx) {
                const categoryStats = getCategoryStats();
                charts.category = new Chart(categoryCtx, {
                    type: 'pie',
                    data: {
                        labels: ['براعم', 'ناشئين', 'شباب'],
                        datasets: [{
                            data: [categoryStats.براعم, categoryStats.ناشئين, categoryStats.شباب],
                            backgroundColor: ['#CD853F', '#8B4513', '#A0522D']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                labels: {
                                    color: currentTheme === 'dark' ? '#ffffff' : '#374151',
                                    font: { family: 'Cairo' }
                                }
                            }
                        }
                    }
                });
            }

            // Source Analysis Chart
            const sourceCtx = document.getElementById('source-analysis-chart');
            if (sourceCtx) {
                const sourceStats = getSourceStats();
                charts.source = new Chart(sourceCtx, {
                    type: 'bar',
                    data: {
                        labels: ['جوال', 'كمبيوتر'],
                        datasets: [{
                            label: 'عدد التسجيلات',
                            data: [sourceStats.mobile, sourceStats.desktop],
                            backgroundColor: ['#8B4513', '#A0522D']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                labels: {
                                    color: currentTheme === 'dark' ? '#ffffff' : '#374151',
                                    font: { family: 'Cairo' }
                                }
                            }
                        },
                        scales: {
                            y: {
                                ticks: { color: currentTheme === 'dark' ? '#ffffff' : '#374151' },
                                grid: { color: currentTheme === 'dark' ? '#4d4d4d' : '#e5e7eb' }
                            },
                            x: {
                                ticks: { color: currentTheme === 'dark' ? '#ffffff' : '#374151' },
                                grid: { color: currentTheme === 'dark' ? '#4d4d4d' : '#e5e7eb' }
                            }
                        }
                    }
                });
            }

            // Performance Metrics Chart
            const performanceCtx = document.getElementById('performance-metrics-chart');
            if (performanceCtx) {
                charts.performance = new Chart(performanceCtx, {
                    type: 'radar',
                    data: {
                        labels: ['معدل القبول', 'سرعة المراجعة', 'رضا العملاء', 'جودة التدريب', 'الحضور'],
                        datasets: [{
                            label: 'الأداء الحالي',
                            data: [85, 78, 92, 88, 90],
                            borderColor: '#8B4513',
                            backgroundColor: 'rgba(139, 69, 19, 0.2)'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                labels: {
                                    color: currentTheme === 'dark' ? '#ffffff' : '#374151',
                                    font: { family: 'Cairo' }
                                }
                            }
                        },
                        scales: {
                            r: {
                                ticks: { color: currentTheme === 'dark' ? '#ffffff' : '#374151' },
                                grid: { color: currentTheme === 'dark' ? '#4d4d4d' : '#e5e7eb' }
                            }
                        }
                    }
                });
            }
        }

        function updateChartsTheme() {
            Object.values(charts).forEach(chart => {
                if (chart && chart.options) {
                    const textColor = currentTheme === 'dark' ? '#ffffff' : '#374151';
                    const gridColor = currentTheme === 'dark' ? '#4d4d4d' : '#e5e7eb';

                    // Update legend colors
                    if (chart.options.plugins && chart.options.plugins.legend) {
                        chart.options.plugins.legend.labels.color = textColor;
                    }

                    // Update scale colors
                    if (chart.options.scales) {
                        Object.values(chart.options.scales).forEach(scale => {
                            if (scale.ticks) scale.ticks.color = textColor;
                            if (scale.grid) scale.grid.color = gridColor;
                        });
                    }

                    chart.update();
                }
            });
        }

        // Start real-time updates
        function startRealtimeUpdates() {
            setInterval(() => {
                updateStatistics();

                // Simulate new applications
                if (Math.random() < 0.1) { // 10% chance every interval
                    const newApp = {
                        id: applications.length + 1,
                        name: 'متقدم جديد',
                        age: Math.floor(Math.random() * 10) + 12,
                        category: ['براعم', 'ناشئين', 'شباب'][Math.floor(Math.random() * 3)],
                        status: 'pending',
                        submitDate: new Date().toISOString().split('T')[0],
                        source: Math.random() > 0.5 ? 'mobile' : 'desktop',
                        phone: '050' + Math.floor(Math.random() * 10000000),
                        email: '<EMAIL>',
                        photo: 'https://via.placeholder.com/100x100/8B4513/FFFFFF?text=جديد',
                        trainingPlan: 'خطة أساسية',
                        medicalCertificate: true,
                        parentConsent: true,
                        aiScore: Math.floor(Math.random() * 40) + 60,
                        aiRecommendation: 'متقدم جديد - يحتاج مراجعة'
                    };

                    applications.unshift(newApp);
                    renderApplications();

                    // Add notification
                    notifications.unshift({
                        id: Date.now(),
                        title: 'طلب جديد',
                        message: `تم استلام طلب انضمام جديد`,
                        time: new Date().toISOString(),
                        read: false,
                        type: 'info'
                    });

                    updateNotificationBadge();
                }
            }, 30000); // Update every 30 seconds
        }

        // Load Players
        async function loadPlayers() {
            try {
                console.log('👥 جاري تحميل بيانات اللاعبين...');

                players = [
                    {
                        id: 1, name: 'محمد أحمد الزهراني', age: 16, category: 'ناشئين', status: 'active',
                        photo: 'https://via.placeholder.com/150x150/8B4513/FFFFFF?text=محمد', joinDate: '2024-01-01',
                        phone: '**********', email: '<EMAIL>', trainingPlan: 'خطة الناشئين المتقدمة',
                        performance: 85, attendance: 92, lastTraining: '2024-01-20',
                        goals: 'تحسين اللياقة البدنية والمهارات التقنية',
                        achievements: 'حصل على المركز الثاني في بطولة الأكاديمية الداخلية',
                        notes: 'لاعب مجتهد ويظهر تحسناً مستمراً في الأداء',
                        loyaltyPoints: 1250, loyaltyLevel: 'فضي', attendanceSessions: 18, referrals: 2, evaluationScore: 88
                    },
                    {
                        id: 2, name: 'سارة علي الشهري', age: 14, category: 'براعم', status: 'active',
                        photo: 'https://via.placeholder.com/150x150/8B4513/FFFFFF?text=سارة', joinDate: '2024-01-05',
                        phone: '**********', email: '<EMAIL>', trainingPlan: 'خطة البراعم الأساسية',
                        performance: 90, attendance: 88, lastTraining: '2024-01-19',
                        goals: 'تطوير المهارات الأساسية والعمل الجماعي',
                        achievements: 'أفضل لاعبة في فئة البراعم لشهر يناير',
                        notes: 'موهبة واعدة تحتاج تطوير إضافي',
                        loyaltyPoints: 890, loyaltyLevel: 'برونزي', attendanceSessions: 15, referrals: 1, evaluationScore: 92
                    },
                    {
                        id: 3, name: 'عبدالله محمد القحطاني', age: 18, category: 'شباب', status: 'active',
                        photo: 'https://via.placeholder.com/150x150/8B4513/FFFFFF?text=عبدالله', joinDate: '2023-12-15',
                        phone: '**********', email: '<EMAIL>', trainingPlan: 'خطة الشباب الاحترافية',
                        performance: 88, attendance: 95, lastTraining: '2024-01-20',
                        goals: 'الوصول للمستوى الاحترافي والمشاركة في البطولات',
                        achievements: 'قائد فريق الشباب وحاصل على جائزة أفضل لاعب',
                        notes: 'قائد طبيعي ومثال يحتذى به للاعبين الآخرين',
                        loyaltyPoints: 2150, loyaltyLevel: 'ذهبي', attendanceSessions: 22, referrals: 3, evaluationScore: 95
                    },
                    {
                        id: 4, name: 'فاطمة سعد الغامدي', age: 15, category: 'ناشئين', status: 'active',
                        photo: 'https://via.placeholder.com/150x150/8B4513/FFFFFF?text=فاطمة', joinDate: '2023-12-20',
                        phone: '**********', email: '<EMAIL>', trainingPlan: 'خطة الناشئين الأساسية',
                        performance: 87, attendance: 90, lastTraining: '2024-01-18',
                        goals: 'تحسين الأداء التكتيكي والمشاركة في المسابقات',
                        achievements: 'عضو في المنتخب المحلي للناشئين',
                        notes: 'تظهر تطوراً ملحوظاً في الجانب التكتيكي',
                        loyaltyPoints: 1680, loyaltyLevel: 'ذهبي', attendanceSessions: 20, referrals: 2, evaluationScore: 89
                    },
                    {
                        id: 5, name: 'خالد عبدالرحمن الدوسري', age: 17, category: 'ناشئين', status: 'inactive',
                        photo: 'https://via.placeholder.com/150x150/8B4513/FFFFFF?text=خالد', joinDate: '2023-11-10',
                        phone: '**********', email: '<EMAIL>', trainingPlan: 'خطة الناشئين المتقدمة',
                        performance: 75, attendance: 70, lastTraining: '2024-01-10',
                        goals: 'العودة للنشاط والتركيز على اللياقة البدنية',
                        achievements: 'مشارك في عدة بطولات محلية',
                        notes: 'يحتاج تحفيز إضافي لتحسين الحضور والالتزام',
                        loyaltyPoints: 420, loyaltyLevel: 'برونزي', attendanceSessions: 8, referrals: 0, evaluationScore: 72
                    },
                    {
                        id: 6, name: 'نورا حسن العتيبي', age: 13, category: 'براعم', status: 'active',
                        photo: 'https://via.placeholder.com/150x150/8B4513/FFFFFF?text=نورا', joinDate: '2024-01-10',
                        phone: '**********', email: '<EMAIL>', trainingPlan: 'خطة البراعم المتقدمة',
                        performance: 82, attendance: 85, lastTraining: '2024-01-19',
                        goals: 'تطوير المهارات الفردية والثقة بالنفس',
                        achievements: 'حديثة الانضمام - تظهر إمكانيات جيدة',
                        notes: 'بداية واعدة تحتاج متابعة مستمرة',
                        loyaltyPoints: 320, loyaltyLevel: 'برونزي', attendanceSessions: 6, referrals: 1, evaluationScore: 85
                    },
                    {
                        id: 7, name: 'يوسف عمر الحربي', age: 19, category: 'شباب', status: 'active',
                        photo: 'https://via.placeholder.com/150x150/8B4513/FFFFFF?text=يوسف', joinDate: '2023-09-01',
                        phone: '**********', email: '<EMAIL>', trainingPlan: 'خطة الشباب الاحترافية',
                        performance: 91, attendance: 97, lastTraining: '2024-01-20',
                        goals: 'الانتقال للمستوى الاحترافي والحصول على منحة دراسية',
                        achievements: 'أفضل لاعب في الأكاديمية لعام 2023',
                        notes: 'موهبة استثنائية مرشح للمنتخبات الوطنية',
                        loyaltyPoints: 3450, loyaltyLevel: 'ماسي', attendanceSessions: 28, referrals: 5, evaluationScore: 98
                    },
                    {
                        id: 8, name: 'ريم فيصل المطيري', age: 16, category: 'ناشئين', status: 'active',
                        photo: 'https://via.placeholder.com/150x150/8B4513/FFFFFF?text=ريم', joinDate: '2023-11-15',
                        phone: '**********', email: '<EMAIL>', trainingPlan: 'خطة الناشئين المتقدمة',
                        performance: 86, attendance: 89, lastTraining: '2024-01-18',
                        goals: 'تطوير مهارات القيادة والمشاركة في البطولات الإقليمية',
                        achievements: 'نائب قائد فريق الناشئين',
                        notes: 'تظهر مهارات قيادية ممتازة',
                        loyaltyPoints: 1920, loyaltyLevel: 'ذهبي', attendanceSessions: 19, referrals: 3, evaluationScore: 91
                    }
                ];

                console.log('✅ تم تحميل بيانات اللاعبين بنجاح');

            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات اللاعبين:', error);
                showNotification('خطأ في تحميل بيانات اللاعبين', 'error');
            }
        }

        // Load Training Plans
        async function loadTrainingPlans() {
            try {
                console.log('🏋️ جاري تحميل خطط التدريب...');

                trainingPlans = [
                    {
                        id: 1, name: 'خطة البراعم الأساسية', category: 'براعم', duration: '3 أشهر',
                        description: 'خطة تدريبية أساسية للفئة العمرية 12-14 سنة تركز على المهارات الأساسية',
                        sessions: 24, price: 800, status: 'active', enrolledPlayers: 12,
                        attendancePoints: 10, evaluationPoints: 50, referralPoints: 100, bonusPoints: 25,
                        schedule: [
                            { day: 'sunday', from: '16:00', to: '17:30' },
                            { day: 'tuesday', from: '16:00', to: '17:30' },
                            { day: 'thursday', from: '16:00', to: '17:30' }
                        ]
                    },
                    {
                        id: 2, name: 'خطة البراعم المتقدمة', category: 'براعم', duration: '6 أشهر',
                        description: 'خطة متقدمة للبراعم المتميزين مع تدريبات إضافية',
                        sessions: 48, price: 1200, status: 'active', enrolledPlayers: 8,
                        attendancePoints: 15, evaluationPoints: 75, referralPoints: 150, bonusPoints: 40,
                        schedule: [
                            { day: 'sunday', from: '17:30', to: '19:00' },
                            { day: 'tuesday', from: '17:30', to: '19:00' },
                            { day: 'thursday', from: '17:30', to: '19:00' }
                        ]
                    },
                    {
                        id: 3, name: 'خطة الناشئين الأساسية', category: 'ناشئين', duration: '3 أشهر',
                        description: 'خطة أساسية للناشئين تركز على بناء الأساس القوي',
                        sessions: 36, price: 1000, status: 'active', enrolledPlayers: 15,
                        attendancePoints: 12, evaluationPoints: 60, referralPoints: 120, bonusPoints: 30,
                        schedule: [
                            { day: 'monday', from: '18:00', to: '19:30' },
                            { day: 'wednesday', from: '18:00', to: '19:30' },
                            { day: 'friday', from: '18:00', to: '19:30' }
                        ]
                    },
                    {
                        id: 4, name: 'خطة الناشئين المتقدمة', category: 'ناشئين', duration: '6 أشهر',
                        description: 'خطة متقدمة للناشئين تركز على التطوير التكتيكي والبدني',
                        sessions: 48, price: 1500, status: 'active', enrolledPlayers: 8,
                        attendancePoints: 18, evaluationPoints: 90, referralPoints: 180, bonusPoints: 45,
                        schedule: [
                            { day: 'monday', from: '19:30', to: '21:00' },
                            { day: 'wednesday', from: '19:30', to: '21:00' },
                            { day: 'friday', from: '19:30', to: '21:00' }
                        ]
                    },
                    {
                        id: 5, name: 'خطة الشباب الاحترافية', category: 'شباب', duration: 'سنة كاملة',
                        description: 'خطة احترافية شاملة للإعداد للمستوى التنافسي',
                        sessions: 96, price: 2800, status: 'active', enrolledPlayers: 5,
                        attendancePoints: 25, evaluationPoints: 125, referralPoints: 250, bonusPoints: 60,
                        schedule: [
                            { day: 'saturday', from: '19:00', to: '21:00' },
                            { day: 'monday', from: '21:00', to: '22:30' },
                            { day: 'wednesday', from: '21:00', to: '22:30' },
                            { day: 'friday', from: '21:00', to: '22:30' }
                        ]
                    }
                ];

                console.log('✅ تم تحميل خطط التدريب بنجاح');

            } catch (error) {
                console.error('❌ خطأ في تحميل خطط التدريب:', error);
                showNotification('خطأ في تحميل خطط التدريب', 'error');
            }
        }

        // Load Certificates
        async function loadCertificates() {
            try {
                console.log('📜 جاري تحميل سندات الاشتراك...');

                certificates = [
                    {
                        id: 1, playerName: 'محمد أحمد الزهراني', certificateNumber: '7C-001-2024',
                        issueDate: '2024-01-15', category: 'ناشئين', status: 'issued'
                    },
                    {
                        id: 2, playerName: 'سارة علي الشهري', certificateNumber: '7C-002-2024',
                        issueDate: '2024-01-16', category: 'براعم', status: 'issued'
                    }
                ];

                console.log('✅ تم تحميل سندات الاشتراك بنجاح');

            } catch (error) {
                console.error('❌ خطأ في تحميل سندات الاشتراك:', error);
                showNotification('خطأ في تحميل سندات الاشتراك', 'error');
            }
        }

        // Load Users
        async function loadUsers() {
            try {
                console.log('👤 جاري تحميل بيانات المستخدمين...');

                users = [
                    {
                        id: 1, name: 'مدير النظام', email: '<EMAIL>', role: 'مدير',
                        lastLogin: '2024-01-20 10:30', status: 'active'
                    },
                    {
                        id: 2, name: 'مساعد المدير', email: '<EMAIL>', role: 'مساعد',
                        lastLogin: '2024-01-19 14:20', status: 'active'
                    }
                ];

                console.log('✅ تم تحميل بيانات المستخدمين بنجاح');

            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات المستخدمين:', error);
                showNotification('خطأ في تحميل بيانات المستخدمين', 'error');
            }
        }

        // Render Functions
        function renderApplicationsTable() {
            const tableBody = document.getElementById('applications-table-body');
            if (!tableBody) return;

            tableBody.innerHTML = applications.map(app => `
                <tr class="table-row border-b cursor-pointer" style="border-color: var(--border-color);" onclick="viewApplicationDetails(${app.id})">
                    <td class="p-4">
                        <input type="checkbox" class="table-checkbox" value="${app.id}" onclick="event.stopPropagation()">
                    </td>
                    <td class="p-4">
                        <img src="${app.photo}" alt="${app.name}" class="w-10 h-10 rounded-full object-cover">
                    </td>
                    <td class="p-4 font-semibold" style="color: var(--text-dark);">${app.name}</td>
                    <td class="p-4" style="color: var(--text-dark);">${app.age}</td>
                    <td class="p-4" style="color: var(--text-dark);">${app.category}</td>
                    <td class="p-4">
                        <span class="badge status-${app.status}">${getStatusText(app.status)}</span>
                    </td>
                    <td class="p-4" style="color: var(--text-dark);">${formatDate(app.submitDate)}</td>
                    <td class="p-4">
                        <div class="flex items-center">
                            <i class="fas fa-${app.source === 'mobile' ? 'mobile-alt' : 'desktop'} ml-1" style="color: var(--text-light);"></i>
                            <span style="color: var(--text-dark);">${app.source === 'mobile' ? 'جوال' : 'كمبيوتر'}</span>
                        </div>
                    </td>
                    <td class="p-4">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold text-white"
                                 style="background: ${app.aiScore >= 80 ? '#10b981' : app.aiScore >= 60 ? '#f59e0b' : '#ef4444'}">
                                ${app.aiScore}
                            </div>
                        </div>
                    </td>
                    <td class="p-4">
                        <div class="flex space-x-1 space-x-reverse">
                            <button onclick="event.stopPropagation(); openEditApplicationModal(${app.id})"
                                    class="p-2 bg-blue-600 text-white rounded hover:bg-blue-700 tooltip" title="تحرير">
                                <i class="fas fa-edit text-xs"></i>
                            </button>
                            <button onclick="event.stopPropagation(); deleteApplication(${app.id})"
                                    class="p-2 bg-red-600 text-white rounded hover:bg-red-700 tooltip" title="حذف">
                                <i class="fas fa-trash text-xs"></i>
                            </button>
                            <button onclick="event.stopPropagation(); approveApplication(${app.id})"
                                    class="p-2 bg-green-600 text-white rounded hover:bg-green-700 tooltip" title="قبول">
                                <i class="fas fa-check text-xs"></i>
                            </button>
                            <button onclick="event.stopPropagation(); rejectApplication(${app.id})"
                                    class="p-2 bg-orange-600 text-white rounded hover:bg-orange-700 tooltip" title="رفض">
                                <i class="fas fa-times text-xs"></i>
                            </button>
                            <button onclick="event.stopPropagation(); generateCertificate(${app.id})"
                                    class="p-2 bg-purple-600 text-white rounded hover:bg-purple-700 tooltip" title="إصدار سند">
                                <i class="fas fa-certificate text-xs"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');

            updatePagination();
        }

        function renderPlayersGrid() {
            const playersGrid = document.getElementById('players-grid');
            if (!playersGrid) return;

            playersGrid.innerHTML = players.map(player => `
                <div class="player-card p-6 relative group">
                    <!-- Quick Actions Overlay -->
                    <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="flex space-x-1 space-x-reverse">
                            <button onclick="event.stopPropagation(); openEditPlayerModal(${player.id})"
                                    class="p-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-all shadow-lg" title="تحرير">
                                <i class="fas fa-edit text-xs"></i>
                            </button>
                            <button onclick="event.stopPropagation(); deletePlayer(${player.id})"
                                    class="p-2 bg-red-600 text-white rounded-full hover:bg-red-700 transition-all shadow-lg" title="حذف">
                                <i class="fas fa-trash text-xs"></i>
                            </button>
                        </div>
                    </div>

                    <div class="cursor-pointer" onclick="viewPlayerDetails(${player.id})">
                        <div class="text-center mb-4">
                            <div class="relative inline-block">
                                <img src="${player.photo}" alt="${player.name}" class="w-20 h-20 rounded-full mx-auto mb-3 object-cover border-4 border-white shadow-lg">
                                <div class="absolute -bottom-1 -right-1 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                                    player.status === 'active' ? 'bg-green-500' :
                                    player.status === 'inactive' ? 'bg-gray-500' :
                                    'bg-red-500'
                                }">
                                    <i class="fas fa-${
                                        player.status === 'active' ? 'check' :
                                        player.status === 'inactive' ? 'pause' :
                                        'times'
                                    }"></i>
                                </div>
                            </div>
                            <h3 class="font-bold text-lg" style="color: var(--text-dark);">${player.name}</h3>
                            <p style="color: var(--text-light);">${player.category} • ${player.age} سنة</p>
                        </div>

                        <div class="space-y-3 mb-4">
                            <!-- Performance Indicator -->
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm" style="color: var(--text-light);">الأداء</span>
                                    <span class="font-semibold text-sm" style="color: var(--text-dark);">${player.performance}%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="progress-bar rounded-full h-2 transition-all duration-500" style="width: ${player.performance}%"></div>
                                </div>
                            </div>

                            <!-- Attendance Indicator -->
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm" style="color: var(--text-light);">الحضور</span>
                                    <span class="font-semibold text-sm" style="color: var(--text-dark);">${player.attendance}%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-500 rounded-full h-2 transition-all duration-500" style="width: ${player.attendance}%"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Training Plan Badge -->
                        <div class="mb-3">
                            <span class="inline-block px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">
                                ${player.trainingPlan}
                            </span>
                        </div>

                        <div class="flex justify-between items-center">
                            <span class="badge ${player.status === 'active' ? 'status-active' : player.status === 'inactive' ? 'status-pending' : 'status-rejected'}">
                                ${getPlayerStatusText(player.status)}
                            </span>
                            <span class="text-xs" style="color: var(--text-light);">
                                آخر تدريب: ${formatDate(player.lastTraining)}
                            </span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function renderTrainingPlans() {
            const plansList = document.getElementById('training-plans-list');
            if (!plansList) return;

            plansList.innerHTML = trainingPlans.map(plan => `
                <div class="card p-6 cursor-pointer" onclick="editTrainingPlan(${plan.id})">
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <h3 class="font-bold text-lg" style="color: var(--text-dark);">${plan.name}</h3>
                            <p style="color: var(--text-light);">${plan.category} • ${plan.duration}</p>
                        </div>
                        <span class="badge ${plan.status === 'active' ? 'status-active' : 'status-rejected'}">
                            ${plan.status === 'active' ? 'نشط' : 'غير نشط'}
                        </span>
                    </div>

                    <p class="text-sm mb-4" style="color: var(--text-dark);">${plan.description}</p>

                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div>
                            <div class="font-bold text-lg" style="color: var(--primary-color);">${plan.sessions}</div>
                            <div class="text-xs" style="color: var(--text-light);">جلسة</div>
                        </div>
                        <div>
                            <div class="font-bold text-lg" style="color: var(--primary-color);">${plan.price} ر.س</div>
                            <div class="text-xs" style="color: var(--text-light);">السعر</div>
                        </div>
                        <div>
                            <div class="font-bold text-lg" style="color: var(--primary-color);">
                                ${applications.filter(app => app.trainingPlan === plan.name).length}
                            </div>
                            <div class="text-xs" style="color: var(--text-light);">مشترك</div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function renderNotificationsHistory() {
            const historyContainer = document.getElementById('notifications-history');
            if (!historyContainer) return;

            historyContainer.innerHTML = notifications.map(notif => `
                <div class="notification-item ${!notif.read ? 'unread' : ''}" onclick="markAsRead(${notif.id})">
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="w-8 h-8 rounded-full flex items-center justify-center ${
                            notif.type === 'success' ? 'bg-green-100 text-green-600' :
                            notif.type === 'warning' ? 'bg-yellow-100 text-yellow-600' :
                            notif.type === 'error' ? 'bg-red-100 text-red-600' :
                            'bg-blue-100 text-blue-600'
                        }">
                            <i class="fas fa-${
                                notif.type === 'success' ? 'check' :
                                notif.type === 'warning' ? 'exclamation-triangle' :
                                notif.type === 'error' ? 'times' :
                                'info'
                            } text-xs"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-sm" style="color: var(--text-dark);">${notif.title}</h4>
                            <p class="text-xs mt-1" style="color: var(--text-light);">${notif.message}</p>
                            <p class="text-xs mt-1" style="color: var(--text-light);">${formatTimeAgo(notif.time)}</p>
                        </div>
                        ${!notif.read ? '<div class="w-2 h-2 bg-blue-500 rounded-full"></div>' : ''}
                    </div>
                </div>
            `).join('');
        }

        // Statistics Functions
        function getApplicationStats() {
            return {
                pending: applications.filter(app => app.status === 'pending').length,
                reviewing: applications.filter(app => app.status === 'reviewing').length,
                approved: applications.filter(app => app.status === 'approved').length,
                rejected: applications.filter(app => app.status === 'rejected').length,
                total: applications.length
            };
        }

        function getCategoryStats() {
            const stats = { براعم: 0, ناشئين: 0, شباب: 0 };
            applications.forEach(app => {
                if (stats.hasOwnProperty(app.category)) {
                    stats[app.category]++;
                }
            });
            return stats;
        }

        function getSourceStats() {
            return {
                mobile: applications.filter(app => app.source === 'mobile').length,
                desktop: applications.filter(app => app.source === 'desktop').length
            };
        }

        function renderAnalytics() {
            const stats = getApplicationStats();

            // Update KPI cards
            const totalElement = document.getElementById('analytics-total-applications');
            const acceptanceElement = document.getElementById('analytics-acceptance-rate');
            const revenueElement = document.getElementById('analytics-revenue');
            const reviewTimeElement = document.getElementById('analytics-review-time');

            if (totalElement) totalElement.textContent = stats.total;
            if (acceptanceElement) acceptanceElement.textContent = Math.round((stats.approved / stats.total) * 100) + '%';
            if (revenueElement) revenueElement.textContent = (stats.approved * 750).toLocaleString('ar-SA') + ' ر.س';
            if (reviewTimeElement) reviewTimeElement.textContent = '2.5 يوم';

            // Update growth indicators
            const growthElement = document.getElementById('applications-growth');
            const revenueGrowthElement = document.getElementById('revenue-growth');

            if (growthElement) growthElement.textContent = '+15%';
            if (revenueGrowthElement) revenueGrowthElement.textContent = '+23%';

            // Update detailed analytics table
            const detailedTable = document.getElementById('analytics-detailed-table');
            if (detailedTable) {
                detailedTable.innerHTML = `
                    <tr class="table-row">
                        <td class="p-4" style="color: var(--text-dark);">هذا الأسبوع</td>
                        <td class="p-4" style="color: var(--text-dark);">${Math.floor(stats.total * 0.3)}</td>
                        <td class="p-4" style="color: var(--text-dark);">${Math.floor(stats.approved * 0.4)}</td>
                        <td class="p-4" style="color: var(--text-dark);">${Math.floor(stats.rejected * 0.2)}</td>
                        <td class="p-4" style="color: var(--text-dark);">85%</td>
                        <td class="p-4" style="color: var(--text-dark);">2.1 يوم</td>
                    </tr>
                    <tr class="table-row">
                        <td class="p-4" style="color: var(--text-dark);">الأسبوع الماضي</td>
                        <td class="p-4" style="color: var(--text-dark);">${Math.floor(stats.total * 0.25)}</td>
                        <td class="p-4" style="color: var(--text-dark);">${Math.floor(stats.approved * 0.3)}</td>
                        <td class="p-4" style="color: var(--text-dark);">${Math.floor(stats.rejected * 0.15)}</td>
                        <td class="p-4" style="color: var(--text-dark);">82%</td>
                        <td class="p-4" style="color: var(--text-dark);">2.8 يوم</td>
                    </tr>
                    <tr class="table-row">
                        <td class="p-4" style="color: var(--text-dark);">هذا الشهر</td>
                        <td class="p-4" style="color: var(--text-dark);">${stats.total}</td>
                        <td class="p-4" style="color: var(--text-dark);">${stats.approved}</td>
                        <td class="p-4" style="color: var(--text-dark);">${stats.rejected}</td>
                        <td class="p-4" style="color: var(--text-dark);">${Math.round((stats.approved / stats.total) * 100)}%</td>
                        <td class="p-4" style="color: var(--text-dark);">2.5 يوم</td>
                    </tr>
                `;
            }
        }

        function updateAnalytics(period) {
            console.log(`📊 تحديث التحليلات للفترة: ${period}`);

            // Simulate different data for different periods
            let multiplier = 1;
            switch(period) {
                case 'today': multiplier = 0.1; break;
                case 'week': multiplier = 0.3; break;
                case 'month': multiplier = 1; break;
                case 'year': multiplier = 12; break;
            }

            // Update charts with new data
            if (charts.trend) {
                charts.trend.data.datasets[0].data = [
                    Math.floor(12 * multiplier), Math.floor(19 * multiplier),
                    Math.floor(15 * multiplier), Math.floor(25 * multiplier),
                    Math.floor(22 * multiplier), Math.floor(30 * multiplier)
                ];
                charts.trend.update();
            }

            renderAnalytics();
            showNotification(`تم تحديث التحليلات للفترة: ${period}`, 'success');
        }

        // Pagination Functions
        function updatePagination() {
            const totalPages = Math.ceil(applications.length / itemsPerPage);
            const paginationControls = document.getElementById('pagination-controls');

            if (!paginationControls) return;

            let paginationHTML = '';

            // Previous button
            paginationHTML += `
                <button class="pagination-btn ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''}"
                        onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;

            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                if (i === currentPage || i === 1 || i === totalPages || (i >= currentPage - 1 && i <= currentPage + 1)) {
                    paginationHTML += `
                        <button class="pagination-btn ${i === currentPage ? 'active' : ''}"
                                onclick="changePage(${i})">${i}</button>
                    `;
                } else if (i === currentPage - 2 || i === currentPage + 2) {
                    paginationHTML += '<span class="px-2">...</span>';
                }
            }

            // Next button
            paginationHTML += `
                <button class="pagination-btn ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''}"
                        onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;

            paginationControls.innerHTML = paginationHTML;

            // Update showing info
            const showingFrom = document.getElementById('showing-from');
            const showingTo = document.getElementById('showing-to');
            const totalApplications = document.getElementById('total-applications');

            if (showingFrom) showingFrom.textContent = ((currentPage - 1) * itemsPerPage) + 1;
            if (showingTo) showingTo.textContent = Math.min(currentPage * itemsPerPage, applications.length);
            if (totalApplications) totalApplications.textContent = applications.length;
        }

        function changePage(page) {
            const totalPages = Math.ceil(applications.length / itemsPerPage);
            if (page < 1 || page > totalPages) return;

            currentPage = page;
            renderApplicationsTable();
        }

        // Placeholder functions for missing functionality
        function toggleSelectAll() {
            const selectAll = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('.table-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        function exportApplications() {
            showNotification('جاري تصدير البيانات...', 'info');
            setTimeout(() => {
                showNotification('تم تصدير البيانات بنجاح', 'success');
            }, 2000);
        }

        function bulkActions() {
            const selectedCheckboxes = document.querySelectorAll('.table-checkbox:checked');
            if (selectedCheckboxes.length === 0) {
                showNotification('يرجى اختيار طلب واحد على الأقل', 'warning');
                return;
            }
            showNotification(`تم اختيار ${selectedCheckboxes.length} طلب للإجراءات المجمعة`, 'info');
        }

        function addNewPlayer() {
            showNotification('فتح نموذج إضافة لاعب جديد...', 'info');
        }

        function exportPlayers() {
            showNotification('جاري تصدير قائمة اللاعبين...', 'info');
        }

        function filterPlayers() {
            showNotification('تطبيق فلتر اللاعبين...', 'info');
        }

        function viewPlayerDetails(id) {
            showNotification(`عرض تفاصيل اللاعب رقم ${id}`, 'info');
        }

        function createTrainingPlan() {
            showNotification('إنشاء خطة تدريب جديدة...', 'info');
        }

        function importTrainingPlan() {
            showNotification('استيراد خطة تدريب...', 'info');
        }

        function editTrainingPlan(id) {
            showNotification(`تحرير خطة التدريب رقم ${id}`, 'info');
        }

        function renderSettings() {
            const usersTable = document.getElementById('users-table');
            if (usersTable) {
                usersTable.innerHTML = users.map(user => `
                    <tr class="table-row">
                        <td class="p-4" style="color: var(--text-dark);">${user.name}</td>
                        <td class="p-4" style="color: var(--text-dark);">${user.email}</td>
                        <td class="p-4" style="color: var(--text-dark);">${user.role}</td>
                        <td class="p-4" style="color: var(--text-dark);">${user.lastLogin}</td>
                        <td class="p-4">
                            <span class="badge ${user.status === 'active' ? 'status-active' : 'status-rejected'}">
                                ${user.status === 'active' ? 'نشط' : 'غير نشط'}
                            </span>
                        </td>
                        <td class="p-4">
                            <div class="flex space-x-1 space-x-reverse">
                                <button class="p-2 bg-blue-600 text-white rounded hover:bg-blue-700" title="تحرير">
                                    <i class="fas fa-edit text-xs"></i>
                                </button>
                                <button class="p-2 bg-red-600 text-white rounded hover:bg-red-700" title="حذف">
                                    <i class="fas fa-trash text-xs"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `).join('');
            }
        }

        function saveAllSettings() {
            showNotification('جاري حفظ جميع الإعدادات...', 'info');
            setTimeout(() => {
                showNotification('تم حفظ الإعدادات بنجاح', 'success');
            }, 1500);
        }

        function resetSettings() {
            Swal.fire({
                title: 'تأكيد إعادة التعيين',
                text: 'هل أنت متأكد من إعادة تعيين جميع الإعدادات؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'نعم، إعادة تعيين',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    showNotification('تم إعادة تعيين الإعدادات', 'success');
                }
            });
        }

        function testDatabaseConnection() {
            showNotification('جاري اختبار الاتصال...', 'info');
            setTimeout(() => {
                showNotification('الاتصال بقاعدة البيانات ناجح', 'success');
            }, 2000);
        }

        function addNewUser() {
            showNotification('إضافة مستخدم جديد...', 'info');
        }

        function exportUsers() {
            showNotification('تصدير قائمة المستخدمين...', 'info');
        }

        function exportAnalytics() {
            showNotification('تصدير تقرير التحليلات...', 'info');
        }

        function renderCertificates() {
            const recentCertificates = document.getElementById('recent-certificates');
            if (recentCertificates) {
                recentCertificates.innerHTML = certificates.map(cert => `
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div>
                            <div class="font-semibold text-sm" style="color: var(--text-dark);">${cert.playerName}</div>
                            <div class="text-xs" style="color: var(--text-light);">${cert.certificateNumber}</div>
                        </div>
                        <div class="text-xs" style="color: var(--text-light);">${formatDate(cert.issueDate)}</div>
                    </div>
                `).join('');
            }
        }

        function bulkGenerateCertificates() {
            showNotification('إنتاج مجمع للسندات...', 'info');
        }

        function editCertificateTemplate() {
            showNotification('تحرير قالب السند...', 'info');
        }

        function updateCertificateTemplate() {
            showNotification('تم تحديث قالب السند', 'success');
        }

        function createNotification() {
            showNotification('إنشاء إشعار جديد...', 'info');
        }

        function sendBulkNotification() {
            showNotification('إرسال إشعار جماعي...', 'info');
        }

        function filterNotifications(type) {
            showNotification(`فلترة الإشعارات: ${type}`, 'info');
        }

        function useTemplate(template) {
            showNotification(`استخدام قالب: ${template}`, 'info');
        }

        // ==================== CRUD Operations for Players ====================

        // Create New Player
        function createPlayer(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const playerData = {
                id: players.length + 1,
                name: formData.get('name'),
                age: parseInt(formData.get('age')),
                category: formData.get('category'),
                phone: formData.get('phone'),
                email: formData.get('email'),
                trainingPlan: formData.get('trainingPlan'),
                status: 'active',
                joinDate: formData.get('joinDate') || new Date().toISOString().split('T')[0],
                photo: 'https://via.placeholder.com/150x150/8B4513/FFFFFF?text=' + encodeURIComponent(formData.get('name').split(' ')[0]),
                performance: 75, // Initial performance score
                attendance: 85,  // Initial attendance rate
                lastTraining: new Date().toISOString().split('T')[0],
                goals: '',
                achievements: 'حديث الانضمام',
                notes: 'لاعب جديد - يحتاج تقييم أولي'
            };

            // Validate data
            if (!validatePlayerData(playerData)) {
                return;
            }

            try {
                players.unshift(playerData);

                // Update UI
                renderPlayersGrid();
                updatePlayerStatistics();

                // Close modal and show success
                closeModal('add-player-modal');
                document.getElementById('add-player-form').reset();
                document.getElementById('add-player-photo-preview').innerHTML = '';

                // Add notification
                notifications.unshift({
                    id: Date.now(),
                    title: 'لاعب جديد',
                    message: `تم إضافة اللاعب ${playerData.name} بنجاح`,
                    time: new Date().toISOString(),
                    read: false,
                    type: 'success'
                });

                updateNotificationBadge();

                Swal.fire({
                    title: 'تم بنجاح!',
                    text: `تم إضافة اللاعب ${playerData.name} بنجاح`,
                    icon: 'success',
                    timer: 3000,
                    showConfirmButton: false
                });

                console.log('✅ تم إنشاء لاعب جديد:', playerData);

            } catch (error) {
                console.error('❌ خطأ في إنشاء اللاعب:', error);
                showNotification('حدث خطأ أثناء إضافة اللاعب', 'error');
            }
        }

        // Update Existing Player
        function updatePlayer(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const playerId = parseInt(formData.get('id'));
            const playerIndex = players.findIndex(player => player.id === playerId);

            if (playerIndex === -1) {
                showNotification('لم يتم العثور على اللاعب', 'error');
                return;
            }

            const updatedData = {
                ...players[playerIndex],
                name: formData.get('name'),
                age: parseInt(formData.get('age')),
                category: formData.get('category'),
                phone: formData.get('phone'),
                email: formData.get('email'),
                trainingPlan: formData.get('trainingPlan'),
                status: formData.get('status'),
                joinDate: formData.get('joinDate'),
                lastTraining: formData.get('lastTraining'),
                performance: parseInt(formData.get('performance')),
                attendance: parseInt(formData.get('attendance')),
                goals: formData.get('goals'),
                achievements: formData.get('achievements'),
                notes: formData.get('notes')
            };

            // Validate data
            if (!validatePlayerData(updatedData)) {
                return;
            }

            try {
                // Update in array
                players[playerIndex] = updatedData;

                // Update UI
                renderPlayersGrid();
                updatePlayerStatistics();

                // Close modal and show success
                closeModal('edit-player-modal');

                Swal.fire({
                    title: 'تم التحديث!',
                    text: `تم تحديث بيانات ${updatedData.name} بنجاح`,
                    icon: 'success',
                    timer: 3000,
                    showConfirmButton: false
                });

                console.log('✅ تم تحديث اللاعب:', updatedData);

            } catch (error) {
                console.error('❌ خطأ في تحديث اللاعب:', error);
                showNotification('حدث خطأ أثناء تحديث اللاعب', 'error');
            }
        }

        // Delete Player
        function deletePlayer(id) {
            const player = players.find(p => p.id === id);
            if (!player) {
                showNotification('لم يتم العثور على اللاعب', 'error');
                return;
            }

            Swal.fire({
                title: 'تأكيد الحذف',
                text: `هل أنت متأكد من حذف اللاعب ${player.name}؟ سيتم حذف جميع بياناته وسجلاته.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    try {
                        // Remove from array
                        const index = players.findIndex(p => p.id === id);
                        players.splice(index, 1);

                        // Update UI
                        renderPlayersGrid();
                        updatePlayerStatistics();

                        Swal.fire({
                            title: 'تم الحذف!',
                            text: 'تم حذف اللاعب بنجاح',
                            icon: 'success',
                            timer: 3000,
                            showConfirmButton: false
                        });

                        console.log('✅ تم حذف اللاعب:', id);

                    } catch (error) {
                        console.error('❌ خطأ في حذف اللاعب:', error);
                        showNotification('حدث خطأ أثناء حذف اللاعب', 'error');
                    }
                }
            });
        }

        // View Player Details
        function viewPlayerDetails(id) {
            const player = players.find(p => p.id === id);
            if (!player) {
                showNotification('لم يتم العثور على اللاعب', 'error');
                return;
            }

            // Generate detailed profile content
            document.getElementById('player-profile-content').innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Player Photo and Basic Info -->
                    <div class="lg:col-span-1">
                        <div class="card p-6 text-center">
                            <img src="${player.photo}" alt="${player.name}" class="w-32 h-32 rounded-full mx-auto mb-4 object-cover">
                            <h3 class="text-2xl font-bold mb-2" style="color: var(--text-dark);">${player.name}</h3>
                            <p class="text-lg mb-4" style="color: var(--text-light);">${player.category} • ${player.age} سنة</p>

                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span style="color: var(--text-light);">الحالة:</span>
                                    <span class="badge ${player.status === 'active' ? 'status-active' : 'status-rejected'}">
                                        ${getPlayerStatusText(player.status)}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span style="color: var(--text-light);">تاريخ الانضمام:</span>
                                    <span style="color: var(--text-dark);">${formatDate(player.joinDate)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span style="color: var(--text-light);">آخر تدريب:</span>
                                    <span style="color: var(--text-dark);">${formatDate(player.lastTraining)}</span>
                                </div>
                            </div>

                            <div class="mt-6 space-y-2">
                                <button onclick="openEditPlayerModal(${player.id})"
                                        class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all">
                                    <i class="fas fa-edit ml-2"></i>
                                    تحرير البيانات
                                </button>
                                <button onclick="deletePlayer(${player.id})"
                                        class="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-all">
                                    <i class="fas fa-trash ml-2"></i>
                                    حذف اللاعب
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Performance and Details -->
                    <div class="lg:col-span-2 space-y-6">
                        <!-- Contact Information -->
                        <div class="card p-6">
                            <h4 class="text-lg font-bold mb-4" style="color: var(--text-dark);">معلومات الاتصال</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="flex items-center">
                                    <i class="fas fa-phone text-blue-600 ml-3"></i>
                                    <div>
                                        <div class="text-sm" style="color: var(--text-light);">رقم الهاتف</div>
                                        <div style="color: var(--text-dark);">${player.phone}</div>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-envelope text-green-600 ml-3"></i>
                                    <div>
                                        <div class="text-sm" style="color: var(--text-light);">البريد الإلكتروني</div>
                                        <div style="color: var(--text-dark);">${player.email}</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Performance Metrics -->
                        <div class="card p-6">
                            <h4 class="text-lg font-bold mb-4" style="color: var(--text-dark);">مؤشرات الأداء</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <div class="flex justify-between mb-2">
                                        <span style="color: var(--text-light);">الأداء العام</span>
                                        <span class="font-bold" style="color: var(--text-dark);">${player.performance}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-3">
                                        <div class="progress-bar rounded-full h-3" style="width: ${player.performance}%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between mb-2">
                                        <span style="color: var(--text-light);">نسبة الحضور</span>
                                        <span class="font-bold" style="color: var(--text-dark);">${player.attendance}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-3">
                                        <div class="bg-blue-500 rounded-full h-3" style="width: ${player.attendance}%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Training Information -->
                        <div class="card p-6">
                            <h4 class="text-lg font-bold mb-4" style="color: var(--text-dark);">معلومات التدريب</h4>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-dumbbell text-purple-600 ml-2"></i>
                                    <span class="font-semibold" style="color: var(--text-dark);">خطة التدريب الحالية</span>
                                </div>
                                <p style="color: var(--text-light);">${player.trainingPlan}</p>
                            </div>
                        </div>

                        <!-- Goals and Achievements -->
                        <div class="card p-6">
                            <h4 class="text-lg font-bold mb-4" style="color: var(--text-dark);">الأهداف والإنجازات</h4>
                            <div class="space-y-4">
                                <div>
                                    <h5 class="font-semibold mb-2" style="color: var(--text-dark);">الأهداف الحالية</h5>
                                    <p class="text-sm p-3 bg-blue-50 rounded-lg" style="color: var(--text-dark);">
                                        ${player.goals || 'لم يتم تحديد أهداف بعد'}
                                    </p>
                                </div>
                                <div>
                                    <h5 class="font-semibold mb-2" style="color: var(--text-dark);">الإنجازات</h5>
                                    <p class="text-sm p-3 bg-green-50 rounded-lg" style="color: var(--text-dark);">
                                        ${player.achievements || 'لا توجد إنجازات مسجلة'}
                                    </p>
                                </div>
                                <div>
                                    <h5 class="font-semibold mb-2" style="color: var(--text-dark);">ملاحظات المدرب</h5>
                                    <p class="text-sm p-3 bg-yellow-50 rounded-lg" style="color: var(--text-dark);">
                                        ${player.notes || 'لا توجد ملاحظات'}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Show modal
            document.getElementById('player-profile-modal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // ==================== CRUD Operations for Applications ====================

        // Create New Application
        function createApplication(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const applicationData = {
                id: applications.length + 1,
                name: formData.get('name'),
                age: parseInt(formData.get('age')),
                category: formData.get('category'),
                phone: formData.get('phone'),
                email: formData.get('email'),
                trainingPlan: formData.get('trainingPlan'),
                source: formData.get('source'),
                medicalCertificate: formData.get('medicalCertificate') === 'on',
                parentConsent: formData.get('parentConsent') === 'on',
                status: 'pending',
                submitDate: new Date().toISOString().split('T')[0],
                photo: 'https://via.placeholder.com/100x100/8B4513/FFFFFF?text=' + encodeURIComponent(formData.get('name').split(' ')[0]),
                aiScore: generateAIScore(applicationData),
                aiRecommendation: generateAIRecommendation(applicationData)
            };

            // Validate data
            if (!validateApplicationData(applicationData)) {
                return;
            }

            // Simulate database save
            try {
                applications.unshift(applicationData);

                // Update UI
                renderApplications();
                renderApplicationsTable();
                updateStatistics();

                // Close modal and show success
                closeModal('add-application-modal');
                document.getElementById('add-application-form').reset();

                // Add notification
                notifications.unshift({
                    id: Date.now(),
                    title: 'طلب جديد',
                    message: `تم إضافة طلب انضمام جديد للمتقدم ${applicationData.name}`,
                    time: new Date().toISOString(),
                    read: false,
                    type: 'success'
                });

                updateNotificationBadge();

                Swal.fire({
                    title: 'تم بنجاح!',
                    text: `تم إضافة طلب ${applicationData.name} بنجاح`,
                    icon: 'success',
                    timer: 3000,
                    showConfirmButton: false
                });

                console.log('✅ تم إنشاء طلب جديد:', applicationData);

            } catch (error) {
                console.error('❌ خطأ في إنشاء الطلب:', error);
                showNotification('حدث خطأ أثناء إضافة الطلب', 'error');
            }
        }

        // Update Existing Application
        function updateApplication(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const applicationId = parseInt(formData.get('id'));
            const applicationIndex = applications.findIndex(app => app.id === applicationId);

            if (applicationIndex === -1) {
                showNotification('لم يتم العثور على الطلب', 'error');
                return;
            }

            const updatedData = {
                ...applications[applicationIndex],
                name: formData.get('name'),
                age: parseInt(formData.get('age')),
                category: formData.get('category'),
                phone: formData.get('phone'),
                email: formData.get('email'),
                trainingPlan: formData.get('trainingPlan'),
                source: formData.get('source'),
                status: formData.get('status'),
                medicalCertificate: formData.get('medicalCertificate') === 'on',
                parentConsent: formData.get('parentConsent') === 'on',
                aiScore: parseInt(formData.get('aiScore')),
                aiRecommendation: formData.get('aiRecommendation')
            };

            // Validate data
            if (!validateApplicationData(updatedData)) {
                return;
            }

            try {
                // Update in array
                applications[applicationIndex] = updatedData;

                // Update UI
                renderApplications();
                renderApplicationsTable();
                updateStatistics();

                // Close modal and show success
                closeModal('edit-application-modal');

                Swal.fire({
                    title: 'تم التحديث!',
                    text: `تم تحديث بيانات ${updatedData.name} بنجاح`,
                    icon: 'success',
                    timer: 3000,
                    showConfirmButton: false
                });

                console.log('✅ تم تحديث الطلب:', updatedData);

            } catch (error) {
                console.error('❌ خطأ في تحديث الطلب:', error);
                showNotification('حدث خطأ أثناء تحديث الطلب', 'error');
            }
        }

        // Delete Application
        function deleteApplication(id) {
            const app = applications.find(a => a.id === id);
            if (!app) {
                showNotification('لم يتم العثور على الطلب', 'error');
                return;
            }

            Swal.fire({
                title: 'تأكيد الحذف',
                text: `هل أنت متأكد من حذف طلب ${app.name}؟ لا يمكن التراجع عن هذا الإجراء.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    try {
                        // Remove from array
                        const index = applications.findIndex(a => a.id === id);
                        applications.splice(index, 1);

                        // Update UI
                        renderApplications();
                        renderApplicationsTable();
                        updateStatistics();

                        Swal.fire({
                            title: 'تم الحذف!',
                            text: 'تم حذف الطلب بنجاح',
                            icon: 'success',
                            timer: 3000,
                            showConfirmButton: false
                        });

                        console.log('✅ تم حذف الطلب:', id);

                    } catch (error) {
                        console.error('❌ خطأ في حذف الطلب:', error);
                        showNotification('حدث خطأ أثناء حذف الطلب', 'error');
                    }
                }
            });
        }

        // Validation Functions
        function validateApplicationData(data) {
            const errors = {};

            // Name validation
            if (!data.name || data.name.trim().length < 3) {
                errors.name = 'الاسم يجب أن يكون 3 أحرف على الأقل';
            }

            // Age validation
            if (!data.age || data.age < 12 || data.age > 25) {
                errors.age = 'العمر يجب أن يكون بين 12 و 25 سنة';
            }

            // Category validation
            if (!data.category) {
                errors.category = 'يجب اختيار الفئة';
            }

            // Phone validation
            const phoneRegex = /^05[0-9]{8}$/;
            if (!data.phone || !phoneRegex.test(data.phone)) {
                errors.phone = 'رقم الهاتف يجب أن يبدأ بـ 05 ويتكون من 10 أرقام';
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!data.email || !emailRegex.test(data.email)) {
                errors.email = 'البريد الإلكتروني غير صحيح';
            }

            // Training plan validation
            if (!data.trainingPlan) {
                errors.trainingPlan = 'يجب اختيار خطة التدريب';
            }

            // Show errors if any
            if (Object.keys(errors).length > 0) {
                Object.keys(errors).forEach(field => {
                    const errorElement = document.getElementById(field + '-error');
                    if (errorElement) {
                        errorElement.textContent = errors[field];
                        errorElement.classList.remove('hidden');
                    }
                });

                showNotification('يرجى تصحيح الأخطاء المذكورة', 'error');
                return false;
            }

            // Clear any existing errors
            document.querySelectorAll('.error-message').forEach(el => {
                el.classList.add('hidden');
                el.textContent = '';
            });

            return true;
        }

        // AI Score Generation
        function generateAIScore(data) {
            let score = 50; // Base score

            // Age factor
            if (data.age >= 12 && data.age <= 16) score += 15;
            else if (data.age >= 17 && data.age <= 20) score += 10;
            else score += 5;

            // Documents factor
            if (data.medicalCertificate) score += 15;
            if (data.parentConsent) score += 10;

            // Email domain factor
            if (data.email && (data.email.includes('gmail') || data.email.includes('outlook'))) {
                score += 5;
            }

            // Phone validation factor
            if (data.phone && data.phone.startsWith('05')) score += 5;

            // Random factor for realism
            score += Math.floor(Math.random() * 10) - 5;

            return Math.max(0, Math.min(100, score));
        }

        function generateAIRecommendation(data) {
            const score = data.aiScore || generateAIScore(data);

            if (score >= 85) {
                return 'مرشح ممتاز - يُنصح بالقبول الفوري. يظهر التزاماً عالياً ومؤهلات ممتازة.';
            } else if (score >= 70) {
                return 'مرشح جيد - مؤهل للقبول. يحتاج متابعة أولية لضمان الالتزام.';
            } else if (score >= 55) {
                return 'مرشح متوسط - يحتاج مراجعة إضافية. قد يحتاج دعم إضافي في البداية.';
            } else {
                return 'مرشح ضعيف - يُنصح بالمراجعة الدقيقة أو طلب مستندات إضافية.';
            }
        }

        // Bulk Operations
        function executeBulkAction() {
            const selectedCheckboxes = document.querySelectorAll('.table-checkbox:checked');
            const actionType = document.getElementById('bulk-action-type').value;
            const reason = document.getElementById('bulk-reason').value;

            if (selectedCheckboxes.length === 0) {
                showNotification('يرجى اختيار طلب واحد على الأقل', 'warning');
                return;
            }

            if (!actionType) {
                showNotification('يرجى اختيار نوع الإجراء', 'warning');
                return;
            }

            const selectedIds = Array.from(selectedCheckboxes).map(cb => parseInt(cb.value));

            Swal.fire({
                title: 'تأكيد الإجراء المجمع',
                text: `هل أنت متأكد من تطبيق "${getActionText(actionType)}" على ${selectedIds.length} طلب؟`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'نعم، تنفيذ',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    processBulkAction(selectedIds, actionType, reason);
                }
            });
        }

        function processBulkAction(ids, actionType, reason) {
            try {
                let successCount = 0;

                ids.forEach(id => {
                    const appIndex = applications.findIndex(app => app.id === id);
                    if (appIndex !== -1) {
                        switch (actionType) {
                            case 'approve':
                                applications[appIndex].status = 'approved';
                                sendAutoNotification(applications[appIndex], 'approved');
                                break;
                            case 'reject':
                                applications[appIndex].status = 'rejected';
                                sendAutoNotification(applications[appIndex], 'rejected');
                                break;
                            case 'review':
                                applications[appIndex].status = 'reviewing';
                                break;
                            case 'delete':
                                applications.splice(appIndex, 1);
                                break;
                            case 'assign-plan':
                                const trainingPlan = document.getElementById('bulk-training-plan').value;
                                if (trainingPlan) {
                                    applications[appIndex].trainingPlan = trainingPlan;
                                }
                                break;
                            case 'export':
                                // Will be handled separately
                                break;
                        }
                        successCount++;
                    }
                });

                // Handle export separately
                if (actionType === 'export') {
                    exportSelectedApplications(ids);
                } else {
                    // Update UI
                    renderApplications();
                    renderApplicationsTable();
                    updateStatistics();
                }

                // Close modal and show success
                closeModal('bulk-actions-modal');

                Swal.fire({
                    title: 'تم التنفيذ!',
                    text: `تم تطبيق الإجراء على ${successCount} طلب بنجاح`,
                    icon: 'success',
                    timer: 3000,
                    showConfirmButton: false
                });

                // Clear selections
                document.getElementById('select-all').checked = false;
                document.querySelectorAll('.table-checkbox').forEach(cb => cb.checked = false);

                console.log(`✅ تم تنفيذ الإجراء المجمع: ${actionType} على ${successCount} طلب`);

            } catch (error) {
                console.error('❌ خطأ في تنفيذ الإجراء المجمع:', error);
                showNotification('حدث خطأ أثناء تنفيذ الإجراء', 'error');
            }
        }

        function getActionText(actionType) {
            const actions = {
                'approve': 'قبول',
                'reject': 'رفض',
                'review': 'تحويل للمراجعة',
                'delete': 'حذف',
                'export': 'تصدير',
                'assign-plan': 'تعيين خطة تدريب'
            };
            return actions[actionType] || actionType;
        }

        // Export and Import Functions
        function exportSelectedApplications(ids) {
            const selectedApps = applications.filter(app => ids.includes(app.id));
            exportApplicationsToCSV(selectedApps, 'selected_applications');
        }

        function exportApplicationsToCSV(data, filename = 'applications') {
            const headers = [
                'الرقم', 'الاسم', 'العمر', 'الفئة', 'الهاتف', 'البريد الإلكتروني',
                'خطة التدريب', 'الحالة', 'تاريخ التقديم', 'المصدر', 'نقاط الذكاء الاصطناعي',
                'الشهادة الطبية', 'موافقة ولي الأمر', 'توصية الذكاء الاصطناعي'
            ];

            const csvContent = [
                headers.join(','),
                ...data.map(app => [
                    app.id,
                    `"${app.name}"`,
                    app.age,
                    `"${app.category}"`,
                    app.phone,
                    app.email,
                    `"${app.trainingPlan}"`,
                    `"${getStatusText(app.status)}"`,
                    app.submitDate,
                    app.source === 'mobile' ? 'جوال' : 'كمبيوتر',
                    app.aiScore,
                    app.medicalCertificate ? 'نعم' : 'لا',
                    app.parentConsent ? 'نعم' : 'لا',
                    `"${app.aiRecommendation}"`
                ].join(','))
            ].join('\n');

            // Add BOM for Arabic support
            const BOM = '\uFEFF';
            const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showNotification('تم تصدير البيانات بنجاح', 'success');
            }
        }

        function exportApplicationsToExcel(data, filename = 'applications') {
            // Simplified Excel export using HTML table method
            const table = document.createElement('table');

            // Headers
            const headerRow = table.insertRow();
            const headers = [
                'الرقم', 'الاسم', 'العمر', 'الفئة', 'الهاتف', 'البريد الإلكتروني',
                'خطة التدريب', 'الحالة', 'تاريخ التقديم', 'المصدر', 'نقاط الذكاء الاصطناعي'
            ];

            headers.forEach(header => {
                const th = document.createElement('th');
                th.textContent = header;
                headerRow.appendChild(th);
            });

            // Data rows
            data.forEach(app => {
                const row = table.insertRow();
                const values = [
                    app.id, app.name, app.age, app.category, app.phone, app.email,
                    app.trainingPlan, getStatusText(app.status), app.submitDate,
                    app.source === 'mobile' ? 'جوال' : 'كمبيوتر', app.aiScore
                ];

                values.forEach(value => {
                    const td = document.createElement('td');
                    td.textContent = value;
                    row.appendChild(td);
                });
            });

            // Export
            const html = table.outerHTML;
            const blob = new Blob([html], { type: 'application/vnd.ms-excel' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${filename}_${new Date().toISOString().split('T')[0]}.xls`;
            link.click();

            showNotification('تم تصدير البيانات إلى Excel بنجاح', 'success');
        }

        // Helper Functions for Modals
        function openAddApplicationModal() {
            document.getElementById('add-application-modal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            // Reset form
            document.getElementById('add-application-form').reset();
            document.getElementById('add-photo-preview').innerHTML = '';

            // Focus on first input
            setTimeout(() => {
                document.getElementById('add-name').focus();
            }, 100);
        }

        function openEditApplicationModal(id) {
            const app = applications.find(a => a.id === id);
            if (!app) {
                showNotification('لم يتم العثور على الطلب', 'error');
                return;
            }

            // Populate form
            document.getElementById('edit-application-id').value = app.id;
            document.getElementById('edit-name').value = app.name;
            document.getElementById('edit-age').value = app.age;
            document.getElementById('edit-category').value = app.category;
            document.getElementById('edit-phone').value = app.phone;
            document.getElementById('edit-email').value = app.email;
            document.getElementById('edit-training-plan').value = app.trainingPlan;
            document.getElementById('edit-status').value = app.status;
            document.getElementById('edit-source').value = app.source;
            document.getElementById('edit-medical-certificate').checked = app.medicalCertificate;
            document.getElementById('edit-parent-consent').checked = app.parentConsent;
            document.getElementById('edit-ai-score').value = app.aiScore;
            document.getElementById('edit-ai-recommendation').value = app.aiRecommendation;

            // Update AI score display
            updateAIScoreDisplay(app.aiScore);

            // Show current photo
            document.getElementById('current-photo-preview').innerHTML = `
                <img src="${app.photo}" alt="${app.name}" class="w-20 h-20 rounded-full object-cover">
                <p class="text-xs text-gray-500 mt-1">الصورة الحالية</p>
            `;

            // Show modal
            document.getElementById('edit-application-modal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function openBulkActionsModal() {
            const selectedCheckboxes = document.querySelectorAll('.table-checkbox:checked');

            if (selectedCheckboxes.length === 0) {
                showNotification('يرجى اختيار طلب واحد على الأقل', 'warning');
                return;
            }

            document.getElementById('selected-count').textContent = selectedCheckboxes.length;
            document.getElementById('bulk-actions-modal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            // Reset form
            document.getElementById('bulk-action-type').value = '';
            document.getElementById('bulk-reason').value = '';
            document.getElementById('bulk-training-plan-section').classList.add('hidden');
            document.getElementById('bulk-reason-section').classList.add('hidden');
        }

        // Event Listeners for Forms and Modals
        document.addEventListener('DOMContentLoaded', function() {
            // Bulk Actions Event Listener
            const bulkActionType = document.getElementById('bulk-action-type');
            if (bulkActionType) {
                bulkActionType.addEventListener('change', function() {
                    const trainingPlanSection = document.getElementById('bulk-training-plan-section');
                    const reasonSection = document.getElementById('bulk-reason-section');

                    // Hide all sections first
                    trainingPlanSection.classList.add('hidden');
                    reasonSection.classList.add('hidden');

                    // Show relevant sections
                    if (this.value === 'assign-plan') {
                        trainingPlanSection.classList.remove('hidden');
                    }

                    if (['approve', 'reject', 'delete'].includes(this.value)) {
                        reasonSection.classList.remove('hidden');
                    }
                });
            }

            // Add Application Form Event Listeners
            const addForm = document.getElementById('add-application-form');
            if (addForm) {
                // Real-time AI preview updates
                ['add-name', 'add-age', 'add-category', 'add-phone', 'add-email', 'add-medical-certificate', 'add-parent-consent'].forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.addEventListener('input', updateAIPreview);
                        element.addEventListener('change', updateAIPreview);
                    }
                });

                // Age-based category suggestion
                const ageInput = document.getElementById('add-age');
                if (ageInput) {
                    ageInput.addEventListener('change', suggestCategory);
                }
            }

            // Edit Application Form Event Listeners
            const editAIScore = document.getElementById('edit-ai-score');
            if (editAIScore) {
                editAIScore.addEventListener('input', function() {
                    updateAIScoreDisplay(this.value);
                });
            }

            // Close modals on Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeAllModals();
                }
            });

            // Prevent modal close when clicking inside modal content
            document.querySelectorAll('.modal-content').forEach(content => {
                content.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            });

            // Close modal when clicking outside
            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeModal(this.id);
                    }
                });
            });

            // Player Form Event Listeners
            const addPlayerForm = document.getElementById('add-player-form');
            if (addPlayerForm) {
                // Age-based category suggestion
                const playerAgeInput = document.getElementById('add-player-age');
                if (playerAgeInput) {
                    playerAgeInput.addEventListener('change', suggestPlayerCategory);
                }
            }

            // Edit Player Form Event Listeners
            const editPlayerPerformance = document.getElementById('edit-player-performance');
            const editPlayerAttendance = document.getElementById('edit-player-attendance');

            if (editPlayerPerformance) {
                editPlayerPerformance.addEventListener('input', function() {
                    updatePerformanceDisplay(this.value);
                });
            }

            if (editPlayerAttendance) {
                editPlayerAttendance.addEventListener('input', function() {
                    updateAttendanceDisplay(this.value);
                });
            }

            // Players Search Event Listener
            const playersSearch = document.getElementById('players-search');
            if (playersSearch) {
                playersSearch.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        filterPlayers();
                    }, 300);
                });
            }

            // Category and Status Filter Event Listeners
            const categoryFilter = document.getElementById('category-filter');
            const statusFilter = document.getElementById('status-filter');

            if (categoryFilter) {
                categoryFilter.addEventListener('change', filterPlayers);
            }

            if (statusFilter) {
                statusFilter.addEventListener('change', filterPlayers);
            }
        });

        // Utility Functions
        function updateAIScoreDisplay(score) {
            const display = document.getElementById('ai-score-display');
            if (display) {
                display.textContent = score;
                display.className = `font-bold ${
                    score >= 80 ? 'text-green-600' :
                    score >= 60 ? 'text-yellow-600' :
                    'text-red-600'
                }`;
            }
        }

        function suggestCategory() {
            const age = parseInt(document.getElementById('add-age').value);
            const categorySelect = document.getElementById('add-category');

            if (age >= 12 && age <= 14) {
                categorySelect.value = 'براعم';
            } else if (age >= 15 && age <= 17) {
                categorySelect.value = 'ناشئين';
            } else if (age >= 18) {
                categorySelect.value = 'شباب';
            }

            // Trigger AI preview update
            updateAIPreview();
        }

        function updateAIPreview() {
            const name = document.getElementById('add-name').value;
            const age = document.getElementById('add-age').value;
            const category = document.getElementById('add-category').value;
            const phone = document.getElementById('add-phone').value;
            const email = document.getElementById('add-email').value;
            const medical = document.getElementById('add-medical-certificate').checked;
            const consent = document.getElementById('add-parent-consent').checked;

            if (name && age && category) {
                const mockData = { name, age: parseInt(age), category, phone, email, medicalCertificate: medical, parentConsent: consent };
                const score = generateAIScore(mockData);

                const previewBar = document.getElementById('ai-preview-bar');
                const previewScore = document.getElementById('ai-preview-score');

                if (previewBar && previewScore) {
                    previewBar.style.width = score + '%';
                    previewScore.textContent = score + '%';
                    previewScore.className = `text-center mt-1 text-sm font-bold ${
                        score >= 80 ? 'text-green-700' :
                        score >= 60 ? 'text-yellow-700' :
                        'text-red-700'
                    }`;
                }
            }
        }

        function previewPhoto(input, previewId) {
            const preview = document.getElementById(previewId);

            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    preview.innerHTML = `
                        <img src="${e.target.result}" alt="معاينة الصورة" class="w-20 h-20 rounded-full object-cover">
                        <p class="text-xs text-gray-500 mt-1">معاينة الصورة الجديدة</p>
                    `;
                };

                reader.readAsDataURL(input.files[0]);
            } else {
                preview.innerHTML = '';
            }
        }

        // ==================== Player Management Helper Functions ====================

        // Validate Player Data
        function validatePlayerData(data) {
            const errors = {};

            // Name validation
            if (!data.name || data.name.trim().length < 3) {
                errors.name = 'الاسم يجب أن يكون 3 أحرف على الأقل';
            }

            // Age validation
            if (!data.age || data.age < 12 || data.age > 25) {
                errors.age = 'العمر يجب أن يكون بين 12 و 25 سنة';
            }

            // Category validation
            if (!data.category) {
                errors.category = 'يجب اختيار الفئة';
            }

            // Phone validation
            const phoneRegex = /^05[0-9]{8}$/;
            if (!data.phone || !phoneRegex.test(data.phone)) {
                errors.phone = 'رقم الهاتف يجب أن يبدأ بـ 05 ويتكون من 10 أرقام';
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!data.email || !emailRegex.test(data.email)) {
                errors.email = 'البريد الإلكتروني غير صحيح';
            }

            // Training plan validation
            if (!data.trainingPlan) {
                errors.trainingPlan = 'يجب اختيار خطة التدريب';
            }

            // Show errors if any
            if (Object.keys(errors).length > 0) {
                Object.keys(errors).forEach(field => {
                    const errorElement = document.getElementById(field + '-error');
                    if (errorElement) {
                        errorElement.textContent = errors[field];
                        errorElement.classList.remove('hidden');
                    }
                });

                showNotification('يرجى تصحيح الأخطاء المذكورة', 'error');
                return false;
            }

            // Clear any existing errors
            document.querySelectorAll('.error-message').forEach(el => {
                el.classList.add('hidden');
                el.textContent = '';
            });

            return true;
        }

        // Player Status Helper
        function getPlayerStatusText(status) {
            const statusMap = {
                'active': 'نشط',
                'inactive': 'غير نشط',
                'suspended': 'موقوف',
                'graduated': 'متخرج'
            };
            return statusMap[status] || status;
        }

        // Open Player Modals
        function openAddPlayerModal() {
            document.getElementById('add-player-modal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            // Reset form
            document.getElementById('add-player-form').reset();
            document.getElementById('add-player-photo-preview').innerHTML = '';

            // Set default join date to today
            document.getElementById('add-player-join-date').value = new Date().toISOString().split('T')[0];

            // Focus on first input
            setTimeout(() => {
                document.getElementById('add-player-name').focus();
            }, 100);
        }

        function openEditPlayerModal(id) {
            const player = players.find(p => p.id === id);
            if (!player) {
                showNotification('لم يتم العثور على اللاعب', 'error');
                return;
            }

            // Populate form
            document.getElementById('edit-player-id').value = player.id;
            document.getElementById('edit-player-name').value = player.name;
            document.getElementById('edit-player-age').value = player.age;
            document.getElementById('edit-player-category').value = player.category;
            document.getElementById('edit-player-phone').value = player.phone;
            document.getElementById('edit-player-email').value = player.email;
            document.getElementById('edit-player-training-plan').value = player.trainingPlan;
            document.getElementById('edit-player-status').value = player.status;
            document.getElementById('edit-player-join-date').value = player.joinDate;
            document.getElementById('edit-player-last-training').value = player.lastTraining;
            document.getElementById('edit-player-performance').value = player.performance;
            document.getElementById('edit-player-attendance').value = player.attendance;
            document.getElementById('edit-player-goals').value = player.goals || '';
            document.getElementById('edit-player-achievements').value = player.achievements || '';
            document.getElementById('edit-player-notes').value = player.notes || '';

            // Update displays
            updatePerformanceDisplay(player.performance);
            updateAttendanceDisplay(player.attendance);

            // Show current photo
            document.getElementById('edit-player-photo-preview').innerHTML = `
                <img src="${player.photo}" alt="${player.name}" class="w-20 h-20 rounded-full object-cover">
                <p class="text-xs text-gray-500 mt-1">الصورة الحالية</p>
            `;

            // Show modal
            document.getElementById('edit-player-modal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // Update Display Functions
        function updatePerformanceDisplay(value) {
            const display = document.getElementById('performance-display');
            if (display) {
                display.textContent = value;
                display.className = `font-bold ${
                    value >= 80 ? 'text-green-600' :
                    value >= 60 ? 'text-yellow-600' :
                    'text-red-600'
                }`;
            }
        }

        function updateAttendanceDisplay(value) {
            const display = document.getElementById('attendance-display');
            if (display) {
                display.textContent = value + '%';
                display.className = `font-bold ${
                    value >= 80 ? 'text-green-600' :
                    value >= 60 ? 'text-yellow-600' :
                    'text-red-600'
                }`;
            }
        }

        function suggestPlayerCategory() {
            const age = parseInt(document.getElementById('add-player-age').value);
            const categorySelect = document.getElementById('add-player-category');

            if (age >= 12 && age <= 14) {
                categorySelect.value = 'براعم';
            } else if (age >= 15 && age <= 17) {
                categorySelect.value = 'ناشئين';
            } else if (age >= 18) {
                categorySelect.value = 'شباب';
            }
        }

        // Player Statistics
        function updatePlayerStatistics() {
            const activePlayersElement = document.getElementById('active-players');
            if (activePlayersElement) {
                const activeCount = players.filter(p => p.status === 'active').length;
                activePlayersElement.textContent = activeCount;
            }
        }

        // Advanced Search and Filter for Players
        function filterPlayers() {
            const searchTerm = document.getElementById('players-search').value.toLowerCase();
            const categoryFilter = document.getElementById('category-filter').value;
            const statusFilter = document.getElementById('status-filter').value;

            let filteredPlayers = players;

            // Apply search filter
            if (searchTerm) {
                filteredPlayers = filteredPlayers.filter(player =>
                    player.name.toLowerCase().includes(searchTerm) ||
                    player.email.toLowerCase().includes(searchTerm) ||
                    player.phone.includes(searchTerm)
                );
            }

            // Apply category filter
            if (categoryFilter) {
                filteredPlayers = filteredPlayers.filter(player => player.category === categoryFilter);
            }

            // Apply status filter
            if (statusFilter) {
                filteredPlayers = filteredPlayers.filter(player => player.status === statusFilter);
            }

            renderFilteredPlayersGrid(filteredPlayers);

            showNotification(`تم العثور على ${filteredPlayers.length} لاعب`, 'info');
        }

        function renderFilteredPlayersGrid(filteredPlayers) {
            const playersGrid = document.getElementById('players-grid');
            if (!playersGrid) return;

            if (filteredPlayers.length === 0) {
                playersGrid.innerHTML = `
                    <div class="col-span-full text-center py-12">
                        <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد نتائج</h3>
                        <p class="text-gray-500">لم يتم العثور على لاعبين يطابقون معايير البحث</p>
                    </div>
                `;
                return;
            }

            playersGrid.innerHTML = filteredPlayers.map(player => `
                <div class="player-card p-6 cursor-pointer" onclick="viewPlayerDetails(${player.id})">
                    <div class="text-center mb-4">
                        <img src="${player.photo}" alt="${player.name}" class="w-20 h-20 rounded-full mx-auto mb-3 object-cover">
                        <h3 class="font-bold text-lg" style="color: var(--text-dark);">${player.name}</h3>
                        <p style="color: var(--text-light);">${player.category} • ${player.age} سنة</p>
                    </div>

                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between">
                            <span style="color: var(--text-light);">الأداء</span>
                            <span class="font-semibold" style="color: var(--text-dark);">${player.performance}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="progress-bar rounded-full h-2" style="width: ${player.performance}%"></div>
                        </div>

                        <div class="flex justify-between">
                            <span style="color: var(--text-light);">الحضور</span>
                            <span class="font-semibold" style="color: var(--text-dark);">${player.attendance}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-500 rounded-full h-2" style="width: ${player.attendance}%"></div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center">
                        <span class="badge ${player.status === 'active' ? 'status-active' : 'status-rejected'}">
                            ${getPlayerStatusText(player.status)}
                        </span>
                        <span class="text-xs" style="color: var(--text-light);">
                            آخر تدريب: ${formatDate(player.lastTraining)}
                        </span>
                    </div>
                </div>
            `).join('');
        }

        // Export Players
        function exportPlayers() {
            Swal.fire({
                title: 'تصدير بيانات اللاعبين',
                text: 'اختر تنسيق التصدير:',
                icon: 'question',
                showCancelButton: true,
                showDenyButton: true,
                confirmButtonText: 'CSV',
                denyButtonText: 'Excel',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#10b981',
                denyButtonColor: '#3b82f6'
            }).then((result) => {
                if (result.isConfirmed) {
                    exportPlayersToCSV(players);
                } else if (result.isDenied) {
                    exportPlayersToExcel(players);
                }
            });
        }

        function exportPlayersToCSV(data, filename = 'players') {
            const headers = [
                'الرقم', 'الاسم', 'العمر', 'الفئة', 'الهاتف', 'البريد الإلكتروني',
                'خطة التدريب', 'الحالة', 'تاريخ الانضمام', 'آخر تدريب', 'الأداء', 'الحضور'
            ];

            const csvContent = [
                headers.join(','),
                ...data.map(player => [
                    player.id,
                    `"${player.name}"`,
                    player.age,
                    `"${player.category}"`,
                    player.phone,
                    player.email,
                    `"${player.trainingPlan}"`,
                    `"${getPlayerStatusText(player.status)}"`,
                    player.joinDate,
                    player.lastTraining,
                    player.performance,
                    player.attendance
                ].join(','))
            ].join('\n');

            // Add BOM for Arabic support
            const BOM = '\uFEFF';
            const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showNotification('تم تصدير بيانات اللاعبين بنجاح', 'success');
            }
        }

        function exportPlayersToExcel(data, filename = 'players') {
            // Simplified Excel export using HTML table method
            const table = document.createElement('table');

            // Headers
            const headerRow = table.insertRow();
            const headers = [
                'الرقم', 'الاسم', 'العمر', 'الفئة', 'الهاتف', 'البريد الإلكتروني',
                'خطة التدريب', 'الحالة', 'تاريخ الانضمام', 'الأداء', 'الحضور'
            ];

            headers.forEach(header => {
                const th = document.createElement('th');
                th.textContent = header;
                headerRow.appendChild(th);
            });

            // Data rows
            data.forEach(player => {
                const row = table.insertRow();
                const values = [
                    player.id, player.name, player.age, player.category, player.phone, player.email,
                    player.trainingPlan, getPlayerStatusText(player.status), player.joinDate,
                    player.performance + '%', player.attendance + '%'
                ];

                values.forEach(value => {
                    const td = document.createElement('td');
                    td.textContent = value;
                    row.appendChild(td);
                });
            });

            // Export
            const html = table.outerHTML;
            const blob = new Blob([html], { type: 'application/vnd.ms-excel' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${filename}_${new Date().toISOString().split('T')[0]}.xls`;
            link.click();

            showNotification('تم تصدير بيانات اللاعبين إلى Excel بنجاح', 'success');
        }

        // Update existing functions to use new modals
        function addNewPlayer() {
            openAddPlayerModal();
        }

        function exportApplications() {
            Swal.fire({
                title: 'تصدير البيانات',
                text: 'اختر تنسيق التصدير:',
                icon: 'question',
                showCancelButton: true,
                showDenyButton: true,
                confirmButtonText: 'CSV',
                denyButtonText: 'Excel',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#10b981',
                denyButtonColor: '#3b82f6'
            }).then((result) => {
                if (result.isConfirmed) {
                    exportApplicationsToCSV(applications);
                } else if (result.isDenied) {
                    exportApplicationsToExcel(applications);
                }
            });
        }

        function bulkActions() {
            openBulkActionsModal();
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' :
                type === 'warning' ? 'bg-yellow-500' :
                'bg-blue-500'
            } text-white`;

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Click outside to close modals
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                e.target.classList.add('hidden');
            }

            // Close notifications dropdown when clicking outside
            const notificationDropdown = document.getElementById('notification-dropdown');
            const notificationButton = e.target.closest('button');

            if (!notificationButton && !notificationDropdown.contains(e.target)) {
                notificationDropdown.classList.add('hidden');
            }
        });
    </script>
</body>
</html>
