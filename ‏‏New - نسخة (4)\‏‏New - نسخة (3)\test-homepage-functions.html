<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار دوال إدارة الصفحة الرئيسية</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            direction: rtl;
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 10px 0;
            border-radius: 10px;
        }
        .test-button {
            background: #8B4513;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            margin: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #D2691E;
        }
        .result {
            background: rgba(0,255,0,0.1);
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid green;
        }
        .error {
            background: rgba(255,0,0,0.1);
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid red;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار دوال إدارة الصفحة الرئيسية</h1>
    
    <div class="test-section">
        <h2>اختبار الدوال الأساسية</h2>
        <button class="test-button" onclick="testFunction('switchLibraryTab')">اختبار switchLibraryTab</button>
        <button class="test-button" onclick="testFunction('toggleDevicePreview')">اختبار toggleDevicePreview</button>
        <button class="test-button" onclick="testFunction('importTemplate')">اختبار importTemplate</button>
        <button class="test-button" onclick="testFunction('insertMedia')">اختبار insertMedia</button>
        <button class="test-button" onclick="testFunction('loadMediaLibrary')">اختبار loadMediaLibrary</button>
        <button class="test-button" onclick="testFunction('uploadMedia')">اختبار uploadMedia</button>
        <button class="test-button" onclick="testFunction('exportTemplate')">اختبار exportTemplate</button>
        <button class="test-button" onclick="testFunction('loadTemplateLibrary')">اختبار loadTemplateLibrary</button>
    </div>

    <div class="test-section">
        <h2>اختبار دوال الحفظ والنشر</h2>
        <button class="test-button" onclick="testFunction('saveHomepage')">اختبار saveHomepage</button>
        <button class="test-button" onclick="testFunction('previewHomepage')">اختبار previewHomepage</button>
        <button class="test-button" onclick="testFunction('publishHomepage')">اختبار publishHomepage</button>
        <button class="test-button" onclick="testFunction('undoAction')">اختبار undoAction</button>
        <button class="test-button" onclick="testFunction('redoAction')">اختبار redoAction</button>
    </div>

    <div class="test-section">
        <h2>اختبار شامل</h2>
        <button class="test-button" onclick="runAllTests()">تشغيل جميع الاختبارات</button>
        <button class="test-button" onclick="clearResults()">مسح النتائج</button>
    </div>

    <div id="results"></div>

    <script>
        // محاكاة البيئة المطلوبة
        let homepageData = {
            sections: [],
            settings: {
                theme: 'default',
                layout: 'modern',
                colors: {
                    primary: '#1a1a1a',
                    secondary: '#8B4513',
                    accent: '#D2691E'
                }
            },
            lastSaved: null,
            version: '1.0'
        };

        let selectedElement = null;
        let draggedElement = null;
        let undoStack = [];
        let redoStack = [];
        let currentDevice = 'desktop';

        // محاكاة SweetAlert2
        window.Swal = {
            fire: function(title, text, icon) {
                console.log(`Swal.fire: ${title} - ${text} - ${icon}`);
                return Promise.resolve({isConfirmed: true});
            }
        };

        function testFunction(functionName) {
            const resultsDiv = document.getElementById('results');
            
            try {
                // التحقق من وجود الدالة
                if (typeof window[functionName] === 'function') {
                    resultsDiv.innerHTML += `<div class="result">✅ الدالة ${functionName} موجودة ومعرفة بشكل صحيح</div>`;
                    
                    // محاولة تشغيل الدالة (مع معاملات تجريبية)
                    try {
                        switch(functionName) {
                            case 'switchLibraryTab':
                                // لا نستدعيها لأنها تحتاج DOM elements
                                resultsDiv.innerHTML += `<div class="result">📝 ${functionName} جاهزة للاستخدام (تحتاج DOM)</div>`;
                                break;
                            case 'toggleDevicePreview':
                                // لا نستدعيها لأنها تحتاج event
                                resultsDiv.innerHTML += `<div class="result">📝 ${functionName} جاهزة للاستخدام (تحتاج event)</div>`;
                                break;
                            case 'insertMedia':
                                // لا نستدعيها لأنها تحتاج DOM elements
                                resultsDiv.innerHTML += `<div class="result">📝 ${functionName} جاهزة للاستخدام (تحتاج DOM)</div>`;
                                break;
                            case 'loadMediaLibrary':
                                // يمكن استدعاؤها بأمان
                                window[functionName]();
                                resultsDiv.innerHTML += `<div class="result">🎯 ${functionName} تم تشغيلها بنجاح</div>`;
                                break;
                            case 'loadTemplateLibrary':
                                // يمكن استدعاؤها بأمان
                                window[functionName]();
                                resultsDiv.innerHTML += `<div class="result">🎯 ${functionName} تم تشغيلها بنجاح</div>`;
                                break;
                            default:
                                resultsDiv.innerHTML += `<div class="result">📝 ${functionName} جاهزة للاستخدام</div>`;
                        }
                    } catch (execError) {
                        resultsDiv.innerHTML += `<div class="error">⚠️ خطأ في تشغيل ${functionName}: ${execError.message}</div>`;
                    }
                } else {
                    resultsDiv.innerHTML += `<div class="error">❌ الدالة ${functionName} غير موجودة أو غير معرفة</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML += `<div class="error">💥 خطأ في اختبار ${functionName}: ${error.message}</div>`;
            }
        }

        function runAllTests() {
            const functions = [
                'switchLibraryTab', 'toggleDevicePreview', 'importTemplate', 
                'insertMedia', 'loadMediaLibrary', 'uploadMedia', 'exportTemplate', 
                'loadTemplateLibrary', 'saveHomepage', 'previewHomepage', 
                'publishHomepage', 'undoAction', 'redoAction'
            ];
            
            clearResults();
            document.getElementById('results').innerHTML += `<div class="result"><h3>🚀 بدء الاختبار الشامل...</h3></div>`;
            
            functions.forEach(func => {
                testFunction(func);
            });
            
            document.getElementById('results').innerHTML += `<div class="result"><h3>✅ انتهى الاختبار الشامل</h3></div>`;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // تحميل الدوال من الملف الأصلي
        function loadMainFile() {
            const script = document.createElement('script');
            script.src = 'admin-advanced.html';
            document.head.appendChild(script);
        }

        console.log('🧪 صفحة اختبار دوال إدارة الصفحة الرئيسية جاهزة');
    </script>
</body>
</html>
