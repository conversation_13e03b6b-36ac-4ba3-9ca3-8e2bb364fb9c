<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>لوحة تحكم اللاعب الشاملة - أكاديمية 7C</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <style>
        :root {
            --primary-bg: #1a1a1a;
            --brand-primary: #8B4513;
            --brand-secondary: #D2691E;
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --border-color: rgba(255, 255, 255, 0.2);
            --glass-bg: rgba(255, 255, 255, 0.05);
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
        }
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        body {
            background-color: var(--primary-bg);
            color: var(--text-primary);
            direction: rtl;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        header {
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            padding: 1rem 2rem;
            text-align: center;
            font-size: 1.5rem;
            font-weight: bold;
            box-shadow: 0 4px 10px rgba(139, 69, 19, 0.7);
        }
        main {
            flex: 1;
            max-width: 1200px;
            margin: 1rem auto;
            padding: 1rem;
            background: var(--glass-bg);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.5);
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 1.5rem;
        }
        nav.sidebar {
            background: var(--brand-primary);
            border-radius: 12px;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        nav.sidebar button {
            background: transparent;
            border: none;
            color: var(--text-primary);
            font-size: 1.1rem;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            text-align: right;
            transition: background 0.3s ease;
        }
        nav.sidebar button.active,
        nav.sidebar button:hover {
            background: var(--brand-secondary);
            box-shadow: 0 0 10px var(--brand-secondary);
        }
        section.content-section {
            background: var(--primary-bg);
            border-radius: 12px;
            padding: 1rem 1.5rem;
            display: none;
            flex-direction: column;
            gap: 1rem;
            color: var(--text-primary);
        }
        section.content-section.active {
            display: flex;
        }
        .profile-header {
            display: flex;
            gap: 1rem;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 1rem;
        }
        .profile-header img.avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 3px solid var(--brand-secondary);
            object-fit: cover;
        }
        .profile-info {
            flex: 1;
        }
        .profile-info h2 {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
            color: var(--brand-secondary);
        }
        .profile-info p {
            margin-bottom: 0.3rem;
            font-size: 1rem;
        }
        .profile-info p span.label {
            font-weight: bold;
            margin-left: 0.5rem;
        }
        .profile-actions {
            margin-top: 1rem;
            display: flex;
            gap: 1rem;
        }
        .btn {
            background: var(--brand-primary);
            border: none;
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: background 0.3s ease;
        }
        .btn:hover {
            background: var(--brand-secondary);
        }
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit,minmax(150px,1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        .stat-card {
            background: var(--glass-bg);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            box-shadow: 0 0 10px rgba(139,69,19,0.3);
        }
        .stat-card h3 {
            margin-bottom: 0.5rem;
            color: var(--brand-secondary);
        }
        .stat-card .value {
            font-size: 1.5rem;
            font-weight: bold;
        }
        table.schedule-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            color: var(--text-primary);
        }
        table.schedule-table th,
        table.schedule-table td {
            border: 1px solid var(--border-color);
            padding: 0.5rem;
            text-align: center;
        }
        table.schedule-table th {
            background: var(--brand-primary);
            color: var(--text-primary);
        }
        .goals-list {
            list-style: none;
            padding: 0;
            margin-top: 1rem;
        }
        .goals-list li {
            background: var(--glass-bg);
            margin-bottom: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .goals-list li .progress {
            width: 60%;
            height: 10px;
            background: var(--border-color);
            border-radius: 5px;
            overflow: hidden;
            margin-left: 1rem;
        }
        .goals-list li .progress-bar {
            height: 100%;
            background: var(--brand-secondary);
            width: 0%;
            transition: width 0.5s ease;
        }
        .payments-list {
            list-style: none;
            padding: 0;
            margin-top: 1rem;
        }
        .payments-list li {
            background: var(--glass-bg);
            margin-bottom: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .payments-list li span.status {
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        .status-active {
            background: var(--success-color);
            color: white;
        }
        .status-expired {
            background: var(--error-color);
            color: white;
        }
        .status-pending {
            background: var(--warning-color);
            color: black;
        }
        .messages-container {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 1rem;
        }
        .message {
            background: var(--glass-bg);
            border-radius: 12px;
            padding: 1rem;
            box-shadow: 0 0 10px rgba(139,69,19,0.3);
        }
        .message .header {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--brand-secondary);
        }
        .message .body {
            font-size: 1rem;
        }
        #notification {
            position: fixed;
            top: 1rem;
            left: 50%;
            transform: translateX(-50%);
            background: var(--brand-secondary);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            box-shadow: 0 0 10px rgba(139,69,19,0.7);
            display: none;
            z-index: 1000;
        }
        @media (max-width: 768px) {
            main {
                grid-template-columns: 1fr;
            }
            nav.sidebar {
                flex-direction: row;
                overflow-x: auto;
            }
            nav.sidebar button {
                flex: 1 0 auto;
                font-size: 0.9rem;
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header>لوحة تحكم اللاعب الشاملة</header>
    <main>
        <nav class="sidebar" aria-label="التنقل بين الأقسام">
            <button class="active" data-section="profile">الملف الشخصي</button>
            <button data-section="stats">الإحصائيات</button>
            <button data-section="schedule">الجدول التدريبي</button>
            <button data-section="goals">الأهداف الشخصية</button>
            <button data-section="payments">المدفوعات والاشتراكات</button>
            <button data-section="messages">نظام الرسائل</button>
        </nav>
        <section id="profile" class="content-section active" aria-label="الملف الشخصي للاعب">
            <div class="profile-header">
                <img src="https://via.placeholder.com/120x120/8B4513/FFFFFF?text=Player" alt="صورة اللاعب" class="avatar" id="playerAvatar" />
                <div class="profile-info">
                    <h2 id="playerName">محمد أحمد الزهراني</h2>
                    <p><span class="label">العمر:</span> <span id="playerAge">16 سنة</span></p>
                    <p><span class="label">المستوى:</span> <span id="playerLevel">متقدم</span></p>
                    <p><span class="label">الهاتف:</span> <span id="playerPhone">0501234567</span></p>
                    <div class="profile-actions">
                        <button class="btn" id="editProfileBtn">تحديث البيانات</button>
                    </div>
                </div>
            </div>
        </section>
        <section id="stats" class="content-section" aria-label="لوحة الإحصائيات الشخصية">
            <h3>الإحصائيات الشخصية</h3>
            <div class="stats-cards">
                <div class="stat-card">
                    <h3>معدل الحضور</h3>
                    <div class="value" id="attendanceRate">95%</div>
                </div>
                <div class="stat-card">
                    <h3>درجة الأداء</h3>
                    <div class="value" id="performanceScore">85</div>
                </div>
                <div class="stat-card">
                    <h3>الحصص المكتملة</h3>
                    <div class="value" id="completedSessions">10</div>
                </div>
                <div class="stat-card">
                    <h3>الإنجازات</h3>
                    <div class="value" id="achievementsCount">3</div>
                </div>
            </div>
            <canvas id="attendanceChart" width="400" height="200" aria-label="رسم بياني لمعدل الحضور"></canvas>
        </section>
        <section id="schedule" class="content-section" aria-label="الجدول التدريبي">
            <h3>الجدول التدريبي الأسبوعي</h3>
            <table class="schedule-table" aria-describedby="scheduleDesc">
                <thead>
                    <tr>
                        <th>اليوم</th>
                        <th>التاريخ</th>
                        <th>الوقت</th>
                        <th>الحصة</th>
                        <th>المدرب</th>
                        <th>الملعب</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody id="scheduleBody">
                </tbody>
            </table>
            <p id="scheduleDesc" class="sr-only">جدول الحصص التدريبية الأسبوعية مع تفاصيل المدرب والملعب وحالة الحصة</p>
        </section>
        <section id="goals" class="content-section" aria-label="تتبع الأهداف الشخصية">
            <h3>الأهداف الشخصية</h3>
            <ul class="goals-list" id="goalsList">
            </ul>
            <button class="btn" id="addGoalBtn">إضافة هدف جديد</button>
        </section>
        <section id="payments" class="content-section" aria-label="المدفوعات والاشتراكات">
            <h3>المدفوعات والاشتراكات</h3>
            <ul class="payments-list" id="paymentsList">
            </ul>
        </section>
        <section id="messages" class="content-section" aria-label="نظام الرسائل">
            <h3>نظام الرسائل</h3>
            <div class="messages-container" id="messagesContainer">
            </div>
            <textarea id="newMessageText" rows="3" placeholder="اكتب رسالة جديدة..."></textarea>
            <button class="btn" id="sendMessageBtn">إرسال الرسالة</button>
        </section>
    </main>
    <div id="notification" role="alert" aria-live="assertive"></div>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode/build/qrcode.min.js"></script>
    <script>
        const playerData = {
            name: "محمد أحمد الزهراني",
            age: 16,
            level: "متقدم",
            phone: "0501234567",
            avatar: "https://via.placeholder.com/120x120/8B4513/FFFFFF?text=Player",
            attendanceRate: 95,
            performanceScore: 85,
            completedSessions: 10,
            achievementsCount: 3,
            schedule: [
                { day: "الأحد", date: "2024-06-02", time: "16:00 - 18:00", session: "تدريب تقني", coach: "المدرب أحمد", field: "الملعب الرئيسي", status: "مؤكدة" },
                { day: "الثلاثاء", date: "2024-06-04", time: "16:00 - 18:00", session: "تدريب بدني", coach: "المدرب محمد", field: "الملعب الفرعي", status: "مؤكدة" },
                { day: "الخميس", date: "2024-06-06", time: "16:00 - 18:00", session: "مباراة تطبيقية", coach: "المدرب أحمد", field: "الملعب الرئيسي", status: "ملغاة" }
            ],
            goals: [
                { id: 1, text: "تحسين اللياقة البدنية", progress: 60 },
                { id: 2, text: "زيادة مهارات التمرير", progress: 40 },
                { id: 3, text: "تحقيق الانضباط في الحضور", progress: 80 }
            ],
            payments: [
                { id: 1, date: "2024-05-01", amount: 500, status: "نشط" },
                { id: 2, date: "2024-04-01", amount: 500, status: "منتهي" },
                { id: 3, date: "2024-06-01", amount: 500, status: "معلق" }
            ],
            messages: [
                { id: 1, sender: "المدرب أحمد", text: "أداء ممتاز في التدريب الأخير، استمر هكذا!", date: "2024-06-01" },
                { id: 2, sender: "الإدارة", text: "تذكير: يرجى تحديث بيانات الاشتراك.", date: "2024-05-28" }
            ]
        };

        const navButtons = document.querySelectorAll('nav.sidebar button');
        const sections = document.querySelectorAll('section.content-section');
        navButtons.forEach(button => {
            button.addEventListener('click', () => {
                navButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                const sectionId = button.getAttribute('data-section');
                sections.forEach(section => {
                    section.classList.toggle('active', section.id === sectionId);
                });
            });
        });

        function loadProfile() {
            document.getElementById('playerName').textContent = playerData.name;
            document.getElementById('playerAge').textContent = playerData.age + " سنة";
            document.getElementById('playerLevel').textContent = playerData.level;
            document.getElementById('playerPhone').textContent = playerData.phone;
            document.getElementById('playerAvatar').src = playerData.avatar;
        }

        document.getElementById('editProfileBtn').addEventListener('click', () => {
            alert('ميزة تحديث البيانات ستتم إضافتها قريباً');
        });

        function loadStats() {
            document.getElementById('attendanceRate').textContent = playerData.attendanceRate + "%";
            document.getElementById('performanceScore').textContent = playerData.performanceScore;
            document.getElementById('completedSessions').textContent = playerData.completedSessions;
            document.getElementById('achievementsCount').textContent = playerData.achievementsCount;
            renderAttendanceChart();
        }

        function renderAttendanceChart() {
            const ctx = document.getElementById('attendanceChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'معدل الحضور',
                        data: [90, 92, 88, 95, 93, playerData.attendanceRate],
                        borderColor: 'rgba(210, 105, 30, 1)',
                        backgroundColor: 'rgba(210, 105, 30, 0.2)',
                        fill: true,
                        tension: 0.3
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: { color: '#fff' },
                            grid: { color: 'rgba(255,255,255,0.1)' }
                        },
                        x: {
                            ticks: { color: '#fff' },
                            grid: { color: 'rgba(255,255,255,0.1)' }
                        }
                    },
                    plugins: {
                        legend: { labels: { color: '#fff' } }
                    }
                }
            });
        }

        function loadSchedule() {
            const tbody = document.getElementById('scheduleBody');
            tbody.innerHTML = '';
            playerData.schedule.forEach(session => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${session.day}</td>
                    <td>${session.date}</td>
                    <td>${session.time}</td>
                    <td>${session.session}</td>
                    <td>${session.coach}</td>
                    <td>${session.field}</td>
                    <td>${session.status}</td>
                `;
                tbody.appendChild(tr);
            });
        }

        function loadGoals() {
            const goalsList = document.getElementById('goalsList');
            goalsList.innerHTML = '';
            playerData.goals.forEach(goal => {
                const li = document.createElement('li');
                li.innerHTML = `
                    <span>${goal.text}</span>
                    <div class="progress">
                        <div class="progress-bar" style="width: ${goal.progress}%;"></div>
                    </div>
                `;
                goalsList.appendChild(li);
            });
        }

        document.getElementById('addGoalBtn').addEventListener('click', () => {
            const goalText = prompt('أدخل هدفاً جديداً:');
            if (goalText) {
                playerData.goals.push({ id: Date.now(), text: goalText, progress: 0 });
                loadGoals();
                showNotification('تم إضافة الهدف بنجاح');
            }
        });

        function loadPayments() {
            const paymentsList = document.getElementById('paymentsList');
            paymentsList.innerHTML = '';
            playerData.payments.forEach(payment => {
                const li = document.createElement('li');
                let statusClass = '';
                if (payment.status === 'نشط') statusClass = 'status-active';
                else if (payment.status === 'منتهي') statusClass = 'status-expired';
                else if (payment.status === 'معلق') statusClass = 'status-pending';
                li.innerHTML = `
                    <span>${payment.date} - ${payment.amount} ر.س</span>
                    <span class="status ${statusClass}">${payment.status}</span>
                `;
                paymentsList.appendChild(li);
            });
        }

        function loadMessages() {
            const messagesContainer = document.getElementById('messagesContainer');
            messagesContainer.innerHTML = '';
            playerData.messages.forEach(message => {
                const div = document.createElement('div');
                div.classList.add('message');
                div.innerHTML = `
                    <div class="header">${message.sender} - ${message.date}</div>
                    <div class="body">${message.text}</div>
                `;
                messagesContainer.appendChild(div);
            });
        }

        document.getElementById('sendMessageBtn').addEventListener('click', () => {
            const textarea = document.getElementById('newMessageText');
            const text = textarea.value.trim();
            if (text === '') {
                showNotification('يرجى كتابة رسالة قبل الإرسال', 'error');
                return;
            }
            playerData.messages.push({
                id: Date.now(),
                sender: 'اللاعب',
                text: text,
                date: new Date().toLocaleDateString('ar-SA')
            });
            textarea.value = '';
            loadMessages();
            showNotification('تم إرسال الرسالة بنجاح');
        });

        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.style.backgroundColor = type === 'error' ? 'var(--error-color)' : 'var(--brand-secondary)';
            notification.style.display = 'block';
            setTimeout(() => {
                notification.style.display = 'none';
            }, 3000);
        }

        window.addEventListener('DOMContentLoaded', () => {
            loadProfile();
            loadStats();
            loadSchedule();
            loadGoals();
            loadPayments();
            loadMessages();
        });
    </script>
</body>
</html>
