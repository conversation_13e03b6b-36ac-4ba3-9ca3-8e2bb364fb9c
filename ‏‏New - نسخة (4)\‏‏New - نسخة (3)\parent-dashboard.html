<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم ولي الأمر - أكاديمية 7C الرياضية</title>
    
    <!-- External Libraries -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.0/dist/sweetalert2.all.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    
    <style>
        /* ==================== متغيرات الألوان الأساسية ==================== */
        :root {
            /* الألوان الأساسية */
            --primary-bg: #1a1a1a;
            --secondary-bg: #2d2d2d;
            --brand-primary: #1e40af;
            --brand-secondary: #3b82f6;
            --accent-color: #60a5fa;
            --accent-dark: #1e293b;

            /* ألوان الحالة */
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --info: #3b82f6;

            /* ألوان الواجهة */
            --glass: rgba(255,255,255,0.1);
            --glass-strong: rgba(255,255,255,0.15);
            --border: rgba(255,255,255,0.2);
            --border-strong: rgba(255,255,255,0.3);

            /* ألوان النصوص */
            --text-primary: #ffffff;
            --text-secondary: #e2e8f0;
            --text-muted: #94a3b8;

            /* الظلال والتأثيرات */
            --shadow-dark: 0 8px 32px rgba(0, 0, 0, 0.3);
            --shadow-glow: 0 0 20px rgba(30, 64, 175, 0.3);
            --shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.4);

            /* متغيرات إضافية للتخصيص */
            --header-bg: var(--glass);
            --card-bg: var(--glass);
            --button-bg: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            --logo-bg: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));

            /* انتقالات سلسة */
            --transition-fast: 0.2s ease;
            --transition-normal: 0.3s ease;
            --transition-slow: 0.5s ease;
        }

        /* ==================== إعدادات أساسية ==================== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--accent-dark) 100%);
            color: var(--text-primary);
            overflow-x: hidden;
            padding-top: 80px;
            transition: all var(--transition-normal);
        }

        /* ==================== شريط الأدوات العلوي ==================== */
        .toolbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: var(--header-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border);
            padding: 1rem 2rem;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all var(--transition-normal);
        }

        .toolbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .academy-logo-toolbar {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 700;
            color: var(--text-primary);
            text-decoration: none;
            transition: all var(--transition-normal);
        }

        .academy-logo-toolbar:hover {
            transform: scale(1.05);
            color: var(--brand-primary);
        }

        .academy-logo-icon {
            width: 40px;
            height: 40px;
            background: var(--logo-bg);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            box-shadow: var(--shadow-glow);
            animation: logoGlow 3s ease-in-out infinite;
        }

        @keyframes logoGlow {
            0%, 100% { box-shadow: var(--shadow-glow); }
            50% { box-shadow: 0 0 30px rgba(30, 64, 175, 0.5); }
        }

        .control-btn {
            background: var(--glass);
            border: 1px solid var(--border);
            border-radius: 8px;
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            cursor: pointer;
            transition: all var(--transition-normal);
            font-family: inherit;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .control-btn:hover {
            background: var(--glass-strong);
            border-color: var(--border-strong);
            transform: translateY(-2px);
        }

        .control-btn.active {
            background: var(--button-bg);
            border-color: var(--brand-primary);
        }

        /* ==================== أداة تخصيص الألوان ==================== */
        .color-customizer {
            position: fixed;
            top: 80px;
            right: 2rem;
            width: 350px;
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 1.5rem;
            z-index: 999;
            transform: translateX(100%);
            opacity: 0;
            visibility: hidden;
            transition: all var(--transition-normal);
            max-height: 80vh;
            overflow-y: auto;
        }

        .color-customizer.active {
            transform: translateX(0);
            opacity: 1;
            visibility: visible;
        }

        .customizer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
        }

        .customizer-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .close-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            transition: all var(--transition-fast);
        }

        .close-btn:hover {
            color: var(--danger);
            background: rgba(239, 68, 68, 0.1);
        }

        .color-group {
            margin-bottom: 1.5rem;
        }

        .color-group-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .color-controls {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 0.5rem;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .color-label {
            font-size: 0.9rem;
            color: var(--text-muted);
        }

        .color-input {
            width: 50px;
            height: 35px;
            border: 2px solid var(--border);
            border-radius: 8px;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .color-input:hover {
            border-color: var(--brand-primary);
            transform: scale(1.1);
        }

        .color-presets {
            margin-bottom: 1.5rem;
        }

        .presets-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
        }

        .preset-btn {
            background: var(--glass);
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 0.75rem;
            cursor: pointer;
            transition: all var(--transition-normal);
            text-align: center;
            font-size: 0.85rem;
            color: var(--text-secondary);
        }

        .preset-btn:hover {
            background: var(--glass-strong);
            border-color: var(--brand-primary);
            color: var(--text-primary);
        }

        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            margin-top: 1.5rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border);
        }

        .action-btn {
            background: var(--glass);
            border: 1px solid var(--border);
            border-radius: 8px;
            color: var(--text-primary);
            padding: 0.75rem;
            cursor: pointer;
            transition: all var(--transition-normal);
            font-family: inherit;
            font-size: 0.9rem;
            text-align: center;
        }

        .action-btn:hover {
            background: var(--glass-strong);
            transform: translateY(-2px);
        }

        .action-btn.primary {
            background: var(--button-bg);
            border-color: var(--brand-primary);
        }

        .action-btn.danger {
            background: linear-gradient(135deg, var(--danger), #dc2626);
            border-color: var(--danger);
        }

        .theme-toggle {
            position: relative;
            width: 60px;
            height: 30px;
            background: var(--glass);
            border: 1px solid var(--border);
            border-radius: 15px;
            cursor: pointer;
            transition: all var(--transition-normal);
        }

        .theme-toggle::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 22px;
            height: 22px;
            background: var(--button-bg);
            border-radius: 50%;
            transition: all var(--transition-normal);
        }

        .theme-toggle.night::before {
            transform: translateX(28px);
            background: linear-gradient(135deg, #1e293b, #334155);
        }

        /* ==================== التخطيط الرئيسي ==================== */
        .main-layout {
            display: grid;
            grid-template-columns: 280px 1fr;
            grid-template-rows: 70px 1fr;
            grid-template-areas:
                "sidebar header"
                "sidebar content";
            min-height: 100vh;
            transition: all var(--transition-normal);
        }

        /* ==================== الشريط الجانبي ==================== */
        .sidebar {
            grid-area: sidebar;
            background: linear-gradient(180deg, var(--brand-primary) 0%, var(--accent-dark) 100%);
            border-left: 1px solid var(--border);
            overflow-y: auto;
            transition: all 0.3s ease;
            position: relative;
        }

        .sidebar-header {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid var(--border);
            background: rgba(0, 0, 0, 0.2);
        }

        .parent-avatar {
            width: 90px;
            height: 90px;
            background: var(--logo-bg);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            font-size: 2.2rem;
            color: white;
            box-shadow: var(--shadow-glow);
            animation: pulse 3s infinite;
            transition: all var(--transition-normal);
        }

        .parent-avatar:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 0 40px rgba(30, 64, 175, 0.5);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .parent-name {
            font-size: 1.1rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.25rem;
        }

        .parent-title {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.7);
            background: rgba(255, 255, 255, 0.1);
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            display: inline-block;
        }

        /* ==================== قائمة التنقل ==================== */
        .nav-menu {
            padding: 1rem 0;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
            position: relative;
            cursor: pointer;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-right-color: var(--brand-secondary);
            transform: translateX(-5px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border-right-color: var(--brand-secondary);
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
        }

        .nav-icon {
            width: 20px;
            margin-left: 1rem;
            font-size: 1.1rem;
        }

        .nav-text {
            font-weight: 600;
            font-size: 0.95rem;
        }

        .nav-badge {
            margin-right: auto;
            background: var(--brand-secondary);
            color: white;
            font-size: 0.7rem;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            font-weight: 600;
        }

        /* ==================== رأس الصفحة ==================== */
        .header {
            grid-area: header;
            background: var(--glass);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 2rem;
            box-shadow: var(--shadow-dark);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--brand-primary);
        }

        .breadcrumb {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-btn {
            background: var(--glass);
            border: 1px solid var(--border);
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .header-btn:hover {
            background: var(--brand-primary);
            transform: translateY(-2px);
        }

        .notification-btn {
            position: relative;
        }

        .notification-count {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--danger);
            color: white;
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
            border-radius: 50%;
            min-width: 18px;
            text-align: center;
        }

        /* ==================== المحتوى الرئيسي ==================== */
        .content {
            grid-area: content;
            padding: 2rem;
            overflow-y: auto;
            background: linear-gradient(135deg, var(--primary-bg) 0%, #1e1e2e 100%);
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* ==================== بطاقات الإحصائيات ==================== */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
            transition: all var(--transition-normal);
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-hover);
            border-color: var(--brand-primary);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--button-bg);
            transition: all var(--transition-normal);
        }

        .stat-card:hover::before {
            height: 6px;
            background: linear-gradient(90deg, var(--brand-primary), var(--accent-color), var(--brand-secondary));
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 600;
        }

        .stat-icon {
            width: 45px;
            height: 45px;
            background: var(--button-bg);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.3rem;
            transition: all var(--transition-normal);
            box-shadow: var(--shadow-glow);
        }

        .stat-card:hover .stat-icon {
            transform: scale(1.1) rotate(10deg);
            box-shadow: 0 0 30px rgba(30, 64, 175, 0.5);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stat-change {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .stat-change.positive {
            color: var(--success);
        }

        .stat-change.negative {
            color: var(--danger);
        }

        .stat-change.neutral {
            color: var(--text-secondary);
        }

        /* ==================== البطاقات العامة ==================== */
        .card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
            transition: all var(--transition-normal);
        }

        .card:hover {
            border-color: var(--border-strong);
            box-shadow: var(--shadow-dark);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .card-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.75rem 1.25rem;
            border: 1px solid var(--border);
            border-radius: 10px;
            background: var(--glass);
            color: var(--text-primary);
            cursor: pointer;
            transition: all var(--transition-normal);
            font-family: inherit;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
        }

        .btn:hover {
            background: var(--brand-primary);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
        }

        .btn.primary {
            background: var(--button-bg);
            border-color: var(--brand-primary);
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
        }

        /* ==================== تصميم متجاوب ==================== */
        @media (max-width: 1024px) {
            .main-layout {
                grid-template-columns: 1fr;
                grid-template-areas: 
                    "header"
                    "content";
            }
            
            .sidebar {
                position: fixed;
                left: -280px;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            
            .sidebar.open {
                left: 0;
            }
        }

        @media (max-width: 768px) {
            .content {
                padding: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .header {
                padding: 0 1rem;
            }
            
            .page-title {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <!-- شريط الأدوات العلوي -->
    <div class="toolbar">
        <div class="toolbar-right">
            <a href="#" class="academy-logo-toolbar">
                <div class="academy-logo-icon">
                    <i class="fas fa-dumbbell"></i>
                </div>
                <span>أكاديمية 7C</span>
            </a>
        </div>
        <div class="toolbar-left">
            <button class="control-btn" onclick="toggleColorCustomizer()">
                <i class="fas fa-palette"></i>
                تخصيص الألوان
            </button>
            <div class="theme-toggle" onclick="toggleTheme()" title="تبديل الوضع الليلي/النهاري">
            </div>
            <button class="control-btn" onclick="resetToDefaults()">
                <i class="fas fa-undo"></i>
                إعادة تعيين
            </button>
        </div>
    </div>

    <!-- أداة تخصيص الألوان -->
    <div class="color-customizer" id="colorCustomizer">
        <div class="customizer-header">
            <h3 class="customizer-title">
                <i class="fas fa-palette" style="margin-left: 0.5rem;"></i>
                تخصيص الألوان
            </h3>
            <button class="close-btn" onclick="toggleColorCustomizer()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- قوالب الألوان الجاهزة -->
        <div class="color-presets">
            <div class="color-group-title">
                <i class="fas fa-swatchbook"></i>
                قوالب جاهزة
            </div>
            <div class="presets-grid">
                <button class="preset-btn" onclick="applyPreset('blue')">
                    الأزرق الكلاسيكي
                </button>
                <button class="preset-btn" onclick="applyPreset('green')">
                    الأخضر الطبيعي
                </button>
                <button class="preset-btn" onclick="applyPreset('orange')">
                    البرتقالي الدافئ
                </button>
                <button class="preset-btn" onclick="applyPreset('purple')">
                    البنفسجي الملكي
                </button>
                <button class="preset-btn" onclick="applyPreset('red')">
                    الأحمر القوي
                </button>
                <button class="preset-btn" onclick="applyPreset('dark')">
                    الداكن الأنيق
                </button>
            </div>
        </div>

        <!-- ألوان العلامة التجارية -->
        <div class="color-group">
            <div class="color-group-title">
                <i class="fas fa-star"></i>
                ألوان العلامة التجارية
            </div>
            <div class="color-controls">
                <label class="color-label">اللون الأساسي</label>
                <input type="color" class="color-input" id="brandPrimary" value="#1e40af" onchange="updateColor('--brand-primary', this.value)">
            </div>
            <div class="color-controls">
                <label class="color-label">اللون الثانوي</label>
                <input type="color" class="color-input" id="brandSecondary" value="#3b82f6" onchange="updateColor('--brand-secondary', this.value)">
            </div>
            <div class="color-controls">
                <label class="color-label">لون التمييز</label>
                <input type="color" class="color-input" id="accentColor" value="#60a5fa" onchange="updateColor('--accent-color', this.value)">
            </div>
        </div>

        <!-- ألوان الخلفية -->
        <div class="color-group">
            <div class="color-group-title">
                <i class="fas fa-fill-drip"></i>
                ألوان الخلفية
            </div>
            <div class="color-controls">
                <label class="color-label">الخلفية الأساسية</label>
                <input type="color" class="color-input" id="primaryBg" value="#1a1a1a" onchange="updateColor('--primary-bg', this.value)">
            </div>
            <div class="color-controls">
                <label class="color-label">الخلفية الثانوية</label>
                <input type="color" class="color-input" id="secondaryBg" value="#2d2d2d" onchange="updateColor('--secondary-bg', this.value)">
            </div>
            <div class="color-controls">
                <label class="color-label">الخلفية الداكنة</label>
                <input type="color" class="color-input" id="accentDark" value="#1e293b" onchange="updateColor('--accent-dark', this.value)">
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="action-buttons">
            <button class="action-btn primary" onclick="saveColorSettings()">
                <i class="fas fa-save"></i>
                حفظ الإعدادات
            </button>
            <button class="action-btn" onclick="exportColorSettings()">
                <i class="fas fa-download"></i>
                تصدير
            </button>
            <button class="action-btn" onclick="importColorSettings()">
                <i class="fas fa-upload"></i>
                استيراد
            </button>
            <button class="action-btn danger" onclick="resetColors()">
                <i class="fas fa-undo"></i>
                إعادة تعيين
            </button>
        </div>
    </div>

    <div class="main-layout">
        <!-- الشريط الجانبي -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="parent-avatar" id="parentAvatar">
                    <i class="fas fa-user-friends"></i>
                </div>
                <div class="parent-name" id="parentName">أحمد والد محمد</div>
                <div class="parent-title" id="parentTitle">ولي أمر</div>
            </div>

            <nav class="nav-menu">
                <div class="nav-item">
                    <div class="nav-link active" onclick="showSection('overview')">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">نظرة عامة</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('children')">
                        <i class="fas fa-child nav-icon"></i>
                        <span class="nav-text">أطفالي</span>
                        <span class="nav-badge" id="childrenCount">0</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('progress')">
                        <i class="fas fa-chart-line nav-icon"></i>
                        <span class="nav-text">متابعة التقدم</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('schedule')">
                        <i class="fas fa-calendar-alt nav-icon"></i>
                        <span class="nav-text">الجدول الزمني</span>
                        <span class="nav-badge" id="upcomingSessionsCount">0</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('payments')">
                        <i class="fas fa-credit-card nav-icon"></i>
                        <span class="nav-text">المدفوعات</span>
                        <span class="nav-badge" id="pendingPaymentsCount">0</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('communication')">
                        <i class="fas fa-comments nav-icon"></i>
                        <span class="nav-text">التواصل</span>
                        <span class="nav-badge" id="unreadMessagesCount">0</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('reports')">
                        <i class="fas fa-file-alt nav-icon"></i>
                        <span class="nav-text">التقارير</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('settings')">
                        <i class="fas fa-cog nav-icon"></i>
                        <span class="nav-text">الإعدادات</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="logout()">
                        <i class="fas fa-sign-out-alt nav-icon"></i>
                        <span class="nav-text">تسجيل الخروج</span>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- رأس الصفحة -->
        <header class="header">
            <div class="header-left">
                <button class="header-btn" onclick="toggleSidebar()" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div>
                    <h1 class="page-title" id="pageTitle">نظرة عامة</h1>
                    <div class="breadcrumb" id="breadcrumb">الرئيسية / نظرة عامة</div>
                </div>
            </div>

            <div class="header-right">
                <button class="header-btn notification-btn" onclick="showNotifications()">
                    <i class="fas fa-bell"></i>
                    <span class="notification-count" id="notificationCount">2</span>
                </button>

                <button class="header-btn" onclick="showQuickActions()">
                    <i class="fas fa-plus"></i>
                    إجراء سريع
                </button>

                <button class="header-btn" onclick="exportParentReport()">
                    <i class="fas fa-download"></i>
                    تصدير التقرير
                </button>
            </div>
        </header>

        <!-- المحتوى الرئيسي -->
        <main class="content">
            <!-- قسم النظرة العامة -->
            <div id="overviewSection" class="content-section active">
                <!-- بطاقات الإحصائيات -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">عدد الأطفال</div>
                            <div class="stat-icon">
                                <i class="fas fa-child"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="totalChildren">0</div>
                        <div class="stat-change neutral">
                            <i class="fas fa-users"></i>
                            <span>مسجلين في الأكاديمية</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">متوسط الحضور</div>
                            <div class="stat-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="avgAttendance">0%</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+3% من الشهر الماضي</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">متوسط الأداء</div>
                            <div class="stat-icon">
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="avgPerformance">0</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+5 نقاط هذا الشهر</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">المدفوعات المعلقة</div>
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="pendingAmount">0</div>
                        <div class="stat-change negative">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>ريال سعودي</span>
                        </div>
                    </div>
                </div>

                <!-- رسم بياني للتقدم -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">تقدم الأطفال (آخر 6 أشهر)</h3>
                        <div class="card-actions">
                            <button class="btn" onclick="changeChartPeriod('3months')">3 أشهر</button>
                            <button class="btn primary" onclick="changeChartPeriod('6months')">6 أشهر</button>
                            <button class="btn" onclick="changeChartPeriod('1year')">سنة</button>
                        </div>
                    </div>
                    <div style="position: relative; height: 300px;">
                        <canvas id="progressChart"></canvas>
                    </div>
                </div>

                <!-- الحصص القادمة -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">الحصص القادمة لأطفالي</h3>
                        <div class="card-actions">
                            <button class="btn primary" onclick="viewFullSchedule()">
                                <i class="fas fa-calendar"></i>
                                عرض الجدول الكامل
                            </button>
                        </div>
                    </div>
                    <div id="upcomingSessionsList">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <!-- الأقسام الأخرى -->
            <div id="childrenSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">أطفالي</h3>
                    </div>
                    <div id="childrenContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <div id="progressSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">متابعة التقدم</h3>
                    </div>
                    <div id="progressContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <div id="scheduleSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">الجدول الزمني</h3>
                    </div>
                    <div id="scheduleContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <div id="paymentsSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">المدفوعات</h3>
                    </div>
                    <div id="paymentsContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <div id="communicationSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">التواصل مع المدربين</h3>
                    </div>
                    <div id="communicationContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <div id="reportsSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">التقارير</h3>
                    </div>
                    <div id="reportsContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <div id="settingsSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">الإعدادات</h3>
                    </div>
                    <div id="settingsContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // ==================== متغيرات عامة ====================
        let currentParent = null;
        let parentData = {};
        let progressChart = null;

        // ==================== بيانات أولياء الأمور التجريبية ====================
        const parentsDatabase = {
            'PAR001': {
                id: 'PAR001',
                name: 'أحمد والد محمد',
                email: '<EMAIL>',
                phone: '0501234567',
                children: ['P001'],
                joinDate: '2024-01-15',
                avatar: null,
                preferredContact: 'whatsapp',
                emergencyContact: '0509876543',
                address: 'الرياض، حي النرجس'
            },
            'PAR002': {
                id: 'PAR002',
                name: 'سارة والدة فاطمة',
                email: '<EMAIL>',
                phone: '0551234567',
                children: ['P002'],
                joinDate: '2024-02-10',
                avatar: null,
                preferredContact: 'email',
                emergencyContact: '0559876543',
                address: 'الرياض، حي الملقا'
            },
            'PAR003': {
                id: 'PAR003',
                name: 'محمد والد أحمد',
                email: '<EMAIL>',
                phone: '0556789012',
                children: ['P003'],
                joinDate: '2024-03-05',
                avatar: null,
                preferredContact: 'sms',
                emergencyContact: '0555555555',
                address: 'الرياض، حي العليا'
            },
            'PAR004': {
                id: 'PAR004',
                name: 'فاطمة والدة سلمى وعلي',
                email: '<EMAIL>',
                phone: '0557777777',
                children: ['P004', 'P005'],
                joinDate: '2024-01-20',
                avatar: null,
                preferredContact: 'whatsapp',
                emergencyContact: '0558888888',
                address: 'الرياض، حي الروضة'
            },
            'PAR005': {
                id: 'PAR005',
                name: 'عبدالله والد نورا',
                email: '<EMAIL>',
                phone: '0559999999',
                children: ['P006'],
                joinDate: '2024-04-01',
                avatar: null,
                preferredContact: 'email',
                emergencyContact: '0551111111',
                address: 'الرياض، حي الياسمين'
            }
        };

        // ==================== بيانات الأطفال المرتبطين بأولياء الأمور ====================
        const childrenDatabase = {
            'P001': {
                id: 'P001',
                name: 'محمد اللاعب',
                parentId: 'PAR001',
                coachId: 'C001',
                age: 18,
                level: 'متقدم',
                sport: 'كرة القدم',
                joinDate: '2024-01-15',
                attendanceRate: 85,
                performanceScore: 92,
                lastEvaluation: '2024-06-20',
                nextEvaluation: '2024-07-20',
                monthlyFee: 500,
                subscriptionStatus: 'نشط',
                subscriptionEnd: '2024-12-31'
            },
            'P002': {
                id: 'P002',
                name: 'فاطمة سعد الدين',
                parentId: 'PAR002',
                coachId: 'C001',
                age: 14,
                level: 'متوسط',
                sport: 'كرة القدم',
                joinDate: '2024-02-10',
                attendanceRate: 78,
                performanceScore: 88,
                lastEvaluation: '2024-06-15',
                nextEvaluation: '2024-07-15',
                monthlyFee: 450,
                subscriptionStatus: 'نشط',
                subscriptionEnd: '2024-11-30'
            },
            'P003': {
                id: 'P003',
                name: 'أحمد عبدالله',
                parentId: 'PAR003',
                coachId: 'C001',
                age: 16,
                level: 'مبتدئ',
                sport: 'كرة القدم',
                joinDate: '2024-03-05',
                attendanceRate: 65,
                performanceScore: 75,
                lastEvaluation: '2024-06-10',
                nextEvaluation: '2024-07-10',
                monthlyFee: 400,
                subscriptionStatus: 'معلق',
                subscriptionEnd: '2024-10-15'
            }
        };

        // ==================== بيانات المدفوعات ====================
        const paymentsDatabase = [
            {
                id: 'PAY001',
                childId: 'P001',
                parentId: 'PAR001',
                amount: 500,
                dueDate: '2024-07-01',
                status: 'معلقة',
                type: 'اشتراك شهري',
                description: 'اشتراك شهر يوليو 2024 - محمد اللاعب',
                invoiceNumber: 'INV-2024-001'
            },
            {
                id: 'PAY002',
                childId: 'P001',
                parentId: 'PAR001',
                amount: 500,
                dueDate: '2024-06-01',
                status: 'مدفوعة',
                type: 'اشتراك شهري',
                description: 'اشتراك شهر يونيو 2024 - محمد اللاعب',
                invoiceNumber: 'INV-2024-002',
                paidDate: '2024-05-28',
                paymentMethod: 'تحويل بنكي'
            }
        ];

        // ==================== بيانات الرسائل ====================
        const messagesDatabase = [
            {
                id: 'MSG001',
                parentId: 'PAR001',
                from: 'كابتن أحمد المدرب',
                fromType: 'coach',
                subject: 'تحسن ملحوظ في أداء محمد',
                message: 'أود أن أبلغكم بالتحسن الملحوظ في أداء محمد خلال الأسبوع الماضي. لقد أظهر تطوراً كبيراً في مهارات التسديد.',
                date: '2024-06-22',
                time: '14:30',
                isRead: false,
                priority: 'عادية',
                childId: 'P001'
            },
            {
                id: 'MSG002',
                parentId: 'PAR001',
                from: 'إدارة الأكاديمية',
                fromType: 'admin',
                subject: 'تذكير بموعد المباراة',
                message: 'نذكركم بموعد المباراة الودية لفريق محمد يوم السبت الساعة 6 مساءً.',
                date: '2024-06-21',
                time: '10:15',
                isRead: false,
                priority: 'مهمة',
                childId: 'P001'
            }
        ];

        // ==================== التهيئة عند تحميل الصفحة ====================
        document.addEventListener('DOMContentLoaded', function() {
            initializeParentDashboard();
            loadParentSession();
            updateDashboard();
            setupEventListeners();

            console.log('👨‍👩‍👧‍👦 لوحة تحكم ولي الأمر جاهزة!');
        });

        function initializeParentDashboard() {
            // تحميل البيانات من localStorage أو إنشاء بيانات تجريبية
            const savedData = localStorage.getItem('parentDashboardData');
            if (savedData) {
                parentData = JSON.parse(savedData);
            } else {
                // إنشاء بيانات تجريبية
                parentData = {
                    parents: parentsDatabase,
                    children: childrenDatabase,
                    payments: paymentsDatabase,
                    messages: messagesDatabase
                };
                saveParentData();
            }
        }

        function loadParentSession() {
            // تحميل بيانات ولي الأمر من الجلسة
            const sessionData = sessionStorage.getItem('userSession');
            if (sessionData) {
                try {
                    const decryptedData = decryptData(sessionData);
                    if (decryptedData && decryptedData.user.role === 'parent') {
                        currentParent = findParentByEmail(decryptedData.user.email);
                        if (currentParent) {
                            updateParentInfo();
                        }
                    }
                } catch (error) {
                    console.error('خطأ في تحميل بيانات الجلسة:', error);
                    // للاختبار - تحميل بيانات افتراضية
                    currentParent = parentData.parents['PAR001'];
                    updateParentInfo();
                }
            } else {
                // للاختبار - تحميل بيانات افتراضية
                currentParent = parentData.parents['PAR001'];
                updateParentInfo();
            }
        }

        function findParentByEmail(email) {
            for (let parentId in parentData.parents) {
                if (parentData.parents[parentId].email === email) {
                    return parentData.parents[parentId];
                }
            }
            return null;
        }

        function decryptData(encryptedData) {
            try {
                const bytes = CryptoJS.AES.decrypt(encryptedData, 'academy7c_secret');
                return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
            } catch (e) {
                throw new Error('فشل في فك التشفير');
            }
        }

        function saveParentData() {
            localStorage.setItem('parentDashboardData', JSON.stringify(parentData));
        }

        // ==================== تحديث معلومات ولي الأمر ====================
        function updateParentInfo() {
            if (!currentParent) return;

            document.getElementById('parentName').textContent = currentParent.name;

            // تحديث الإحصائيات
            const children = currentParent.children.map(id => parentData.children[id]).filter(c => c);
            const totalChildren = children.length;

            let totalAttendance = 0;
            let totalPerformance = 0;
            let pendingAmount = 0;

            children.forEach(child => {
                totalAttendance += child.attendanceRate;
                totalPerformance += child.performanceScore;
            });

            const avgAttendance = totalChildren > 0 ? Math.round(totalAttendance / totalChildren) : 0;
            const avgPerformance = totalChildren > 0 ? Math.round(totalPerformance / totalChildren) : 0;

            // حساب المدفوعات المعلقة
            const parentPayments = parentData.payments.filter(p => p.parentId === currentParent.id && p.status === 'معلقة');
            pendingAmount = parentPayments.reduce((sum, p) => sum + p.amount, 0);

            document.getElementById('totalChildren').textContent = totalChildren;
            document.getElementById('avgAttendance').textContent = avgAttendance + '%';
            document.getElementById('avgPerformance').textContent = avgPerformance;
            document.getElementById('pendingAmount').textContent = pendingAmount;

            // تحديث الشارات
            updateNavigationBadges();
        }

        function updateNavigationBadges() {
            if (!currentParent) return;

            const children = currentParent.children || [];
            document.getElementById('childrenCount').textContent = children.length;

            // حساب الحصص القادمة
            const upcomingSessions = children.reduce((count, childId) => {
                // محاكاة حساب الحصص القادمة
                return count + 2; // كل طفل لديه حصتان قادمتان
            }, 0);
            document.getElementById('upcomingSessionsCount').textContent = upcomingSessions;

            // حساب المدفوعات المعلقة
            const pendingPayments = parentData.payments.filter(p =>
                p.parentId === currentParent.id && p.status === 'معلقة'
            );
            document.getElementById('pendingPaymentsCount').textContent = pendingPayments.length;

            // حساب الرسائل غير المقروءة
            const unreadMessages = parentData.messages.filter(m =>
                m.parentId === currentParent.id && !m.isRead
            );
            document.getElementById('unreadMessagesCount').textContent = unreadMessages.length;
        }

        // ==================== تحديث لوحة المعلومات ====================
        function updateDashboard() {
            updateUpcomingSessionsList();
            updateChildrenContent();
            updateProgressContent();
            updateScheduleContent();
            updatePaymentsContent();
            updateCommunicationContent();
            updateReportsContent();
            updateSettingsContent();
            createProgressChart();
        }

        function updateUpcomingSessionsList() {
            if (!currentParent) return;

            const container = document.getElementById('upcomingSessionsList');
            const children = currentParent.children.map(id => parentData.children[id]).filter(c => c);

            if (children.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">لا توجد حصص قادمة</p>';
                return;
            }

            // محاكاة حصص قادمة لكل طفل
            const upcomingSessions = children.map(child => ({
                childName: child.name,
                title: `تدريب ${child.sport}`,
                coach: child.coachId === 'C001' ? 'كابتن أحمد المدرب' :
                       child.coachId === 'C002' ? 'كابتن سارة المدربة' : 'كابتن محمد الرياضي',
                date: '2024-06-25',
                time: '16:00',
                location: 'الملعب الرئيسي',
                type: 'جماعي'
            }));

            container.innerHTML = upcomingSessions.map(session => `
                <div style="
                    background: rgba(255,255,255,0.05);
                    border: 1px solid var(--border);
                    border-radius: 12px;
                    padding: 1rem;
                    margin-bottom: 1rem;
                    transition: all 0.3s ease;
                " onmouseover="this.style.background='rgba(255,255,255,0.1)'"
                   onmouseout="this.style.background='rgba(255,255,255,0.05)'">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                        <h4 style="color: var(--text-primary); font-size: 1rem;">${session.title}</h4>
                        <span style="
                            background: var(--brand-primary);
                            color: white;
                            padding: 0.2rem 0.5rem;
                            border-radius: 6px;
                            font-size: 0.8rem;
                        ">${session.childName}</span>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; font-size: 0.9rem; color: var(--text-secondary);">
                        <div><i class="fas fa-calendar" style="margin-left: 0.5rem;"></i>${formatDate(session.date)}</div>
                        <div><i class="fas fa-clock" style="margin-left: 0.5rem;"></i>${session.time}</div>
                        <div><i class="fas fa-user-tie" style="margin-left: 0.5rem;"></i>${session.coach}</div>
                        <div><i class="fas fa-map-marker-alt" style="margin-left: 0.5rem;"></i>${session.location}</div>
                    </div>
                    <div style="margin-top: 1rem;">
                        <button onclick="sendMessageToCoach('${session.coach}')" style="
                            background: var(--info);
                            color: white;
                            border: none;
                            padding: 0.4rem 0.8rem;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 0.8rem;
                            margin-left: 0.5rem;
                        ">رسالة للمدرب</button>
                        <button onclick="requestAbsence('${session.childName}')" style="
                            background: var(--warning);
                            color: white;
                            border: none;
                            padding: 0.4rem 0.8rem;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 0.8rem;
                        ">طلب غياب</button>
                    </div>
                </div>
            `).join('');
        }

        function updateChildrenContent() {
            if (!currentParent) return;

            const children = currentParent.children.map(id => parentData.children[id]).filter(c => c);
            const container = document.getElementById('childrenContent');

            if (children.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                        <i class="fas fa-child" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>لا يوجد أطفال مسجلون</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = children.map(child => `
                <div style="
                    background: rgba(255,255,255,0.05);
                    border: 1px solid var(--border);
                    border-radius: 12px;
                    padding: 1.5rem;
                    margin-bottom: 1rem;
                    transition: all 0.3s ease;
                " onmouseover="this.style.background='rgba(255,255,255,0.1)'"
                   onmouseout="this.style.background='rgba(255,255,255,0.05)'">

                    <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 1rem;">
                        <div style="display: flex; align-items: center; gap: 1rem;">
                            <div style="
                                width: 60px;
                                height: 60px;
                                background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 1.5rem;
                                color: white;
                            ">
                                <i class="fas fa-child"></i>
                            </div>
                            <div>
                                <h4 style="color: var(--text-primary); font-size: 1.1rem; margin-bottom: 0.5rem;">
                                    ${child.name}
                                </h4>
                                <div style="display: flex; align-items: center; gap: 0.5rem;">
                                    <span style="
                                        background: var(--brand-primary);
                                        color: white;
                                        padding: 0.2rem 0.6rem;
                                        border-radius: 12px;
                                        font-size: 0.8rem;
                                    ">${child.sport}</span>
                                    <span style="
                                        background: ${child.subscriptionStatus === 'نشط' ? 'var(--success)' : 'var(--warning)'};
                                        color: white;
                                        padding: 0.2rem 0.6rem;
                                        border-radius: 12px;
                                        font-size: 0.8rem;
                                    ">${child.subscriptionStatus}</span>
                                    <span style="color: var(--text-secondary); font-size: 0.9rem;">
                                        ${child.age} سنة - ${child.level}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: left;">
                            <div style="color: var(--success); font-size: 1.2rem; font-weight: bold;">
                                ${child.performanceScore}/100
                            </div>
                            <div style="color: var(--text-secondary); font-size: 0.8rem;">
                                درجة الأداء
                            </div>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                        <div style="text-align: center; padding: 0.5rem; background: rgba(40, 167, 69, 0.1); border-radius: 8px;">
                            <div style="color: #28a745; font-weight: bold;">${child.attendanceRate}%</div>
                            <div style="color: var(--text-secondary); font-size: 0.8rem;">معدل الحضور</div>
                        </div>
                        <div style="text-align: center; padding: 0.5rem; background: rgba(23, 162, 184, 0.1); border-radius: 8px;">
                            <div style="color: #17a2b8; font-weight: bold;">${formatDate(child.lastEvaluation)}</div>
                            <div style="color: var(--text-secondary); font-size: 0.8rem;">آخر تقييم</div>
                        </div>
                        <div style="text-align: center; padding: 0.5rem; background: rgba(255, 193, 7, 0.1); border-radius: 8px;">
                            <div style="color: #ffc107; font-weight: bold;">${child.monthlyFee} ريال</div>
                            <div style="color: var(--text-secondary); font-size: 0.8rem;">الرسوم الشهرية</div>
                        </div>
                    </div>

                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                        <button onclick="viewChildProgress('${child.id}')" style="
                            background: var(--brand-primary);
                            color: white;
                            border: none;
                            padding: 0.5rem 1rem;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 0.9rem;
                            display: flex;
                            align-items: center;
                            gap: 0.3rem;
                        ">
                            <i class="fas fa-chart-line"></i>
                            عرض التقدم
                        </button>

                        <button onclick="viewChildSchedule('${child.id}')" style="
                            background: var(--info);
                            color: white;
                            border: none;
                            padding: 0.5rem 1rem;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 0.9rem;
                            display: flex;
                            align-items: center;
                            gap: 0.3rem;
                        ">
                            <i class="fas fa-calendar"></i>
                            الجدول الزمني
                        </button>

                        <button onclick="contactCoach('${child.coachId}')" style="
                            background: var(--success);
                            color: white;
                            border: none;
                            padding: 0.5rem 1rem;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 0.9rem;
                            display: flex;
                            align-items: center;
                            gap: 0.3rem;
                        ">
                            <i class="fas fa-envelope"></i>
                            التواصل مع المدرب
                        </button>

                        <button onclick="generateChildReport('${child.id}')" style="
                            background: transparent;
                            color: var(--text-secondary);
                            border: 1px solid var(--border);
                            padding: 0.5rem 1rem;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 0.9rem;
                            display: flex;
                            align-items: center;
                            gap: 0.3rem;
                        ">
                            <i class="fas fa-file-alt"></i>
                            تقرير شامل
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // ==================== وظائف مساعدة ====================
        function formatDate(dateString) {
            const date = new Date(dateString);
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            };
            return date.toLocaleDateString('ar-SA', options);
        }

        function showSection(sectionName) {
            // إخفاء جميع الأقسام
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
                section.classList.remove('active');
            });

            // إزالة الحالة النشطة من جميع الروابط
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // إظهار القسم المحدد
            const targetSection = document.getElementById(sectionName + 'Section');
            if (targetSection) {
                targetSection.style.display = 'block';
                targetSection.classList.add('active');
            }

            // تفعيل الرابط المحدد
            event.target.closest('.nav-link').classList.add('active');

            // تحديث عنوان الصفحة
            updatePageTitle(sectionName);
        }

        function updatePageTitle(sectionName) {
            const titles = {
                'overview': 'نظرة عامة',
                'children': 'أطفالي',
                'progress': 'متابعة التقدم',
                'schedule': 'الجدول الزمني',
                'payments': 'المدفوعات',
                'communication': 'التواصل',
                'reports': 'التقارير',
                'settings': 'الإعدادات'
            };

            const title = titles[sectionName] || 'لوحة التحكم';
            document.getElementById('pageTitle').textContent = title;
            document.getElementById('breadcrumb').textContent = `الرئيسية / ${title}`;
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }

        function createProgressChart() {
            const ctx = document.getElementById('progressChart');
            if (!ctx) return;

            // بيانات تجريبية لتقدم الأطفال
            const progressData = [75, 78, 82, 85, 88, 92];
            const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'];

            if (progressChart) {
                progressChart.destroy();
            }

            progressChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: months,
                    datasets: [{
                        label: 'متوسط تقدم الأطفال',
                        data: progressData,
                        borderColor: '#8B4513',
                        backgroundColor: 'rgba(139, 69, 19, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#D2691E',
                        pointBorderColor: '#8B4513',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff',
                                font: {
                                    family: 'Cairo',
                                    size: 12
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 60,
                            max: 100,
                            ticks: {
                                color: '#cccccc',
                                font: {
                                    family: 'Cairo'
                                }
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: '#cccccc',
                                font: {
                                    family: 'Cairo'
                                }
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    }
                }
            });
        }

        // ==================== وظائف أساسية أخرى ====================
        function updateProgressContent() {
            const container = document.getElementById('progressContent');
            container.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">قسم متابعة التقدم قيد التطوير...</p>';
        }

        function updateScheduleContent() {
            const container = document.getElementById('scheduleContent');
            container.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">قسم الجدول الزمني قيد التطوير...</p>';
        }

        function updatePaymentsContent() {
            const container = document.getElementById('paymentsContent');
            container.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">قسم المدفوعات قيد التطوير...</p>';
        }

        function updateCommunicationContent() {
            const container = document.getElementById('communicationContent');
            container.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">قسم التواصل قيد التطوير...</p>';
        }

        function updateReportsContent() {
            const container = document.getElementById('reportsContent');
            container.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">قسم التقارير قيد التطوير...</p>';
        }

        function updateSettingsContent() {
            const container = document.getElementById('settingsContent');
            container.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">قسم الإعدادات قيد التطوير...</p>';
        }

        function setupEventListeners() {
            // إضافة مستمعي الأحداث للتفاعل
            document.addEventListener('keydown', function(e) {
                if (e.altKey) {
                    switch(e.key) {
                        case '1': showSection('overview'); break;
                        case '2': showSection('children'); break;
                        case '3': showSection('progress'); break;
                        case '4': showSection('schedule'); break;
                        case '5': showSection('payments'); break;
                        case '6': showSection('communication'); break;
                        case '7': showSection('reports'); break;
                        case '8': showSection('settings'); break;
                    }
                }
            });
        }

        function showNotifications() {
            Swal.fire({
                title: 'الإشعارات',
                html: `
                    <div style="text-align: right;">
                        <div style="padding: 1rem; border-bottom: 1px solid #333;">
                            <strong>تحسن في الأداء</strong><br>
                            <small style="color: #888;">محمد حقق تقدماً ممتازاً هذا الأسبوع</small>
                        </div>
                        <div style="padding: 1rem;">
                            <strong>مباراة قادمة</strong><br>
                            <small style="color: #888;">مباراة ودية يوم السبت الساعة 6 مساءً</small>
                        </div>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                confirmButtonText: 'إغلاق'
            });
        }

        function showQuickActions() {
            Swal.fire({
                title: 'إجراءات سريعة',
                html: `
                    <div style="display: grid; gap: 1rem; text-align: center;">
                        <button onclick="contactCoach('C001')" class="swal2-confirm swal2-styled">التواصل مع المدرب</button>
                        <button onclick="requestAbsence('P001')" class="swal2-confirm swal2-styled">طلب غياب</button>
                        <button onclick="viewChildProgress('P001')" class="swal2-confirm swal2-styled">عرض تقدم الطفل</button>
                        <button onclick="exportParentReport()" class="swal2-confirm swal2-styled">تصدير تقرير</button>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                showConfirmButton: false,
                showCancelButton: true,
                cancelButtonText: 'إغلاق'
            });
        }

        function exportParentReport() {
            Swal.fire({
                title: 'تصدير تقرير ولي الأمر',
                text: 'جاري إنشاء التقرير...',
                background: '#1a1a1a',
                color: '#ffffff',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                    setTimeout(() => {
                        Swal.close();
                        Swal.fire({
                            title: 'تم إنشاء التقرير!',
                            text: 'تم تحميل تقرير ولي الأمر بنجاح',
                            icon: 'success',
                            background: '#1a1a1a',
                            color: '#ffffff'
                        });
                    }, 2000);
                }
            });
        }

        function logout() {
            Swal.fire({
                title: 'تسجيل الخروج',
                text: 'هل أنت متأكد من تسجيل الخروج؟',
                icon: 'question',
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'نعم، سجل خروجي',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    sessionStorage.removeItem('userSession');
                    window.location.href = 'login.html';
                }
            });
        }

        // ==================== وظائف تفاعلية إضافية ====================
        function contactCoach(coachId) {
            Swal.fire({
                title: 'التواصل مع المدرب',
                text: 'سيتم تطوير هذه الميزة قريباً',
                icon: 'info',
                background: '#1a1a1a',
                color: '#ffffff'
            });
        }

        function requestAbsence(childId) {
            Swal.fire({
                title: 'طلب غياب',
                text: 'سيتم تطوير هذه الميزة قريباً',
                icon: 'info',
                background: '#1a1a1a',
                color: '#ffffff'
            });
        }

        function viewChildProgress(childId) {
            showSection('progress');
        }

        function viewChildSchedule(childId) {
            showSection('schedule');
        }

        function generateChildReport(childId) {
            Swal.fire({
                title: 'تقرير شامل',
                text: 'سيتم تطوير هذه الميزة قريباً',
                icon: 'info',
                background: '#1a1a1a',
                color: '#ffffff'
            });
        }

        // ==================== نظام تخصيص الألوان ====================
        let isNightMode = false;
        let currentColorSettings = {};

        // قوالب الألوان الجاهزة
        const colorPresets = {
            blue: {
                '--brand-primary': '#1e40af',
                '--brand-secondary': '#3b82f6',
                '--accent-color': '#60a5fa',
                '--primary-bg': '#1a1a1a',
                '--secondary-bg': '#2d2d2d',
                '--accent-dark': '#1e293b'
            },
            green: {
                '--brand-primary': '#059669',
                '--brand-secondary': '#10b981',
                '--accent-color': '#34d399',
                '--primary-bg': '#1a1a1a',
                '--secondary-bg': '#2d2d2d',
                '--accent-dark': '#1e3a2e'
            },
            orange: {
                '--brand-primary': '#ea580c',
                '--brand-secondary': '#f97316',
                '--accent-color': '#fb923c',
                '--primary-bg': '#1a1a1a',
                '--secondary-bg': '#2d2d2d',
                '--accent-dark': '#3a2317'
            },
            purple: {
                '--brand-primary': '#7c3aed',
                '--brand-secondary': '#8b5cf6',
                '--accent-color': '#a78bfa',
                '--primary-bg': '#1a1a1a',
                '--secondary-bg': '#2d2d2d',
                '--accent-dark': '#2e1065'
            },
            red: {
                '--brand-primary': '#dc2626',
                '--brand-secondary': '#ef4444',
                '--accent-color': '#f87171',
                '--primary-bg': '#1a1a1a',
                '--secondary-bg': '#2d2d2d',
                '--accent-dark': '#3f1f1f'
            },
            dark: {
                '--brand-primary': '#374151',
                '--brand-secondary': '#4b5563',
                '--accent-color': '#6b7280',
                '--primary-bg': '#111827',
                '--secondary-bg': '#1f2937',
                '--accent-dark': '#0f172a'
            }
        };

        // تبديل أداة تخصيص الألوان
        function toggleColorCustomizer() {
            const customizer = document.getElementById('colorCustomizer');
            customizer.classList.toggle('active');
        }

        // تحديث لون معين
        function updateColor(property, value) {
            document.documentElement.style.setProperty(property, value);
            currentColorSettings[property] = value;
            updateDerivedColors();
            saveColorSettings();
        }

        // تحديث الألوان المشتقة
        function updateDerivedColors() {
            const primary = currentColorSettings['--brand-primary'] || getComputedStyle(document.documentElement).getPropertyValue('--brand-primary');
            const secondary = currentColorSettings['--brand-secondary'] || getComputedStyle(document.documentElement).getPropertyValue('--brand-secondary');

            document.documentElement.style.setProperty('--button-bg', `linear-gradient(135deg, ${primary}, ${secondary})`);
            document.documentElement.style.setProperty('--logo-bg', `linear-gradient(135deg, ${primary}, ${secondary})`);
            document.documentElement.style.setProperty('--shadow-glow', `0 0 20px ${primary}33`);
        }

        // تطبيق قالب ألوان جاهز
        function applyPreset(presetName) {
            const preset = colorPresets[presetName];
            if (!preset) return;

            Object.entries(preset).forEach(([property, value]) => {
                document.documentElement.style.setProperty(property, value);
                currentColorSettings[property] = value;

                const inputId = property.replace('--', '').replace(/-([a-z])/g, (g) => g[1].toUpperCase());
                const input = document.getElementById(inputId);
                if (input) {
                    input.value = value;
                }
            });

            updateDerivedColors();
            saveColorSettings();

            Swal.fire({
                title: 'تم تطبيق القالب!',
                text: `تم تطبيق قالب الألوان بنجاح`,
                icon: 'success',
                background: '#1a1a1a',
                color: '#ffffff',
                timer: 2000,
                showConfirmButton: false
            });
        }

        // حفظ إعدادات الألوان
        function saveColorSettings() {
            try {
                localStorage.setItem('7c_parent_color_settings', JSON.stringify(currentColorSettings));
                localStorage.setItem('7c_parent_night_mode', isNightMode);
            } catch (error) {
                console.error('خطأ في حفظ إعدادات الألوان:', error);
            }
        }

        // تحميل إعدادات الألوان
        function loadColorSettings() {
            try {
                const savedSettings = localStorage.getItem('7c_parent_color_settings');
                const savedNightMode = localStorage.getItem('7c_parent_night_mode');

                if (savedSettings) {
                    currentColorSettings = JSON.parse(savedSettings);
                    Object.entries(currentColorSettings).forEach(([property, value]) => {
                        document.documentElement.style.setProperty(property, value);

                        const inputId = property.replace('--', '').replace(/-([a-z])/g, (g) => g[1].toUpperCase());
                        const input = document.getElementById(inputId);
                        if (input) {
                            input.value = value;
                        }
                    });
                    updateDerivedColors();
                }

                if (savedNightMode === 'true') {
                    isNightMode = true;
                    document.querySelector('.theme-toggle').classList.add('night');
                    applyNightMode();
                }
            } catch (error) {
                console.error('خطأ في تحميل إعدادات الألوان:', error);
            }
        }

        // إعادة تعيين الألوان
        function resetColors() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الألوان للإعدادات الافتراضية؟')) {
                localStorage.removeItem('7c_parent_color_settings');
                localStorage.removeItem('7c_parent_night_mode');
                location.reload();
            }
        }

        // تصدير إعدادات الألوان
        function exportColorSettings() {
            try {
                const exportData = {
                    colorSettings: currentColorSettings,
                    nightMode: isNightMode,
                    exportDate: new Date().toISOString(),
                    version: '1.0'
                };

                const dataStr = JSON.stringify(exportData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `7c_parent_colors_${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                Swal.fire({
                    title: 'تم التصدير!',
                    text: 'تم تصدير إعدادات الألوان بنجاح',
                    icon: 'success',
                    background: '#1a1a1a',
                    color: '#ffffff'
                });
            } catch (error) {
                console.error('خطأ في تصدير الإعدادات:', error);
                Swal.fire({
                    title: 'خطأ!',
                    text: 'حدث خطأ في تصدير الإعدادات',
                    icon: 'error',
                    background: '#1a1a1a',
                    color: '#ffffff'
                });
            }
        }

        // استيراد إعدادات الألوان
        function importColorSettings() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';

            input.onchange = (e) => {
                const file = e.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const importData = JSON.parse(e.target.result);

                        if (importData.colorSettings) {
                            currentColorSettings = importData.colorSettings;
                            Object.entries(currentColorSettings).forEach(([property, value]) => {
                                document.documentElement.style.setProperty(property, value);

                                const inputId = property.replace('--', '').replace(/-([a-z])/g, (g) => g[1].toUpperCase());
                                const input = document.getElementById(inputId);
                                if (input) {
                                    input.value = value;
                                }
                            });
                            updateDerivedColors();
                            saveColorSettings();

                            Swal.fire({
                                title: 'تم الاستيراد!',
                                text: 'تم استيراد إعدادات الألوان بنجاح',
                                icon: 'success',
                                background: '#1a1a1a',
                                color: '#ffffff'
                            });
                        }

                        if (importData.nightMode !== undefined) {
                            isNightMode = importData.nightMode;
                            const toggle = document.querySelector('.theme-toggle');
                            if (isNightMode) {
                                toggle.classList.add('night');
                                applyNightMode();
                            } else {
                                toggle.classList.remove('night');
                                removeNightMode();
                            }
                        }

                    } catch (error) {
                        console.error('خطأ في قراءة الملف:', error);
                        Swal.fire({
                            title: 'خطأ!',
                            text: 'ملف غير صالح',
                            icon: 'error',
                            background: '#1a1a1a',
                            color: '#ffffff'
                        });
                    }
                };

                reader.readAsText(file);
            };

            input.click();
        }

        // تبديل الوضع الليلي/النهاري
        function toggleTheme() {
            isNightMode = !isNightMode;
            const toggle = document.querySelector('.theme-toggle');

            if (isNightMode) {
                toggle.classList.add('night');
                applyNightMode();
                Swal.fire({
                    title: 'الوضع الليلي',
                    text: 'تم تفعيل الوضع الليلي',
                    icon: 'info',
                    background: '#1a1a1a',
                    color: '#ffffff',
                    timer: 1500,
                    showConfirmButton: false
                });
            } else {
                toggle.classList.remove('night');
                removeNightMode();
                Swal.fire({
                    title: 'الوضع النهاري',
                    text: 'تم تفعيل الوضع النهاري',
                    icon: 'info',
                    background: '#1a1a1a',
                    color: '#ffffff',
                    timer: 1500,
                    showConfirmButton: false
                });
            }

            saveColorSettings();
        }

        // تطبيق الوضع الليلي
        function applyNightMode() {
            document.documentElement.style.setProperty('--primary-bg', '#0f172a');
            document.documentElement.style.setProperty('--secondary-bg', '#1e293b');
            document.documentElement.style.setProperty('--accent-dark', '#334155');
            document.documentElement.style.setProperty('--text-primary', '#f1f5f9');
            document.documentElement.style.setProperty('--text-secondary', '#cbd5e1');
            document.documentElement.style.setProperty('--text-muted', '#64748b');
            document.documentElement.style.setProperty('--glass', 'rgba(255,255,255,0.05)');
            document.documentElement.style.setProperty('--glass-strong', 'rgba(255,255,255,0.1)');
            document.documentElement.style.setProperty('--border', 'rgba(255,255,255,0.1)');
            document.documentElement.style.setProperty('--border-strong', 'rgba(255,255,255,0.2)');
        }

        // إزالة الوضع الليلي
        function removeNightMode() {
            document.documentElement.style.setProperty('--primary-bg', '#1a1a1a');
            document.documentElement.style.setProperty('--secondary-bg', '#2d2d2d');
            document.documentElement.style.setProperty('--accent-dark', '#1e293b');
            document.documentElement.style.setProperty('--text-primary', '#ffffff');
            document.documentElement.style.setProperty('--text-secondary', '#e2e8f0');
            document.documentElement.style.setProperty('--text-muted', '#94a3b8');
            document.documentElement.style.setProperty('--glass', 'rgba(255,255,255,0.1)');
            document.documentElement.style.setProperty('--glass-strong', 'rgba(255,255,255,0.15)');
            document.documentElement.style.setProperty('--border', 'rgba(255,255,255,0.2)');
            document.documentElement.style.setProperty('--border-strong', 'rgba(255,255,255,0.3)');
        }

        // إعادة تعيين للإعدادات الافتراضية
        function resetToDefaults() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات للافتراضية؟')) {
                resetColors();
            }
        }

        // تهيئة نظام الألوان عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل إعدادات الألوان المحفوظة
            loadColorSettings();

            // إغلاق أداة التخصيص عند النقر خارجها
            document.addEventListener('click', function(e) {
                const customizer = document.getElementById('colorCustomizer');
                const toggleBtn = e.target.closest('[onclick="toggleColorCustomizer()"]');

                if (!customizer.contains(e.target) && !toggleBtn && customizer.classList.contains('active')) {
                    customizer.classList.remove('active');
                }
            });

            // تحديث الألوان المشتقة عند التحميل
            setTimeout(() => {
                updateDerivedColors();
            }, 500);

            console.log('🎨 تم تحميل نظام تخصيص الألوان للوحة ولي الأمر!');
        });

        // حفظ الإعدادات عند إغلاق الصفحة
        window.addEventListener('beforeunload', function() {
            saveColorSettings();
        });

        console.log('🏆 لوحة تحكم ولي الأمر جاهزة!');
        console.log('🎨 نظام تخصيص الألوان الشامل متاح!');
    </script>
</body>
</html>
