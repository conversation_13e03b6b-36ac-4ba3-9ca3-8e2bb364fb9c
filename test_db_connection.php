<?php
// اختبار الاتصال بقاعدة البيانات
$host = 'localhost';
$dbname = 'new7cdata';
$username = 'komaro';
$password = 'Zd<PERSON>haker@14';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح!\n";
    
    // إنشاء جدول المدربين
    $createTableSQL = "
    CREATE TABLE IF NOT EXISTS coaches (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        phone VARCHAR(20) NOT NULL,
        specialization VARCHAR(100) NOT NULL,
        experience_years INT DEFAULT 0,
        certification TEXT,
        hire_date DATE NOT NULL,
        salary DECIMAL(10,2) DEFAULT 0.00,
        photo_url VARCHAR(500),
        bio TEXT,
        emergency_contact VARCHAR(255),
        address TEXT,
        status TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createTableSQL);
    echo "✅ تم إنشاء جدول المدربين بنجاح!\n";
    
    // التحقق من وجود بيانات
    $checkSQL = "SELECT COUNT(*) as count FROM coaches";
    $stmt = $pdo->prepare($checkSQL);
    $stmt->execute();
    $count = $stmt->fetch()['count'];
    
    echo "📊 عدد المدربين الحالي: $count\n";
    
    if ($count == 0) {
        echo "🔄 إضافة بيانات تجريبية...\n";
        
        // إضافة مدرب تجريبي
        $insertSQL = "INSERT INTO coaches (name, email, phone, specialization, experience_years, hire_date, salary, status) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($insertSQL);
        $result = $stmt->execute([
            'الكابتن أحمد محمد',
            '<EMAIL>',
            '+966501234567',
            'كرة القدم',
            5,
            '2024-01-01',
            5000,
            1
        ]);
        
        if ($result) {
            echo "✅ تم إضافة مدرب تجريبي بنجاح!\n";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ خطأ في الاتصال: " . $e->getMessage() . "\n";
}
?>
