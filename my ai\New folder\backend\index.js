const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

const app = express();
const PORT = 5000;

app.use(cors());
app.use(bodyParser.json());

// ذكاء اصطناعي بسيط (محلي)
function simpleAI(question) {
    // يمكنك تطوير هذه الدالة لاحقًا أو ربطها بذكاء اصطناعي مفتوح المصدر
    if (question.toLowerCase().includes('مرحبا')) return 'أهلاً بك! كيف يمكنني مساعدتك اليوم؟';
    if (question.toLowerCase().includes('اسمك')) return 'أنا مساعدك الذكي المجاني!';
    return 'عذراً، لم أفهم سؤالك. حاول مرة أخرى.';
}

async function askHuggingFace(question) {
    try {
        const response = await axios.post(
            'https://api-inference.huggingface.co/models/meta-llama/Llama-2-7b-chat-hf',
            { inputs: question },
            {
                headers: {
                    'Authorization': '', // بدون مفتاح API (محدود)
                    'Content-Type': 'application/json'
                },
                timeout: 20000
            }
        );
        if (response.data && response.data.length > 0 && response.data[0].generated_text) {
            return response.data[0].generated_text;
        } else if (response.data && response.data.generated_text) {
            return response.data.generated_text;
        } else {
            return 'لم أستطع الحصول على إجابة من الذكاء الاصطناعي المجاني.';
        }
    } catch (err) {
        return 'حدث خطأ أثناء الاتصال بواجهة الذكاء الاصطناعي المجانية.';
    }
}

app.post('/api/ask', async (req, res) => {
    const { question } = req.body;
    if (!question) return res.json({ answer: 'يرجى كتابة سؤال.' });
    const answer = await askHuggingFace(question);
    res.json({ answer });
});

// نقطة نهاية التثبيت (install)
app.post('/api/install', (req, res) => {
    const { adminName, adminPass, adminEmail } = req.body;
    if (!adminName || !adminPass || !adminEmail) {
        return res.json({ success: false, error: 'جميع الحقول مطلوبة.' });
    }
    // حفظ الإعدادات في ملف (يمكنك تعديل المسار لاحقًا)
    const configPath = path.join(__dirname, 'config.json');
    fs.writeFileSync(configPath, JSON.stringify({ adminName, adminPass, adminEmail }, null, 2));
    res.json({ success: true });
});

app.listen(PORT, () => {
    console.log(`AI Assistant backend running on http://localhost:${PORT}`);
});