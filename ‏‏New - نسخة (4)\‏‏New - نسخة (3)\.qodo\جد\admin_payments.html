<?php
session_start();
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
  header("Location: index.html");
  exit;
}

$host = "localhost";
$db = "academy7c";
$user = "root";
$pass = "";
$conn = new mysqli($host, $user, $pass, $db);
if ($conn->connect_error) {
  die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
}

// إنشاء جدول الإشعارات إن لم يكن موجودًا
$conn->query("CREATE TABLE IF NOT EXISTS notifications (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT,
  message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)");

// Export CSV
if (isset($_GET['export']) && $_GET['export'] === 'csv') {
  header('Content-Type: text/csv');
  header('Content-Disposition: attachment; filename="payments_export.csv"');
  $output = fopen("php://output", "w");
  fputcsv($output, ['اسم اللاعب', 'الشهر', 'قيمة الاشتراك', 'الحالة', 'تاريخ الدفع']);
  $sql_export = "SELECT users.fullname, payments.month, payments.amount, payments.status, payments.payment_date
                 FROM payments JOIN users ON payments.user_id = users.id";
  if (!empty($_GET['month'])) {
    $month = $_GET['month'];
    $sql_export .= " WHERE payments.month = '" . $conn->real_escape_string($month) . "'";
  }
  $res = $conn->query($sql_export);
  while ($r = $res->fetch_assoc()) {
    fputcsv($output, [$r['fullname'], $r['month'], $r['amount'], ($r['status'] == 'paid' ? 'مدفوع' : 'غير مدفوع'), $r['payment_date']]);
  }
  fclose($output);
  exit;
}

// Export PDF
if (isset($_GET['export']) && $_GET['export'] === 'pdf') {
  require('fpdf.php');
  $pdf = new FPDF();
  $pdf->AddPage();
  $pdf->SetFont('Arial', 'B', 14);
  $pdf->Cell(0, 10, 'تقرير المدفوعات - أكاديمية 7C', 0, 1, 'C');
  $pdf->Ln(10);
  $pdf->SetFont('Arial', 'B', 10);
  $pdf->Cell(40, 10, 'الاسم', 1);
  $pdf->Cell(30, 10, 'الشهر', 1);
  $pdf->Cell(30, 10, 'القيمة', 1);
  $pdf->Cell(30, 10, 'الحالة', 1);
  $pdf->Cell(50, 10, 'تاريخ الدفع', 1);
  $pdf->Ln();

  $sql_export = "SELECT users.fullname, payments.month, payments.amount, payments.status, payments.payment_date
                 FROM payments JOIN users ON payments.user_id = users.id";
  if (!empty($_GET['month'])) {
    $month = $conn->real_escape_string($_GET['month']);
    $sql_export .= " WHERE payments.month = '$month'";
  }
  $res = $conn->query($sql_export);
  $pdf->SetFont('Arial', '', 10);
  while ($r = $res->fetch_assoc()) {
    $pdf->Cell(40, 10, iconv('UTF-8', 'windows-1256', $r['fullname']), 1);
    $pdf->Cell(30, 10, iconv('UTF-8', 'windows-1256', $r['month']), 1);
    $pdf->Cell(30, 10, $r['amount'] . ' ر.س', 1);
    $pdf->Cell(30, 10, iconv('UTF-8', 'windows-1256', ($r['status'] == 'paid' ? 'مدفوع' : 'غير مدفوع')), 1);
    $pdf->Cell(50, 10, $r['payment_date'], 1);
    $pdf->Ln();
  }
  $pdf->Output();
  exit;
}

// إشعار + تأكيد الدفع
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['confirm_id'])) {
    $confirm_id = intval($_POST['confirm_id']);
    $get_user = $conn->query("SELECT user_id, month, amount FROM payments WHERE id = $confirm_id LIMIT 1")->fetch_assoc();
    $conn->query("UPDATE payments SET status = 'paid', payment_date = NOW() WHERE id = $confirm_id");
    $msg = "تم تسجيل دفع اشتراك شهر {$get_user['month']} بقيمة {$get_user['amount']} ريال بنجاح.";
    $conn->query("INSERT INTO notifications (user_id, message) VALUES ({$get_user['user_id']}, '{$msg}')");
    $successMessage = "✅ تم تأكيد الدفع وإشعار المستخدم.";
  }
  elseif (isset($_POST['add_payment'])) {
    $user_id = intval($_POST['user_id']);
    $month = $_POST['month'];
    $amount = floatval($_POST['amount']);
    $status = $_POST['status'];
    $conn->query("INSERT INTO payments (user_id, month, amount, status) VALUES ($user_id, '$month', $amount, '$status')");
    if ($status === 'paid') {
      $msg = "تم تسجيل دفع اشتراك شهر $month بقيمة $amount ريال بنجاح.";
      $conn->query("INSERT INTO notifications (user_id, message) VALUES ($user_id, '$msg')");
    }
    $successMessage = "✅ تم إضافة المدفوعات" . ($status === 'paid' ? " مع إشعار المستخدم." : ".");
  }
}
?>
