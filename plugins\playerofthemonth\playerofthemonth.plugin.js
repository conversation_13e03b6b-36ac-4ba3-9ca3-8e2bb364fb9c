// ==================== Plugin: لاعب الشهر ====================
export const PlayerOfTheMonthPlugin = {
    id: 'playerofthemonth',
    name: 'لاعب الشهر',
    init() {
        if (!document.getElementById('playerofthemonth-plugin-style')) {
            const style = document.createElement('style');
            style.id = 'playerofthemonth-plugin-style';
            style.innerHTML = `
                .plugin-section {background: #181f2a; color: #fff; border-radius: 18px; box-shadow: 0 2px 16px #0002; max-width: 600px; margin: 32px auto 0; padding: 32px 24px 24px; font-family: 'Cairo',sans-serif;}
                .plugin-header {display: flex; align-items: center; justify-content: space-between; margin-bottom: 18px;}
                .plugin-header h2 {font-size: 1.5rem; font-weight: bold; margin: 0;}
                .plugin-btn {background: linear-gradient(90deg,#4f8cff,#6f6bff); color: #fff; border: none; border-radius: 8px; padding: 10px 22px; font-size: 1rem; font-weight: 600; cursor: pointer; transition: background 0.2s;}
                .plugin-btn:hover {background: linear-gradient(90deg,#6f6bff,#4f8cff);}
                .potm-card {background: #232b3b; border-radius: 12px; padding: 24px; text-align: center; margin-bottom: 18px;}
                .potm-img {width: 90px; height: 90px; border-radius: 50%; object-fit: cover; margin-bottom: 12px; border: 3px solid #8bb4ff;}
                .potm-name {font-size: 1.2rem; font-weight: bold; color: #8bb4ff; margin-bottom: 6px;}
                .potm-desc {color: #b6cfff; font-size: 1rem;}
            `;
            document.head.appendChild(style);
        }
        if (!document.getElementById('playerofthemonth-plugin-container')) {
            const container = document.createElement('section');
            container.id = 'playerofthemonth-plugin-container';
            container.className = 'plugin-section';
            container.innerHTML = `
                <div class="plugin-header">
                    <h2>لاعب الشهر</h2>
                    <button id="set-potm-btn" class="plugin-btn">تعيين لاعب الشهر</button>
                </div>
                <div class="potm-card" id="potm-card">
                    <div class="potm-name">لا يوجد لاعب محدد</div>
                    <div class="potm-desc">يمكنك تعيين لاعب الشهر من خلال الزر أعلاه.</div>
                </div>
            `;
            document.body.prepend(container);
        }
        this.renderPOTM();
        document.getElementById('set-potm-btn').onclick = () => this.openPOTMModal();
    },
    destroy() {
        const container = document.getElementById('playerofthemonth-plugin-container');
        if (container) container.remove();
        const style = document.getElementById('playerofthemonth-plugin-style');
        if (style) style.remove();
        this.closeModal();
    },
    renderPOTM() {
        const potm = JSON.parse(localStorage.getItem('plugin_potm') || 'null');
        const card = document.getElementById('potm-card');
        if (!card) return;
        if (potm) {
            card.innerHTML = `
                <img class="potm-img" src="${potm.img || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(potm.name)}" alt="لاعب الشهر" />
                <div class="potm-name">${potm.name}</div>
                <div class="potm-desc">${potm.desc || ''}</div>
            `;
        } else {
            card.innerHTML = `<div class="potm-name">لا يوجد لاعب محدد</div><div class="potm-desc">يمكنك تعيين لاعب الشهر من خلال الزر أعلاه.</div>`;
        }
    },
    openPOTMModal() {
        this.closeModal();
        const potm = JSON.parse(localStorage.getItem('plugin_potm') || 'null') || { name: '', desc: '', img: '' };
        const modalBg = document.createElement('div');
        modalBg.className = 'plugin-modal-bg';
        modalBg.id = 'potm-plugin-modal-bg';
        modalBg.innerHTML = `
            <div class="plugin-modal">
                <h3>تعيين لاعب الشهر</h3>
                <label>اسم اللاعب</label>
                <input id="modal-potm-name" type="text" value="${potm.name || ''}" placeholder="مثال: عبدالله صالح" />
                <label>نبذة عن اللاعب</label>
                <input id="modal-potm-desc" type="text" value="${potm.desc || ''}" placeholder="إنجازات أو ملاحظات" />
                <label>رابط صورة اللاعب (اختياري)</label>
                <input id="modal-potm-img" type="url" value="${potm.img || ''}" placeholder="رابط صورة" />
                <div class="modal-actions">
                    <button class="plugin-btn" id="save-potm-btn">حفظ</button>
                    <button class="plugin-btn" id="cancel-potm-btn" style="background:#2d3950;">إلغاء</button>
                </div>
            </div>
        `;
        document.body.appendChild(modalBg);
        document.getElementById('cancel-potm-btn').onclick = () => this.closeModal();
        document.getElementById('save-potm-btn').onclick = () => {
            const name = document.getElementById('modal-potm-name').value.trim();
            const desc = document.getElementById('modal-potm-desc').value.trim();
            const img = document.getElementById('modal-potm-img').value.trim();
            if (!name) {
                alert('يرجى إدخال اسم اللاعب');
                return;
            }
            localStorage.setItem('plugin_potm', JSON.stringify({ name, desc, img }));
            this.closeModal();
            this.renderPOTM();
        };
        setTimeout(() => {
            document.getElementById('modal-potm-name').focus();
        }, 100);
    },
    closeModal() {
        const modalBg = document.getElementById('potm-plugin-modal-bg');
        if (modalBg) modalBg.remove();
    }
};
