// ==================== Plugin: مساعد الذكاء الاصطناعي ====================
export const AIAssistantPlugin = {
    id: 'aiassistant',
    name: 'مساعد الذكاء الاصطناعي',
    init() {
        if (!document.getElementById('aiassistant-plugin-style')) {
            const style = document.createElement('style');
            style.id = 'aiassistant-plugin-style';
            style.innerHTML = `
                .plugin-section {background: #181f2a; color: #fff; border-radius: 18px; box-shadow: 0 2px 16px #0002; max-width: 600px; margin: 32px auto 0; padding: 32px 24px 24px; font-family: 'Cairo',sans-serif;}
                .plugin-header {display: flex; align-items: center; justify-content: space-between; margin-bottom: 18px;}
                .plugin-header h2 {font-size: 1.5rem; font-weight: bold; margin: 0;}
                .ai-chat-box {background: #232b3b; border-radius: 12px; padding: 18px; min-height: 180px; max-height: 320px; overflow-y: auto; margin-bottom: 16px;}
                .ai-chat-msg {margin-bottom: 10px;}
                .ai-chat-msg.user {text-align: right; color: #8bb4ff;}
                .ai-chat-msg.ai {text-align: left; color: #fff;}
                .ai-chat-input-row {display: flex; gap: 8px;}
                .ai-chat-input {flex: 1; border-radius: 8px; border: none; padding: 10px; font-size: 1rem; background: #181f2a; color: #fff;}
                .plugin-btn {background: linear-gradient(90deg,#4f8cff,#6f6bff); color: #fff; border: none; border-radius: 8px; padding: 10px 22px; font-size: 1rem; font-weight: 600; cursor: pointer; transition: background 0.2s;}
                .plugin-btn:hover {background: linear-gradient(90deg,#6f6bff,#4f8cff);}
            `;
            document.head.appendChild(style);
        }
        if (!document.getElementById('aiassistant-plugin-container')) {
            const container = document.createElement('section');
            container.id = 'aiassistant-plugin-container';
            container.className = 'plugin-section';
            container.innerHTML = `
                <div class="plugin-header">
                    <h2>مساعد الذكاء الاصطناعي</h2>
                </div>
                <div class="ai-chat-box" id="ai-chat-box"></div>
                <div class="ai-chat-input-row">
                    <input id="ai-chat-input" class="ai-chat-input" type="text" placeholder="اكتب سؤالك..." />
                    <button id="ai-chat-send" class="plugin-btn">إرسال</button>
                </div>
            `;
            document.body.prepend(container);
        }
        this.renderChat();
        document.getElementById('ai-chat-send').onclick = () => this.sendMessage();
        document.getElementById('ai-chat-input').onkeydown = (e) => { if(e.key==='Enter'){ this.sendMessage(); } };
    },
    destroy() {
        const container = document.getElementById('aiassistant-plugin-container');
        if (container) container.remove();
        const style = document.getElementById('aiassistant-plugin-style');
        if (style) style.remove();
    },
    renderChat() {
        const chatBox = document.getElementById('ai-chat-box');
        if (!chatBox) return;
        const messages = JSON.parse(localStorage.getItem('plugin_ai_chat') || '[]');
        chatBox.innerHTML = messages.map(m => `<div class="ai-chat-msg ${m.sender}">${m.text}</div>`).join('');
        chatBox.scrollTop = chatBox.scrollHeight;
    },
    sendMessage() {
        const input = document.getElementById('ai-chat-input');
        const text = input.value.trim();
        if (!text) return;
        const messages = JSON.parse(localStorage.getItem('plugin_ai_chat') || '[]');
        messages.push({ sender: 'user', text });
        // رد افتراضي تجريبي
        const aiReply = this.generateAIReply(text);
        messages.push({ sender: 'ai', text: aiReply });
        localStorage.setItem('plugin_ai_chat', JSON.stringify(messages));
        input.value = '';
        this.renderChat();
    },
    generateAIReply(text) {
        // ردود افتراضية بسيطة
        if (text.includes('لاعب')) return 'يمكنك إدارة اللاعبين من خلال قسم إدارة اللاعبين.';
        if (text.includes('مباراة')) return 'لإدارة المباريات انتقل إلى قسم إدارة المباريات.';
        if (text.includes('اشتراك')) return 'قسم الاشتراكات يوفر لك كل ما تحتاجه.';
        return 'أنا هنا لمساعدتك في أي استفسار متعلق بالأكاديمية.';
    }
};
