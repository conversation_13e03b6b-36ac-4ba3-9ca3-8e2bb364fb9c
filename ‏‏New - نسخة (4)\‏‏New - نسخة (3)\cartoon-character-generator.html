<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة توليد الشخصيات الكرتونية الذكية - أكاديمية 7C</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark-gradient: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            --glass-bg: rgba(255, 255, 255, 0.08);
            --glass-border: rgba(255, 255, 255, 0.15);
            --text-primary: #ffffff;
            --text-secondary: #b8c5d6;
            --accent-color: #00d4ff;
            --ai-glow: rgba(0, 212, 255, 0.3);
            --cartoon-pink: #ff6b9d;
            --cartoon-blue: #4ecdc4;
            --cartoon-yellow: #ffe66d;
            --cartoon-purple: #a8e6cf;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--dark-gradient);
            color: var(--text-primary);
            overflow-x: hidden;
            position: relative;
            min-height: 100vh;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, var(--cartoon-pink) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, var(--cartoon-blue) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, var(--cartoon-yellow) 0%, transparent 50%);
            opacity: 0.1;
            animation: floatingColors 15s ease-in-out infinite alternate;
            z-index: -2;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(255, 107, 157, 0.1) 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, rgba(78, 205, 196, 0.1) 2px, transparent 2px);
            background-size: 80px 80px;
            animation: floatingDots 20s linear infinite;
            z-index: -1;
        }

        @keyframes floatingColors {
            0% { opacity: 0.1; transform: scale(1) rotate(0deg); }
            100% { opacity: 0.2; transform: scale(1.1) rotate(5deg); }
        }

        @keyframes floatingDots {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-80px) translateY(-80px); }
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(30px);
            border-bottom: 1px solid var(--glass-border);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, var(--cartoon-pink) 25%, var(--cartoon-blue) 50%, var(--cartoon-yellow) 75%, transparent 100%);
        }

        .header-content {
            max-width: 1600px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-icon {
            width: 60px;
            height: 60px;
            background: var(--primary-gradient);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            font-weight: 900;
            position: relative;
            box-shadow: 0 8px 25px var(--ai-glow);
            animation: logoFloat 3s ease-in-out infinite alternate;
        }

        .logo-icon::before {
            content: '🎨';
            position: absolute;
            top: -5px;
            right: -5px;
            font-size: 1rem;
            animation: sparkle 2s linear infinite;
        }

        @keyframes logoFloat {
            0% { transform: translateY(0px) rotate(0deg); }
            100% { transform: translateY(-5px) rotate(2deg); }
        }

        @keyframes sparkle {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }

        .logo-text h1 {
            font-size: 1.8rem;
            font-weight: 800;
            background: linear-gradient(45deg, var(--cartoon-pink), var(--cartoon-blue), var(--cartoon-yellow));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.2rem;
        }

        .logo-text p {
            font-size: 0.9rem;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .ai-badge {
            background: var(--success-gradient);
            color: white;
            padding: 0.2rem 0.6rem;
            border-radius: 20px;
            font-size: 0.7rem;
            font-weight: 700;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 15px;
            border: none;
            cursor: pointer;
            font-weight: 700;
            font-size: 0.9rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
            font-family: 'Cairo', sans-serif;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: var(--success-gradient);
            color: white;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        }

        .btn-warning {
            background: var(--warning-gradient);
            color: white;
            box-shadow: 0 4px 15px rgba(67, 233, 123, 0.3);
        }

        .btn-cartoon {
            background: linear-gradient(135deg, var(--cartoon-pink), var(--cartoon-blue));
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
        }

        .btn-glass {
            background: var(--glass-bg);
            color: var(--text-primary);
            border: 1px solid var(--glass-border);
            backdrop-filter: blur(20px);
        }

        .btn-glass:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--cartoon-pink);
            transform: translateY(-2px);
        }

        /* Main Layout */
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 350px 1fr 300px;
            gap: 2rem;
            min-height: calc(100vh - 140px);
        }

        /* Sidebar */
        .sidebar {
            background: var(--glass-bg);
            backdrop-filter: blur(30px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            padding: 2rem;
            height: fit-content;
            position: sticky;
            top: 120px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .sidebar-section {
            margin-bottom: 2.5rem;
        }

        .sidebar-title {
            font-size: 1.1rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid var(--glass-border);
        }

        .sidebar-title i {
            color: var(--cartoon-pink);
            font-size: 1.2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: white;
            font-size: 0.9rem;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            background: var(--glass-bg);
            color: white;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--cartoon-pink);
            box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
            background: rgba(255, 255, 255, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: left 0.75rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-left: 2.5rem;
        }

        .color-picker-group {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.75rem;
        }

        .color-picker {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }

        .color-input {
            width: 50px;
            height: 50px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid var(--glass-border);
        }

        .color-input:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        /* Main Content */
        .main-content {
            background: var(--glass-bg);
            backdrop-filter: blur(30px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            padding: 2.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .main-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, var(--cartoon-pink) 50%, transparent 100%);
        }

        .content-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .content-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, var(--cartoon-pink), var(--cartoon-blue), var(--cartoon-yellow));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .content-subtitle {
            font-size: 1.1rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        .generate-section {
            text-align: center;
            margin-bottom: 3rem;
        }

        .generate-btn {
            background: linear-gradient(135deg, var(--cartoon-pink), var(--cartoon-blue), var(--cartoon-yellow));
            color: white;
            border: none;
            padding: 1.5rem 3rem;
            border-radius: 20px;
            font-size: 1.2rem;
            font-weight: 800;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(255, 107, 157, 0.3);
            position: relative;
            overflow: hidden;
            font-family: 'Cairo', sans-serif;
        }

        .generate-btn:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 35px rgba(255, 107, 157, 0.4);
        }

        .generate-btn:active {
            transform: translateY(-2px) scale(1.02);
        }

        .generate-btn.loading {
            pointer-events: none;
            opacity: 0.8;
        }

        .generate-btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .character-preview {
            background: rgba(255, 255, 255, 0.05);
            border: 2px dashed var(--glass-border);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            min-height: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .character-preview.has-character {
            border-style: solid;
            border-color: var(--cartoon-pink);
            background: rgba(255, 107, 157, 0.05);
        }

        .character-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            margin-bottom: 1rem;
        }

        .character-placeholder {
            font-size: 4rem;
            color: var(--glass-border);
            margin-bottom: 1rem;
        }

        .character-placeholder-text {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        /* Character Panel */
        .character-panel {
            background: var(--glass-bg);
            backdrop-filter: blur(30px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            padding: 2rem;
            height: fit-content;
            position: sticky;
            top: 120px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .character-info {
            margin-bottom: 2rem;
        }

        .character-name {
            font-size: 1.5rem;
            font-weight: 800;
            color: white;
            margin-bottom: 0.5rem;
        }

        .character-details {
            color: var(--text-secondary);
            font-size: 0.9rem;
            line-height: 1.6;
        }

        .character-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .action-btn {
            padding: 0.75rem 1rem;
            border-radius: 12px;
            border: none;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            justify-content: center;
            font-family: 'Cairo', sans-serif;
        }

        .action-btn.save {
            background: var(--success-gradient);
            color: white;
        }

        .action-btn.edit {
            background: var(--warning-gradient);
            color: white;
        }

        .action-btn.export {
            background: var(--primary-gradient);
            color: white;
        }

        .action-btn.delete {
            background: var(--danger-gradient);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* Loading States */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 24px;
            z-index: 100;
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(255, 107, 157, 0.3);
            border-top: 3px solid var(--cartoon-pink);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        .loading-text {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .loading-subtext {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 300px 1fr 250px;
            }
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .sidebar, .character-panel {
                position: static;
                order: 2;
            }
            
            .main-content {
                order: 1;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .content-title {
                font-size: 2rem;
            }
            
            .generate-btn {
                padding: 1rem 2rem;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">7C</div>
                <div class="logo-text">
                    <h1>منصة توليد الشخصيات الكرتونية</h1>
                    <p>أكاديمية 7C الإبداعية <span class="ai-badge">AI مدعوم</span></p>
                </div>
            </div>
            <div class="header-actions">
                <button class="btn btn-glass" onclick="showGallery()">
                    <i class="fas fa-images"></i>
                    معرض الشخصيات
                </button>
                <button class="btn btn-cartoon" onclick="showTemplates()">
                    <i class="fas fa-layer-group"></i>
                    القوالب الجاهزة
                </button>
                <button class="btn btn-success" onclick="showProjects()">
                    <i class="fas fa-folder"></i>
                    مشاريعي
                </button>
                <button class="btn btn-primary" onclick="showSettings()">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </button>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container">
        <div class="main-grid">
            <!-- Character Customization Sidebar -->
            <div class="sidebar">
                <div class="sidebar-section">
                    <div class="sidebar-title">
                        <i class="fas fa-user-edit"></i>
                        تخصيص الشخصية
                    </div>

                    <div class="form-group">
                        <label class="form-label">اسم الشخصية</label>
                        <input type="text" id="characterName" class="form-input" placeholder="أدخل اسم الشخصية">
                    </div>

                    <div class="form-group">
                        <label class="form-label">العمر</label>
                        <select id="characterAge" class="form-input form-select">
                            <option value="">اختر العمر</option>
                            <option value="child">طفل (5-12 سنة)</option>
                            <option value="teen">مراهق (13-19 سنة)</option>
                            <option value="adult">بالغ (20-40 سنة)</option>
                            <option value="elder">كبير السن (40+ سنة)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">الجنس</label>
                        <select id="characterGender" class="form-input form-select">
                            <option value="">اختر الجنس</option>
                            <option value="male">ذكر</option>
                            <option value="female">أنثى</option>
                            <option value="neutral">محايد</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">نوع الشخصية</label>
                        <select id="characterType" class="form-input form-select">
                            <option value="">اختر النوع</option>
                            <option value="hero">بطل</option>
                            <option value="villain">شرير</option>
                            <option value="sidekick">مساعد</option>
                            <option value="comic">كوميدي</option>
                            <option value="wise">حكيم</option>
                            <option value="mysterious">غامض</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">الوصف التفصيلي</label>
                        <textarea id="characterDescription" class="form-input form-textarea" placeholder="اكتب وصفاً تفصيلياً للشخصية..."></textarea>
                    </div>
                </div>

                <div class="sidebar-section">
                    <div class="sidebar-title">
                        <i class="fas fa-palette"></i>
                        الألوان والمظهر
                    </div>

                    <div class="form-group">
                        <label class="form-label">نمط الرسم</label>
                        <select id="artStyle" class="form-input form-select">
                            <option value="cartoon">كرتوني كلاسيكي</option>
                            <option value="anime">أنمي</option>
                            <option value="disney">ديزني</option>
                            <option value="pixar">بيكسار</option>
                            <option value="chibi">تشيبي</option>
                            <option value="realistic">واقعي</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">ألوان الشخصية</label>
                        <div class="color-picker-group">
                            <div class="color-picker">
                                <label>البشرة</label>
                                <input type="color" id="skinColor" class="color-input" value="#ffdbac">
                            </div>
                            <div class="color-picker">
                                <label>الشعر</label>
                                <input type="color" id="hairColor" class="color-input" value="#8b4513">
                            </div>
                            <div class="color-picker">
                                <label>العيون</label>
                                <input type="color" id="eyeColor" class="color-input" value="#4169e1">
                            </div>
                            <div class="color-picker">
                                <label>الملابس</label>
                                <input type="color" id="clothesColor" class="color-input" value="#ff6b9d">
                            </div>
                            <div class="color-picker">
                                <label>الإكسسوار</label>
                                <input type="color" id="accessoryColor" class="color-input" value="#ffd700">
                            </div>
                            <div class="color-picker">
                                <label>الخلفية</label>
                                <input type="color" id="backgroundColor" class="color-input" value="#87ceeb">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">التعبير</label>
                        <select id="characterExpression" class="form-input form-select">
                            <option value="happy">سعيد</option>
                            <option value="sad">حزين</option>
                            <option value="angry">غاضب</option>
                            <option value="surprised">متفاجئ</option>
                            <option value="confident">واثق</option>
                            <option value="shy">خجول</option>
                            <option value="excited">متحمس</option>
                            <option value="serious">جدي</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="main-content">
                <div class="content-header">
                    <h1 class="content-title">مولد الشخصيات الكرتونية الذكي</h1>
                    <p class="content-subtitle">أنشئ شخصيات كرتونية احترافية لمسلسلك الرسوم المتحركة باستخدام الذكاء الاصطناعي</p>
                </div>

                <div class="generate-section">
                    <button class="generate-btn" id="generateBtn" onclick="generateCharacter()">
                        <i class="fas fa-magic"></i>
                        توليد شخصية جديدة
                    </button>
                </div>

                <div class="character-preview" id="characterPreview">
                    <div class="character-placeholder">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="character-placeholder-text">
                        اضغط على "توليد شخصية جديدة" لإنشاء شخصيتك الأولى
                    </div>
                </div>

                <!-- Loading Overlay -->
                <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                    <div class="loading-content">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">جاري توليد الشخصية...</div>
                        <div class="loading-subtext">يرجى الانتظار، هذا قد يستغرق بضع ثوانٍ</div>
                    </div>
                </div>
            </div>

            <!-- Character Information Panel -->
            <div class="character-panel">
                <div class="character-info">
                    <h3 class="character-name" id="displayCharacterName">اسم الشخصية</h3>
                    <div class="character-details" id="characterDetails">
                        <p><strong>العمر:</strong> <span id="displayAge">غير محدد</span></p>
                        <p><strong>النوع:</strong> <span id="displayType">غير محدد</span></p>
                        <p><strong>الوصف:</strong> <span id="displayDescription">لم يتم إنشاء شخصية بعد</span></p>
                    </div>
                </div>

                <div class="character-actions">
                    <button class="action-btn save" onclick="saveCharacter()" disabled id="saveBtn">
                        <i class="fas fa-save"></i>
                        حفظ الشخصية
                    </button>

                    <button class="action-btn edit" onclick="editCharacter()" disabled id="editBtn">
                        <i class="fas fa-edit"></i>
                        تحرير الشخصية
                    </button>

                    <button class="action-btn export" onclick="exportCharacter()" disabled id="exportBtn">
                        <i class="fas fa-download"></i>
                        تصدير الشخصية
                    </button>

                    <button class="action-btn delete" onclick="deleteCharacter()" disabled id="deleteBtn">
                        <i class="fas fa-trash"></i>
                        حذف الشخصية
                    </button>
                </div>

                <div class="sidebar-section" style="margin-top: 2rem;">
                    <div class="sidebar-title">
                        <i class="fas fa-chart-bar"></i>
                        إحصائيات المشروع
                    </div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number" id="totalCharacters">0</div>
                            <div class="stat-label">إجمالي الشخصيات</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="savedCharacters">0</div>
                            <div class="stat-label">الشخصيات المحفوظة</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="projectsCount">1</div>
                            <div class="stat-label">المشاريع النشطة</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
