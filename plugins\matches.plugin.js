// ==================== Plugin: إدارة المباريات ====================
export const MatchesManagementPlugin = {
    id: 'matches',
    name: 'إدارة المباريات',
    init() {
        // إضافة CSS مدمج للواجهة الاحترافية
        if (!document.getElementById('matches-plugin-style')) {
            const style = document.createElement('style');
            style.id = 'matches-plugin-style';
            style.innerHTML = `
                .plugin-section {background: #181f2a; color: #fff; border-radius: 18px; box-shadow: 0 2px 16px #0002; max-width: 900px; margin: 32px auto 0; padding: 32px 24px 24px; font-family: 'Cairo',sans-serif;}
                .plugin-header {display: flex; align-items: center; justify-content: space-between; margin-bottom: 18px;}
                .plugin-header h2 {font-size: 1.5rem; font-weight: bold; margin: 0;}
                .plugin-btn {background: linear-gradient(90deg,#4f8cff,#6f6bff); color: #fff; border: none; border-radius: 8px; padding: 10px 22px; font-size: 1rem; font-weight: 600; cursor: pointer; transition: background 0.2s;}
                .plugin-btn:hover {background: linear-gradient(90deg,#6f6bff,#4f8cff);}
                .plugin-table {width: 100%; border-collapse: collapse; background: #232b3b; border-radius: 12px; overflow: hidden; margin-top: 10px;}
                .plugin-table th, .plugin-table td {padding: 12px 8px; text-align: center;}
                .plugin-table th {background: #232b3b; color: #8bb4ff; font-weight: 700; border-bottom: 2px solid #2d3950;}
                .plugin-table tr {transition: background 0.2s;}
                .plugin-table tr:hover {background: #232b3bcc;}
                .plugin-table td {border-bottom: 1px solid #232b3b;}
                .plugin-action-btn {background: #2d3950; color: #8bb4ff; border: none; border-radius: 6px; padding: 6px 16px; margin: 0 2px; font-size: 0.95rem; cursor: pointer; transition: background 0.2s;}
                .plugin-action-btn:hover {background: #4f8cff; color: #fff;}
                .plugin-modal-bg {position: fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.45); z-index: 9999; display: flex; align-items: center; justify-content: center;}
                .plugin-modal {background: #232b3b; border-radius: 16px; padding: 32px 24px 24px; min-width: 320px; max-width: 95vw; box-shadow: 0 4px 32px #0005;}
                .plugin-modal h3 {margin-top:0; color:#8bb4ff; font-size:1.2rem; margin-bottom:18px;}
                .plugin-modal label {display:block; margin-bottom:6px; color:#b6cfff; font-size:1rem;}
                .plugin-modal input {width:100%; padding:8px 10px; border-radius:6px; border:none; background:#181f2a; color:#fff; margin-bottom:16px; font-size:1rem;}
                .plugin-modal .modal-actions {display:flex; justify-content:flex-end; gap:10px;}
                .plugin-modal .plugin-btn {padding:8px 18px; font-size:1rem;}
            `;
            document.head.appendChild(style);
        }
        // إنشاء واجهة إدارة المباريات إذا لم تكن موجودة
        if (!document.getElementById('matches-plugin-container')) {
            const container = document.createElement('section');
            container.id = 'matches-plugin-container';
            container.className = 'plugin-section';
            container.innerHTML = `
                <div class="plugin-header">
                    <h2>إدارة المباريات</h2>
                    <button id="add-match-btn" class="plugin-btn">إضافة مباراة</button>
                </div>
                <table class="plugin-table" id="matches-table">
                    <thead>
                        <tr>
                            <th>الفريق</th>
                            <th>التاريخ</th>
                            <th>الوقت</th>
                            <th>الملعب</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="matches-table-body">
                        <!-- سيتم تعبئة المباريات هنا -->
                    </tbody>
                </table>
            `;
            document.body.prepend(container);
        }
        this.renderMatches();
        document.getElementById('add-match-btn').onclick = () => this.openMatchModal();
    },
    destroy() {
        // إزالة واجهة إدارة المباريات وCSS
        const container = document.getElementById('matches-plugin-container');
        if (container) container.remove();
        const style = document.getElementById('matches-plugin-style');
        if (style) style.remove();
        this.closeModal();
    },
    renderMatches() {
        const matches = JSON.parse(localStorage.getItem('plugin_matches') || '[]');
        const tbody = document.getElementById('matches-table-body');
        if (!tbody) return;
        tbody.innerHTML = matches.length ? matches.map((m, i) => `
            <tr>
                <td>${m.team || ''}</td>
                <td>${m.date || ''}</td>
                <td>${m.time || ''}</td>
                <td>${m.venue || ''}</td>
                <td>
                    <button class="plugin-action-btn" onclick="window.PluginManager.plugins.matches.openMatchModal(${i})">تعديل</button>
                    <button class="plugin-action-btn" onclick="window.PluginManager.plugins.matches.deleteMatch(${i})">حذف</button>
                </td>
            </tr>
        `).join('') : '<tr><td colspan="5">لا يوجد مباريات</td></tr>';
    },
    openMatchModal(index = null) {
        this.closeModal();
        const matches = JSON.parse(localStorage.getItem('plugin_matches') || '[]');
        const match = index !== null ? matches[index] : { team: '', date: '', time: '', venue: '' };
        const modalBg = document.createElement('div');
        modalBg.className = 'plugin-modal-bg';
        modalBg.id = 'matches-plugin-modal-bg';
        modalBg.innerHTML = `
            <div class="plugin-modal">
                <h3>${index !== null ? 'تعديل مباراة' : 'إضافة مباراة جديدة'}</h3>
                <label>اسم الفريق المنافس</label>
                <input id="modal-match-team" type="text" value="${match.team || ''}" placeholder="مثال: الهلال" />
                <label>تاريخ المباراة</label>
                <input id="modal-match-date" type="date" value="${match.date || ''}" />
                <label>وقت المباراة</label>
                <input id="modal-match-time" type="time" value="${match.time || ''}" />
                <label>اسم الملعب</label>
                <input id="modal-match-venue" type="text" value="${match.venue || ''}" placeholder="مثال: ملعب الأكاديمية" />
                <div class="modal-actions">
                    <button class="plugin-btn" id="save-match-btn">${index !== null ? 'حفظ التعديلات' : 'إضافة'}</button>
                    <button class="plugin-btn" id="cancel-match-btn" style="background:#2d3950;">إلغاء</button>
                </div>
            </div>
        `;
        document.body.appendChild(modalBg);
        document.getElementById('cancel-match-btn').onclick = () => this.closeModal();
        document.getElementById('save-match-btn').onclick = () => {
            const team = document.getElementById('modal-match-team').value.trim();
            const date = document.getElementById('modal-match-date').value;
            const time = document.getElementById('modal-match-time').value;
            const venue = document.getElementById('modal-match-venue').value.trim();
            if (!team || !date || !time || !venue) {
                alert('يرجى تعبئة جميع الحقول');
                return;
            }
            if (index !== null) {
                matches[index] = { team, date, time, venue };
            } else {
                matches.push({ team, date, time, venue });
            }
            localStorage.setItem('plugin_matches', JSON.stringify(matches));
            this.closeModal();
            this.renderMatches();
        };
        setTimeout(() => {
            document.getElementById('modal-match-team').focus();
        }, 100);
    },
    closeModal() {
        const modalBg = document.getElementById('matches-plugin-modal-bg');
        if (modalBg) modalBg.remove();
    },
    deleteMatch(index) {
        if (!confirm('هل أنت متأكد من حذف المباراة؟')) return;
        const matches = JSON.parse(localStorage.getItem('plugin_matches') || '[]');
        matches.splice(index, 1);
        localStorage.setItem('plugin_matches', JSON.stringify(matches));
        this.renderMatches();
    }
};
