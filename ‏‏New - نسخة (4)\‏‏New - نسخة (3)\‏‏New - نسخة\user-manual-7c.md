# 📚 دليل المستخدم الشامل - نظام إدارة أكاديمية 7C الرياضية

## 🎯 **نظرة عامة على النظام**

نظام إدارة أكاديمية 7C الرياضية هو منصة متكاملة تهدف إلى تسهيل إدارة الأكاديميات الرياضية بكفاءة عالية. يتضمن النظام 6 ملفات رئيسية:

### **الملفات المطورة:**
1. **`login.html`** - صفحة تسجيل الدخول الموحدة
2. **`admin-dashboard-7c.html`** - لوحة تحكم الإدارة الرئيسية
3. **`coach-dashboard.html`** - لوحة تحكم المدرب (قيد التطوير)
4. **`player-dashboard.html`** - لوحة تحكم اللاعب (قيد التطوير)
5. **`parent-dashboard.html`** - لوحة تحكم ولي الأمر (قيد التطوير)
6. **`supervisor-dashboard.html`** - لوحة تحكم المشرف (قيد التطوير)

---

## 🔐 **1. صفحة تسجيل الدخول (login.html)**

### **المميزات الرئيسية:**
- **3 طرق تسجيل دخول مختلفة**
- **مصادقة بيومترية حقيقية**
- **نظام أمان متقدم**
- **تشفير البيانات**

### **طرق تسجيل الدخول:**

#### **أ. تسجيل الدخول بالجوال + OTP:**
1. اختر تبويب "رقم الجوال"
2. أدخل رقم الجوال (يجب أن يبدأ بـ 05 ويتكون من 10 أرقام)
3. اضغط "إرسال رمز التحقق"
4. أدخل رمز OTP المرسل (سيظهر في الكونسول للاختبار)
5. سيتم تسجيل الدخول تلقائياً

#### **ب. تسجيل الدخول بالبريد الإلكتروني:**
1. اختر تبويب "البريد الإلكتروني"
2. أدخل البريد الإلكتروني وكلمة المرور
3. اضغط "تسجيل الدخول"

#### **ج. تسجيل الدخول برقم الهوية:**
1. اختر تبويب "رقم الهوية"
2. أدخل رقم الهوية (10 أرقام) وتاريخ الميلاد
3. اضغط "تسجيل الدخول"

### **المصادقة البيومترية:**

#### **🔸 بصمة الإصبع:**
- يستخدم WebAuthn API الحقيقي
- يطلب إذن المتصفح للوصول لأجهزة المصادقة
- في حالة عدم توفر الأجهزة، يتم محاكاة النجاح للاختبار

#### **🔸 التعرف على الوجه:**
- يطلب إذن الوصول للكاميرا
- يعرض نافذة كاميرا حقيقية
- يحاكي عملية التعرف على الوجه مع شريط تقدم
- يمكن إلغاء العملية في أي وقت

#### **🔸 رمز PIN:**
- لوحة أرقام تفاعلية
- رمز PIN الافتراضي: **1234**
- يدعم الحذف والإلغاء

### **بيانات الاختبار:**

| الدور | البريد الإلكتروني | كلمة المرور | رقم الجوال | رقم الهوية | تاريخ الميلاد |
|-------|------------------|-------------|------------|------------|---------------|
| **الإدارة** | <EMAIL> | admin123 | 0501234567 | 1234567890 | 1985-01-15 |
| **المدرب** | <EMAIL> | coach123 | 0509876543 | 0987654321 | 1990-05-20 |
| **اللاعب** | <EMAIL> | player123 | 0551122334 | 1122334455 | 2005-08-10 |
| **ولي الأمر** | <EMAIL> | parent123 | 0555544332 | 5544332211 | 1980-12-25 |
| **المشرف** | <EMAIL> | super123 | 0559988776 | 9988776655 | 1988-03-12 |

### **ميزات الأمان:**
- **تشفير كلمات المرور:** SHA-256 + Salt
- **تشفير الجلسات:** AES-256
- **قفل الحساب:** بعد 3 محاولات فاشلة لمدة 15 دقيقة
- **تسجيل محاولات الدخول:** مع التوقيت والتفاصيل
- **انتهاء صلاحية الجلسة:** 8 ساعات

---

## 🏢 **2. لوحة تحكم الإدارة (admin-dashboard-7c.html)**

### **المميزات الرئيسية:**
- **لوحة معلومات ذكية** مع KPIs تفاعلية
- **ذكاء اصطناعي متقدم** للتحليل والتنبؤ
- **نظام تنقل سهل** مع شريط جانبي
- **تصميم متجاوب** لجميع الأجهزة
- **بيانات تجريبية شاملة** للاختبار

### **أقسام لوحة التحكم:**

#### **أ. لوحة المعلومات الرئيسية:**
- **4 بطاقات KPI:**
  1. إجمالي اللاعبين (مع نسبة النمو)
  2. المدربين النشطين (مع تقييم الأداء)
  3. معدل الحضور اليومي (مع مقارنة بالأمس)
  4. الإيرادات الشهرية (مع توقع نهاية الشهر)

#### **ب. الأقسام المتاحة:**
1. **إدارة اللاعبين** - إدارة شاملة لبيانات اللاعبين
2. **إدارة المدربين** - إدارة المدربين والموظفين
3. **الحضور والغياب** - نظام تتبع الحضور
4. **النظام المالي** - إدارة الفواتير والمدفوعات
5. **التقارير والإحصائيات** - تقارير تفصيلية
6. **نظام الرسائل** - التواصل مع المستخدمين
7. **إعدادات النظام** - تخصيص النظام

### **الذكاء الاصطناعي المدمج:**

#### **🤖 تحليل مخاطر الانقطاع:**
```javascript
function calculateDropoutRisk(player) {
    // يحلل:
    // - معدل الحضور (وزن 30%)
    // - درجة الأداء (وزن 25%) 
    // - آخر حضور (وزن 20%)
    // - عوامل أخرى (وزن 25%)
    
    return riskScore; // 0-100%
}
```

#### **🤖 التوصيات الذكية:**
- **توصيات الحضور:** للاعبين ذوي الحضور المنخفض
- **توصيات الأداء:** لبرامج التدريب الإضافية
- **توصيات مالية:** لمتابعة المدفوعات المتأخرة

#### **🤖 تحليل الأداء العام:**
- حساب متوسط الأداء للأكاديمية
- تحديد اللاعبين المتميزين
- تحديد اللاعبين المحتاجين للدعم

### **البيانات التجريبية المدمجة:**

#### **اللاعبين (3 لاعبين):**
1. **أحمد محمد علي** - متقدم، معدل حضور 85%، أداء 92%
2. **فاطمة سعد الدين** - متوسط، معدل حضور 78%، أداء 88%
3. **محمد عبدالله** - مبتدئ، معدل حضور 65%، أداء 75% (تحذير)

#### **المدربين (2 مدرب):**
1. **كابتن أحمد المدرب** - كرة القدم، 8 سنوات خبرة، تقييم 4.8
2. **كابتن سارة المدربة** - السباحة، 5 سنوات خبرة، تقييم 4.9

### **الوظائف التفاعلية:**

#### **🔔 الإشعارات:**
- تحذيرات الغياب
- تذكيرات الفواتير
- إشعارات اللاعبين الجدد

#### **➕ الإضافة السريعة:**
- إضافة لاعب جديد
- إضافة مدرب جديد
- تسجيل حضور
- إنشاء فاتورة

#### **👤 قائمة المستخدم:**
- تعديل الملف الشخصي
- تغيير كلمة المرور
- عرض النشاط
- تسجيل الخروج

### **الاختصارات المتاحة:**
- **Ctrl+1:** لوحة المعلومات
- **Ctrl+2:** إدارة اللاعبين
- **Ctrl+3:** إدارة المدربين
- **Ctrl+S:** حفظ البيانات

### **ميزات إضافية:**
- **تحديث تلقائي:** كل 30 ثانية
- **حفظ تلقائي:** عند إغلاق النافذة
- **تصدير البيانات:** JSON و CSV
- **معالجة الأخطاء:** شاملة مع رسائل واضحة

---

## 🔧 **3. التثبيت والإعداد**

### **المتطلبات:**
- متصفح حديث (Chrome, Firefox, Safari, Edge)
- اتصال بالإنترنت (للمكتبات الخارجية)
- دعم JavaScript

### **خطوات التشغيل:**
1. تحميل جميع الملفات في مجلد واحد
2. فتح `login.html` في المتصفح
3. استخدام بيانات الاختبار للدخول
4. الاستمتاع بالنظام!

### **الملفات المطلوبة:**
- `login.html` - صفحة تسجيل الدخول
- `admin-dashboard-7c.html` - لوحة تحكم الإدارة
- (الملفات الأخرى قيد التطوير)

---

## 🎨 **4. التصميم والألوان**

### **نظام الألوان المستخدم:**
```css
:root {
    --primary-bg: #1a1a1a;           /* الخلفية الرئيسية */
    --brand-primary: #8B4513;        /* البني الرياضي */
    --brand-secondary: #D2691E;      /* البرتقالي */
    --accent-dark: #2F4F4F;          /* الرمادي الداكن */
    --success: #28a745;              /* نجاح */
    --danger: #dc3545;               /* خطر */
    --warning: #ffc107;              /* تحذير */
    --info: #17a2b8;                 /* معلومات */
}
```

### **الخطوط المستخدمة:**
- **Cairo** - للنصوص العربية
- دعم كامل لـ RTL (من اليمين لليسار)

### **التصميم المتجاوب:**
- **Mobile:** 320px - 767px
- **Tablet:** 768px - 1023px
- **Desktop:** 1024px+

---

## 🔍 **5. استكشاف الأخطاء**

### **مشاكل شائعة وحلولها:**

#### **مشكلة: لا يعمل تسجيل الدخول**
- **الحل:** تأكد من استخدام بيانات الاختبار الصحيحة
- **الحل:** تحقق من وحدة التحكم للأخطاء

#### **مشكلة: المصادقة البيومترية لا تعمل**
- **الحل:** تأكد من دعم المتصفح لـ WebAuthn
- **الحل:** امنح الإذن للوصول للكاميرا/الأجهزة

#### **مشكلة: البيانات لا تُحفظ**
- **الحل:** تأكد من تفعيل localStorage في المتصفح
- **الحل:** تحقق من عدم وجود قيود أمنية

#### **مشكلة: التصميم لا يظهر بشكل صحيح**
- **الحل:** تأكد من اتصال الإنترنت (للمكتبات الخارجية)
- **الحل:** امسح ذاكرة التخزين المؤقت

---

## 📞 **6. الدعم والمساعدة**

### **للحصول على المساعدة:**
- راجع وحدة التحكم في المتصفح للأخطاء
- تأكد من استخدام بيانات الاختبار الصحيحة
- تحقق من دعم المتصفح للتقنيات المستخدمة

### **معلومات تقنية:**
- **الإصدار:** 1.0
- **تاريخ الإنشاء:** يونيو 2024
- **المطور:** نظام إدارة أكاديمية 7C

---

## ✅ **7. قائمة التحقق السريع**

### **قبل البدء:**
- [ ] تحميل جميع الملفات
- [ ] فتح `login.html` في المتصفح
- [ ] تجربة تسجيل الدخول بالبيانات التجريبية
- [ ] التأكد من عمل جميع الوظائف

### **اختبار الوظائف:**
- [ ] تسجيل الدخول بالطرق الثلاث
- [ ] المصادقة البيومترية
- [ ] لوحة تحكم الإدارة
- [ ] الذكاء الاصطناعي والتحليلات
- [ ] الإشعارات والتنبيهات
- [ ] تسجيل الخروج

---

**🎉 مبروك! أنت الآن جاهز لاستخدام نظام إدارة أكاديمية 7C الرياضية بكفاءة عالية!**
