<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أكاديمية 7C الرياضية | تطوير المواهب الرياضية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #2d3748;
        }
        
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 2rem;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #1a365d, #3182ce);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            font-weight: 900;
            box-shadow: 0 8px 25px rgba(26, 54, 93, 0.3);
        }
        
        .logo-text h1 {
            font-size: 1.5rem;
            font-weight: 800;
            color: #1a365d;
            margin-bottom: 2px;
        }
        
        .logo-text p {
            font-size: 0.9rem;
            color: #64748b;
        }
        
        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }
        
        .nav-link {
            color: #334155;
            text-decoration: none;
            font-weight: 600;
            padding: 10px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            background: #f1f5f9;
            color: #1e40af;
        }
        
        .cta-button {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 12px 25px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 700;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }
        
        .hero {
            min-height: 100vh;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        }
        
        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 2;
        }
        
        .hero-content {
            color: white;
        }
        
        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .hero-title {
            font-size: 3.5rem;
            font-weight: 900;
            line-height: 1.1;
            margin-bottom: 1.5rem;
        }
        
        .hero-highlight {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .hero-subtitle {
            font-size: 1.3rem;
            line-height: 1.7;
            margin-bottom: 2.5rem;
            color: #cbd5e1;
            font-weight: 400;
        }
        
        .hero-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            color: white;
            padding: 1.2rem 2.5rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.1rem;
            display: inline-flex;
            align-items: center;
            gap: 0.8rem;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(30, 64, 175, 0.4);
        }
        
        .btn-secondary {
            background: transparent;
            color: white;
            padding: 1.2rem 2.5rem;
            border: 2px solid white;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.1rem;
            display: inline-flex;
            align-items: center;
            gap: 0.8rem;
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: white;
            color: #1e40af;
        }
        
        .hero-stats {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .stats-header {
            text-align: center;
            margin-bottom: 1.5rem;
        }
        
        .stats-header h3 {
            color: white;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .stats-header p {
            color: #cbd5e1;
            font-size: 0.9rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 900;
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #e2e8f0;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .stats-footer {
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 10px;
            padding: 0.8rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .stats-footer span {
            color: white;
            font-weight: 600;
            font-size: 0.8rem;
        }
        
        @media (max-width: 1024px) {
            .hero-container {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 3rem;
            }
            .hero-title { font-size: 2.5rem; }
        }
        
        @media (max-width: 768px) {
            .nav-menu { display: none; }
            .hero-title { font-size: 2rem; }
            .hero-subtitle { font-size: 1.1rem; }
            .stats-grid { grid-template-columns: 1fr; }
        }

        /* تصميم قسم خطط الاشتراك */
        .subscription-section {
            padding: 5rem 0;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            position: relative;
        }

        .subscription-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
        }

        .subscription-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            position: relative;
            z-index: 2;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(59, 130, 246, 0.1);
            color: #1e40af;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 1rem;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 900;
            color: #1e293b;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: #64748b;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .plans-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .plan-card {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
        }

        .plan-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }

        .plan-card.featured {
            border-color: #f59e0b;
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
        }

        .plan-card.featured::before {
            content: 'الأكثر شعبية';
            position: absolute;
            top: 20px;
            right: -30px;
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            padding: 5px 40px;
            font-size: 12px;
            font-weight: bold;
            transform: rotate(45deg);
            box-shadow: 0 2px 10px rgba(245, 158, 11, 0.3);
        }

        .plan-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .plan-name {
            font-size: 1.8rem;
            font-weight: 800;
            color: #1e293b;
            text-align: center;
            margin-bottom: 0.5rem;
        }

        .plan-description {
            text-align: center;
            color: #64748b;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .plan-price {
            text-align: center;
            margin-bottom: 2rem;
        }

        .price-amount {
            font-size: 3rem;
            font-weight: 900;
            color: #1e293b;
            line-height: 1;
        }

        .price-currency {
            font-size: 1.2rem;
            color: #64748b;
            margin-right: 0.5rem;
        }

        .price-period {
            display: block;
            font-size: 1rem;
            color: #64748b;
            margin-top: 0.5rem;
        }

        .price-discount {
            background: #dcfce7;
            color: #166534;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-top: 0.5rem;
            display: inline-block;
        }

        .plan-features {
            list-style: none;
            margin-bottom: 2.5rem;
        }

        .plan-features li {
            display: flex;
            align-items: center;
            padding: 0.8rem 0;
            color: #374151;
            font-weight: 500;
        }

        .plan-features li i {
            color: #10b981;
            margin-left: 1rem;
            font-size: 1.1rem;
        }

        .plan-features li.unavailable {
            color: #9ca3af;
        }

        .plan-features li.unavailable i {
            color: #ef4444;
        }

        .plan-button {
            width: 100%;
            padding: 1rem 2rem;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .plan-button-primary {
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            color: white;
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
        }

        .plan-button-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(30, 64, 175, 0.4);
        }

        .plan-button-secondary {
            background: transparent;
            color: #1e40af;
            border: 2px solid #1e40af;
        }

        .plan-button-secondary:hover {
            background: #1e40af;
            color: white;
        }

        @media (max-width: 768px) {
            .plans-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .plan-card {
                padding: 2rem;
            }

            .section-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo">
                <div class="logo-icon">7C</div>
                <div class="logo-text">
                    <h1>أكاديمية 7C الرياضية</h1>
                    <p>تطوير المواهب الرياضية</p>
                </div>
            </div>
            
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">الرئيسية</a></li>
                <li><a href="#about" class="nav-link">عن الأكاديمية</a></li>
                <li><a href="#subscription" class="nav-link">خطط الاشتراك</a></li>
                <li><a href="#programs" class="nav-link">البرامج</a></li>
                <li><a href="#contact" class="nav-link">اتصل بنا</a></li>
            </ul>
            
            <a href="admin/" class="cta-button">
                <i class="fas fa-sign-in-alt"></i>
                دخول الإدارة
            </a>
        </div>
    </nav>

    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-star"></i>
                    <span>الأكاديمية الرياضية الرائدة في المملكة</span>
                </div>
                
                <h1 class="hero-title">
                    مرحباً بكم في
                    <span class="hero-highlight">أكاديمية 7C الرياضية</span>
                </h1>
                
                <p class="hero-subtitle">
                    نحن نؤمن بأن كل طفل يحمل في داخله بذرة موهبة رياضية تحتاج للرعاية والتطوير المتخصص. 
                    في أكاديمية 7C، نوفر بيئة احترافية ومتطورة لتنمية المواهب الرياضية وبناء جيل من الأبطال المتميزين.
                </p>
                
                <div class="hero-buttons">
                    <a href="#programs" class="btn-primary">
                        <i class="fas fa-rocket"></i>
                        ابدأ رحلتك الرياضية
                    </a>
                    <a href="#about" class="btn-secondary">
                        <i class="fas fa-play"></i>
                        تعرف علينا أكثر
                    </a>
                </div>
            </div>
            
            <div class="hero-stats">
                <div class="stats-header">
                    <h3>إحصائيات الأكاديمية</h3>
                    <p>أرقام تتحدث عن تميزنا وريادتنا</p>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">500+</div>
                        <div class="stat-label">لاعب مسجل</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">25</div>
                        <div class="stat-label">مدرب محترف</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">15</div>
                        <div class="stat-label">برنامج تدريبي</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">8</div>
                        <div class="stat-label">سنوات خبرة</div>
                    </div>
                </div>
                
                <div class="stats-footer">
                    <div class="status-indicator"></div>
                    <span>نحن نقبل تسجيلات جديدة - ابدأ رحلتك معنا اليوم</span>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم خطط الاشتراك -->
    <section id="subscription" class="subscription-section">
        <div class="subscription-container">
            <div class="section-header">
                <div class="section-badge">
                    <i class="fas fa-crown"></i>
                    <span>خطط الاشتراك المميزة</span>
                </div>
                <h2 class="section-title">اختر الخطة المناسبة لك</h2>
                <p class="section-subtitle">
                    نوفر لك مجموعة متنوعة من خطط الاشتراك المصممة خصيصاً لتلبية احتياجاتك وأهدافك الرياضية.
                    كل خطة تتضمن ميزات فريدة وخدمات متخصصة لضمان تجربة تدريبية استثنائية.
                </p>
            </div>

            <div class="plans-grid" id="subscriptionPlansGrid">
                <!-- خطة أساسية -->
                <div class="plan-card">
                    <div class="plan-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3 class="plan-name">الخطة الأساسية</h3>
                    <p class="plan-description">خطة مثالية للمبتدئين مع الميزات الأساسية لبدء رحلتك الرياضية</p>

                    <div class="plan-price">
                        <span class="price-amount">200<span class="price-currency">ريال</span></span>
                        <span class="price-period">شهرياً</span>
                        <span class="price-discount">وفر 20% - كان 250 ريال</span>
                    </div>

                    <ul class="plan-features">
                        <li><i class="fas fa-check"></i>تدريبات أساسية 3 مرات أسبوعياً</li>
                        <li><i class="fas fa-check"></i>وصول للملاعب الأساسية</li>
                        <li><i class="fas fa-check"></i>تقييم شهري للأداء</li>
                        <li><i class="fas fa-check"></i>دعم فني أساسي</li>
                        <li><i class="fas fa-check"></i>تطبيق الهاتف المحمول</li>
                        <li class="unavailable"><i class="fas fa-times"></i>تدريبات شخصية</li>
                        <li class="unavailable"><i class="fas fa-times"></i>تحليلات متقدمة</li>
                    </ul>

                    <a href="#" class="plan-button plan-button-secondary" onclick="selectPlan('basic')">
                        اختيار الخطة الأساسية
                    </a>
                </div>

                <!-- خطة متقدمة -->
                <div class="plan-card featured">
                    <div class="plan-icon" style="background: linear-gradient(135deg, #3b82f6, #1e40af);">
                        <i class="fas fa-gem"></i>
                    </div>
                    <h3 class="plan-name">الخطة المتقدمة</h3>
                    <p class="plan-description">خطة شاملة مع ميزات متقدمة ومدربين محترفين لتطوير مهاراتك</p>

                    <div class="plan-price">
                        <span class="price-amount">350<span class="price-currency">ريال</span></span>
                        <span class="price-period">شهرياً</span>
                        <span class="price-discount">وفر 15% عند الدفع السنوي</span>
                    </div>

                    <ul class="plan-features">
                        <li><i class="fas fa-check"></i>تدريبات متقدمة 5 مرات أسبوعياً</li>
                        <li><i class="fas fa-check"></i>وصول لجميع الملاعب</li>
                        <li><i class="fas fa-check"></i>تقييم أسبوعي للأداء</li>
                        <li><i class="fas fa-check"></i>تحليلات متقدمة بالذكاء الاصطناعي</li>
                        <li><i class="fas fa-check"></i>جلسات تدريب شخصية</li>
                        <li><i class="fas fa-check"></i>دعم فني متقدم 24/7</li>
                        <li><i class="fas fa-check"></i>تطبيق الهاتف المحمول المتقدم</li>
                    </ul>

                    <a href="#" class="plan-button plan-button-primary" onclick="selectPlan('premium')">
                        اختيار الخطة المتقدمة
                    </a>
                </div>

                <!-- خطة ذهبية -->
                <div class="plan-card">
                    <div class="plan-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                        <i class="fas fa-crown"></i>
                    </div>
                    <h3 class="plan-name">الخطة الذهبية</h3>
                    <p class="plan-description">خطة VIP مع جميع الميزات والخدمات الحصرية للوصول للاحتراف</p>

                    <div class="plan-price">
                        <span class="price-amount">500<span class="price-currency">ريال</span></span>
                        <span class="price-period">شهرياً</span>
                        <span class="price-discount">خصم 25% للثلاثة أشهر الأولى</span>
                    </div>

                    <ul class="plan-features">
                        <li><i class="fas fa-check"></i>تدريبات يومية مخصصة</li>
                        <li><i class="fas fa-check"></i>وصول حصري لجميع المرافق</li>
                        <li><i class="fas fa-check"></i>تقييم يومي للأداء</li>
                        <li><i class="fas fa-check"></i>مدرب شخصي مخصص</li>
                        <li><i class="fas fa-check"></i>برامج تغذية مخصصة</li>
                        <li><i class="fas fa-check"></i>جلسات علاج طبيعي</li>
                        <li><i class="fas fa-check"></i>دعم فني VIP 24/7</li>
                        <li><i class="fas fa-check"></i>أولوية في المباريات والبطولات</li>
                    </ul>

                    <a href="#" class="plan-button plan-button-secondary" onclick="selectPlan('vip')">
                        اختيار الخطة الذهبية
                    </a>
                </div>
            </div>

            <!-- مقارنة سريعة -->
            <div style="text-align: center; margin-top: 3rem;">
                <a href="#" class="btn-primary" onclick="showPlanComparison()" style="display: inline-flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-balance-scale"></i>
                    مقارنة تفصيلية للخطط
                </a>
            </div>
        </div>
    </section>

    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 4px 30px rgba(0,0,0,0.15)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.boxShadow = '0 2px 20px rgba(0,0,0,0.1)';
            }
        });

        // وظائف خطط الاشتراك
        function selectPlan(planType) {
            const planNames = {
                'basic': 'الخطة الأساسية',
                'premium': 'الخطة المتقدمة',
                'vip': 'الخطة الذهبية'
            };

            const planPrices = {
                'basic': '200 ريال شهرياً',
                'premium': '350 ريال شهرياً',
                'vip': '500 ريال شهرياً'
            };

            if (confirm('هل تريد اختيار ' + planNames[planType] + ' بسعر ' + planPrices[planType] + '؟\n\nسيتم توجيهك لصفحة التسجيل.')) {
                // يمكن توجيه المستخدم لصفحة التسجيل مع معرف الخطة
                window.location.href = 'registration.html?plan=' + planType;
            }
        }

        function showPlanComparison() {
            alert('سيتم فتح صفحة مقارنة تفصيلية للخطط قريباً!\n\nيمكنك الاتصال بنا للحصول على مزيد من التفاصيل.');
        }

        // تحريك الأرقام في الإحصائيات
        function animateNumbers() {
            const numbers = document.querySelectorAll('.stat-number');
            numbers.forEach(number => {
                const target = parseInt(number.textContent.replace('+', ''));
                let current = 0;
                const increment = target / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    number.textContent = Math.floor(current) + (target >= 100 ? '+' : '');
                }, 50);
            });
        }

        // تشغيل تحريك الأرقام عند التمرير
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateNumbers();
                    observer.unobserve(entry.target);
                }
            });
        });

        const statsSection = document.querySelector('.hero-stats');
        if (statsSection) {
            observer.observe(statsSection);
        }

        console.log('✅ تم تحميل موقع أكاديمية 7C الرياضية بنجاح');
    </script>
</body>
</html>
