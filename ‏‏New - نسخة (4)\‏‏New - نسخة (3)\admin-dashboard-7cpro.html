<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>لوحة تحكم إدارة المباريات - أكاديمية 7C</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <style>
        :root {
            --primary-bg: #1a1a1a;
            --brand-primary: #8B4513;
            --brand-secondary: #D2691E;
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --border-color: rgba(255, 255, 255, 0.2);
            --glass-bg: rgba(255, 255, 255, 0.05);
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
        }
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        body {
            background-color: var(--primary-bg);
            color: var(--text-primary);
            direction: rtl;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        header {
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            padding: 1rem 2rem;
            text-align: center;
            font-size: 1.5rem;
            font-weight: bold;
            box-shadow: 0 4px 10px rgba(139, 69, 19, 0.7);
        }
        main {
            flex: 1;
            max-width: 1400px;
            margin: 1rem auto;
            padding: 1rem;
            background: var(--glass-bg);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.5);
            display: flex;
            gap: 1.5rem;
        }
        nav.sidebar {
            width: 280px;
            background: var(--brand-primary);
            border-radius: 12px;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            height: fit-content;
        }
        nav.sidebar button {
            background: transparent;
            border: none;
            color: var(--text-primary);
            font-size: 1.1rem;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            text-align: right;
            transition: background 0.3s ease;
        }
        nav.sidebar button.active,
        nav.sidebar button:hover {
            background: var(--brand-secondary);
            box-shadow: 0 0 10px var(--brand-secondary);
        }
        section.content-section {
            flex: 1;
            background: var(--primary-bg);
            border-radius: 12px;
            padding: 1rem 1.5rem;
            display: none;
            flex-direction: column;
            gap: 1rem;
            color: var(--text-primary);
            overflow-y: auto;
            max-height: 80vh;
        }
        section.content-section.active {
            display: flex;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            color: var(--text-primary);
        }
        th, td {
            border: 1px solid var(--border-color);
            padding: 0.5rem;
            text-align: center;
        }
        th {
            background: var(--brand-primary);
        }
        .btn {
            background: var(--brand-primary);
            border: none;
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: background 0.3s ease;
        }
        .btn:hover {
            background: var(--brand-secondary);
        }
        .form-group {
            margin-bottom: 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
        }
        label {
            font-weight: bold;
        }
        input, select {
            padding: 0.5rem;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            background: var(--glass-bg);
            color: var(--text-primary);
        }
        /* إشعارات */
        #notification {
            position: fixed;
            top: 1rem;
            left: 50%;
            transform: translateX(-50%);
            background: var(--brand-secondary);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            box-shadow: 0 0 10px rgba(139,69,19,0.7);
            display: none;
            z-index: 1000;
        }
        /* استجابة */
        @media (max-width: 768px) {
            main {
                flex-direction: column;
                max-width: 95%;
            }
            nav.sidebar {
                width: 100%;
                flex-direction: row;
                overflow-x: auto;
                height: auto;
            }
            nav.sidebar button {
                flex: 1 0 auto;
                font-size: 1rem;
                padding: 0.5rem;
            }
            section.content-section {
                max-height: none;
            }
        }
    </style>
</head>
<body>
    <header>لوحة تحكم إدارة المباريات - أكاديمية 7C</header>
    <main>
        <nav class="sidebar" aria-label="التنقل بين الأقسام">
            <button class="active" data-section="matches">المباريات</button>
            <button data-section="teams">الفرق</button>
            <button data-section="referees">الحكام</button>
            <button data-section="venues">الملاعب</button>
        </nav>
        <section id="matches" class="content-section active" aria-label="إدارة المباريات">
            <h2>إدارة المباريات</h2>
            <button class="btn" id="addMatchBtn">إضافة مباراة جديدة</button>
            <table>
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>الوقت</th>
                        <th>الفريق الأول</th>
                        <th>الفريق الثاني</th>
                        <th>الملعب</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="matchesTableBody">
                    <!-- سيتم تعبئة المباريات هنا -->
                </tbody>
            </table>
        </section>
        <section id="teams" class="content-section" aria-label="إدارة الفرق">
            <h2>إدارة الفرق</h2>
            <button class="btn" id="addTeamBtn">إضافة فريق جديد</button>
            <table>
                <thead>
                    <tr>
                        <th>اسم الفريق</th>
                        <th>عدد اللاعبين</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="teamsTableBody">
                    <!-- سيتم تعبئة الفرق هنا -->
                </tbody>
            </table>
        </section>
        <section id="referees" class="content-section" aria-label="إدارة الحكام">
            <h2>إدارة الحكام</h2>
            <button class="btn" id="addRefereeBtn">إضافة حكم جديد</button>
            <table>
                <thead>
                    <tr>
                        <th>اسم الحكم</th>
                        <th>الخبرة (سنوات)</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="refereesTableBody">
                    <!-- سيتم تعبئة الحكام هنا -->
                </tbody>
            </table>
        </section>
        <section id="venues" class="content-section" aria-label="إدارة الملاعب">
            <h2>إدارة الملاعب</h2>
            <button class="btn" id="addVenueBtn">إضافة ملعب جديد</button>
            <table>
                <thead>
                    <tr>
                        <th>اسم الملعب</th>
                        <th>الموقع</th>
                        <th>السعة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="venuesTableBody">
                    <!-- سيتم تعبئة الملاعب هنا -->
                </tbody>
            </table>
        </section>
    </main>
    <div id="notification" role="alert" aria-live="assertive"></div>

    <script>
        // بيانات تجريبية
        const academyData = {
            players: [],
            coaches: [],
            matches: [],
            tournaments: [],
            teams: [],
            referees: [],
            venues: [],
            matchStats: [],
            formations: [],
            liveMatches: []
        };

        // تحميل بيانات تجريبية (يمكن تعديلها لاحقاً)
        function loadSampleData() {
            // إضافة فرق
            academyData.teams = [
                { id: 1, name: "الفريق الأول", players: [1,2,3,4,5] },
                { id: 2, name: "الفريق الثاني", players: [6,7,8,9,10] },
                { id: 3, name: "الفريق الثالث", players: [11,12,13,14,15] },
                { id: 4, name: "الفريق الرابع", players: [16,17,18,19,20] },
                { id: 5, name: "الفريق الخامس", players: [21,22,23,24,25] },
                { id: 6, name: "الفريق السادس", players: [26,27,28,29,30] },
                { id: 7, name: "الفريق السابع", players: [31,32,33,34,35] },
                { id: 8, name: "الفريق الثامن", players: [36,37,38,39,40] }
            ];
            // إضافة حكام
            academyData.referees = [
                { id: 1, name: "الحكم أحمد", experience: 10 },
                { id: 2, name: "الحكم محمد", experience: 8 },
                { id: 3, name: "الحكم علي", experience: 5 },
                { id: 4, name: "الحكم خالد", experience: 7 },
                { id: 5, name: "الحكم سامي", experience: 6 },
                { id: 6, name: "الحكم يوسف", experience: 9 }
            ];
            // إضافة ملاعب
            academyData.venues = [
                { id: 1, name: "الملعب الرئيسي", location: "المدينة الرياضية", capacity: 5000 },
                { id: 2, name: "الملعب الفرعي", location: "المدينة الرياضية", capacity: 2000 },
                { id: 3, name: "ملعب التدريب 1", location: "مركز التدريب", capacity: 1000 },
                { id: 4, name: "ملعب التدريب 2", location: "مركز التدريب", capacity: 1000 }
            ];
            // إضافة مباريات
            academyData.matches = [
                { id: 1, date: "2024-06-10", time: "16:00", teamA: 1, teamB: 2, venue: 1, status: "قادمة" },
                { id: 2, date: "2024-06-11", time: "18:00", teamA: 3, teamB: 4, venue: 2, status: "قادمة" },
                { id: 3, date: "2024-06-05", time: "15:00", teamA: 5, teamB: 6, venue: 3, status: "جارية" },
                { id: 4, date: "2024-06-01", time: "14:00", teamA: 7, teamB: 8, venue: 4, status: "منتهية" },
                { id: 5, date: "2024-06-12", time: "17:00", teamA: 1, teamB: 3, venue: 1, status: "قادمة" },
                { id: 6, date: "2024-06-13", time: "19:00", teamA: 2, teamB: 4, venue: 2, status: "قادمة" },
                { id: 7, date: "2024-06-14", time: "16:00", teamA: 5, teamB: 7, venue: 3, status: "قادمة" },
                { id: 8, date: "2024-06-15", time: "18:00", teamA: 6, teamB: 8, venue: 4, status: "قادمة" },
                { id: 9, date: "2024-06-16", time: "15:00", teamA: 1, teamB: 4, venue: 1, status: "قادمة" },
                { id: 10, date: "2024-06-17", time: "14:00", teamA: 2, teamB: 5, venue: 2, status: "قادمة" },
                { id: 11, date: "2024-06-18", time: "17:00", teamA: 3, teamB: 6, venue: 3, status: "قادمة" },
                { id: 12, date: "2024-06-19", time: "19:00", teamA: 4, teamB: 7, venue: 4, status: "قادمة" },
                { id: 13, date: "2024-06-20", time: "16:00", teamA: 5, teamB: 8, venue: 1, status: "قادمة" },
                { id: 14, date: "2024-06-21", time: "18:00", teamA: 6, teamB: 1, venue: 2, status: "قادمة" },
                { id: 15, date: "2024-06-22", time: "15:00", teamA: 7, teamB: 2, venue: 3, status: "قادمة" },
                { id: 16, date: "2024-06-23", time: "14:00", teamA: 8, teamB: 3, venue: 4, status: "قادمة" },
                { id: 17, date: "2024-06-24", time: "17:00", teamA: 1, teamB: 5, venue: 1, status: "قادمة" },
                { id: 18, date: "2024-06-25", time: "19:00", teamA: 2, teamB: 6, venue: 2, status: "قادمة" },
                { id: 19, date: "2024-06-26", time: "16:00", teamA: 3, teamB: 7, venue: 3, status: "قادمة" },
                { id: 20, date: "2024-06-27", time: "18:00", teamA: 4, teamB: 8, venue: 4, status: "قادمة" }
            ];
        }

        // تحديث شارة عدد المباريات القادمة
        function updateMatchesCount() {
            const count = academyData.matches.filter(m => m.status === "قادمة").length;
            document.getElementById('matchesCount').textContent = count;
        }

        // عرض قائمة المباريات
        function renderMatches() {
            const tbody = document.getElementById('matchesTableBody');
            tbody.innerHTML = '';
            academyData.matches.forEach(match => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${match.date}</td>
                    <td>${match.time}</td>
                    <td>${getTeamName(match.teamA)}</td>
                    <td>${getTeamName(match.teamB)}</td>
                    <td>${getVenueName(match.venue)}</td>
                    <td>${match.status}</td>
                    <td>
                        <button class="btn" onclick="editMatch(${match.id})">تعديل</button>
                        <button class="btn" onclick="deleteMatch(${match.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        }

        // الحصول على اسم الفريق من المعرف
        function getTeamName(id) {
            const team = academyData.teams.find(t => t.id === id);
            return team ? team.name : 'غير معروف';
        }

        // الحصول على اسم الملعب من المعرف
        function getVenueName(id) {
            const venue = academyData.venues.find(v => v.id === id);
            return venue ? venue.name : 'غير معروف';
        }

        // إضافة مباراة جديدة
        document.getElementById('addMatchBtn').addEventListener('click', () => {
            const date = prompt('أدخل تاريخ المباراة (YYYY-MM-DD):');
            if (!date) return;
            const time = prompt('أدخل وقت المباراة (HH:MM):');
            if (!time) return;
            const teamA = parseInt(prompt('أدخل معرف الفريق الأول:'));
            if (isNaN(teamA)) return;
            const teamB = parseInt(prompt('أدخل معرف الفريق الثاني:'));
            if (isNaN(teamB)) return;
            const venue = parseInt(prompt('أدخل معرف الملعب:'));
            if (isNaN(venue)) return;
            const status = 'قادمة';
            const newId = academyData.matches.length ? Math.max(...academyData.matches.map(m => m.id)) + 1 : 1;
            academyData.matches.push({ id: newId, date, time, teamA, teamB, venue, status });
            updateMatchesCount();
            renderMatches();
            showNotification('تم إضافة المباراة بنجاح');
        });

        // تعديل مباراة
        function editMatch(id) {
            const match = academyData.matches.find(m => m.id === id);
            if (!match) return alert('المباراة غير موجودة');
            const date = prompt('تعديل تاريخ المباراة (YYYY-MM-DD):', match.date);
            if (!date) return;
            const time = prompt('تعديل وقت المباراة (HH:MM):', match.time);
            if (!time) return;
            const status = prompt('تعديل حالة المباراة (قادمة، جارية، منتهية):', match.status);
            if (!status) return;
            match.date = date;
            match.time = time;
            match.status = status;
            updateMatchesCount();
            renderMatches();
            showNotification('تم تعديل المباراة بنجاح');
        }

        // حذف مباراة
        function deleteMatch(id) {
            if (!confirm('هل أنت متأكد من حذف هذه المباراة؟')) return;
            academyData.matches = academyData.matches.filter(m => m.id !== id);
            updateMatchesCount();
            renderMatches();
            showNotification('تم حذف المباراة بنجاح');
        }

        // عرض قائمة الفرق
        function renderTeams() {
            const tbody = document.getElementById('teamsTableBody');
            tbody.innerHTML = '';
            academyData.teams.forEach(team => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${team.name}</td>
                    <td>${team.players.length}</td>
                    <td>
                        <button class="btn" onclick="editTeam(${team.id})">تعديل</button>
                        <button class="btn" onclick="deleteTeam(${team.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        }

        // إضافة فريق جديد
        document.getElementById('addTeamBtn').addEventListener('click', () => {
            const name = prompt('أدخل اسم الفريق الجديد:');
            if (!name) return;
            const newId = academyData.teams.length ? Math.max(...academyData.teams.map(t => t.id)) + 1 : 1;
            academyData.teams.push({ id: newId, name, players: [] });
            renderTeams();
            showNotification('تم إضافة الفريق بنجاح');
        });

        // تعديل فريق
        function editTeam(id) {
            const team = academyData.teams.find(t => t.id === id);
            if (!team) return alert('الفريق غير موجود');
            const name = prompt('تعديل اسم الفريق:', team.name);
            if (!name) return;
            team.name = name;
            renderTeams();
            showNotification('تم تعديل الفريق بنجاح');
        }

        // حذف فريق
        function deleteTeam(id) {
            if (!confirm('هل أنت متأكد من حذف هذا الفريق؟')) return;
            academyData.teams = academyData.teams.filter(t => t.id !== id);
            renderTeams();
            showNotification('تم حذف الفريق بنجاح');
        }

        // عرض قائمة الحكام
        function renderReferees() {
            const tbody = document.getElementById('refereesTableBody');
            tbody.innerHTML = '';
            academyData.referees.forEach(ref => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${ref.name}</td>
                    <td>${ref.experience}</td>
                    <td>
                        <button class="btn" onclick="editReferee(${ref.id})">تعديل</button>
                        <button class="btn" onclick="deleteReferee(${ref.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        }

        // إضافة حكم جديد
        document.getElementById('addRefereeBtn').addEventListener('click', () => {
            const name = prompt('أدخل اسم الحكم الجديد:');
            if (!name) return;
            const experience = parseInt(prompt('أدخل سنوات الخبرة:'));
            if (isNaN(experience)) return;
            const newId = academyData.referees.length ? Math.max(...academyData.referees.map(r => r.id)) + 1 : 1;
            academyData.referees.push({ id: newId, name, experience });
            renderReferees();
            showNotification('تم إضافة الحكم بنجاح');
        });

        // تعديل حكم
        function editReferee(id) {
            const ref = academyData.referees.find(r => r.id === id);
            if (!ref) return alert('الحكم غير موجود');
            const name = prompt('تعديل اسم الحكم:', ref.name);
            if (!name) return;
            const experience = parseInt(prompt('تعديل سنوات الخبرة:', ref.experience));
            if (isNaN(experience)) return;
            ref.name = name;
            ref.experience = experience;
            renderReferees();
            showNotification('تم تعديل الحكم بنجاح');
        }

        // حذف حكم
        function deleteReferee(id) {
            if (!confirm('هل أنت متأكد من حذف هذا الحكم؟')) return;
            academyData.referees = academyData.referees.filter(r => r.id !== id);
            renderReferees();
            showNotification('تم حذف الحكم بنجاح');
        }

        // عرض قائمة الملاعب
        function renderVenues() {
            const tbody = document.getElementById('venuesTableBody');
            tbody.innerHTML = '';
            academyData.venues.forEach(venue => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${venue.name}</td>
                    <td>${venue.location}</td>
                    <td>${venue.capacity}</td>
                    <td>
                        <button class="btn" onclick="editVenue(${venue.id})">تعديل</button>
                        <button class="btn" onclick="deleteVenue(${venue.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        }

        // إضافة ملعب جديد
        document.getElementById('addVenueBtn').addEventListener('click', () => {
            const name = prompt('أدخل اسم الملعب:');
            if (!name) return;
            const location = prompt('أدخل موقع الملعب:');
            if (!location) return;
            const capacity = parseInt(prompt('أدخل سعة الملعب:'));
            if (isNaN(capacity)) return;
            const newId = academyData.venues.length ? Math.max(...academyData.venues.map(v => v.id)) + 1 : 1;
            academyData.venues.push({ id: newId, name, location, capacity });
            renderVenues();
            showNotification('تم إضافة الملعب بنجاح');
        });

        // تعديل ملعب
        function editVenue(id) {
            const venue = academyData.venues.find(v => v.id === id);
            if (!venue) return alert('الملعب غير موجود');
            const name = prompt('تعديل اسم الملعب:', venue.name);
            if (!name) return;
            const location = prompt('تعديل موقع الملعب:', venue.location);
            if (!location) return;
            const capacity = parseInt(prompt('تعديل سعة الملعب:', venue.capacity));
            if (isNaN(capacity)) return;
            venue.name = name;
            venue.location = location;
            venue.capacity = capacity;
            renderVenues();
            showNotification('تم تعديل الملعب بنجاح');
        }

        // حذف ملعب
        function deleteVenue(id) {
            if (!confirm('هل أنت متأكد من حذف هذا الملعب؟')) return;
            academyData.venues = academyData.venues.filter(v => v.id !== id);
            renderVenues();
            showNotification('تم حذف الملعب بنجاح');
        }

        // التنقل بين الأقسام
        const navButtons = document.querySelectorAll('nav.sidebar button');
        const sections = document.querySelectorAll('section.content-section');
        navButtons.forEach(button => {
            button.addEventListener('click', () => {
                navButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                const sectionId = button.getAttribute('data-section');
                sections.forEach(section => {
                    section.classList.toggle('active', section.id === sectionId);
                });
            });
        });

        // عرض الإشعارات
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.style.backgroundColor = type === 'error' ? 'var(--error-color)' : 'var(--brand-secondary)';
            notification.style.display = 'block';
            setTimeout(() => {
                notification.style.display = 'none';
            }, 3000);
        }

        // تهيئة البيانات والواجهة
        window.addEventListener('DOMContentLoaded', () => {
            loadSampleData();
            updateMatchesCount();
            renderMatches();
            renderTeams();
            renderReferees();
            renderVenues();
        });
    </script>
</body>
</html>
