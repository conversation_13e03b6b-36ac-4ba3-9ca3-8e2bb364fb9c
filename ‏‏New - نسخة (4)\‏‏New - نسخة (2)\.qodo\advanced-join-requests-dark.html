<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة طلبات الانضمام - أكاديمية 7C</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #0f172a; min-height: 100vh; color: #e2e8f0; }
        .glass { background: rgba(30, 41, 59, 0.7); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.05); }
        .action-btn { transition: all 0.3s ease; }
        .action-btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); }
        .progress-step { display: flex; align-items: center; margin-bottom: 10px; }
        .progress-step.completed .step-icon { background: #10b981; color: #e2e8f0; }
        .progress-step.active .step-icon { background: #3b82f6; color: #e2e8f0; }
        .progress-step .step-icon { width: 24px; height: 24px; border-radius: 50%; background: rgba(255, 255, 255, 0.2); display: flex; align-items: center; justify-content: center; font-size: 12px; margin-left: 10px; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 1000; backdrop-filter: blur(5px); }
        .modal.show { display: flex; align-items: center; justify-content: center; }
        .modal-content { background: #0f172a; border-radius: 20px; padding: 30px; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto; border: 1px solid rgba(255, 255, 255, 0.05); }
        .status-badge { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 500; }
        .status-pending { background: rgba(251, 191, 36, 0.2); color: #fbbf24; border: 1px solid rgba(251, 191, 36, 0.3); }
        .status-reviewing { background: rgba(59, 130, 246, 0.2); color: #3b82f6; border: 1px solid rgba(59, 130, 246, 0.3); }
        .status-approved { background: rgba(16, 185, 129, 0.2); color: #10b981; border: 1px solid rgba(16, 185, 129, 0.3); }
        .status-rejected { background: rgba(239, 68, 68, 0.2); color: #ef4444; border: 1px solid rgba(239, 68, 68, 0.3); }
        .status-payment { background: rgba(245, 158, 11, 0.2); color: #f59e0b; border: 1px solid rgba(245, 158, 11, 0.3); }
    </style>
</head>
<body>
    <div class="min-h-screen p-6">
        <div class="glass rounded-2xl p-6 mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-slate-100">إدارة طلبات الانضمام</h1>
                </div>
            </div>
        </div>
        <div class="glass rounded-2xl p-6 mb-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold text-slate-100">طلبات الانضمام</h2>
            </div>
            <div id="requestsList" class="space-y-4"></div>
        </div>
    </div>
    <div id="requestModal" class="modal">
        <div class="modal-content">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-slate-100">تفاصيل طلب الانضمام</h3>
                <button onclick="closeModal('requestModal')" class="text-slate-100/70 hover:text-slate-100">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="requestDetails"></div>
        </div>
    </div>
    <script>
    // تحميل الطلبات من قاعدة البيانات
    let requests = [];
    function loadRequests() {
        fetch('https://c7c.club/join_requests_api.php')
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                requests = data.data.map(row => ({
                    id: row.id,
                    name: row.fullName,
                    phone: row.phone,
                    email: row.email,
                    status: row.status,
                    plan: row.subscriptionPlan,
                    time: row.created_at ? new Date(row.created_at).toLocaleDateString('ar-SA') : '',
                }));
                renderRequests();
            }
        });
    }
    function renderRequests() {
        const list = document.getElementById('requestsList');
        list.innerHTML = '';
        requests.forEach(req => {
            let statusBadge = {
                'pending': '<span class="status-badge status-pending">في الانتظار</span>',
                'reviewing': '<span class="status-badge status-reviewing">قيد المراجعة</span>',
                'approved': '<span class="status-badge status-approved">مقبولة</span>',
                'payment_pending': '<span class="status-badge status-payment">انتظار الدفع</span>',
                'rejected': '<span class="status-badge status-rejected">مرفوضة</span>',
            }[req.status] || '';
            list.innerHTML += `
            <div class="bg-white/5 rounded-xl p-4 border border-white/10 hover:border-white/20 transition-all">
                <div class="flex items-center justify-between">
                    <div>
                        <h5 class="text-slate-100 font-bold">${req.name}</h5>
                        <p class="text-slate-100/60 text-sm">${req.phone} • ${req.email}</p>
                        <div class="flex items-center gap-2 mt-2">
                            ${statusBadge}
                            <span class="text-slate-100/50 text-xs">${req.time}</span>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <button onclick="viewRequestDetails('${req.id}')" class="action-btn bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm"><i class="fas fa-eye ml-1"></i>عرض</button>
                        <button onclick="approveRequest('${req.id}')" class="action-btn bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-sm"><i class="fas fa-check ml-1"></i>موافقة</button>
                        <button onclick="rejectRequest('${req.id}')" class="action-btn bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm"><i class="fas fa-times ml-1"></i>رفض</button>
                    </div>
                </div>
            </div>`;
        });
    }
    function viewRequestDetails(requestId) {
        const req = requests.find(r => r.id == requestId);
        if (!req) {
            document.getElementById('requestDetails').innerHTML = '<div class="text-red-400">لم يتم العثور على بيانات هذا الطلب.</div>';
            document.getElementById('requestModal').classList.add('show');
            return;
        }
        const requestDetails = `
            <div class="space-y-4">
                <div class="bg-white/5 rounded-xl p-4">
                    <h4 class="text-lg font-bold text-slate-100 mb-3">المعلومات الشخصية</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div><span class="text-slate-100/70">الاسم:</span> <span class="text-slate-100">${req.name || ''}</span></div>
                        <div><span class="text-slate-100/70">رقم الهاتف:</span> <span class="text-slate-100">${req.phone || ''}</span></div>
                        <div><span class="text-slate-100/70">البريد:</span> <span class="text-slate-100">${req.email || ''}</span></div>
                        <div><span class="text-slate-100/70">الخطة:</span> <span class="text-blue-400">${req.plan || ''}</span></div>
                        <div><span class="text-slate-100/70">الحالة:</span> <span class="text-slate-100">${req.status || ''}</span></div>
                    </div>
                </div>
                <div class="bg-white/5 rounded-xl p-4">
                    <h4 class="text-lg font-bold text-slate-100 mb-3">مراحل التقدم</h4>
                    <div class="space-y-2">
                        <div class="progress-step completed">
                            <div class="step-icon"><i class="fas fa-check text-xs"></i></div>
                            <span class="text-sm text-slate-100">تم التسجيل - ${req.time || ''}</span>
                        </div>
                        <div class="progress-step active">
                            <div class="step-icon">2</div>
                            <span class="text-sm text-slate-100">قيد المراجعة - جاري الآن</span>
                        </div>
                        <div class="progress-step">
                            <div class="step-icon">3</div>
                            <span class="text-sm text-slate-100/60">الموافقة</span>
                        </div>
                        <div class="progress-step">
                            <div class="step-icon">4</div>
                            <span class="text-sm text-slate-100/60">الدفع</span>
                        </div>
                        <div class="progress-step">
                            <div class="step-icon">5</div>
                            <span class="text-sm text-slate-100/60">بدء الاشتراك</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.getElementById('requestDetails').innerHTML = requestDetails;
        document.getElementById('requestModal').classList.add('show');
    }
    function closeModal(modalId) {
        document.getElementById(modalId).classList.remove('show');
    }
    function approveRequest(requestId) {
        alert('تمت الموافقة على الطلب رقم ' + requestId);
        closeModal('requestModal');
    }
    function rejectRequest(requestId) {
        alert('تم رفض الطلب رقم ' + requestId);
        closeModal('requestModal');
    }
    document.addEventListener('DOMContentLoaded', loadRequests);
    </script>
</body>
</html>
