# 🔑 دليل إعداد APIs للمرحلة الثانية

## نظرة عامة
هذا الدليل يوضح كيفية الحصول على مفاتيح APIs وتكاملها مع المنصة للحصول على توليد حقيقي بالذكاء الاصطناعي.

## 🤖 OpenAI APIs

### **1. الحصول على مفتاح OpenAI**
1. اذهب إلى [platform.openai.com](https://platform.openai.com)
2. أنشئ حساب أو سجل دخول
3. اذهب إلى "API Keys" في لوحة التحكم
4. اضغط "Create new secret key"
5. احفظ المفتاح في مكان آمن

### **2. تكامل DALL-E 3 لتوليد الصور**
```javascript
// إضافة إلى ai-cartoon-studio.html
async function generateWithDALLE(prompt) {
    const response = await fetch('https://api.openai.com/v1/images/generations', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer YOUR_OPENAI_API_KEY`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            model: "dall-e-3",
            prompt: prompt,
            n: 1,
            size: "1024x1024",
            quality: "standard",
            style: "vivid"
        })
    });
    
    const data = await response.json();
    return data.data[0].url;
}
```

### **3. تكامل GPT-4 لتوليد القصص**
```javascript
async function generateStoryWithGPT(characterData) {
    const prompt = `أنشئ قصة قصيرة لشخصية كرتونية:
    الاسم: ${characterData.name}
    العمر: ${characterData.age}
    النوع: ${characterData.type}
    الوصف: ${characterData.description}`;
    
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer YOUR_OPENAI_API_KEY`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            model: "gpt-4",
            messages: [
                {
                    role: "system",
                    content: "أنت كاتب قصص أطفال محترف. اكتب قصص قصيرة وممتعة للشخصيات الكرتونية."
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            max_tokens: 500,
            temperature: 0.8
        })
    });
    
    const data = await response.json();
    return data.choices[0].message.content;
}
```

## 🎨 Stability AI (Alternative)

### **1. الحصول على مفتاح Stability AI**
1. اذهب إلى [platform.stability.ai](https://platform.stability.ai)
2. أنشئ حساب
3. اذهب إلى "API Keys"
4. أنشئ مفتاح جديد

### **2. تكامل Stable Diffusion**
```javascript
async function generateWithStability(prompt) {
    const response = await fetch('https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer YOUR_STABILITY_API_KEY`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            text_prompts: [
                {
                    text: prompt,
                    weight: 1
                }
            ],
            cfg_scale: 7,
            height: 1024,
            width: 1024,
            samples: 1,
            steps: 30,
            style_preset: "anime"
        })
    });
    
    const data = await response.json();
    return `data:image/png;base64,${data.artifacts[0].base64}`;
}
```

## 🖼️ Cloudinary للتخزين

### **1. إعداد Cloudinary**
1. اذهب إلى [cloudinary.com](https://cloudinary.com)
2. أنشئ حساب مجاني
3. احصل على Cloud Name, API Key, API Secret

### **2. رفع الصور**
```javascript
async function uploadToCloudinary(imageData) {
    const formData = new FormData();
    formData.append('file', imageData);
    formData.append('upload_preset', 'YOUR_UPLOAD_PRESET');
    
    const response = await fetch(`https://api.cloudinary.com/v1_1/YOUR_CLOUD_NAME/image/upload`, {
        method: 'POST',
        body: formData
    });
    
    const data = await response.json();
    return data.secure_url;
}
```

## 🔧 تحديث الكود الحالي

### **1. استبدال دالة التوليد المحاكية**
```javascript
// استبدال simulateAIGeneration بـ:
async function generateWithAI(characterData) {
    try {
        // إنشاء prompt مفصل
        const prompt = createDetailedPrompt(characterData);
        
        // توليد الصورة
        const imageUrl = await generateWithDALLE(prompt);
        
        // توليد القصة
        const story = await generateStoryWithGPT(characterData);
        
        // إنشاء الشخصية
        const character = {
            id: Date.now(),
            ...characterData,
            imageUrl: imageUrl,
            story: story,
            createdAt: new Date().toISOString(),
            personality: generatePersonality(characterData)
        };
        
        return character;
    } catch (error) {
        console.error('خطأ في التوليد:', error);
        // العودة للطريقة المحاكية في حالة الخطأ
        return simulateAIGeneration(characterData);
    }
}
```

### **2. إنشاء prompt مفصل**
```javascript
function createDetailedPrompt(data) {
    const styleMap = {
        'كرتوني': 'cartoon style',
        'أنمي': 'anime style',
        'ديزني': 'Disney style',
        'بيكسار': 'Pixar 3D style',
        'تشيبي': 'chibi style',
        'واقعي': 'realistic style'
    };
    
    const typeMap = {
        'بطل': 'heroic character',
        'شرير': 'villain character',
        'مساعد': 'sidekick character',
        'كوميدي': 'funny character',
        'حكيم': 'wise character',
        'غامض': 'mysterious character'
    };
    
    return `${styleMap[data.artStyle] || 'cartoon style'} ${typeMap[data.type] || 'character'}, 
    ${data.description}, 
    ${data.age} ${data.gender}, 
    skin color: ${data.skinColor}, 
    hair color: ${data.hairColor}, 
    eye color: ${data.eyeColor}, 
    clothes color: ${data.clothesColor}, 
    background: ${data.backgroundColor}, 
    high quality, detailed, professional animation character design`;
}
```

## 🔒 أمان المفاتيح

### **1. متغيرات البيئة (للإنتاج)**
```javascript
// إنشاء ملف .env
OPENAI_API_KEY=your_openai_key_here
STABILITY_API_KEY=your_stability_key_here
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
```

### **2. Backend Proxy (موصى به)**
```javascript
// إنشاء خادم Node.js بسيط
const express = require('express');
const app = express();

app.post('/api/generate-image', async (req, res) => {
    try {
        const { prompt } = req.body;
        const imageUrl = await generateWithDALLE(prompt);
        res.json({ imageUrl });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});
```

## 💰 تكلفة APIs

### **OpenAI Pricing (تقريبي)**
- **DALL-E 3**: $0.040 لكل صورة (1024x1024)
- **GPT-4**: $0.03 لكل 1K tokens

### **Stability AI Pricing**
- **Stable Diffusion XL**: $0.035 لكل صورة

### **نصائح لتوفير التكلفة**
1. استخدم cache للصور المولدة
2. قلل من عدد الطلبات المكررة
3. استخدم أحجام صور أصغر للمعاينة
4. اعرض تحذيرات للمستخدمين حول التكلفة

## 🧪 اختبار التكامل

### **1. اختبار بسيط**
```javascript
// اختبار الاتصال بـ OpenAI
async function testOpenAI() {
    try {
        const response = await fetch('https://api.openai.com/v1/models', {
            headers: {
                'Authorization': `Bearer YOUR_API_KEY`
            }
        });
        
        if (response.ok) {
            console.log('✅ اتصال OpenAI ناجح');
        } else {
            console.log('❌ فشل اتصال OpenAI');
        }
    } catch (error) {
        console.log('❌ خطأ في الاتصال:', error);
    }
}
```

### **2. اختبار التوليد**
```javascript
// اختبار توليد صورة بسيطة
async function testImageGeneration() {
    const testPrompt = "cartoon character, friendly, colorful";
    try {
        const imageUrl = await generateWithDALLE(testPrompt);
        console.log('✅ تم توليد الصورة:', imageUrl);
    } catch (error) {
        console.log('❌ فشل التوليد:', error);
    }
}
```

## 📋 قائمة المراجعة

### **قبل التطبيق**
- [ ] الحصول على مفاتيح APIs
- [ ] اختبار الاتصال
- [ ] إعداد نظام الأمان
- [ ] تحديد حدود الاستخدام
- [ ] إنشاء نظام cache

### **بعد التطبيق**
- [ ] مراقبة الاستخدام والتكلفة
- [ ] تحسين الـ prompts
- [ ] جمع ملاحظات المستخدمين
- [ ] تحديث الوثائق

---

**🔑 دليل إعداد APIs - استوديو الشخصيات الكرتونية الذكي**

*"الخطوة التالية نحو الذكاء الاصطناعي الحقيقي"*
