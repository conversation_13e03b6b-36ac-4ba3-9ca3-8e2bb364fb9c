<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>متجر أكاديمية 7C الذكي</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #23272f 100%);
            color: #f3f4f6;
            margin: 0;
            padding: 0;
        }
        .main-header {
            display: flex;
            align-items: center;
            gap: 24px;
            background: linear-gradient(135deg, #1a1a1a 0%, #8B4513 50%, #D2691E 100%);
            padding: 24px 32px 16px 32px;
            border-radius: 0 0 32px 32px;
            box-shadow: 0 4px 24px #1a1a1a44;
        }
        .main-header img {
            width: 80px; height: 80px; border-radius: 18px; box-shadow: 0 2px 8px #8B4513;
        }
        .main-header .title {
            font-size: 2.2rem;
            font-weight: bold;
            color: #FFD700;
            letter-spacing: 1px;
        }
        .store-container {
            max-width: 1200px;
            margin: 32px auto;
            background: rgba(30,41,59,0.7);
            border-radius: 24px;
            box-shadow: 0 2px 16px #23272f55;
            padding: 32px 24px;
        }
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
            gap: 24px;
        }
        .product-card {
            background: linear-gradient(135deg, #23272f 60%, #8B4513 100%);
            border-radius: 18px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px #1a1a1a33;
            color: #fff;
            position: relative;
        }
        .product-card img {
            width: 120px; height: 120px; object-fit: contain; border-radius: 12px; margin-bottom: 12px;
            background: #fff;
        }
        .product-card .name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 6px;
        }
        .product-card .price {
            color: #10b981;
            font-weight: bold;
            margin-bottom: 4px;
        }
        .product-card .points {
            color: #D2691E;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .product-card .buy-btn {
            background: linear-gradient(135deg, #8B4513, #D2691E);
            color: #fff;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-size: 1rem;
            cursor: pointer;
            margin-top: 8px;
            transition: background 0.2s;
        }
        .product-card .buy-btn:hover {
            background: linear-gradient(135deg, #D2691E, #8B4513);
        }
        .cart-section {
            background: #23272f;
            border-radius: 18px;
            padding: 24px;
            margin-top: 32px;
            box-shadow: 0 2px 8px #1a1a1a22;
        }
        .cart-header {
            font-size: 1.2rem;
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 12px;
        }
        .cart-list {
            margin-bottom: 16px;
        }
        .cart-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #1a1a1a;
            border-radius: 8px;
            padding: 10px 16px;
            margin-bottom: 8px;
        }
        .cart-item .remove-btn {
            color: #ef4444;
            background: none;
            border: none;
            font-size: 1.1rem;
            cursor: pointer;
        }
        .cart-summary {
            color: #fff;
            font-size: 1.1rem;
            margin-bottom: 12px;
        }
        .cart-actions button {
            margin-left: 8px;
        }
        @media (max-width: 700px) {
            .main-header { flex-direction: column; gap: 12px; padding: 16px 8px; }
            .store-container { padding: 12px 4px; }
        }
    </style>
</head>
<body>
    <div class="main-header">
        <img src="academy-logo.png" alt="شعار الأكاديمية">
        <span class="title">متجر أكاديمية 7C الذكي</span>
    </div>
    <div class="store-container">
        <div class="products-grid" id="products-grid"></div>
        <div class="cart-section">
            <div class="cart-header">سلة المشتريات</div>
            <div class="cart-list" id="cart-list"></div>
            <div class="cart-summary" id="cart-summary"></div>
            <div class="cart-actions">
                <button onclick="checkout('pay')" class="buy-btn"><i class="fas fa-credit-card ml-2"></i>دفع إلكتروني</button>
                <button onclick="checkout('loyalty')" class="buy-btn"><i class="fas fa-gift ml-2"></i>شراء بنقاط الولاء</button>
            </div>
        </div>
    </div>
    <script>
    // منتجات تجريبية
    const demoProducts = [
        {id:1, name:'تيشيرت أكاديمية 7C', price:120, points:80, img:'https://i.imgur.com/1Q9Z1Zm.png'},
        {id:2, name:'كرة قدم احترافية', price:90, points:60, img:'https://i.imgur.com/8Q1Q2Zm.png'},
        {id:3, name:'حقيبة رياضية', price:60, points:40, img:'https://i.imgur.com/3Q9Q3Zm.png'},
        {id:4, name:'زجاجة ماء ذكية', price:30, points:20, img:'https://i.imgur.com/4Q9Q4Zm.png'}
    ];
    if (!localStorage.getItem('souq7c_products')) {
        localStorage.setItem('souq7c_products', JSON.stringify(demoProducts));
    }
    // عرض المنتجات
    function renderProducts() {
        const products = JSON.parse(localStorage.getItem('souq7c_products') || '[]');
        let html = '';
        products.forEach(p => {
            html += `<div class='product-card'>
                <img src='${p.img}' alt='${p.name}'>
                <div class='name'>${p.name}</div>
                <div class='price'>${p.price} ريال</div>
                <div class='points'>${p.points} نقطة ولاء</div>
                <button class='buy-btn' onclick='addToCart(${p.id})'><i class="fas fa-cart-plus ml-2"></i>إضافة للسلة</button>
            </div>`;
        });
        document.getElementById('products-grid').innerHTML = html;
    }
    // سلة المشتريات
    let cart = [];
    function addToCart(id) {
        const products = JSON.parse(localStorage.getItem('souq7c_products') || '[]');
        const prod = products.find(p => p.id === id);
        if (!prod) return;
        cart.push(prod);
        renderCart();
        Swal.fire('تمت الإضافة', 'تمت إضافة المنتج إلى السلة', 'success');
    }
    function renderCart() {
        let html = '';
        let total = 0, totalPoints = 0;
        cart.forEach((p, i) => {
            html += `<div class='cart-item'>
                <span>${p.name}</span>
                <span class='text-green-400 font-bold'>${p.price} ريال</span>
                <span class='text-lime-400 font-bold'>${p.points} نقطة</span>
                <button class='remove-btn' onclick='removeFromCart(${i})'><i class='fas fa-trash'></i></button>
            </div>`;
            total += p.price;
            totalPoints += p.points;
        });
        document.getElementById('cart-list').innerHTML = html || '<div class="text-gray-400">السلة فارغة</div>';
        document.getElementById('cart-summary').innerHTML = `الإجمالي: <span class='text-green-400 font-bold'>${total} ريال</span> أو <span class='text-lime-400 font-bold'>${totalPoints} نقطة</span>`;
    }
    function removeFromCart(i) {
        cart.splice(i, 1);
        renderCart();
    }
    function checkout(method) {
        if (!cart.length) {
            Swal.fire('تنبيه', 'السلة فارغة', 'warning');
            return;
        }
        if (method === 'loyalty') {
            Swal.fire('تم الشراء', 'تم شراء المنتجات بنقاط الولاء بنجاح! (محاكاة)', 'success');
        } else {
            Swal.fire('تم الدفع', 'تم الدفع الإلكتروني بنجاح! (محاكاة)', 'success');
        }
        cart = [];
        renderCart();
    }
    // عند تحميل الصفحة
    renderProducts();
    renderCart();
    </script>
</body>
</html>
