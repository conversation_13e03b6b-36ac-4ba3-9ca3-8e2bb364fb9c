<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة استقبال طلبات الانضمام - أكاديمية 7C</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: #0f172a; }
        .glass { background: rgba(255,255,255,0.08); backdrop-filter: blur(25px); border: 1px solid rgba(255,255,255,0.15); box-shadow: 0 8px 32px rgba(0,0,0,0.3); }
        .dark-mode { background: #0f172a; color: #e0e7ef; }
        .ai-badge { background: linear-gradient(90deg,#6366f1,#a21caf); color: #fff; border-radius: 8px; padding: 2px 10px; font-size: 0.9em; margin-right: 8px; }
        .stamp {
            position: absolute;
            right: 20px;
            bottom: 20px;
            width: 120px;
            opacity: 0.25;
        }
        .signature {
            font-family: 'Cairo', cursive; font-size: 1.2em; color: #6366f1; margin-top: 10px;
        }
        .player-card {
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .player-card:hover {
            transform: translateY(-6px) scale(1.03);
            box-shadow: 0 8px 32px rgba(139,92,246,0.2);
        }
    </style>
</head>
<body class="dark-mode">
    <!-- Plans Comparison Section -->
    <div class="glass p-8 m-4 rounded-2xl">
        <h2 class="text-2xl font-bold mb-6 text-center text-slate-100">مقارنة خطط الاشتراك</h2>
                <div class="overflow-x-auto">
            <table class="min-w-full text-slate-100 text-center border-separate border-spacing-y-2">
                <thead>
                    <tr class="bg-slate-800/60">
                        <th class="p-3"></th>
                        <th class="p-3 text-xl font-bold bg-gradient-to-r from-gray-400 to-gray-600 bg-clip-text text-transparent">لاعب 7C فضي<br><span class="text-lg text-slate-200">250/ الشهر</span><br><span class="text-xs bg-green-600/30 text-green-300 rounded px-2 py-1">الأكثر شراء</span></th>
                        <th class="p-3 text-xl font-bold bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent">لاعب 7C ذهبي<br><span class="text-lg text-slate-200">350/ الشهر</span></th>
                    </tr>
                </thead>
                <tbody class="text-lg">
                    <tr><td class="p-2">الزي الرياضي الكامل</td><td>✔️</td><td>✔️</td></tr>
                    <tr><td class="p-2">إشتراك تدريب 30 يوم</td><td>✔️</td><td>✔️</td></tr>
                    <tr><td class="p-2">تدريب خاص إضافي (Private)</td><td>✔️</td><td>✔️</td></tr>
                    <tr><td class="p-2">شامل البطولات والمباريات</td><td>✔️</td><td>✔️</td></tr>
                    <tr><td class="p-2">ملف اللاعب المطور</td><td>✔️</td><td>✔️</td></tr>
                    <tr><td class="p-2">تعليق الإستراك</td><td>✔️</td><td>✔️</td></tr>
                    <tr><td class="p-2">إنتاج للاعب من المركز الإعلامي</td><td>✔️</td><td>✔️</td></tr>
                    <tr><td class="p-2">جهاز GPS للاعب لقياس العلامات الحيوية</td><td>✔️</td><td>✔️</td></tr>
                    <tr><td class="p-2">تطبيق إلكتروني لإدارة الإشتراك</td><td>✔️</td><td>✔️</td></tr>
                    <tr><td class="p-2">توثيق المشاركات الرسمية للاعب</td><td>✔️</td><td>✔️</td></tr>
                    <tr><td class="p-2">تثبيت فوري، إذا لم نتمكن من إرضائك، فسنعوضك.</td><td>✔️</td><td>✔️</td></tr>
                </tbody>
            </table>
        </div>
        <div class="flex flex-col md:flex-row gap-8 justify-center mt-8">
            <div class="player-card glass p-6 flex-1 flex flex-col items-center border-2 border-gray-400">
                <div class="text-2xl font-bold bg-gradient-to-r from-gray-400 to-gray-600 bg-clip-text text-transparent mb-2">لاعب 7C فضي</div>
                <div class="text-lg text-slate-200 mb-2">250/ الشهر</div>
                <div class="text-xs bg-green-600/30 text-green-300 rounded px-2 py-1 mb-2">الأكثر شراء</div>
                <ul class="text-slate-100 text-right list-disc pr-6 mb-4">
                    <li>الزي الرياضي الكامل</li>
                    <li>إشتراك تدريب 30 يوم</li>
                    <li>تدريب خاص إضافي (Private)</li>
                    <li>شامل البطولات والمباريات</li>
                    <li>ملف اللاعب المطور</li>
                    <li>تعليق الإستراك</li>
                    <li>إنتاج للاعب من المركز الإعلامي</li>
                    <li>جهاز GPS للاعب لقياس العلامات الحيوية</li>
                    <li>تطبيق إلكتروني لإدارة الإشتراك</li>
                    <li>توثيق المشاركات الرسمية للاعب</li>
                    <li>تثبيت فوري، إذا لم نتمكن من إرضائك، فسنعوضك.</li>
                </ul>
                <button class="bg-gradient-to-r from-gray-400 to-gray-600 text-white px-8 py-3 rounded-xl font-bold text-lg mt-auto">اشترك الآن</button>
            </div>
            <div class="player-card glass p-6 flex-1 flex flex-col items-center border-2 border-yellow-400">
                <div class="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent mb-2">لاعب 7C ذهبي</div>
                <div class="text-lg text-slate-200 mb-2">350/ الشهر</div>
                <ul class="text-slate-100 text-right list-disc pr-6 mb-4">
                    <li>الزي الرياضي الكامل</li>
                    <li>إشتراك تدريب 30يوم</li>
                    <li>تدريب خاص إضافي (Private)</li>
                    <li>شامل البطولات والمباريات</li>
                    <li>ملف اللاعب المطور</li>
                    <li>تعليق الإستراك</li>
                    <li>إنتاج للاعب من المركز الإعلامي</li>
                    <li>جهاز GPS لقياس العلامات الحيوية للاعب</li>
                    <li>تطبيق إلكتروني لإدارة الإشتراك</li>
                    <li>توثيق المشاركات الرسمية للاعب</li>
                    <li>تثبيت فوري، إذا لم نتمكن من إرضائك، فسنعوضك.</li>
                </ul>
                <button class="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-8 py-3 rounded-xl font-bold text-lg mt-auto">اشترك الآن</button>
            </div>
        </div>

        <!-- Modal for Add/Edit Plan -->
        <div id="plan-modal" class="fixed inset-0 bg-black/60 flex items-center justify-center z-50 hidden">
            <div class="glass p-8 rounded-2xl w-full max-w-lg relative">
                <button onclick="closePlanModal()" class="absolute left-4 top-4 text-slate-400 hover:text-red-400 text-2xl"><i class="fas fa-times"></i></button>
                <h2 id="plan-modal-title" class="text-xl font-bold mb-4 text-center">إضافة/تعديل خطة</h2>
                <form id="plan-form" class="flex flex-col gap-4">
                    <input id="plan-name" type="text" class="bg-slate-800/60 border border-slate-600/30 rounded-xl p-2 text-slate-100" placeholder="اسم الخطة" required>
                    <input id="plan-price" type="number" class="bg-slate-800/60 border border-slate-600/30 rounded-xl p-2 text-slate-100" placeholder="سعر الخطة (ر.س/شهر)" required>
                    <textarea id="plan-features" class="bg-slate-800/60 border border-slate-600/30 rounded-xl p-2 text-slate-100" placeholder="المميزات (كل ميزة بسطر)" rows="5" required></textarea>
                    <label class="flex items-center gap-2"><input type="checkbox" id="plan-home" class="accent-blue-500">عرض في الصفحة الرئيسية</label>
                    <div class="flex gap-2 justify-end">
                        <button type="button" onclick="closePlanModal()" class="bg-slate-600 hover:bg-slate-700 text-white px-4 py-2 rounded-xl">إلغاء</button>
                        <button type="submit" class="bg-blue-700 hover:bg-blue-800 text-white px-4 py-2 rounded-xl font-bold">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- Header -->
    <div class="glass p-6 m-4 rounded-2xl flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                لوحة استقبال طلبات الانضمام <span class="ai-badge">مدعومة بالذكاء الاصطناعي</span>
            </h1>
            <p class="text-slate-300 mt-2">تحكم كامل واحترافي في جميع الطلبات - أكاديمية 7C الرياضية</p>
        </div>
        <div class="flex items-center gap-4">
            <button id="toggleDark" class="bg-slate-800/60 px-4 py-2 rounded-lg border border-slate-600/30 text-slate-200 hover:bg-slate-700 transition-all">
                <i class="fas fa-moon"></i> الوضع الليلي
            </button>
            <span class="bg-green-600/20 px-4 py-2 rounded-lg border border-green-600/30">
                <i class="fas fa-bolt text-green-400 ml-2"></i> إشعارات لحظية مفعلة
            </span>
        </div>
    </div>
    <!-- Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 m-4">
        <div class="glass p-6 rounded-2xl flex flex-col items-center">
            <div class="text-3xl font-bold text-blue-400" id="stat-total">0</div>
            <div class="text-slate-300 mt-2">إجمالي الطلبات</div>
        </div>
        <div class="glass p-6 rounded-2xl flex flex-col items-center">
            <div class="text-3xl font-bold text-purple-400" id="stat-pending">0</div>
            <div class="text-slate-300 mt-2">طلبات قيد المراجعة</div>
        </div>
        <div class="glass p-6 rounded-2xl flex flex-col items-center">
            <div class="text-3xl font-bold text-green-400" id="stat-accepted">0</div>
            <div class="text-slate-300 mt-2">طلبات مقبولة</div>
        </div>
        <div class="glass p-6 rounded-2xl flex flex-col items-center">
            <div class="text-3xl font-bold text-red-400" id="stat-rejected">0</div>
            <div class="text-slate-300 mt-2">طلبات مرفوضة</div>
        </div>
    </div>
    <!-- Filters & Search -->
    <div class="glass p-4 m-4 rounded-2xl flex flex-wrap gap-4 items-center justify-between">
        <div class="flex gap-4 flex-wrap">
            <select id="filter-category" class="bg-slate-800/60 border border-slate-600/30 rounded-xl p-2 text-slate-100">
                <option value="">كل الفئات</option>
                <option value="أشبال">أشبال</option>
                <option value="براعم">براعم</option>
                <option value="ناشئين">ناشئين</option>
                <option value="شباب">شباب</option>
            </select>
            <select id="filter-stage" class="bg-slate-800/60 border border-slate-600/30 rounded-xl p-2 text-slate-100">
                <option value="">كل المراحل</option>
                <option value="جديد">جديد</option>
                <option value="مراجعة">مراجعة</option>
                <option value="مقبول">مقبول</option>
                <option value="مرفوض">مرفوض</option>
                <option value="مكتمل">مكتمل</option>
            </select>
            <select id="filter-source" class="bg-slate-800/60 border border-slate-600/30 rounded-xl p-2 text-slate-100">
                <option value="">كل المصادر</option>
                <option value="جوال">جوال</option>
                <option value="كمبيوتر">كمبيوتر</option>
                <option value="أخرى">أخرى</option>
            </select>
        </div>
        <input id="search" type="text" placeholder="بحث بالاسم أو رقم الهوية..." class="bg-slate-800/60 border border-slate-600/30 rounded-xl p-2 text-slate-100 w-64">
    </div>
    <!-- Requests Table -->
    <div class="glass p-4 m-4 rounded-2xl overflow-x-auto">
        <table class="min-w-full text-slate-100 text-center">
            <thead>
                <tr class="bg-slate-800/60">
                    <th class="p-2">#</th>
                    <th class="p-2">الاسم</th>
                    <th class="p-2">الفئة</th>
                    <th class="p-2">الخطة</th>
                    <th class="p-2">المرحلة</th>
                    <th class="p-2">المصدر</th>
                    <th class="p-2">تاريخ الطلب</th>
                    <th class="p-2">الحالة</th>
                    <th class="p-2">التحكم</th>
                </tr>
            </thead>
            <tbody id="requests-table">
                <!-- الطلبات تظهر هنا -->
            </tbody>
        </table>
    </div>
    <!-- Player Details Modal -->
    <div id="player-modal" class="fixed inset-0 bg-black/60 flex items-center justify-center z-50 hidden">
        <div class="glass p-8 rounded-2xl w-full max-w-2xl relative">
            <button onclick="closePlayerModal()" class="absolute left-4 top-4 text-slate-400 hover:text-red-400 text-2xl"><i class="fas fa-times"></i></button>
            <div class="flex gap-8 items-center">
                <img id="player-modal-img" src="" alt="صورة اللاعب" class="w-32 h-32 rounded-full border-4 border-blue-400 object-cover">
                <div>
                    <h2 id="player-modal-name" class="text-2xl font-bold mb-2"></h2>
                    <div class="mb-2"><span class="text-slate-400">رقم الهوية:</span> <span id="player-modal-id"></span></div>
                    <div class="mb-2"><span class="text-slate-400">الفئة:</span> <span id="player-modal-category"></span></div>
                    <div class="mb-2"><span class="text-slate-400">الخطة:</span> <span id="player-modal-plan"></span></div>
                    <div class="mb-2"><span class="text-slate-400">المرحلة الحالية:</span> <span id="player-modal-stage"></span></div>
                    <div class="mb-2"><span class="text-slate-400">المصدر:</span> <span id="player-modal-source"></span></div>
                    <div class="mb-2"><span class="text-slate-400">تاريخ الطلب:</span> <span id="player-modal-date"></span></div>
                </div>
            </div>
            <div class="mt-6">
                <h3 class="text-lg font-bold mb-2">سجل مراحل الطلب</h3>
                <ul id="player-modal-stages" class="text-slate-200 text-sm list-disc pr-6">
                    <!-- مراحل الطلب -->
                </ul>
            </div>
            <div class="mt-6 flex gap-4">
                <button class="bg-green-600 hover:bg-green-700 px-6 py-2 rounded-xl transition-all" onclick="acceptRequest()"><i class="fas fa-check ml-2"></i>قبول</button>
                <button class="bg-red-600 hover:bg-red-700 px-6 py-2 rounded-xl transition-all" onclick="rejectRequest()"><i class="fas fa-times ml-2"></i>رفض</button>
                <button class="bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-xl transition-all" onclick="sendActivation()"><i class="fas fa-paper-plane ml-2"></i>إرسال تفعيل</button>
                <button class="bg-yellow-600 hover:bg-yellow-700 px-6 py-2 rounded-xl transition-all" onclick="printReceipt()"><i class="fas fa-file-invoice ml-2"></i>سند اشتراك</button>
            </div>
        </div>
    </div>
    <!-- Last Registered Players by Category -->
    <div class="glass p-6 m-4 rounded-2xl">
        <h2 class="text-xl font-bold mb-4 text-slate-200">آخر المسجلين حسب الفئة</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6" id="last-players">
            <!-- بطاقات اللاعبين -->
        </div>
    </div>
    <!-- AI Assistant -->
    <div class="fixed bottom-8 right-8 z-50">
        <button onclick="toggleAIAssistant()" class="bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full p-4 shadow-lg hover:scale-110 transition-all">
            <i class="fas fa-robot text-2xl"></i>
        </button>
        <div id="ai-assistant" class="hidden mt-4 glass p-6 rounded-2xl w-80">
            <h3 class="font-bold mb-2">مساعد الذكاء الاصطناعي</h3>
            <input id="ai-input" type="text" class="w-full bg-slate-800/60 border border-slate-600/30 rounded-xl p-2 text-slate-100 mb-2" placeholder="اسأل عن الطلبات أو اللاعبين...">
            <button onclick="askAI()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-xl transition-all w-full">اسأل الذكاء الاصطناعي</button>
            <div id="ai-response" class="mt-2 text-slate-200 text-sm"></div>
        </div>
    </div>
    <!-- Receipt Modal -->
    <div id="receipt-modal" class="fixed inset-0 bg-black/60 flex items-center justify-center z-50 hidden">
        <div class="glass p-8 rounded-2xl w-full max-w-md relative">
            <button onclick="closeReceiptModal()" class="absolute left-4 top-4 text-slate-400 hover:text-red-400 text-2xl"><i class="fas fa-times"></i></button>
            <h2 class="text-xl font-bold mb-4 text-center">سند اشتراك</h2>
            <div id="receipt-content" class="text-slate-200 text-lg mb-4">
                <!-- تفاصيل السند -->
            </div>
            <img src="/static/stamp.png" class="stamp" alt="ختم رسمي">
            <div class="signature">توقيع الإدارة: <span style="font-family: 'Cairo', cursive;">7C Academy</span></div>
            <button onclick="window.print()" class="bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-xl transition-all w-full mt-6">طباعة السند</button>
        </div>
    </div>
    <script>
// الوضع الليلي
const toggleDark = document.getElementById('toggleDark');
toggleDark.onclick = () => {
    document.body.classList.toggle('dark-mode');
};

let requests = [];
let requestsMap = {};
let selectedRequestId = null;

async function fetchRequests() {
    const res = await fetch('api/get_requests.php');
    requests = await res.json();
    requestsMap = {};
    requests.forEach(r => requestsMap[r.id] = r);
}

async function fetchStats() {
    const res = await fetch('api/get_stats.php');
    const stats = await res.json();
    document.getElementById('stat-total').innerText = stats.total;
    document.getElementById('stat-pending').innerText = stats.pending;
    document.getElementById('stat-accepted').innerText = stats.accepted;
    document.getElementById('stat-rejected').innerText = stats.rejected;
}

async function fetchLastPlayers() {
    const res = await fetch('api/get_last_players.php');
    const last = await res.json();
    const container = document.getElementById('last-players');
    container.innerHTML = '';
    const cats = ['أشبال','براعم','ناشئين','شباب'];
    cats.forEach(cat => {
        const player = last[cat];
        if (player) {
            container.innerHTML += `<div class="player-card glass p-4 flex flex-col items-center">
                <img src="${player.face_image || 'https://randomuser.me/api/portraits/men/32.jpg'}" class="w-20 h-20 rounded-full border-4 border-purple-400 mb-2 object-cover">
                <div class="font-bold text-lg">${player.full_name}</div>
                <div class="text-slate-400 text-sm">${player.category}</div>
                <div class="text-slate-300 text-xs mt-1">${player.subscription_plan}</div>
            </div>`;
        } else {
            container.innerHTML += `<div class="player-card glass p-4 flex flex-col items-center opacity-50">
                <div class="w-20 h-20 rounded-full border-4 border-slate-600 mb-2 bg-slate-700 flex items-center justify-center text-2xl text-slate-500"><i class="fas fa-user"></i></div>
                <div class="font-bold text-lg">لا يوجد</div>
                <div class="text-slate-400 text-sm">${cat}</div>
            </div>`;
        }
    });
}

function renderTable() {
    const table = document.getElementById('requests-table');
    table.innerHTML = '';
    let filtered = requests;
    const cat = document.getElementById('filter-category').value;
    const stage = document.getElementById('filter-stage').value;
    const source = document.getElementById('filter-source').value;
    const search = document.getElementById('search').value.trim();
    if (cat) filtered = filtered.filter(r => r.category === cat);
    if (stage) filtered = filtered.filter(r => r.stage === stage);
    if (source) filtered = filtered.filter(r => r.source === source);
    if (search) filtered = filtered.filter(r => (r.full_name && r.full_name.includes(search)) || (r.national_id && r.national_id.includes(search)));
    filtered.forEach((r, i) => {
        table.innerHTML += `<tr class="hover:bg-slate-800/40 cursor-pointer" onclick="showPlayerModal(${r.id})">
            <td class="p-2">${i+1}</td>
            <td class="p-2">${r.full_name || ''}</td>
            <td class="p-2">${r.category || ''}</td>
            <td class="p-2">${r.subscription_plan || ''}</td>
            <td class="p-2">${r.stage || ''}</td>
            <td class="p-2">${r.source || ''}</td>
            <td class="p-2">${r.created_at ? r.created_at.split('T')[0] : ''}</td>
            <td class="p-2">${r.status || ''}</td>
            <td class="p-2">
                <button onclick="event.stopPropagation(); showPlayerModal(${r.id})" class="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded-xl"><i class="fas fa-eye"></i></button>
            </td>
        </tr>`;
    });
}

document.getElementById('filter-category').onchange = renderTable;
document.getElementById('filter-stage').onchange = renderTable;
document.getElementById('filter-source').onchange = renderTable;
document.getElementById('search').oninput = renderTable;

async function showPlayerModal(id) {
    selectedRequestId = id;
    const res = await fetch('api/get_request_details.php?id=' + id);
    const data = await res.json();
    if (!data.success) return;
    const r = data.request;
    document.getElementById('player-modal-img').src = r.face_image || 'https://randomuser.me/api/portraits/men/32.jpg';
    document.getElementById('player-modal-name').innerText = r.full_name || '';
    document.getElementById('player-modal-id').innerText = r.national_id || '';
    document.getElementById('player-modal-category').innerText = r.category || '';
    document.getElementById('player-modal-plan').innerText = r.subscription_plan || '';
    document.getElementById('player-modal-stage').innerText = r.stage || '';
    document.getElementById('player-modal-source').innerText = r.source || '';
    document.getElementById('player-modal-date').innerText = r.created_at ? r.created_at.split('T')[0] : '';
    document.getElementById('player-modal-stages').innerHTML = `<li>${r.status || ''}</li>`;
    document.getElementById('player-modal').classList.remove('hidden');
}
function closePlayerModal() {
    document.getElementById('player-modal').classList.add('hidden');
}

async function acceptRequest() {
    if (!selectedRequestId) return;
    await fetch('api/update_request_status.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({id: selectedRequestId, status: 'مقبول'})
    });
    await refreshAll();
    closePlayerModal();
}
async function rejectRequest() {
    if (!selectedRequestId) return;
    await fetch('api/update_request_status.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({id: selectedRequestId, status: 'مرفوض'})
    });
    await refreshAll();
    closePlayerModal();
}
async function sendActivation() {
    if (!selectedRequestId) return;
    await fetch('api/activate_request.php', {method: 'POST'});
    alert('تم إرسال رسالة التفعيل (تجريبي)');
}
async function printReceipt() {
    if (!selectedRequestId) return;
    const res = await fetch('api/get_receipt.php?id=' + selectedRequestId);
    const data = await res.json();
    if (data.success) {
        const r = data.receipt;
        document.getElementById('receipt-content').innerHTML = `
            <div>اسم اللاعب: <b>${r.full_name}</b></div>
            <div>رقم الهوية: <b>${r.national_id}</b></div>
            <div>الخطة: <b>${r.plan}</b></div>
            <div>تاريخ الاشتراك: <b>${r.date}</b></div>
            <div>تم إصدار هذا السند من أكاديمية 7C</div>
        `;
        document.getElementById('receipt-modal').classList.remove('hidden');
    } else {
        alert('تعذر إصدار السند');
    }
}
function closeReceiptModal() {
    document.getElementById('receipt-modal').classList.add('hidden');
}

// AI Assistant (تجريبي)
function toggleAIAssistant() {
    const ai = document.getElementById('ai-assistant');
    ai.classList.toggle('hidden');
}
function askAI() {
    const q = document.getElementById('ai-input').value;
    document.getElementById('ai-response').innerText = 'جاري التحليل بالذكاء الاصطناعي... (تجريبي)';
    setTimeout(() => {
        document.getElementById('ai-response').innerText = 'الإجابة الذكية: لا توجد بيانات كافية حالياً.';
    }, 1200);
}

async function refreshAll() {
    await fetchRequests();
    await fetchStats();
    await fetchLastPlayers();
    renderTable();
}

setInterval(refreshAll, 3000); // تحديث لحظي كل 3 ثواني
refreshAll();
    </script>
</body>
</html>
