<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استوديو الشخصيات الكرتونية الذكي - أكاديمية 7C</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        :root {
            --primary: #667eea;
            --secondary: #764ba2;
            --accent: #ff6b9d;
            --success: #4ecdc4;
            --warning: #ffe66d;
            --danger: #ff6b6b;
            --dark: #1a1a2e;
            --darker: #16213e;
            --light: #ffffff;
            --gray: #6c757d;
            --glass: rgba(255, 255, 255, 0.1);
            --border: rgba(255, 255, 255, 0.2);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, var(--darker) 100%);
            color: var(--light);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(255, 107, 157, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(78, 205, 196, 0.1) 0%, transparent 50%);
            animation: float 8s ease-in-out infinite alternate;
            z-index: -1;
        }

        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); }
            100% { transform: translateY(-20px) rotate(2deg); }
        }

        /* Header */
        .header {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .logo-text h1 {
            font-size: 1.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, var(--accent), var(--success));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .logo-text p {
            font-size: 0.9rem;
            color: var(--gray);
        }

        .nav-menu {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-family: inherit;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-accent {
            background: linear-gradient(135deg, var(--accent), #ff8fab);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
        }

        .btn-glass {
            background: var(--glass);
            color: var(--light);
            border: 1px solid var(--border);
            backdrop-filter: blur(10px);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        /* Main Layout */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .studio-grid {
            display: grid;
            grid-template-columns: 300px 1fr 280px;
            gap: 2rem;
            min-height: calc(100vh - 120px);
        }

        /* Sidebar */
        .sidebar {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 20px;
            padding: 1.5rem;
            height: fit-content;
            position: sticky;
            top: 120px;
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--accent);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            color: var(--light);
            font-size: 0.9rem;
            font-family: inherit;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--accent);
            background: rgba(255, 255, 255, 0.1);
        }

        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: left 0.75rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-left: 2.5rem;
        }

        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }

        .color-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .color-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
        }

        .color-input {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            border: 2px solid var(--border);
        }

        .color-label {
            font-size: 0.7rem;
            color: var(--gray);
        }

        /* Main Studio */
        .studio-main {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 20px;
            padding: 2rem;
            position: relative;
        }

        .studio-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .studio-title {
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(45deg, var(--accent), var(--success), var(--warning));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }

        .studio-subtitle {
            color: var(--gray);
            font-size: 1rem;
        }

        .generate-section {
            text-align: center;
            margin: 2rem 0;
        }

        .generate-btn {
            background: linear-gradient(135deg, var(--accent), var(--success));
            color: white;
            border: none;
            padding: 1.2rem 2.5rem;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(255, 107, 157, 0.3);
            font-family: inherit;
        }

        .generate-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(255, 107, 157, 0.4);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .character-canvas {
            background: rgba(255, 255, 255, 0.02);
            border: 2px dashed var(--border);
            border-radius: 15px;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin: 2rem 0;
            transition: all 0.3s ease;
        }

        .character-canvas.has-image {
            border-style: solid;
            border-color: var(--accent);
            background: rgba(255, 107, 157, 0.05);
        }

        .character-image {
            max-width: 100%;
            max-height: 350px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .placeholder {
            text-align: center;
            color: var(--gray);
        }

        .placeholder-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .placeholder-text {
            font-size: 1rem;
        }

        /* Character Panel */
        .character-panel {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 20px;
            padding: 1.5rem;
            height: fit-content;
            position: sticky;
            top: 120px;
        }

        .character-info {
            margin-bottom: 1.5rem;
        }

        .character-name {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--accent);
        }

        .character-details {
            font-size: 0.9rem;
            line-height: 1.6;
            color: var(--gray);
        }

        .character-actions {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .action-btn {
            padding: 0.6rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            justify-content: center;
            font-family: inherit;
        }

        .action-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .action-save { background: var(--success); color: white; }
        .action-edit { background: var(--warning); color: var(--dark); }
        .action-export { background: var(--primary); color: white; }
        .action-delete { background: var(--danger); color: white; }

        .action-btn:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        /* Loading */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            display: none;
            align-items: center;
            justify-content: center;
            border-radius: 20px;
            z-index: 100;
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 107, 157, 0.3);
            border-top: 3px solid var(--accent);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .loading-subtext {
            font-size: 0.9rem;
            color: var(--gray);
        }

        /* Stats */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid var(--border);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.8rem;
            color: var(--gray);
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .studio-grid {
                grid-template-columns: 250px 1fr 220px;
                gap: 1rem;
            }
        }

        @media (max-width: 768px) {
            .studio-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .sidebar, .character-panel {
                position: static;
                order: 2;
            }
            
            .studio-main {
                order: 1;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-menu {
                flex-wrap: wrap;
                justify-content: center;
            }
        }

        /* Quick Templates */
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
            margin-top: 1rem;
        }

        .template-btn {
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 0.8rem;
            color: var(--light);
            font-family: inherit;
        }

        .template-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--accent);
            transform: translateY(-1px);
        }

        .template-icon {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
            display: block;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🎨</div>
                <div class="logo-text">
                    <h1>استوديو الشخصيات الذكي</h1>
                    <p>أكاديمية 7C الإبداعية</p>
                </div>
            </div>
            <nav class="nav-menu">
                <button class="btn btn-glass" onclick="showGallery()">
                    <i class="fas fa-images"></i> المعرض
                </button>
                <button class="btn btn-glass" onclick="showProjects()">
                    <i class="fas fa-folder"></i> المشاريع
                </button>
                <button class="btn btn-primary" onclick="showSettings()">
                    <i class="fas fa-cog"></i> الإعدادات
                </button>
            </nav>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container">
        <div class="studio-grid">
            <!-- Character Customization Sidebar -->
            <div class="sidebar">
                <div class="section-title">
                    <i class="fas fa-user-edit"></i>
                    تخصيص الشخصية
                </div>

                <div class="form-group">
                    <label class="form-label">اسم الشخصية</label>
                    <input type="text" id="characterName" class="form-input" placeholder="أدخل اسم الشخصية">
                </div>

                <div class="form-group">
                    <label class="form-label">العمر</label>
                    <select id="characterAge" class="form-input form-select">
                        <option value="">اختر العمر</option>
                        <option value="طفل">طفل (5-12 سنة)</option>
                        <option value="مراهق">مراهق (13-19 سنة)</option>
                        <option value="بالغ">بالغ (20-40 سنة)</option>
                        <option value="كبير">كبير السن (40+ سنة)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">الجنس</label>
                    <select id="characterGender" class="form-input form-select">
                        <option value="">اختر الجنس</option>
                        <option value="ذكر">ذكر</option>
                        <option value="أنثى">أنثى</option>
                        <option value="محايد">محايد</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">نوع الشخصية</label>
                    <select id="characterType" class="form-input form-select">
                        <option value="">اختر النوع</option>
                        <option value="بطل">بطل</option>
                        <option value="شرير">شرير</option>
                        <option value="مساعد">مساعد</option>
                        <option value="كوميدي">كوميدي</option>
                        <option value="حكيم">حكيم</option>
                        <option value="غامض">غامض</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">نمط الرسم</label>
                    <select id="artStyle" class="form-input form-select">
                        <option value="كرتوني">كرتوني كلاسيكي</option>
                        <option value="أنمي">أنمي</option>
                        <option value="ديزني">ديزني</option>
                        <option value="بيكسار">بيكسار</option>
                        <option value="تشيبي">تشيبي</option>
                        <option value="واقعي">واقعي</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">الوصف التفصيلي</label>
                    <textarea id="characterDescription" class="form-input form-textarea" placeholder="اكتب وصفاً تفصيلياً للشخصية..."></textarea>
                </div>

                <div class="section-title" style="margin-top: 1.5rem;">
                    <i class="fas fa-palette"></i>
                    الألوان
                </div>

                <div class="color-grid">
                    <div class="color-item">
                        <input type="color" id="skinColor" class="color-input" value="#ffdbac">
                        <span class="color-label">البشرة</span>
                    </div>
                    <div class="color-item">
                        <input type="color" id="hairColor" class="color-input" value="#8b4513">
                        <span class="color-label">الشعر</span>
                    </div>
                    <div class="color-item">
                        <input type="color" id="eyeColor" class="color-input" value="#4169e1">
                        <span class="color-label">العيون</span>
                    </div>
                    <div class="color-item">
                        <input type="color" id="clothesColor" class="color-input" value="#ff6b9d">
                        <span class="color-label">الملابس</span>
                    </div>
                    <div class="color-item">
                        <input type="color" id="accessoryColor" class="color-input" value="#ffd700">
                        <span class="color-label">الإكسسوار</span>
                    </div>
                    <div class="color-item">
                        <input type="color" id="backgroundColor" class="color-input" value="#87ceeb">
                        <span class="color-label">الخلفية</span>
                    </div>
                </div>

                <div class="section-title" style="margin-top: 1.5rem;">
                    <i class="fas fa-magic"></i>
                    قوالب سريعة
                </div>

                <div class="templates-grid">
                    <button class="template-btn" onclick="applyTemplate('hero')">
                        <span class="template-icon">🦸</span>
                        بطل
                    </button>
                    <button class="template-btn" onclick="applyTemplate('villain')">
                        <span class="template-icon">🦹</span>
                        شرير
                    </button>
                    <button class="template-btn" onclick="applyTemplate('princess')">
                        <span class="template-icon">👸</span>
                        أميرة
                    </button>
                    <button class="template-btn" onclick="applyTemplate('wizard')">
                        <span class="template-icon">🧙</span>
                        ساحر
                    </button>
                    <button class="template-btn" onclick="applyTemplate('animal')">
                        <span class="template-icon">🐱</span>
                        حيوان
                    </button>
                    <button class="template-btn" onclick="applyTemplate('robot')">
                        <span class="template-icon">🤖</span>
                        روبوت
                    </button>
                </div>
            </div>

            <!-- Main Studio Area -->
            <div class="studio-main">
                <div class="studio-header">
                    <h1 class="studio-title">مولد الشخصيات الكرتونية</h1>
                    <p class="studio-subtitle">أنشئ شخصيات احترافية لمسلسلك باستخدام الذكاء الاصطناعي</p>
                </div>

                <div class="generate-section">
                    <button class="generate-btn" id="generateBtn" onclick="generateCharacter()">
                        <i class="fas fa-magic"></i>
                        توليد شخصية جديدة
                    </button>
                </div>

                <div class="character-canvas" id="characterCanvas">
                    <div class="placeholder">
                        <div class="placeholder-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="placeholder-text">
                            اضغط على "توليد شخصية جديدة" لإنشاء شخصيتك الأولى
                        </div>
                    </div>
                </div>

                <!-- Loading Overlay -->
                <div class="loading-overlay" id="loadingOverlay">
                    <div class="loading-content">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">جاري توليد الشخصية...</div>
                        <div class="loading-subtext">يرجى الانتظار، هذا قد يستغرق بضع ثوانٍ</div>
                    </div>
                </div>
            </div>

            <!-- Character Information Panel -->
            <div class="character-panel">
                <div class="character-info">
                    <h3 class="character-name" id="displayCharacterName">اسم الشخصية</h3>
                    <div class="character-details" id="characterDetails">
                        <p><strong>العمر:</strong> <span id="displayAge">غير محدد</span></p>
                        <p><strong>النوع:</strong> <span id="displayType">غير محدد</span></p>
                        <p><strong>الوصف:</strong> <span id="displayDescription">لم يتم إنشاء شخصية بعد</span></p>
                    </div>
                </div>

                <div class="character-actions">
                    <button class="action-btn action-save" onclick="saveCharacter()" disabled id="saveBtn">
                        <i class="fas fa-save"></i>
                        حفظ الشخصية
                    </button>

                    <button class="action-btn action-edit" onclick="editCharacter()" disabled id="editBtn">
                        <i class="fas fa-edit"></i>
                        تحرير الشخصية
                    </button>

                    <button class="action-btn action-export" onclick="exportCharacter()" disabled id="exportBtn">
                        <i class="fas fa-download"></i>
                        تصدير الشخصية
                    </button>

                    <button class="action-btn action-delete" onclick="deleteCharacter()" disabled id="deleteBtn">
                        <i class="fas fa-trash"></i>
                        حذف الشخصية
                    </button>
                </div>

                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number" id="totalCharacters">0</div>
                        <div class="stat-label">إجمالي الشخصيات</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="savedCharacters">0</div>
                        <div class="stat-label">المحفوظة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="projectsCount">1</div>
                        <div class="stat-label">المشاريع</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="generatedToday">0</div>
                        <div class="stat-label">اليوم</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // ==================== Global Variables ====================
        let currentCharacter = null;
        let characters = JSON.parse(localStorage.getItem('cartoonCharacters') || '[]');
        let projects = JSON.parse(localStorage.getItem('cartoonProjects') || '[{"id": 1, "name": "مشروع افتراضي", "characters": []}]');
        let currentProject = projects[0];
        let isGenerating = false;

        // Character templates
        const characterTemplates = {
            hero: {
                name: 'البطل الشجاع',
                age: 'بالغ',
                gender: 'ذكر',
                type: 'بطل',
                description: 'بطل شجاع يحارب من أجل العدالة والخير',
                skinColor: '#ffdbac',
                hairColor: '#8b4513',
                eyeColor: '#4169e1',
                clothesColor: '#ff0000',
                accessoryColor: '#ffd700',
                backgroundColor: '#87ceeb'
            },
            villain: {
                name: 'الشرير الماكر',
                age: 'بالغ',
                gender: 'ذكر',
                type: 'شرير',
                description: 'شرير ماكر يسعى للسيطرة على العالم',
                skinColor: '#f5deb3',
                hairColor: '#000000',
                eyeColor: '#8b0000',
                clothesColor: '#800080',
                accessoryColor: '#000000',
                backgroundColor: '#2f4f4f'
            },
            princess: {
                name: 'الأميرة الجميلة',
                age: 'بالغ',
                gender: 'أنثى',
                type: 'بطل',
                description: 'أميرة جميلة وذكية تحب مساعدة الآخرين',
                skinColor: '#ffdbac',
                hairColor: '#ffd700',
                eyeColor: '#00bfff',
                clothesColor: '#ff69b4',
                accessoryColor: '#ffd700',
                backgroundColor: '#ffe4e1'
            },
            wizard: {
                name: 'الساحر الحكيم',
                age: 'كبير',
                gender: 'ذكر',
                type: 'حكيم',
                description: 'ساحر حكيم يمتلك قوى سحرية عظيمة',
                skinColor: '#f5deb3',
                hairColor: '#d3d3d3',
                eyeColor: '#9370db',
                clothesColor: '#4b0082',
                accessoryColor: '#ffd700',
                backgroundColor: '#191970'
            },
            animal: {
                name: 'الحيوان الأليف',
                age: 'طفل',
                gender: 'محايد',
                type: 'مساعد',
                description: 'حيوان أليف لطيف ومخلص',
                skinColor: '#daa520',
                hairColor: '#8b4513',
                eyeColor: '#000000',
                clothesColor: '#ff6347',
                accessoryColor: '#32cd32',
                backgroundColor: '#98fb98'
            },
            robot: {
                name: 'الروبوت الذكي',
                age: 'محايد',
                gender: 'محايد',
                type: 'مساعد',
                description: 'روبوت ذكي يساعد في المهام المختلفة',
                skinColor: '#c0c0c0',
                hairColor: '#696969',
                eyeColor: '#00ff00',
                clothesColor: '#4682b4',
                accessoryColor: '#ff4500',
                backgroundColor: '#708090'
            }
        };

        // ==================== Initialization ====================
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            loadCharacterForm();
            console.log('🎨 استوديو الشخصيات الكرتونية جاهز!');
        });

        // ==================== Character Generation ====================
        function generateCharacter() {
            if (isGenerating) return;

            isGenerating = true;
            const generateBtn = document.getElementById('generateBtn');
            const loadingOverlay = document.getElementById('loadingOverlay');

            // Disable button and show loading
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التوليد...';
            loadingOverlay.style.display = 'flex';

            // Get form data
            const characterData = getCharacterFormData();

            // Simulate AI generation (replace with real API call)
            setTimeout(() => {
                const generatedCharacter = simulateAIGeneration(characterData);
                displayCharacter(generatedCharacter);

                // Reset UI
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-magic"></i> توليد شخصية جديدة';
                loadingOverlay.style.display = 'none';
                isGenerating = false;

                // Enable action buttons
                enableActionButtons();

                // Update stats
                updateStats();

                Swal.fire({
                    title: 'تم التوليد بنجاح!',
                    text: 'تم إنشاء شخصية جديدة بنجاح',
                    icon: 'success',
                    confirmButtonText: 'رائع!',
                    background: '#1a1a2e',
                    color: '#ffffff'
                });

            }, 3000); // Simulate API delay
        }

        function getCharacterFormData() {
            return {
                name: document.getElementById('characterName').value || 'شخصية جديدة',
                age: document.getElementById('characterAge').value || 'بالغ',
                gender: document.getElementById('characterGender').value || 'محايد',
                type: document.getElementById('characterType').value || 'مساعد',
                artStyle: document.getElementById('artStyle').value || 'كرتوني',
                description: document.getElementById('characterDescription').value || 'شخصية كرتونية رائعة',
                skinColor: document.getElementById('skinColor').value,
                hairColor: document.getElementById('hairColor').value,
                eyeColor: document.getElementById('eyeColor').value,
                clothesColor: document.getElementById('clothesColor').value,
                accessoryColor: document.getElementById('accessoryColor').value,
                backgroundColor: document.getElementById('backgroundColor').value
            };
        }

        function simulateAIGeneration(data) {
            // Create a character object with generated image
            const character = {
                id: Date.now(),
                ...data,
                imageUrl: generatePlaceholderImage(data),
                createdAt: new Date().toISOString(),
                story: generateCharacterStory(data),
                personality: generatePersonality(data)
            };

            currentCharacter = character;
            return character;
        }

        function generatePlaceholderImage(data) {
            // Generate a placeholder image URL (replace with real AI API)
            const canvas = document.createElement('canvas');
            canvas.width = 400;
            canvas.height = 400;
            const ctx = canvas.getContext('2d');

            // Background
            ctx.fillStyle = data.backgroundColor;
            ctx.fillRect(0, 0, 400, 400);

            // Simple character representation
            // Head
            ctx.fillStyle = data.skinColor;
            ctx.beginPath();
            ctx.arc(200, 150, 80, 0, 2 * Math.PI);
            ctx.fill();

            // Hair
            ctx.fillStyle = data.hairColor;
            ctx.beginPath();
            ctx.arc(200, 120, 85, 0, Math.PI, true);
            ctx.fill();

            // Eyes
            ctx.fillStyle = data.eyeColor;
            ctx.beginPath();
            ctx.arc(180, 140, 8, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(220, 140, 8, 0, 2 * Math.PI);
            ctx.fill();

            // Body
            ctx.fillStyle = data.clothesColor;
            ctx.fillRect(150, 230, 100, 120);

            // Arms
            ctx.fillStyle = data.skinColor;
            ctx.fillRect(120, 240, 30, 80);
            ctx.fillRect(250, 240, 30, 80);

            // Add character name
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(data.name, 200, 380);

            return canvas.toDataURL();
        }

        function generateCharacterStory(data) {
            const stories = {
                'بطل': `${data.name} هو بطل شجاع يحارب من أجل العدالة. نشأ في قرية صغيرة وتدرب على فنون القتال منذ صغره.`,
                'شرير': `${data.name} شخصية معقدة تسعى للسيطرة. لديه خطط ماكرة ولكن في أعماقه قصة حزينة.`,
                'مساعد': `${data.name} مساعد مخلص ووفي. يقدم المساعدة دائماً ولديه حس فكاهي رائع.`,
                'كوميدي': `${data.name} شخصية مرحة تحب إضحاك الآخرين. لديه طرق مبتكرة لحل المشاكل.`,
                'حكيم': `${data.name} شخصية حكيمة ومتزنة. يقدم النصائح القيمة ولديه خبرة واسعة في الحياة.`,
                'غامض': `${data.name} شخصية غامضة لها أسرار كثيرة. ظهوره المفاجئ يغير مجرى الأحداث.`
            };

            return stories[data.type] || `${data.name} شخصية مميزة لها دور مهم في القصة.`;
        }

        function generatePersonality(data) {
            const personalities = {
                'بطل': ['شجاع', 'عادل', 'مخلص', 'قوي', 'محب للخير'],
                'شرير': ['ماكر', 'ذكي', 'طموح', 'معقد', 'قوي الإرادة'],
                'مساعد': ['مخلص', 'مرح', 'مساعد', 'ودود', 'موثوق'],
                'كوميدي': ['مرح', 'مبدع', 'اجتماعي', 'متفائل', 'محب للمرح'],
                'حكيم': ['حكيم', 'صبور', 'متزن', 'عالم', 'مرشد'],
                'غامض': ['غامض', 'هادئ', 'ذكي', 'مستقل', 'لا يُتوقع']
            };

            return personalities[data.type] || ['مميز', 'فريد', 'مثير للاهتمام'];
        }

        function displayCharacter(character) {
            const canvas = document.getElementById('characterCanvas');
            canvas.innerHTML = `
                <img src="${character.imageUrl}" alt="${character.name}" class="character-image">
            `;
            canvas.classList.add('has-image');

            // Update character info panel
            document.getElementById('displayCharacterName').textContent = character.name;
            document.getElementById('displayAge').textContent = character.age;
            document.getElementById('displayType').textContent = character.type;
            document.getElementById('displayDescription').textContent = character.description;
        }

        // ==================== Template Functions ====================
        function applyTemplate(templateName) {
            const template = characterTemplates[templateName];
            if (!template) return;

            // Fill form with template data
            document.getElementById('characterName').value = template.name;
            document.getElementById('characterAge').value = template.age;
            document.getElementById('characterGender').value = template.gender;
            document.getElementById('characterType').value = template.type;
            document.getElementById('characterDescription').value = template.description;
            document.getElementById('skinColor').value = template.skinColor;
            document.getElementById('hairColor').value = template.hairColor;
            document.getElementById('eyeColor').value = template.eyeColor;
            document.getElementById('clothesColor').value = template.clothesColor;
            document.getElementById('accessoryColor').value = template.accessoryColor;
            document.getElementById('backgroundColor').value = template.backgroundColor;

            Swal.fire({
                title: 'تم تطبيق القالب!',
                text: `تم تطبيق قالب ${template.name} بنجاح`,
                icon: 'success',
                timer: 1500,
                showConfirmButton: false,
                background: '#1a1a2e',
                color: '#ffffff'
            });
        }

        // ==================== Character Actions ====================
        function saveCharacter() {
            if (!currentCharacter) return;

            // Add to characters array
            characters.push(currentCharacter);

            // Add to current project
            currentProject.characters.push(currentCharacter.id);

            // Save to localStorage
            localStorage.setItem('cartoonCharacters', JSON.stringify(characters));
            localStorage.setItem('cartoonProjects', JSON.stringify(projects));

            updateStats();

            Swal.fire({
                title: 'تم الحفظ!',
                text: 'تم حفظ الشخصية في مشروعك',
                icon: 'success',
                confirmButtonText: 'رائع!',
                background: '#1a1a2e',
                color: '#ffffff'
            });
        }

        function editCharacter() {
            if (!currentCharacter) return;

            Swal.fire({
                title: 'تحرير الشخصية',
                html: `
                    <div style="text-align: right;">
                        <label>اسم جديد:</label>
                        <input type="text" id="newName" class="swal2-input" value="${currentCharacter.name}">
                        <label>وصف جديد:</label>
                        <textarea id="newDescription" class="swal2-textarea">${currentCharacter.description}</textarea>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'حفظ التغييرات',
                cancelButtonText: 'إلغاء',
                background: '#1a1a2e',
                color: '#ffffff',
                preConfirm: () => {
                    const newName = document.getElementById('newName').value;
                    const newDescription = document.getElementById('newDescription').value;

                    if (!newName) {
                        Swal.showValidationMessage('يرجى إدخال اسم الشخصية');
                        return false;
                    }

                    return { name: newName, description: newDescription };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    currentCharacter.name = result.value.name;
                    currentCharacter.description = result.value.description;

                    // Update display
                    document.getElementById('displayCharacterName').textContent = currentCharacter.name;
                    document.getElementById('displayDescription').textContent = currentCharacter.description;

                    Swal.fire({
                        title: 'تم التحديث!',
                        text: 'تم تحديث بيانات الشخصية',
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false,
                        background: '#1a1a2e',
                        color: '#ffffff'
                    });
                }
            });
        }

        function exportCharacter() {
            if (!currentCharacter) return;

            Swal.fire({
                title: 'تصدير الشخصية',
                text: 'اختر تنسيق التصدير',
                icon: 'question',
                showCancelButton: true,
                showDenyButton: true,
                confirmButtonText: 'صورة PNG',
                denyButtonText: 'بيانات JSON',
                cancelButtonText: 'إلغاء',
                background: '#1a1a2e',
                color: '#ffffff'
            }).then((result) => {
                if (result.isConfirmed) {
                    exportAsImage();
                } else if (result.isDenied) {
                    exportAsJSON();
                }
            });
        }

        function exportAsImage() {
            const link = document.createElement('a');
            link.download = `${currentCharacter.name}.png`;
            link.href = currentCharacter.imageUrl;
            link.click();
        }

        function exportAsJSON() {
            const dataStr = JSON.stringify(currentCharacter, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `${currentCharacter.name}.json`;
            link.click();
        }

        function deleteCharacter() {
            if (!currentCharacter) return;

            Swal.fire({
                title: 'تأكيد الحذف',
                text: 'هل أنت متأكد من حذف هذه الشخصية؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#ff6b6b',
                background: '#1a1a2e',
                color: '#ffffff'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Clear display
                    const canvas = document.getElementById('characterCanvas');
                    canvas.innerHTML = `
                        <div class="placeholder">
                            <div class="placeholder-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="placeholder-text">
                                اضغط على "توليد شخصية جديدة" لإنشاء شخصيتك الأولى
                            </div>
                        </div>
                    `;
                    canvas.classList.remove('has-image');

                    // Reset character info
                    document.getElementById('displayCharacterName').textContent = 'اسم الشخصية';
                    document.getElementById('displayAge').textContent = 'غير محدد';
                    document.getElementById('displayType').textContent = 'غير محدد';
                    document.getElementById('displayDescription').textContent = 'لم يتم إنشاء شخصية بعد';

                    // Disable action buttons
                    disableActionButtons();

                    currentCharacter = null;

                    Swal.fire({
                        title: 'تم الحذف!',
                        text: 'تم حذف الشخصية بنجاح',
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false,
                        background: '#1a1a2e',
                        color: '#ffffff'
                    });
                }
            });
        }

        // ==================== UI Helper Functions ====================
        function enableActionButtons() {
            document.getElementById('saveBtn').disabled = false;
            document.getElementById('editBtn').disabled = false;
            document.getElementById('exportBtn').disabled = false;
            document.getElementById('deleteBtn').disabled = false;
        }

        function disableActionButtons() {
            document.getElementById('saveBtn').disabled = true;
            document.getElementById('editBtn').disabled = true;
            document.getElementById('exportBtn').disabled = true;
            document.getElementById('deleteBtn').disabled = true;
        }

        function updateStats() {
            const today = new Date().toDateString();
            const todayCharacters = characters.filter(char =>
                new Date(char.createdAt).toDateString() === today
            ).length;

            document.getElementById('totalCharacters').textContent = characters.length;
            document.getElementById('savedCharacters').textContent = characters.length;
            document.getElementById('projectsCount').textContent = projects.length;
            document.getElementById('generatedToday').textContent = todayCharacters;
        }

        function loadCharacterForm() {
            // Initialize form with default values if needed
            if (!document.getElementById('characterName').value) {
                document.getElementById('characterName').value = '';
            }
        }

        // ==================== Navigation Functions ====================
        function showGallery() {
            if (characters.length === 0) {
                Swal.fire({
                    title: 'المعرض فارغ',
                    text: 'لم يتم حفظ أي شخصيات بعد',
                    icon: 'info',
                    background: '#1a1a2e',
                    color: '#ffffff'
                });
                return;
            }

            let galleryHTML = '<div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem;">';
            characters.forEach(char => {
                galleryHTML += `
                    <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.1); border-radius: 10px;">
                        <img src="${char.imageUrl}" style="width: 80px; height: 80px; border-radius: 10px; margin-bottom: 0.5rem;">
                        <div style="font-weight: bold; margin-bottom: 0.25rem;">${char.name}</div>
                        <div style="font-size: 0.8rem; color: #999;">${char.type}</div>
                    </div>
                `;
            });
            galleryHTML += '</div>';

            Swal.fire({
                title: 'معرض الشخصيات',
                html: galleryHTML,
                width: '800px',
                background: '#1a1a2e',
                color: '#ffffff'
            });
        }

        function showProjects() {
            Swal.fire({
                title: 'مشاريعي',
                html: `
                    <div style="text-align: right;">
                        <p>المشروع الحالي: ${currentProject.name}</p>
                        <p>عدد الشخصيات: ${currentProject.characters.length}</p>
                        <p>إجمالي المشاريع: ${projects.length}</p>
                    </div>
                `,
                background: '#1a1a2e',
                color: '#ffffff'
            });
        }

        function showSettings() {
            Swal.fire({
                title: 'الإعدادات',
                html: `
                    <div style="text-align: right;">
                        <h4>إعدادات التوليد</h4>
                        <label>جودة الصورة:</label>
                        <select class="swal2-select">
                            <option>عالية</option>
                            <option>متوسطة</option>
                            <option>منخفضة</option>
                        </select>
                        <br><br>
                        <label>نمط التوليد:</label>
                        <select class="swal2-select">
                            <option>إبداعي</option>
                            <option>واقعي</option>
                            <option>كرتوني</option>
                        </select>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'حفظ',
                cancelButtonText: 'إلغاء',
                background: '#1a1a2e',
                color: '#ffffff'
            });
        }

        // ==================== Auto-save functionality ====================
        setInterval(() => {
            if (currentCharacter) {
                localStorage.setItem('currentCharacter', JSON.stringify(currentCharacter));
            }
        }, 30000); // Auto-save every 30 seconds

        // Load saved character on page load
        window.addEventListener('load', () => {
            const savedCharacter = localStorage.getItem('currentCharacter');
            if (savedCharacter) {
                try {
                    currentCharacter = JSON.parse(savedCharacter);
                    displayCharacter(currentCharacter);
                    enableActionButtons();
                } catch (e) {
                    console.log('No valid saved character found');
                }
            }
        });

        console.log('🎨 استوديو الشخصيات الكرتونية الذكي جاهز للاستخدام!');
    </script>
</body>
</html>
