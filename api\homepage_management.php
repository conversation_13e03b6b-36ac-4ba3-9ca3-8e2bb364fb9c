<?php
/**
 * نظام إدارة الصفحة الرئيسية - API
 * أكاديمية 7C الرياضية
 * 
 * @version 1.0.0
 * <AUTHOR> 7C Team
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'new7cdata';
$username = 'komaro';
$password = 'ZdShaker@14';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    // إنشاء جدول الصفحة الرئيسية إذا لم يكن موجوداً
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS homepage_data (
            id INT AUTO_INCREMENT PRIMARY KEY,
            data_key VARCHAR(100) NOT NULL UNIQUE,
            content LONGTEXT NOT NULL,
            version VARCHAR(20) DEFAULT '1.0.0',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_by VARCHAR(100) DEFAULT 'admin',
            status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
            backup_data LONGTEXT NULL,
            INDEX idx_data_key (data_key),
            INDEX idx_status (status),
            INDEX idx_updated_at (updated_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createTableSQL);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'فشل الاتصال بقاعدة البيانات',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

// الحصول على طريقة الطلب والبيانات
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

// دالة الاستجابة
function sendResponse($success, $message, $data = null, $code = 200) {
    http_response_code($code);
    $response = [
        'success' => $success,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit();
}

// دالة التحقق من صحة البيانات
function validateHomepageData($data) {
    if (!isset($data['sections']) || !is_array($data['sections'])) {
        return false;
    }
    
    if (!isset($data['settings']) || !is_array($data['settings'])) {
        return false;
    }
    
    return true;
}

// معالجة الطلبات
switch ($method) {
    case 'GET':
        handleGetRequest($pdo);
        break;
        
    case 'POST':
        handlePostRequest($pdo, $input);
        break;
        
    case 'PUT':
        handlePutRequest($pdo, $input);
        break;
        
    case 'DELETE':
        handleDeleteRequest($pdo, $input);
        break;
        
    default:
        sendResponse(false, 'طريقة الطلب غير مدعومة', null, 405);
}

/**
 * معالجة طلبات GET - استرجاع البيانات
 */
function handleGetRequest($pdo) {
    $action = $_GET['action'] ?? 'get_homepage';
    
    switch ($action) {
        case 'get_homepage':
            try {
                $stmt = $pdo->prepare("SELECT * FROM homepage_data WHERE data_key = 'main_homepage' ORDER BY updated_at DESC LIMIT 1");
                $stmt->execute();
                $result = $stmt->fetch();
                
                if ($result) {
                    $homepageData = json_decode($result['content'], true);
                    sendResponse(true, 'تم استرجاع بيانات الصفحة الرئيسية بنجاح', [
                        'homepage' => $homepageData,
                        'version' => $result['version'],
                        'status' => $result['status'],
                        'last_updated' => $result['updated_at']
                    ]);
                } else {
                    // إرجاع بيانات افتراضية
                    $defaultData = [
                        'sections' => [],
                        'settings' => [
                            'theme' => 'default',
                            'layout' => 'modern',
                            'colors' => [
                                'primary' => '#1a1a1a',
                                'secondary' => '#8B4513',
                                'accent' => '#D2691E'
                            ]
                        ],
                        'lastSaved' => null,
                        'version' => '1.0.0'
                    ];
                    
                    sendResponse(true, 'لا توجد بيانات محفوظة، تم إرجاع البيانات الافتراضية', [
                        'homepage' => $defaultData,
                        'version' => '1.0.0',
                        'status' => 'draft',
                        'last_updated' => null
                    ]);
                }
            } catch (PDOException $e) {
                sendResponse(false, 'خطأ في استرجاع البيانات', ['error' => $e->getMessage()], 500);
            }
            break;
            
        case 'get_backups':
            try {
                $stmt = $pdo->prepare("SELECT id, version, created_at, status FROM homepage_data WHERE data_key = 'main_homepage' ORDER BY created_at DESC LIMIT 10");
                $stmt->execute();
                $backups = $stmt->fetchAll();
                
                sendResponse(true, 'تم استرجاع النسخ الاحتياطية بنجاح', ['backups' => $backups]);
            } catch (PDOException $e) {
                sendResponse(false, 'خطأ في استرجاع النسخ الاحتياطية', ['error' => $e->getMessage()], 500);
            }
            break;
            
        default:
            sendResponse(false, 'إجراء غير مدعوم', null, 400);
    }
}

/**
 * معالجة طلبات POST - حفظ البيانات
 */
function handlePostRequest($pdo, $input) {
    $action = $input['action'] ?? 'save_homepage';
    
    switch ($action) {
        case 'save_homepage':
            if (!validateHomepageData($input['data'])) {
                sendResponse(false, 'بيانات الصفحة الرئيسية غير صحيحة', null, 400);
            }
            
            try {
                $pdo->beginTransaction();
                
                // حفظ نسخة احتياطية من البيانات الحالية
                $stmt = $pdo->prepare("SELECT content FROM homepage_data WHERE data_key = 'main_homepage' ORDER BY updated_at DESC LIMIT 1");
                $stmt->execute();
                $currentData = $stmt->fetch();
                
                $backupData = $currentData ? $currentData['content'] : null;
                
                // حفظ البيانات الجديدة
                $content = json_encode($input['data'], JSON_UNESCAPED_UNICODE);
                $version = $input['version'] ?? '1.0.0';
                $status = $input['status'] ?? 'draft';
                
                $stmt = $pdo->prepare("
                    INSERT INTO homepage_data (data_key, content, version, status, backup_data, created_by) 
                    VALUES ('main_homepage', ?, ?, ?, ?, 'admin')
                ");
                
                $stmt->execute([$content, $version, $status, $backupData]);
                
                $pdo->commit();
                
                sendResponse(true, 'تم حفظ بيانات الصفحة الرئيسية بنجاح', [
                    'id' => $pdo->lastInsertId(),
                    'version' => $version,
                    'status' => $status
                ]);
                
            } catch (PDOException $e) {
                $pdo->rollBack();
                sendResponse(false, 'خطأ في حفظ البيانات', ['error' => $e->getMessage()], 500);
            }
            break;
            
        default:
            sendResponse(false, 'إجراء غير مدعوم', null, 400);
    }
}

/**
 * معالجة طلبات PUT - تحديث البيانات
 */
function handlePutRequest($pdo, $input) {
    $action = $input['action'] ?? 'publish_homepage';
    
    switch ($action) {
        case 'publish_homepage':
            try {
                $pdo->beginTransaction();
                
                // الحصول على آخر إصدار محفوظ
                $stmt = $pdo->prepare("SELECT * FROM homepage_data WHERE data_key = 'main_homepage' ORDER BY updated_at DESC LIMIT 1");
                $stmt->execute();
                $latestData = $stmt->fetch();
                
                if (!$latestData) {
                    $pdo->rollBack();
                    sendResponse(false, 'لا توجد بيانات للنشر', null, 404);
                }
                
                // تحديث حالة النشر
                $stmt = $pdo->prepare("UPDATE homepage_data SET status = 'published' WHERE id = ?");
                $stmt->execute([$latestData['id']]);
                
                // إنشاء نسخة منشورة جديدة
                $publishedContent = $latestData['content'];
                $newVersion = incrementVersion($latestData['version']);
                
                $stmt = $pdo->prepare("
                    INSERT INTO homepage_data (data_key, content, version, status, backup_data, created_by) 
                    VALUES ('published_homepage', ?, ?, 'published', ?, 'admin')
                ");
                
                $stmt->execute([$publishedContent, $newVersion, $latestData['content']]);
                
                $pdo->commit();
                
                sendResponse(true, 'تم نشر الصفحة الرئيسية بنجاح', [
                    'published_id' => $pdo->lastInsertId(),
                    'version' => $newVersion,
                    'published_at' => date('Y-m-d H:i:s')
                ]);
                
            } catch (PDOException $e) {
                $pdo->rollBack();
                sendResponse(false, 'خطأ في نشر الصفحة', ['error' => $e->getMessage()], 500);
            }
            break;
            
        default:
            sendResponse(false, 'إجراء غير مدعوم', null, 400);
    }
}

/**
 * معالجة طلبات DELETE - حذف البيانات
 */
function handleDeleteRequest($pdo, $input) {
    $action = $input['action'] ?? 'delete_backup';
    
    switch ($action) {
        case 'delete_backup':
            $backupId = $input['backup_id'] ?? null;
            
            if (!$backupId) {
                sendResponse(false, 'معرف النسخة الاحتياطية مطلوب', null, 400);
            }
            
            try {
                $stmt = $pdo->prepare("DELETE FROM homepage_data WHERE id = ? AND status != 'published'");
                $result = $stmt->execute([$backupId]);
                
                if ($stmt->rowCount() > 0) {
                    sendResponse(true, 'تم حذف النسخة الاحتياطية بنجاح');
                } else {
                    sendResponse(false, 'لم يتم العثور على النسخة الاحتياطية أو لا يمكن حذفها', null, 404);
                }
                
            } catch (PDOException $e) {
                sendResponse(false, 'خطأ في حذف النسخة الاحتياطية', ['error' => $e->getMessage()], 500);
            }
            break;
            
        default:
            sendResponse(false, 'إجراء غير مدعوم', null, 400);
    }
}

/**
 * زيادة رقم الإصدار
 */
function incrementVersion($version) {
    $parts = explode('.', $version);
    $parts[2] = (int)$parts[2] + 1;
    return implode('.', $parts);
}
?>
