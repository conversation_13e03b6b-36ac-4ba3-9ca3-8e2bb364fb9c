<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص API المدربين - أكاديمية 7C</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #e2e8f0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
        }

        .test-card {
            background: rgba(255,255,255,0.05);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .btn {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .result.success {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid #10b981;
            color: #10b981;
        }

        .result.error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid #ef4444;
            color: #ef4444;
        }

        .result.info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid #3b82f6;
            color: #3b82f6;
        }

        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status.online {
            background: #10b981;
            color: white;
        }

        .status.offline {
            background: #ef4444;
            color: white;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bug"></i> تشخيص API المدربين</h1>
            <p>أداة تشخيص لحل مشاكل الاتصال والتحميل</p>
            <div id="apiStatus" style="margin-top: 15px;">
                <span class="status offline">غير متصل</span>
            </div>
        </div>

        <div class="grid">
            <!-- اختبار الاتصال الأساسي -->
            <div class="test-card">
                <h3><i class="fas fa-wifi"></i> اختبار الاتصال الأساسي</h3>
                <p>فحص إمكانية الوصول إلى API</p>
                <button class="btn" onclick="testBasicConnection()">
                    <i class="fas fa-plug"></i> اختبار الاتصال
                </button>
                <button class="btn" onclick="testWithParams()">
                    <i class="fas fa-cog"></i> اختبار مع معاملات
                </button>
                <div id="connectionResult" class="result" style="display: none;"></div>
            </div>

            <!-- اختبار قاعدة البيانات -->
            <div class="test-card">
                <h3><i class="fas fa-database"></i> اختبار قاعدة البيانات</h3>
                <p>فحص الاتصال بقاعدة بيانات coaches7c</p>
                <button class="btn" onclick="testDatabase()">
                    <i class="fas fa-server"></i> اختبار قاعدة البيانات
                </button>
                <button class="btn" onclick="testTables()">
                    <i class="fas fa-table"></i> فحص الجداول
                </button>
                <div id="databaseResult" class="result" style="display: none;"></div>
            </div>

            <!-- اختبار العمليات -->
            <div class="test-card">
                <h3><i class="fas fa-tasks"></i> اختبار العمليات</h3>
                <p>فحص عمليات CRUD الأساسية</p>
                <button class="btn" onclick="testGetCoaches()">
                    <i class="fas fa-download"></i> جلب المدربين
                </button>
                <button class="btn" onclick="testAddCoach()">
                    <i class="fas fa-plus"></i> إضافة مدرب تجريبي
                </button>
                <div id="operationsResult" class="result" style="display: none;"></div>
            </div>

            <!-- معلومات النظام -->
            <div class="test-card">
                <h3><i class="fas fa-info-circle"></i> معلومات النظام</h3>
                <p>معلومات تقنية مفيدة للتشخيص</p>
                <button class="btn" onclick="getSystemInfo()">
                    <i class="fas fa-info"></i> معلومات النظام
                </button>
                <button class="btn" onclick="checkCORS()">
                    <i class="fas fa-shield-alt"></i> فحص CORS
                </button>
                <div id="systemResult" class="result" style="display: none;"></div>
            </div>
        </div>

        <!-- سجل الأخطاء -->
        <div class="test-card">
            <h3><i class="fas fa-list"></i> سجل التشخيص</h3>
            <button class="btn" onclick="clearLog()">
                <i class="fas fa-trash"></i> مسح السجل
            </button>
            <div id="debugLog" class="result info" style="display: block; min-height: 100px;">
                جاري تهيئة أداة التشخيص...
            </div>
        </div>
    </div>

    <script>
        const API_URL = './api/coaches_management.php';
        let debugLog = [];

        // إضافة رسالة إلى سجل التشخيص
        function addToLog(message, type = 'info') {
            const timestamp = new Date().toLocaleString('ar-SA');
            debugLog.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateLogDisplay();
        }

        // تحديث عرض السجل
        function updateLogDisplay() {
            const logElement = document.getElementById('debugLog');
            logElement.textContent = debugLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }

        // مسح السجل
        function clearLog() {
            debugLog = [];
            updateLogDisplay();
        }

        // عرض النتيجة
        function showResult(elementId, success, message, data = null) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            
            if (success) {
                element.className = 'result success';
                element.textContent = `✅ ${message}`;
                if (data) {
                    element.textContent += '\n\nالبيانات:\n' + JSON.stringify(data, null, 2);
                }
                addToLog(message, 'success');
            } else {
                element.className = 'result error';
                element.textContent = `❌ ${message}`;
                if (data) {
                    element.textContent += '\n\nتفاصيل الخطأ:\n' + JSON.stringify(data, null, 2);
                }
                addToLog(message, 'error');
            }
        }

        // تحديث حالة API
        function updateApiStatus(online) {
            const statusElement = document.getElementById('apiStatus');
            if (online) {
                statusElement.innerHTML = '<span class="status online">متصل</span>';
            } else {
                statusElement.innerHTML = '<span class="status offline">غير متصل</span>';
            }
        }

        // اختبار الاتصال الأساسي
        async function testBasicConnection() {
            addToLog('بدء اختبار الاتصال الأساسي...');
            
            try {
                const response = await fetch(`${API_URL}?test=1`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult('connectionResult', true, 'API يعمل بنجاح', data);
                    updateApiStatus(true);
                } else {
                    showResult('connectionResult', false, 'API لا يستجيب بشكل صحيح', data);
                    updateApiStatus(false);
                }
            } catch (error) {
                showResult('connectionResult', false, 'فشل في الاتصال: ' + error.message);
                updateApiStatus(false);
            }
        }

        // اختبار مع معاملات
        async function testWithParams() {
            addToLog('اختبار API مع معاملات...');
            
            try {
                const response = await fetch(`${API_URL}?action=test&timestamp=${Date.now()}`);
                const data = await response.json();
                
                showResult('connectionResult', response.ok, 'اختبار المعاملات', data);
            } catch (error) {
                showResult('connectionResult', false, 'خطأ في اختبار المعاملات: ' + error.message);
            }
        }

        // اختبار قاعدة البيانات
        async function testDatabase() {
            addToLog('اختبار الاتصال بقاعدة البيانات...');
            
            try {
                const response = await fetch(`${API_URL}?action=test_db`);
                const data = await response.json();
                
                showResult('databaseResult', data.success, data.success ? 'قاعدة البيانات متصلة' : 'فشل الاتصال بقاعدة البيانات', data);
            } catch (error) {
                showResult('databaseResult', false, 'خطأ في اختبار قاعدة البيانات: ' + error.message);
            }
        }

        // فحص الجداول
        async function testTables() {
            addToLog('فحص جداول قاعدة البيانات...');
            
            try {
                const response = await fetch(`${API_URL}?action=check_tables`);
                const data = await response.json();
                
                showResult('databaseResult', data.success, data.success ? 'الجداول موجودة' : 'مشكلة في الجداول', data);
            } catch (error) {
                showResult('databaseResult', false, 'خطأ في فحص الجداول: ' + error.message);
            }
        }

        // اختبار جلب المدربين
        async function testGetCoaches() {
            addToLog('اختبار جلب المدربين...');
            
            try {
                const response = await fetch(`${API_URL}?page=1&limit=5`);
                const data = await response.json();
                
                if (data.success) {
                    showResult('operationsResult', true, `تم جلب ${data.data.coaches.length} مدرب`, data.data);
                } else {
                    showResult('operationsResult', false, 'فشل في جلب المدربين', data);
                }
            } catch (error) {
                showResult('operationsResult', false, 'خطأ في جلب المدربين: ' + error.message);
            }
        }

        // اختبار إضافة مدرب
        async function testAddCoach() {
            addToLog('اختبار إضافة مدرب تجريبي...');
            
            const testCoach = {
                id: 'DEBUG' + Date.now(),
                name: 'مدرب تشخيص',
                email: 'debug' + Date.now() + '@test.com',
                phone: '0500000000',
                specialization: 'اختبار',
                experience_years: 1,
                hire_date: '2024-01-01',
                salary: 1000,
                certification: 'شهادة تشخيص',
                bio: 'مدرب للتشخيص فقط',
                status: 1,
                assigned_players: '[]',
                avg_attendance: 0
            };

            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testCoach)
                });

                const data = await response.json();
                
                showResult('operationsResult', data.success, data.success ? 'تم إضافة المدرب التجريبي' : 'فشل في إضافة المدرب', data);
            } catch (error) {
                showResult('operationsResult', false, 'خطأ في إضافة المدرب: ' + error.message);
            }
        }

        // معلومات النظام
        function getSystemInfo() {
            const info = {
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                url: window.location.href,
                protocol: window.location.protocol,
                host: window.location.host,
                timestamp: new Date().toISOString()
            };

            showResult('systemResult', true, 'معلومات النظام', info);
        }

        // فحص CORS
        async function checkCORS() {
            addToLog('فحص إعدادات CORS...');
            
            try {
                const response = await fetch(API_URL, {
                    method: 'OPTIONS'
                });

                const corsInfo = {
                    status: response.status,
                    headers: Object.fromEntries(response.headers.entries())
                };

                showResult('systemResult', response.ok, 'معلومات CORS', corsInfo);
            } catch (error) {
                showResult('systemResult', false, 'خطأ في فحص CORS: ' + error.message);
            }
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addToLog('تم تحميل أداة التشخيص');
            testBasicConnection();
        });
    </script>
</body>
</html>
