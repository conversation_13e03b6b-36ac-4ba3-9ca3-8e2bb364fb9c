// ==================== Plugin: الشركاء والرعاة ====================
export const PartnersPlugin = {
    id: 'partners',
    name: 'الشركاء والرعاة',
    init() {
        if (!document.getElementById('partners-plugin-style')) {
            const style = document.createElement('style');
            style.id = 'partners-plugin-style';
            style.innerHTML = `
                .plugin-section {background: #181f2a; color: #fff; border-radius: 18px; box-shadow: 0 2px 16px #0002; max-width: 900px; margin: 32px auto 0; padding: 32px 24px 24px; font-family: 'Cairo',sans-serif;}
                .plugin-header {display: flex; align-items: center; justify-content: space-between; margin-bottom: 18px;}
                .plugin-header h2 {font-size: 1.5rem; font-weight: bold; margin: 0;}
                .plugin-btn {background: linear-gradient(90deg,#4f8cff,#6f6bff); color: #fff; border: none; border-radius: 8px; padding: 10px 22px; font-size: 1rem; font-weight: 600; cursor: pointer; transition: background 0.2s;}
                .plugin-btn:hover {background: linear-gradient(90deg,#6f6bff,#4f8cff);}
                .plugin-table {width: 100%; border-collapse: collapse; background: #232b3b; border-radius: 12px; overflow: hidden; margin-top: 10px;}
                .plugin-table th, .plugin-table td {padding: 12px 8px; text-align: center;}
                .plugin-table th {background: #232b3b; color: #8bb4ff; font-weight: 700; border-bottom: 2px solid #2d3950;}
                .plugin-table tr {transition: background 0.2s;}
                .plugin-table tr:hover {background: #232b3bcc;}
                .plugin-table td {border-bottom: 1px solid #232b3b;}
                .plugin-action-btn {background: #2d3950; color: #8bb4ff; border: none; border-radius: 6px; padding: 6px 16px; margin: 0 2px; font-size: 0.95rem; cursor: pointer; transition: background 0.2s;}
                .plugin-action-btn:hover {background: #4f8cff; color: #fff;}
                .plugin-modal-bg {position: fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.45); z-index: 9999; display: flex; align-items: center; justify-content: center;}
                .plugin-modal {background: #232b3b; border-radius: 16px; padding: 32px 24px 24px; min-width: 320px; max-width: 95vw; box-shadow: 0 4px 32px #0005;}
                .plugin-modal h3 {margin-top:0; color:#8bb4ff; font-size:1.2rem; margin-bottom:18px;}
                .plugin-modal label {display:block; margin-bottom:6px; color:#b6cfff; font-size:1rem;}
                .plugin-modal input {width:100%; padding:8px 10px; border-radius:6px; border:none; background:#181f2a; color:#fff; margin-bottom:16px; font-size:1rem;}
                .plugin-modal .modal-actions {display:flex; justify-content:flex-end; gap:10px;}
                .plugin-modal .plugin-btn {padding:8px 18px; font-size:1rem;}
            `;
            document.head.appendChild(style);
        }
        if (!document.getElementById('partners-plugin-container')) {
            const container = document.createElement('section');
            container.id = 'partners-plugin-container';
            container.className = 'plugin-section';
            container.innerHTML = `
                <div class="plugin-header">
                    <h2>الشركاء والرعاة</h2>
                    <button id="add-partner-btn" class="plugin-btn">إضافة شريك/راعي</button>
                </div>
                <table class="plugin-table" id="partners-table">
                    <thead>
                        <tr>
                            <th>اسم الشريك/الراعي</th>
                            <th>رابط الموقع</th>
                            <th>شعار</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="partners-table-body">
                        <!-- سيتم تعبئة الشركاء هنا -->
                    </tbody>
                </table>
            `;
            document.body.prepend(container);
        }
        this.renderPartners();
        document.getElementById('add-partner-btn').onclick = () => this.openPartnerModal();
    },
    destroy() {
        const container = document.getElementById('partners-plugin-container');
        if (container) container.remove();
        const style = document.getElementById('partners-plugin-style');
        if (style) style.remove();
        this.closeModal();
    },
    renderPartners() {
        const partners = JSON.parse(localStorage.getItem('plugin_partners') || '[]');
        const tbody = document.getElementById('partners-table-body');
        if (!tbody) return;
        tbody.innerHTML = partners.length ? partners.map((p, i) => `
            <tr>
                <td>${p.name || ''}</td>
                <td><a href="${p.url || ''}" target="_blank" style="color:#8bb4ff;">${p.url || ''}</a></td>
                <td>${p.logo ? `<img src="${p.logo}" alt="logo" style="max-width:60px;max-height:40px;border-radius:6px;"/>` : ''}</td>
                <td>
                    <button class="plugin-action-btn" onclick="window.PluginManager.plugins.partners.openPartnerModal(${i})">تعديل</button>
                    <button class="plugin-action-btn" onclick="window.PluginManager.plugins.partners.deletePartner(${i})">حذف</button>
                </td>
            </tr>
        `).join('') : '<tr><td colspan="4">لا يوجد شركاء/رعاة</td></tr>';
    },
    openPartnerModal(index = null) {
        this.closeModal();
        const partners = JSON.parse(localStorage.getItem('plugin_partners') || '[]');
        const partner = index !== null ? partners[index] : { name: '', url: '', logo: '' };
        const modalBg = document.createElement('div');
        modalBg.className = 'plugin-modal-bg';
        modalBg.id = 'partners-plugin-modal-bg';
        modalBg.innerHTML = `
            <div class="plugin-modal">
                <h3>${index !== null ? 'تعديل شريك/راعي' : 'إضافة شريك/راعي جديد'}</h3>
                <label>اسم الشريك/الراعي</label>
                <input id="modal-partner-name" type="text" value="${partner.name || ''}" placeholder="مثال: شركة سابك" />
                <label>رابط الموقع</label>
                <input id="modal-partner-url" type="url" value="${partner.url || ''}" placeholder="https://example.com" />
                <label>رابط الشعار (صورة)</label>
                <input id="modal-partner-logo" type="url" value="${partner.logo || ''}" placeholder="رابط صورة الشعار" />
                <div class="modal-actions">
                    <button class="plugin-btn" id="save-partner-btn">${index !== null ? 'حفظ التعديلات' : 'إضافة'}</button>
                    <button class="plugin-btn" id="cancel-partner-btn" style="background:#2d3950;">إلغاء</button>
                </div>
            </div>
        `;
        document.body.appendChild(modalBg);
        document.getElementById('cancel-partner-btn').onclick = () => this.closeModal();
        document.getElementById('save-partner-btn').onclick = () => {
            const name = document.getElementById('modal-partner-name').value.trim();
            const url = document.getElementById('modal-partner-url').value.trim();
            const logo = document.getElementById('modal-partner-logo').value.trim();
            if (!name || !url) {
                alert('يرجى تعبئة جميع الحقول المطلوبة');
                return;
            }
            if (index !== null) {
                partners[index] = { name, url, logo };
            } else {
                partners.push({ name, url, logo });
            }
            localStorage.setItem('plugin_partners', JSON.stringify(partners));
            this.closeModal();
            this.renderPartners();
        };
        setTimeout(() => {
            document.getElementById('modal-partner-name').focus();
        }, 100);
    },
    closeModal() {
        const modalBg = document.getElementById('partners-plugin-modal-bg');
        if (modalBg) modalBg.remove();
    },
    deletePartner(index) {
        if (!confirm('هل أنت متأكد من حذف الشريك/الراعي؟')) return;
        const partners = JSON.parse(localStorage.getItem('plugin_partners') || '[]');
        partners.splice(index, 1);
        localStorage.setItem('plugin_partners', JSON.stringify(partners));
        this.renderPartners();
    }
};
