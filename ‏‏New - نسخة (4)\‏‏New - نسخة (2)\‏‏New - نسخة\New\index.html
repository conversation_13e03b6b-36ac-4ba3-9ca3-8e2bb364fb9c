<!DOCTYPE html>
<html lang="ar" dir="rtl">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>أكاديمية 7C الرياضية | نظام إدارة رياضي ذكي</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap"
            rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
            :root {
                --primary-color: #8B4513;
                --secondary-color: #A0522D;
                --accent-color: #CD853F;
                --success-color: #228B22;
                --warning-color: #DAA520;
                --danger-color: #B22222;
                --text-dark: #2D1B0E;
                --text-light: #8B4513;
            }

            body {
                font-family: 'Cairo', sans-serif;
                background: #ffffff;
                color: var(--text-dark);
                overflow-x: hidden;
            }

            .hero-section {
                background: linear-gradient(135deg, #ffffff 0%, #fefefe 50%, #ffffff 100%);
                min-height: 100vh;
                position: relative;
            }

            .hero-pattern {
                background-image:
                    radial-gradient(circle at 25% 25%, rgba(139, 69, 19, 0.05) 0%, transparent 50%),
                    radial-gradient(circle at 75% 75%, rgba(160, 82, 45, 0.05) 0%, transparent 50%);
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
            }

            .glass-effect {
                background: rgba(255, 255, 255, 0.9);
                backdrop-filter: blur(20px);
                border: 1px solid rgba(139, 69, 19, 0.1);
                box-shadow: 0 8px 32px rgba(139, 69, 19, 0.1);
            }

            .icon-oud {
                color: var(--primary-color);
                filter: drop-shadow(2px 2px 4px rgba(139, 69, 19, 0.3));
            }

            .btn-primary {
                background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                color: white;
                border: none;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
            }

            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(139, 69, 19, 0.4);
            }

            .btn-secondary {
                background: transparent;
                color: var(--primary-color);
                border: 2px solid var(--primary-color);
                transition: all 0.3s ease;
            }

            .btn-secondary:hover {
                background: var(--primary-color);
                color: white;
                transform: translateY(-2px);
            }

            .feature-card {
                background: white;
                border: 1px solid rgba(139, 69, 19, 0.1);
                border-radius: 20px;
                padding: 2rem;
                transition: all 0.3s ease;
                box-shadow: 0 4px 20px rgba(139, 69, 19, 0.1);
            }

            .feature-card:hover {
                transform: translateY(-10px);
                box-shadow: 0 20px 40px rgba(139, 69, 19, 0.2);
                border-color: var(--primary-color);
            }

            .stat-card {
                background: linear-gradient(135deg, white 0%, #fefefe 100%);
                border: 1px solid rgba(139, 69, 19, 0.1);
                border-radius: 15px;
                padding: 1.5rem;
                text-align: center;
                transition: all 0.3s ease;
            }

            .stat-card:hover {
                transform: scale(1.05);
                box-shadow: 0 10px 30px rgba(139, 69, 19, 0.2);
            }

            .gradient-text {
                background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            .animate-float {
                animation: float 6s ease-in-out infinite;
            }

            @keyframes float {

                0%,
                100% {
                    transform: translateY(0px);
                }

                50% {
                    transform: translateY(-20px);
                }
            }

            .animate-fade-in {
                animation: fadeIn 1s ease-in;
            }

            @keyframes fadeIn {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }

                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .header-nav {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-bottom: 1px solid rgba(139, 69, 19, 0.1);
            }

            .nav-link {
                color: var(--text-dark);
                transition: all 0.3s ease;
                position: relative;
            }

            .nav-link:hover {
                color: var(--primary-color);
            }

            .nav-link::after {
                content: '';
                position: absolute;
                bottom: -5px;
                left: 0;
                width: 0;
                height: 2px;
                background: var(--primary-color);
                transition: width 0.3s ease;
            }

            .nav-link:hover::after {
                width: 100%;
            }

            .section-title {
                color: var(--text-dark);
                font-weight: 800;
                margin-bottom: 1rem;
            }

            .section-subtitle {
                color: var(--text-light);
                font-size: 1.1rem;
                line-height: 1.6;
            }

            .logo-container {
                background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                border-radius: 15px;
                padding: 1rem;
                color: white;
                font-weight: bold;
                font-size: 1.5rem;
                box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
            }

            .ai-badge {
                background: linear-gradient(135deg, var(--accent-color), var(--warning-color));
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 25px;
                font-size: 0.9rem;
                font-weight: 600;
                box-shadow: 0 2px 10px rgba(218, 165, 32, 0.3);
            }

            .footer-section {
                background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
                border-top: 1px solid rgba(139, 69, 19, 0.1);
            }

            .social-icon {
                background: white;
                color: var(--primary-color);
                width: 50px;
                height: 50px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                border: 1px solid rgba(139, 69, 19, 0.2);
            }

            .social-icon:hover {
                background: var(--primary-color);
                color: white;
                transform: translateY(-3px);
                box-shadow: 0 5px 15px rgba(139, 69, 19, 0.3);
            }

            .notification-dot {
                background: var(--danger-color);
                width: 8px;
                height: 8px;
                border-radius: 50%;
                position: absolute;
                top: -2px;
                right: -2px;
                animation: pulse 2s infinite;
            }

            @keyframes pulse {

                0%,
                100% {
                    opacity: 1;
                }

                50% {
                    opacity: 0.5;
                }
            }

            .hero-image {
                background: linear-gradient(135deg, rgba(139, 69, 19, 0.1), rgba(160, 82, 45, 0.1));
                border-radius: 20px;
                padding: 2rem;
                border: 1px solid rgba(139, 69, 19, 0.2);
            }

            .stats-counter {
                font-size: 3rem;
                font-weight: 900;
                color: var(--primary-color);
                line-height: 1;
            }

            .stats-label {
                color: var(--text-light);
                font-size: 1rem;
                margin-top: 0.5rem;
            }

            .feature-icon {
                background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                color: white;
                width: 80px;
                height: 80px;
                border-radius: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 2rem;
                margin-bottom: 1.5rem;
                box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
            }

            .testimonial-card {
                background: white;
                border: 1px solid rgba(139, 69, 19, 0.1);
                border-radius: 15px;
                padding: 2rem;
                box-shadow: 0 4px 20px rgba(139, 69, 19, 0.1);
                transition: all 0.3s ease;
            }

            .testimonial-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 30px rgba(139, 69, 19, 0.2);
            }

            .quote-icon {
                color: var(--accent-color);
                font-size: 3rem;
                opacity: 0.3;
            }

            .cta-section {
                background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                color: white;
                border-radius: 20px;
                padding: 4rem 2rem;
                margin: 4rem 0;
                text-align: center;
                position: relative;
                overflow: hidden;
            }

            .cta-section::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
                opacity: 0.5;
            }

            .cta-content {
                position: relative;
                z-index: 1;
            }

            /* أنماط منطقة التجريب */
            #demo-area {
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            }

            .demo-card {
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .demo-card:hover {
                transform: translateY(-5px) scale(1.05);
                box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            }

            #demo-components > div {
                transition: all 0.3s ease;
            }

            #demo-components > div:hover {
                transform: translateY(-2px);
                box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            }

            /* تأثيرات خاصة للمناطق التجريبية */
            #slider-demo {
                border: 2px dashed #e2e8f0;
                min-height: 200px;
                display: flex;
                flex-direction: column;
                justify-content: center;
            }

            #charts-demo {
                border: 2px dashed #e2e8f0;
                min-height: 300px;
                display: flex;
                flex-direction: column;
                justify-content: center;
            }

            #free-demo {
                border: 2px dashed #8b5cf6;
                min-height: 250px;
            }
        </style>
    </head>

    <body>
        <!-- Header -->
        <header class="header-nav sticky top-0 z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-20">
                    <div class="flex items-center">
                        <div class="logo-container ml-4">
                            7C
                        </div>
                        <div class="mr-4">
                            <h1 class="text-2xl font-bold gradient-text">أكاديمية 7C الرياضية</h1>
                            <p class="text-sm text-gray-600">نظام إدارة رياضي ذكي</p>
                        </div>
                    </div>

                    <nav class="hidden lg:flex space-x-8 space-x-reverse">
                        <a href="#home" class="nav-link"><i class="fas fa-home icon-oud ml-2"></i>الرئيسية</a>
                        <a href="#features" class="nav-link"><i class="fas fa-star icon-oud ml-2"></i>الميزات</a>
                        <a href="#about" class="nav-link"><i class="fas fa-info-circle icon-oud ml-2"></i>عن
                            الأكاديمية</a>
                        <a href="#stats" class="nav-link"><i class="fas fa-chart-bar icon-oud ml-2"></i>الإحصائيات</a>
                        <a href="#contact" class="nav-link"><i class="fas fa-envelope icon-oud ml-2"></i>اتصل بنا</a>
                    </nav>

                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="ai-badge">
                            <i class="fas fa-robot ml-2"></i>مدعوم بالذكاء الاصطناعي
                        </div>
                        <button onclick="showDashboard()" class="btn-primary px-6 py-3 rounded-xl font-bold">
                            <i class="fas fa-tachometer-alt ml-2"></i>لوحة التحكم
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Hero Section -->
        <section id="home" class="hero-section">
            <div class="hero-pattern"></div>
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 relative z-10">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <!-- Left Content -->
                    <div class="text-center lg:text-right animate-fade-in">
                        <div class="inline-flex items-center glass-effect rounded-full px-6 py-3 mb-8">
                            <div class="notification-dot"></div>
                            <span class="text-sm font-medium mr-3">النظام متصل ويعمل بكفاءة عالية</span>
                        </div>

                        <h1 class="text-5xl lg:text-7xl font-black mb-6 leading-tight">
                            <span class="gradient-text">أكاديمية</span><br>
                            <span class="text-gray-800">7C الرياضية</span>
                        </h1>

                        <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                            نظام إدارة رياضي ذكي مدعوم بالذكاء الاصطناعي لتطوير المواهب الرياضية
                        </p>

                        <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-12">
                            <button onclick="showDashboard()"
                                class="btn-primary px-8 py-4 rounded-2xl text-lg font-bold">
                                <i class="fas fa-rocket icon-oud ml-3"></i>
                                دخول لوحة التحكم
                            </button>
                            <button onclick="startDemo()" class="btn-secondary px-8 py-4 rounded-2xl text-lg font-bold">
                                <i class="fas fa-play icon-oud ml-3"></i>
                                مشاهدة العرض التوضيحي
                            </button>
                        </div>

                        <!-- Quick Stats -->
                        <div class="grid grid-cols-3 gap-6 max-w-md mx-auto lg:mx-0">
                            <div class="text-center">
                                <div class="stats-counter">1250</div>
                                <div class="stats-label">لاعب نشط</div>
                            </div>
                            <div class="text-center">
                                <div class="stats-counter">89</div>
                                <div class="stats-label">مدرب محترف</div>
                            </div>
                            <div class="text-center">
                                <div class="stats-counter">156</div>
                                <div class="stats-label">برنامج تدريبي</div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Content - Dashboard Preview -->
                    <div class="animate-float">
                        <div class="hero-image">
                            <div class="glass-effect rounded-3xl p-8">
                                <div class="flex items-center justify-between mb-6">
                                    <h3 class="text-xl font-bold text-gray-800">لوحة التحكم الذكية</h3>
                                    <div class="notification-dot"></div>
                                </div>

                                <!-- Mini Chart Placeholder -->
                                <div class="bg-gradient-to-r from-orange-100 to-yellow-100 rounded-xl p-6 mb-6">
                                    <canvas id="heroChart" width="300" height="150"></canvas>
                                </div>

                                <!-- Stats Grid -->
                                <div class="grid grid-cols-2 gap-4 mb-6">
                                    <div class="stat-card">
                                        <div class="text-2xl font-bold text-gray-800">94%</div>
                                        <div class="text-sm text-gray-600">معدل الحضور</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="text-2xl font-bold text-gray-800">4.8</div>
                                        <div class="text-sm text-gray-600">تقييم الأداء</div>
                                    </div>
                                </div>

                                <!-- AI Insights -->
                                <div class="glass-effect p-4 rounded-xl">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-robot icon-oud ml-2"></i>
                                        <span class="text-sm font-medium text-gray-800">رؤى الذكاء الاصطناعي</span>
                                    </div>
                                    <p class="text-xs text-gray-600">تحسن ملحوظ في أداء الفريق بنسبة 23% هذا الشهر</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section id="features" class="py-20 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="section-title text-4xl lg:text-5xl">
                        ميزات النظام المتقدمة
                    </h2>
                    <p class="section-subtitle max-w-3xl mx-auto">
                        اكتشف مجموعة شاملة من الأدوات والتقنيات المتطورة لإدارة أكاديميتك الرياضية بكفاءة عالية
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Player Management -->
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">إدارة اللاعبين</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            نظام شامل لإدارة ملفات اللاعبين مع تتبع الأداء والإحصائيات التفصيلية والتقييمات الدورية
                        </p>
                        <ul class="space-y-2 text-sm text-gray-600">
                            <li class="flex items-center"><i class="fas fa-check icon-oud ml-2"></i>ملفات شخصية مفصلة
                            </li>
                            <li class="flex items-center"><i class="fas fa-check icon-oud ml-2"></i>تتبع الأداء المباشر
                            </li>
                            <li class="flex items-center"><i class="fas fa-check icon-oud ml-2"></i>تقارير تقييم شاملة
                            </li>
                        </ul>
                    </div>

                    <!-- Training Management -->
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-dumbbell"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">إدارة التدريب</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            جدولة ذكية للتدريبات مع تتبع الحضور وتخطيط البرامج التدريبية المخصصة لكل لاعب
                        </p>
                        <ul class="space-y-2 text-sm text-gray-600">
                            <li class="flex items-center"><i class="fas fa-check icon-oud ml-2"></i>جدولة تلقائية ذكية
                            </li>
                            <li class="flex items-center"><i class="fas fa-check icon-oud ml-2"></i>برامج تدريبية مخصصة
                            </li>
                            <li class="flex items-center"><i class="fas fa-check icon-oud ml-2"></i>تتبع التقدم المستمر
                            </li>
                        </ul>
                    </div>

                    <!-- AI Analytics -->
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">الذكاء الاصطناعي</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            تحليلات متقدمة مدعومة بالذكاء الاصطناعي لتقييم الأداء وتقديم توصيات مخصصة لكل لاعب
                        </p>
                        <ul class="space-y-2 text-sm text-gray-600">
                            <li class="flex items-center"><i class="fas fa-check icon-oud ml-2"></i>تحليل الأداء الذكي
                            </li>
                            <li class="flex items-center"><i class="fas fa-check icon-oud ml-2"></i>توصيات مخصصة</li>
                            <li class="flex items-center"><i class="fas fa-check icon-oud ml-2"></i>تنبؤات مستقبلية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- منطقة التجريب والتطوير -->
        <section id="demo-area" class="py-20 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="section-title text-4xl lg:text-5xl">
                        🧪 منطقة التجريب والتطوير
                    </h2>
                    <p class="section-subtitle max-w-3xl mx-auto">
                        هذا المكان مخصص لتجربة المكتبات والمكونات الجديدة - يمكنك التعديل عليه مباشرة
                    </p>
                </div>

                <!-- حاوي المكونات التجريبية -->
                <div id="demo-components" class="space-y-8">

                    <!-- مكان للسلايدر -->
                    <div id="slider-demo" class="bg-white rounded-2xl p-8 shadow-lg">
                        <h3 class="text-2xl font-bold text-center mb-6 text-gray-800">🖼️ مكان السلايدر</h3>
                        <div class="text-center text-gray-500">
                            <p>أضف مكتبة سلايدر وستظهر هنا تلقائياً</p>
                            <p class="text-sm mt-2">يمكنك التعديل على هذا القسم مباشرة في وضع التحرير</p>
                        </div>
                    </div>

                    <!-- مكان للرسوم البيانية -->
                    <div id="charts-demo" class="bg-white rounded-2xl p-8 shadow-lg">
                        <h3 class="text-2xl font-bold text-center mb-6 text-gray-800">📊 مكان الرسوم البيانية</h3>
                        <div class="text-center text-gray-500">
                            <p>أضف Chart.js أو أي مكتبة رسوم وستظهر هنا</p>
                        </div>
                    </div>

                    <!-- مكان للحركات والتأثيرات -->
                    <div id="animations-demo" class="bg-white rounded-2xl p-8 shadow-lg">
                        <h3 class="text-2xl font-bold text-center mb-6 text-gray-800">🎭 مكان الحركات والتأثيرات</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="demo-card bg-blue-100 p-6 rounded-xl text-center">
                                <div class="text-4xl mb-4">⚽</div>
                                <h4 class="font-bold">كرة القدم</h4>
                            </div>
                            <div class="demo-card bg-green-100 p-6 rounded-xl text-center">
                                <div class="text-4xl mb-4">🏀</div>
                                <h4 class="font-bold">كرة السلة</h4>
                            </div>
                            <div class="demo-card bg-yellow-100 p-6 rounded-xl text-center">
                                <div class="text-4xl mb-4">🎾</div>
                                <h4 class="font-bold">التنس</h4>
                            </div>
                        </div>
                    </div>

                    <!-- مكان للنصوص المتحركة -->
                    <div id="text-effects-demo" class="bg-white rounded-2xl p-8 shadow-lg">
                        <h3 class="text-2xl font-bold text-center mb-6 text-gray-800">⌨️ مكان النصوص المتحركة</h3>
                        <div class="text-center">
                            <div id="typed-demo-text" class="text-2xl font-bold text-blue-600 min-h-[60px] flex items-center justify-center">
                                أضف Typed.js لرؤية تأثير الكتابة هنا
                            </div>
                        </div>
                    </div>

                    <!-- مكان مفتوح للتجريب -->
                    <div id="free-demo" class="bg-gradient-to-r from-purple-100 to-pink-100 rounded-2xl p-8 shadow-lg">
                        <h3 class="text-2xl font-bold text-center mb-6 text-gray-800">🎨 مساحة حرة للتجريب</h3>
                        <div class="text-center text-gray-600">
                            <p class="mb-4">هذا المكان مخصص لتجاربك الخاصة</p>
                            <p class="text-sm">يمكنك إضافة أي HTML أو CSS أو JavaScript هنا</p>
                            <div class="mt-6 p-4 bg-white rounded-lg">
                                <p class="text-lg">🚀 ابدأ التجريب الآن!</p>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="cta-section">
                    <div class="cta-content">
                        <h2 class="text-4xl font-bold mb-6">ابدأ رحلتك مع أكاديمية 7C اليوم</h2>
                        <p class="text-xl mb-8 opacity-90">
                            انضم إلى آلاف الأكاديميات التي تثق في نظامنا المتطور
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <button onclick="startTrial()"
                                class="bg-white text-gray-800 px-8 py-4 rounded-2xl text-lg font-bold hover:bg-gray-100 transition-all">
                                <i class="fas fa-play ml-3"></i>
                                تجربة مجانية لمدة 30 يوم
                            </button>
                            <button onclick="contactSales()"
                                class="border-2 border-white text-white px-8 py-4 rounded-2xl text-lg font-bold hover:bg-white hover:text-gray-800 transition-all">
                                <i class="fas fa-phone ml-3"></i>
                                تحدث مع فريق المبيعات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer id="contact" class="footer-section py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                    <!-- Company Info -->
                    <div class="lg:col-span-2">
                        <div class="flex items-center mb-6">
                            <div class="logo-container ml-4">
                                7C
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-gray-800">أكاديمية 7C الرياضية</h3>
                                <p class="text-gray-600">نظام إدارة رياضي ذكي</p>
                            </div>
                        </div>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            منصة متكاملة لإدارة الأكاديميات الرياضية مع تقنيات الذكاء الاصطناعي المتقدمة لتحليل الأداء
                            وتطوير المواهب الرياضية بكفاءة عالية.
                        </p>
                        <div class="flex space-x-4 space-x-reverse">
                            <a href="#" class="social-icon">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="social-icon">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="social-icon">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="social-icon">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div>
                        <h4 class="text-lg font-bold text-gray-800 mb-6">روابط سريعة</h4>
                        <ul class="space-y-3">
                            <li><a href="#home"
                                    class="text-gray-600 hover:text-gray-800 transition-colors flex items-center"><i
                                        class="fas fa-chevron-left icon-oud ml-2 text-xs"></i>الرئيسية</a></li>
                            <li><a href="#features"
                                    class="text-gray-600 hover:text-gray-800 transition-colors flex items-center"><i
                                        class="fas fa-chevron-left icon-oud ml-2 text-xs"></i>الميزات</a></li>
                            <li><a href="#about"
                                    class="text-gray-600 hover:text-gray-800 transition-colors flex items-center"><i
                                        class="fas fa-chevron-left icon-oud ml-2 text-xs"></i>عن الأكاديمية</a></li>
                            <li><a href="#stats"
                                    class="text-gray-600 hover:text-gray-800 transition-colors flex items-center"><i
                                        class="fas fa-chevron-left icon-oud ml-2 text-xs"></i>الإحصائيات</a></li>
                        </ul>
                    </div>

                    <!-- Contact Info -->
                    <div>
                        <h4 class="text-lg font-bold text-gray-800 mb-6">تواصل معنا</h4>
                        <ul class="space-y-4">
                            <li class="flex items-center text-gray-600">
                                <i class="fas fa-envelope icon-oud ml-3"></i>
                                <EMAIL>
                            </li>
                            <li class="flex items-center text-gray-600">
                                <i class="fas fa-phone icon-oud ml-3"></i>
                                +966-XX-XXX-XXXX
                            </li>
                            <li class="flex items-center text-gray-600">
                                <i class="fas fa-map-marker-alt icon-oud ml-3"></i>
                                الرياض، المملكة العربية السعودية
                            </li>
                            <li class="flex items-center text-gray-600">
                                <i class="fas fa-clock icon-oud ml-3"></i>
                                24/7 دعم فني
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="border-t border-gray-200 mt-12 pt-8 text-center">
                    <p class="text-gray-600 mb-4">
                        © 2024 أكاديمية 7C الرياضية. جميع الحقوق محفوظة.
                    </p>
                    <p class="text-gray-500 text-sm">
                        مطور بـ ❤️ باستخدام أحدث تقنيات الذكاء الاصطناعي
                    </p>
                </div>
            </div>
        </footer>

        <!-- JavaScript -->
        <script>
            // Initialize hero chart
            document.addEventListener('DOMContentLoaded', function () {
                const ctx = document.getElementById('heroChart');
                if (ctx) {
                    new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                            datasets: [{
                                label: 'الأداء',
                                data: [65, 78, 85, 92, 88, 95],
                                borderColor: '#8B4513',
                                backgroundColor: 'rgba(139, 69, 19, 0.1)',
                                tension: 0.4,
                                fill: true
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                }
                            },
                            scales: {
                                y: {
                                    display: false
                                },
                                x: {
                                    display: false
                                }
                            }
                        }
                    });
                }
            });

            // Navigation functions
            function showDashboard() {
                window.location.href = 'admin-advanced.html';
            }

            function startDemo() {
                alert('سيتم توجيهك إلى العرض التوضيحي قريباً');
            }

            function startTrial() {
                alert('سيتم توجيهك إلى صفحة التسجيل للتجربة المجانية');
            }

            function contactSales() {
                alert('سيتم توجيهك إلى صفحة التواصل مع فريق المبيعات');
            }

            // Smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add scroll effect to header
            window.addEventListener('scroll', function () {
                const header = document.querySelector('.header-nav');
                if (window.scrollY > 100) {
                    header.style.background = 'rgba(255, 255, 255, 0.98)';
                    header.style.boxShadow = '0 2px 20px rgba(139, 69, 19, 0.1)';
                } else {
                    header.style.background = 'rgba(255, 255, 255, 0.95)';
                    header.style.boxShadow = 'none';
                }
            });

            // نظام تحرير الصفحة المتقدم
            let isEditMode = false;
            let originalContent = '';
            let selectedLibrary = null;
            let addedLibraries = [];

            // إعدادات النظام
            const EDIT_CONFIG = {
                activationCode: '54139',
                password: 'jfi4622@1',
                saveCode: 'save2024'
            };

            // متغيرات للتحكم في الكود المدخل
            let enteredCode = '';
            let codeTimeout = null;

            // استماع لضغطات المفاتيح لتفعيل النظام
            document.addEventListener('keydown', function(e) {
                // إضافة الرقم المضغوط للكود
                if (e.key >= '0' && e.key <= '9') {
                    enteredCode += e.key;

                    // إعادة تعيين المؤقت
                    clearTimeout(codeTimeout);
                    codeTimeout = setTimeout(() => {
                        enteredCode = '';
                    }, 3000); // مسح الكود بعد 3 ثوان

                    // فحص الكود
                    if (enteredCode === EDIT_CONFIG.activationCode) {
                        showPasswordDialog();
                        enteredCode = '';
                    }
                }

                // إذا كان في وضع التحرير وضغط كود الحفظ
                if (isEditMode && enteredCode.includes(EDIT_CONFIG.saveCode)) {
                    saveChanges();
                    enteredCode = '';
                }

                // الخروج من وضع التحرير بـ ESC
                if (e.key === 'Escape' && isEditMode) {
                    exitEditMode();
                }
            });

            // عرض نافذة كلمة المرور
            function showPasswordDialog() {
                document.getElementById('edit-overlay').style.display = 'block';
                document.getElementById('edit-password').focus();
            }

            // إغلاق نافذة التحرير
            function closeEditMode() {
                document.getElementById('edit-overlay').style.display = 'none';
                document.getElementById('edit-password').value = '';
            }

            // التحقق من كلمة المرور
            function verifyPassword() {
                const password = document.getElementById('edit-password').value;

                if (password === EDIT_CONFIG.password) {
                    closeEditMode();
                    activateEditMode();
                } else {
                    alert('❌ كلمة المرور غير صحيحة!');
                    document.getElementById('edit-password').value = '';
                    document.getElementById('edit-password').focus();
                }
            }

            // تفعيل وضع التحرير
            function activateEditMode() {
                isEditMode = true;
                originalContent = document.body.innerHTML;

                // إظهار شريط الأدوات
                document.getElementById('edit-toolbar').style.display = 'block';

                // تفعيل التحرير المباشر
                document.body.contentEditable = true;
                document.body.style.outline = '2px dashed #1e40af';
                document.body.style.outlineOffset = '5px';

                // إضافة أنماط التحرير
                addEditStyles();

                // تفعيل السحب والإفلات
                enableDragAndDrop();

                // إظهار رسالة النجاح
                showNotification('✅ تم تفعيل وضع التحرير بنجاح!', 'success');
            }

            // إضافة أنماط التحرير
            function addEditStyles() {
                const style = document.createElement('style');
                style.id = 'edit-mode-styles';
                style.textContent = `
                    [contenteditable="true"] {
                        transition: all 0.3s ease;
                    }
                    [contenteditable="true"]:hover {
                        background: rgba(30, 64, 175, 0.1) !important;
                        border-radius: 8px;
                    }
                    [contenteditable="true"]:focus {
                        background: rgba(30, 64, 175, 0.2) !important;
                        border-radius: 8px;
                        box-shadow: 0 0 0 2px #1e40af;
                    }
                    .draggable {
                        cursor: move;
                        position: relative;
                    }
                    .draggable:hover::before {
                        content: "🔄 اسحب لتحريك";
                        position: absolute;
                        top: -30px;
                        left: 50%;
                        transform: translateX(-50%);
                        background: #1e40af;
                        color: white;
                        padding: 5px 10px;
                        border-radius: 5px;
                        font-size: 12px;
                        z-index: 1000;
                    }
                `;
                document.head.appendChild(style);
            }

            // تفعيل السحب والإفلات
            function enableDragAndDrop() {
                const elements = document.querySelectorAll('section, div, header, footer');
                elements.forEach(el => {
                    el.draggable = true;
                    el.classList.add('draggable');

                    el.addEventListener('dragstart', function(e) {
                        e.dataTransfer.setData('text/html', this.outerHTML);
                        e.dataTransfer.effectAllowed = 'move';
                        this.style.opacity = '0.5';
                    });

                    el.addEventListener('dragend', function(e) {
                        this.style.opacity = '1';
                    });

                    el.addEventListener('dragover', function(e) {
                        e.preventDefault();
                        e.dataTransfer.dropEffect = 'move';
                        this.style.background = 'rgba(30, 64, 175, 0.1)';
                    });

                    el.addEventListener('dragleave', function(e) {
                        this.style.background = '';
                    });

                    el.addEventListener('drop', function(e) {
                        e.preventDefault();
                        this.style.background = '';

                        const draggedHTML = e.dataTransfer.getData('text/html');
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = draggedHTML;
                        const draggedElement = tempDiv.firstChild;

                        this.parentNode.insertBefore(draggedElement, this.nextSibling);
                        enableDragAndDrop(); // إعادة تفعيل السحب للعنصر الجديد
                    });
                });
            }

            // حفظ التغييرات
            function saveChanges() {
                try {
                    // حفظ المحتوى
                    localStorage.setItem('7c_academy_content', document.body.innerHTML);
                    localStorage.setItem('7c_academy_timestamp', new Date().toISOString());

                    // حفظ المكتبات
                    saveLibrariesToStorage();

                    showNotification('💾 تم حفظ التغييرات والمكتبات بنجاح!', 'success');
                } catch (error) {
                    showNotification('❌ خطأ في حفظ التغييرات!', 'error');
                }
            }

            // الخروج من وضع التحرير
            function exitEditMode() {
                isEditMode = false;

                // إخفاء شريط الأدوات
                document.getElementById('edit-toolbar').style.display = 'none';

                // إلغاء التحرير
                document.body.contentEditable = false;
                document.body.style.outline = 'none';

                // إزالة أنماط التحرير
                const editStyles = document.getElementById('edit-mode-styles');
                if (editStyles) editStyles.remove();

                // إزالة السحب والإفلات
                const draggables = document.querySelectorAll('.draggable');
                draggables.forEach(el => {
                    el.draggable = false;
                    el.classList.remove('draggable');
                });

                showNotification('🚪 تم الخروج من وضع التحرير', 'info');
            }

            // عرض الإشعارات
            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 100px;
                    right: 20px;
                    background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#1e40af'};
                    color: white;
                    padding: 15px 20px;
                    border-radius: 10px;
                    z-index: 10001;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    animation: slideIn 0.3s ease;
                    direction: rtl;
                    font-weight: bold;
                `;

                notification.textContent = message;
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => notification.remove(), 300);
                }, 3000);
            }

            // قائمة المكتبات المتاحة
            const LIBRARIES = [
                {name: 'Bootstrap RTL', url: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css', type: 'css', desc: 'إطار تصميم متوافق مع العربية'},
                {name: 'jQuery', url: 'https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js', type: 'js', desc: 'مكتبة جافاسكريبت شهيرة'},
                {name: 'Chart.js', url: 'https://cdn.jsdelivr.net/npm/chart.js', type: 'js', desc: 'رسوم بيانية تفاعلية متقدمة'},
                {name: 'SweetAlert2', url: 'https://cdn.jsdelivr.net/npm/sweetalert2@11', type: 'js', desc: 'نوافذ تنبيه وحوار عصرية'},
                {name: 'Animate.css', url: 'https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css', type: 'css', desc: 'حركات وتأثيرات CSS جاهزة'},
                {name: 'Alpine.js', url: 'https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js', type: 'js', desc: 'تفاعلات واجهة بسيطة وقوية'},
                {name: 'Typed.js', url: 'https://cdn.jsdelivr.net/npm/typed.js@2.1.0/dist/typed.umd.js', type: 'js', desc: 'تأثير الكتابة التدريجية'},
                {name: 'AOS Animation', url: 'https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css', type: 'css', desc: 'حركات عند التمرير'},
                {name: 'AOS JS', url: 'https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js', type: 'js', desc: 'مكتبة حركات التمرير'},
                {name: 'Swiper CSS', url: 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css', type: 'css', desc: 'أنماط سلايدر Swiper'},
                {name: 'Swiper JS', url: 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js', type: 'js', desc: 'سلايدر صور وعناصر متطور'},
                {name: 'Glide.js', url: 'https://cdn.jsdelivr.net/npm/@glidejs/glide@3.6.0/dist/glide.min.js', type: 'js', desc: 'سلايدر خفيف وسريع'},
                {name: 'Glide CSS', url: 'https://cdn.jsdelivr.net/npm/@glidejs/glide@3.6.0/dist/css/glide.core.min.css', type: 'css', desc: 'أنماط Glide الأساسية'},
                {name: 'Splide.js', url: 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/js/splide.min.js', type: 'js', desc: 'سلايدر مرن ومتجاوب'},
                {name: 'Splide CSS', url: 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css', type: 'css', desc: 'أنماط Splide'},
                {name: 'Flickity', url: 'https://unpkg.com/flickity@2/dist/flickity.pkgd.min.js', type: 'js', desc: 'سلايدر باللمس والسحب'},
                {name: 'Flickity CSS', url: 'https://unpkg.com/flickity@2/dist/flickity.min.css', type: 'css', desc: 'أنماط Flickity'}
            ];

            // وظائف نظام المكتبات
            function showLibraryPanel() {
                document.getElementById('library-panel').style.display = 'block';
                setupLibrarySearch();
                loadSavedLibraries();
            }

            function hideLibraryPanel() {
                document.getElementById('library-panel').style.display = 'none';
                document.getElementById('custom-library').style.display = 'none';

                // إعادة تعيين الحقول
                document.getElementById('library-search').value = '';
                document.getElementById('custom-url').value = '';
                document.getElementById('library-results').innerHTML = '<div style="text-align:center;color:#666;padding:20px;">اكتب في البحث لعرض المكتبات المتاحة</div>';
                document.getElementById('add-library-btn').disabled = true;
                selectedLibrary = null;

                // إخفاء رسالة المكتبة
                document.getElementById('library-message').style.display = 'none';
            }

            function setupLibrarySearch() {
                const searchInput = document.getElementById('library-search');
                const resultsDiv = document.getElementById('library-results');

                searchInput.oninput = function() {
                    const query = searchInput.value.trim().toLowerCase();
                    resultsDiv.innerHTML = '';
                    selectedLibrary = null;
                    document.getElementById('add-library-btn').disabled = true;

                    if (!query) {
                        resultsDiv.innerHTML = '<div style="text-align:center;color:#666;padding:20px;">اكتب في البحث لعرض المكتبات المتاحة</div>';
                        return;
                    }

                    const filtered = LIBRARIES.filter(lib =>
                        lib.name.toLowerCase().includes(query) ||
                        lib.desc.toLowerCase().includes(query) ||
                        lib.type.toLowerCase().includes(query)
                    );

                    if (filtered.length === 0) {
                        resultsDiv.innerHTML = '<div style="text-align:center;color:#666;padding:20px;">لا توجد مكتبات مطابقة للبحث</div>';
                        return;
                    }

                    filtered.forEach(lib => {
                        const libDiv = document.createElement('div');
                        libDiv.style.cssText = `
                            padding: 12px;
                            margin-bottom: 8px;
                            border: 2px solid #e5e7eb;
                            border-radius: 8px;
                            cursor: pointer;
                            transition: all 0.2s;
                            background: #f9fafb;
                        `;

                        libDiv.innerHTML = `
                            <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                                <div style="flex: 1;">
                                    <div style="font-weight:bold;color:#1e40af;margin-bottom:4px;">
                                        ${lib.name}
                                        <span style="background:${lib.type === 'js' ? '#10b981' : '#8b5cf6'};color:white;padding:2px 6px;border-radius:4px;font-size:11px;margin-right:8px;">${lib.type.toUpperCase()}</span>
                                    </div>
                                    <div style="font-size:13px;color:#666;">${lib.desc}</div>
                                </div>
                                <div style="display: flex; flex-direction: column; gap: 4px; margin-right: 10px;">
                                    <button onclick="previewLibraryFeatures('${lib.name}')" style="background:#8b5cf6;color:white;border:none;border-radius:4px;padding:4px 8px;cursor:pointer;font-size:10px;" title="معاينة المميزات">👁️ معاينة</button>
                                    <button onclick="showLibraryInfo('${lib.name}')" style="background:#06b6d4;color:white;border:none;border-radius:4px;padding:4px 8px;cursor:pointer;font-size:10px;" title="معلومات المكتبة">ℹ️ معلومات</button>
                                </div>
                            </div>
                        `;

                        libDiv.onmouseover = () => {
                            libDiv.style.borderColor = '#1e40af';
                            libDiv.style.background = '#eff6ff';
                        };

                        libDiv.onmouseout = () => {
                            if (selectedLibrary !== lib) {
                                libDiv.style.borderColor = '#e5e7eb';
                                libDiv.style.background = '#f9fafb';
                            }
                        };

                        libDiv.onclick = () => {
                            // إزالة التحديد من العناصر الأخرى
                            resultsDiv.querySelectorAll('div').forEach(d => {
                                d.style.borderColor = '#e5e7eb';
                                d.style.background = '#f9fafb';
                            });

                            // تحديد العنصر الحالي
                            libDiv.style.borderColor = '#1e40af';
                            libDiv.style.background = '#eff6ff';

                            selectedLibrary = lib;
                            document.getElementById('add-library-btn').disabled = false;
                            showLibraryMessage(`تم اختيار: ${lib.name}`, 'info');
                        };

                        resultsDiv.appendChild(libDiv);
                    });
                };
            }

            function addSelectedLibrary() {
                if (!selectedLibrary) {
                    showLibraryMessage('يرجى اختيار مكتبة أولاً', 'error');
                    return;
                }

                // إضافة المكتبة مع رسالة واضحة
                showLibraryMessage(`🔄 جاري إضافة ${selectedLibrary.name}...`, 'info');
                addLibraryToPage(selectedLibrary.url, selectedLibrary.type, selectedLibrary.name);
            }

            function addLibraryToPage(url, type, name) {
                // فحص إذا كانت المكتبة موجودة مسبقاً
                const existing = addedLibraries.find(lib => lib.url === url);
                if (existing) {
                    showLibraryMessage(`المكتبة ${name} موجودة مسبقاً`, 'warning');
                    return;
                }

                // فحص إذا كان العنصر موجود في DOM مسبقاً
                const existingElement = document.querySelector(`[src="${url}"], [href="${url}"]`);
                if (existingElement) {
                    showLibraryMessage(`المكتبة ${name} محملة مسبقاً`, 'warning');
                    return;
                }

                let element;
                if (type === 'js') {
                    element = document.createElement('script');
                    element.src = url;
                    element.async = false;
                    element.defer = false;
                    element.crossOrigin = 'anonymous';
                    document.head.appendChild(element);
                } else if (type === 'css') {
                    element = document.createElement('link');
                    element.rel = 'stylesheet';
                    element.href = url;
                    element.crossOrigin = 'anonymous';
                    document.head.appendChild(element);
                }

                // إضافة المكتبة للقائمة
                const libraryInfo = {
                    name: name,
                    url: url,
                    type: type,
                    element: element,
                    id: Date.now() + Math.random(),
                    loaded: false
                };

                addedLibraries.push(libraryInfo);
                updateAddedLibrariesDisplay();

                element.onload = () => {
                    libraryInfo.loaded = true;
                    showLibraryMessage(`✅ تم تحميل ${name} بنجاح!`, 'success');
                    updateLibraryStatus(libraryInfo.id, 'loaded');

                    // تطبيق تأثيرات فورية على الصفحة
                    setTimeout(() => {
                        applyLibraryEffects(name);
                        checkLibraryLoaded(name, type);
                        // إنشاء لوحة تحكم مصغرة للمكتبة
                        createLibraryControlPanel(name, libraryInfo.id);
                    }, 500);
                };

                element.onerror = () => {
                    showLibraryMessage(`❌ فشل في تحميل ${name}`, 'error');
                    updateLibraryStatus(libraryInfo.id, 'error');
                };

                showLibraryMessage(`🔄 جاري تحميل ${name}...`, 'info');
            }

            function applyLibraryEffects(name) {
                console.log(`🎨 تطبيق تأثيرات ${name}...`);

                if (name === 'jQuery' && (window.jQuery || window.$)) {
                    // تأثير فوري على العنوان
                    const title = document.querySelector('h1');
                    if (title) {
                        $(title).css({
                            'transition': 'all 0.5s ease',
                            'text-shadow': '0 0 20px rgba(16, 185, 129, 0.8)',
                            'transform': 'scale(1.05)'
                        });

                        setTimeout(() => {
                            $(title).css({
                                'text-shadow': 'none',
                                'transform': 'scale(1)'
                            });
                        }, 2000);
                    }

                    // إضافة تأثير على الأزرار
                    $('.btn-primary, .btn-secondary').hover(
                        function() {
                            $(this).css('transform', 'scale(1.1) rotate(2deg)');
                        },
                        function() {
                            $(this).css('transform', 'scale(1) rotate(0deg)');
                        }
                    );

                    showNotification('🎉 jQuery يعمل! شاهد التأثيرات على العنوان والأزرار', 'success');
                }

                if (name === 'SweetAlert2' && window.Swal) {
                    // عرض نافذة ترحيب
                    Swal.fire({
                        title: '🎊 مرحباً!',
                        text: 'SweetAlert2 جاهز للاستخدام!',
                        icon: 'success',
                        timer: 2500,
                        timerProgressBar: true,
                        showConfirmButton: false,
                        position: 'top-end',
                        toast: true
                    });
                }

                if (name === 'Animate.css') {
                    // تطبيق حركات على العناصر
                    const elements = document.querySelectorAll('.feature-card, .stat-card');
                    elements.forEach((el, index) => {
                        setTimeout(() => {
                            el.classList.add('animate__animated', 'animate__pulse');
                            setTimeout(() => {
                                el.classList.remove('animate__animated', 'animate__pulse');
                            }, 1000);
                        }, index * 200);
                    });

                    showNotification('🎭 Animate.css يعمل! شاهد الحركات على البطاقات', 'success');
                }

                if (name === 'Chart.js' && window.Chart) {
                    // إنشاء رسوم بيانية في المنطقة المخصصة
                    createChartInDemoArea();
                    showNotification('📊 Chart.js جاهز! تم إنشاء رسوم بيانية في منطقة التجريب', 'success');
                }

                if (name === 'Swiper JS' && window.Swiper) {
                    // إنشاء سلايدر في المنطقة المخصصة
                    createSwiperInDemoArea();
                    showNotification('🖼️ Swiper جاهز! تم إنشاء سلايدر في منطقة التجريب', 'success');
                }

                if (name.includes('Typed') && window.Typed) {
                    // تأثير الكتابة في المنطقة المخصصة
                    createTypedInDemoArea();
                    showNotification('⌨️ Typed.js جاهز! شاهد تأثير الكتابة في منطقة التجريب', 'success');
                }

                if (name === 'Animate.css') {
                    // تطبيق حركات على البطاقات التجريبية
                    const demoCards = document.querySelectorAll('.demo-card');
                    demoCards.forEach((card, index) => {
                        setTimeout(() => {
                            card.classList.add('animate__animated', 'animate__bounceIn');
                            setTimeout(() => {
                                card.classList.remove('animate__animated', 'animate__bounceIn');
                            }, 1000);
                        }, index * 200);
                    });

                    showNotification('🎭 Animate.css جاهز! شاهد الحركات على البطاقات', 'success');
                }
            }

            function createMiniChart() {
                // إزالة أي رسم سابق
                const existingChart = document.getElementById('demo-mini-chart');
                if (existingChart) existingChart.remove();

                // إنشاء عنصر للرسم
                const chartContainer = document.createElement('div');
                chartContainer.id = 'demo-mini-chart';
                chartContainer.style.cssText = `
                    position: fixed;
                    bottom: 20px;
                    left: 20px;
                    background: white;
                    padding: 15px;
                    border-radius: 10px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                    z-index: 9998;
                    width: 250px;
                    height: 150px;
                `;

                const canvas = document.createElement('canvas');
                canvas.width = 220;
                canvas.height = 120;
                chartContainer.appendChild(canvas);

                const closeBtn = document.createElement('button');
                closeBtn.textContent = '×';
                closeBtn.style.cssText = `
                    position: absolute;
                    top: 5px;
                    right: 10px;
                    background: none;
                    border: none;
                    font-size: 18px;
                    cursor: pointer;
                    color: #666;
                `;
                closeBtn.onclick = () => chartContainer.remove();
                chartContainer.appendChild(closeBtn);

                document.body.appendChild(chartContainer);

                // إنشاء الرسم البياني
                new Chart(canvas, {
                    type: 'doughnut',
                    data: {
                        labels: ['jQuery', 'SweetAlert2', 'Chart.js'],
                        datasets: [{
                            data: [30, 25, 45],
                            backgroundColor: ['#10b981', '#8b5cf6', '#1e40af']
                        }]
                    },
                    options: {
                        responsive: false,
                        plugins: {
                            legend: { display: false },
                            title: {
                                display: true,
                                text: '📊 المكتبات المحملة'
                            }
                        }
                    }
                });

                // إزالة تلقائية بعد 10 ثوان
                setTimeout(() => {
                    if (chartContainer.parentNode) {
                        chartContainer.remove();
                    }
                }, 10000);
            }

            function createDemoSlider() {
                // إزالة أي سلايدر سابق
                const existingSlider = document.getElementById('demo-slider');
                if (existingSlider) existingSlider.remove();

                // إنشاء حاوي السلايدر
                const sliderContainer = document.createElement('div');
                sliderContainer.id = 'demo-slider';
                sliderContainer.style.cssText = `
                    position: fixed;
                    top: 50%;
                    right: 20px;
                    transform: translateY(-50%);
                    width: 300px;
                    height: 200px;
                    background: white;
                    border-radius: 15px;
                    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                    z-index: 9998;
                    overflow: hidden;
                `;

                // إنشاء السلايدر
                sliderContainer.innerHTML = `
                    <div style="position: relative; height: 100%;">
                        <button onclick="document.getElementById('demo-slider').remove()" style="position: absolute; top: 10px; right: 15px; background: rgba(0,0,0,0.5); color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; z-index: 10;">×</button>
                        <div class="swiper demo-swiper" style="width: 100%; height: 100%;">
                            <div class="swiper-wrapper">
                                <div class="swiper-slide" style="background: linear-gradient(45deg, #1e40af, #8b5cf6); display: flex; align-items: center; justify-content: center; color: white; font-size: 18px; font-weight: bold;">🏆 أكاديمية 7C</div>
                                <div class="swiper-slide" style="background: linear-gradient(45deg, #10b981, #06b6d4); display: flex; align-items: center; justify-content: center; color: white; font-size: 18px; font-weight: bold;">⚽ تدريب رياضي</div>
                                <div class="swiper-slide" style="background: linear-gradient(45deg, #f59e0b, #ef4444); display: flex; align-items: center; justify-content: center; color: white; font-size: 18px; font-weight: bold;">🎯 أهداف عالية</div>
                                <div class="swiper-slide" style="background: linear-gradient(45deg, #8b5cf6, #ec4899); display: flex; align-items: center; justify-content: center; color: white; font-size: 18px; font-weight: bold;">🌟 نجاح مضمون</div>
                            </div>
                            <div class="swiper-pagination"></div>
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                        </div>
                    </div>
                `;

                document.body.appendChild(sliderContainer);

                // تفعيل السلايدر
                setTimeout(() => {
                    const swiper = new Swiper('.demo-swiper', {
                        slidesPerView: 1,
                        spaceBetween: 10,
                        loop: true,
                        autoplay: {
                            delay: 2500,
                            disableOnInteraction: false,
                        },
                        pagination: {
                            el: '.swiper-pagination',
                            clickable: true,
                        },
                        navigation: {
                            nextEl: '.swiper-button-next',
                            prevEl: '.swiper-button-prev',
                        },
                    });
                }, 100);

                // إزالة تلقائية بعد 15 ثانية
                setTimeout(() => {
                    if (sliderContainer.parentNode) {
                        sliderContainer.remove();
                    }
                }, 15000);
            }

            function createGlideSlider() {
                // إزالة أي سلايدر سابق
                const existingSlider = document.getElementById('demo-glide');
                if (existingSlider) existingSlider.remove();

                // إنشاء حاوي السلايدر
                const sliderContainer = document.createElement('div');
                sliderContainer.id = 'demo-glide';
                sliderContainer.style.cssText = `
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    width: 350px;
                    background: white;
                    border-radius: 15px;
                    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                    z-index: 9998;
                    padding: 20px;
                `;

                sliderContainer.innerHTML = `
                    <button onclick="document.getElementById('demo-glide').remove()" style="position: absolute; top: 10px; right: 15px; background: #ef4444; color: white; border: none; border-radius: 50%; width: 25px; height: 25px; cursor: pointer;">×</button>
                    <h4 style="margin: 0 0 15px 0; color: #1e40af; text-align: center;">🎠 Glide.js سلايدر</h4>
                    <div class="glide demo-glide-slider">
                        <div class="glide__track" data-glide-el="track">
                            <ul class="glide__slides">
                                <li class="glide__slide" style="background: #1e40af; color: white; padding: 30px; text-align: center; border-radius: 10px; margin: 0 5px;">📊 إحصائيات</li>
                                <li class="glide__slide" style="background: #10b981; color: white; padding: 30px; text-align: center; border-radius: 10px; margin: 0 5px;">👥 لاعبين</li>
                                <li class="glide__slide" style="background: #f59e0b; color: white; padding: 30px; text-align: center; border-radius: 10px; margin: 0 5px;">🏆 بطولات</li>
                            </ul>
                        </div>
                        <div class="glide__arrows" data-glide-el="controls">
                            <button class="glide__arrow glide__arrow--left" data-glide-dir="<" style="left: 10px;">‹</button>
                            <button class="glide__arrow glide__arrow--right" data-glide-dir=">" style="right: 10px;">›</button>
                        </div>
                        <div class="glide__bullets" data-glide-el="controls[nav]">
                            <button class="glide__bullet" data-glide-dir="=0"></button>
                            <button class="glide__bullet" data-glide-dir="=1"></button>
                            <button class="glide__bullet" data-glide-dir="=2"></button>
                        </div>
                    </div>
                `;

                document.body.appendChild(sliderContainer);

                // تفعيل السلايدر
                setTimeout(() => {
                    const glide = new Glide('.demo-glide-slider', {
                        type: 'carousel',
                        startAt: 0,
                        perView: 1,
                        autoplay: 3000,
                        hoverpause: true
                    });
                    glide.mount();
                }, 100);

                // إزالة تلقائية بعد 15 ثانية
                setTimeout(() => {
                    if (sliderContainer.parentNode) {
                        sliderContainer.remove();
                    }
                }, 15000);
            }

            function createTypingEffect() {
                // إنشاء عنصر للكتابة
                const typingContainer = document.createElement('div');
                typingContainer.id = 'demo-typing';
                typingContainer.style.cssText = `
                    position: fixed;
                    top: 30%;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(30, 64, 175, 0.95);
                    color: white;
                    padding: 30px 40px;
                    border-radius: 20px;
                    font-size: 24px;
                    font-weight: bold;
                    z-index: 10002;
                    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                    text-align: center;
                    direction: rtl;
                    min-width: 400px;
                `;

                typingContainer.innerHTML = `
                    <button onclick="document.getElementById('demo-typing').remove()" style="position: absolute; top: 10px; right: 15px; background: rgba(255,255,255,0.2); color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer;">×</button>
                    <div id="typed-text"></div>
                `;

                document.body.appendChild(typingContainer);

                // تفعيل تأثير الكتابة
                setTimeout(() => {
                    const typed = new Typed('#typed-text', {
                        strings: [
                            'مرحباً بك في أكاديمية 7C!',
                            'نحن نقدم أفضل التدريبات الرياضية',
                            'انضم إلينا لتحقيق أهدافك!',
                            'Typed.js يعمل بشكل رائع! 🎉'
                        ],
                        typeSpeed: 50,
                        backSpeed: 30,
                        backDelay: 1500,
                        loop: true,
                        showCursor: true,
                        cursorChar: '|'
                    });
                }, 100);

                // إزالة تلقائية بعد 12 ثانية
                setTimeout(() => {
                    if (typingContainer.parentNode) {
                        typingContainer.remove();
                    }
                }, 12000);
            }

            // وظائف إنشاء المحتوى في المناطق المخصصة
            function createSwiperInDemoArea() {
                const sliderDemo = document.getElementById('slider-demo');
                if (!sliderDemo) return;

                sliderDemo.innerHTML = `
                    <h3 contenteditable="true" class="text-2xl font-bold text-center mb-6 text-gray-800">🖼️ سلايدر قابل للتعديل</h3>

                    <!-- سلايدر بسيط قابل للتعديل -->
                    <div class="simple-slider" style="position: relative; width: 100%; height: 300px; overflow: hidden; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">

                        <!-- الشرائح -->
                        <div class="slide active" contenteditable="true" style="position: absolute; width: 100%; height: 100%; background: linear-gradient(45deg, #1e40af, #8b5cf6); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold; transition: all 0.5s ease;">
                            <div class="text-center">
                                <div style="font-size: 4rem; margin-bottom: 1rem;">🏆</div>
                                <div>أكاديمية 7C الرياضية</div>
                                <div style="font-size: 16px; margin-top: 10px; opacity: 0.9;">اضغط لتعديل النص</div>
                            </div>
                        </div>

                        <div class="slide" contenteditable="true" style="position: absolute; width: 100%; height: 100%; background: linear-gradient(45deg, #10b981, #06b6d4); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold; transition: all 0.5s ease; opacity: 0;">
                            <div class="text-center">
                                <div style="font-size: 4rem; margin-bottom: 1rem;">⚽</div>
                                <div>تدريب رياضي متطور</div>
                                <div style="font-size: 16px; margin-top: 10px; opacity: 0.9;">يمكنك تغيير هذا النص</div>
                            </div>
                        </div>

                        <div class="slide" contenteditable="true" style="position: absolute; width: 100%; height: 100%; background: linear-gradient(45deg, #f59e0b, #ef4444); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold; transition: all 0.5s ease; opacity: 0;">
                            <div class="text-center">
                                <div style="font-size: 4rem; margin-bottom: 1rem;">🎯</div>
                                <div>أهداف عالية الجودة</div>
                                <div style="font-size: 16px; margin-top: 10px; opacity: 0.9;">عدّل الألوان والنصوص</div>
                            </div>
                        </div>

                        <div class="slide" contenteditable="true" style="position: absolute; width: 100%; height: 100%; background: linear-gradient(45deg, #8b5cf6, #ec4899); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold; transition: all 0.5s ease; opacity: 0;">
                            <div class="text-center">
                                <div style="font-size: 4rem; margin-bottom: 1rem;">🌟</div>
                                <div>نجاح مضمون</div>
                                <div style="font-size: 16px; margin-top: 10px; opacity: 0.9;">سلايدر بسيط وقابل للتعديل</div>
                            </div>
                        </div>

                        <!-- أزرار التنقل -->
                        <button onclick="previousSlide()" style="position: absolute; left: 20px; top: 50%; transform: translateY(-50%); background: rgba(255,255,255,0.3); color: white; border: none; border-radius: 50%; width: 50px; height: 50px; font-size: 20px; cursor: pointer; z-index: 10; transition: all 0.3s ease;">‹</button>
                        <button onclick="nextSlide()" style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%); background: rgba(255,255,255,0.3); color: white; border: none; border-radius: 50%; width: 50px; height: 50px; font-size: 20px; cursor: pointer; z-index: 10; transition: all 0.3s ease;">›</button>

                        <!-- نقاط التنقل -->
                        <div style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); display: flex; gap: 10px; z-index: 10;">
                            <div onclick="goToSlide(0)" style="width: 12px; height: 12px; border-radius: 50%; background: rgba(255,255,255,0.8); cursor: pointer; transition: all 0.3s ease;" class="dot active-dot"></div>
                            <div onclick="goToSlide(1)" style="width: 12px; height: 12px; border-radius: 50%; background: rgba(255,255,255,0.4); cursor: pointer; transition: all 0.3s ease;" class="dot"></div>
                            <div onclick="goToSlide(2)" style="width: 12px; height: 12px; border-radius: 50%; background: rgba(255,255,255,0.4); cursor: pointer; transition: all 0.3s ease;" class="dot"></div>
                            <div onclick="goToSlide(3)" style="width: 12px; height: 12px; border-radius: 50%; background: rgba(255,255,255,0.4); cursor: pointer; transition: all 0.3s ease;" class="dot"></div>
                        </div>
                    </div>

                    <div class="text-center mt-4 text-sm text-gray-600">
                        <p>✨ <strong>قابل للتعديل بالكامل!</strong> اضغط على أي نص لتعديله</p>
                        <p>🎨 يمكنك تغيير الألوان والنصوص والرموز مباشرة</p>
                        <p>🖱️ استخدم الأزرار أو النقاط للتنقل بين الشرائح</p>
                    </div>
                `;

                // تفعيل السلايدر التلقائي
                startAutoSlider();
            }

            function createChartInDemoArea() {
                const chartsDemo = document.getElementById('charts-demo');
                if (!chartsDemo || !window.Chart) return;

                chartsDemo.innerHTML = `
                    <h3 class="text-2xl font-bold text-center mb-6 text-gray-800">📊 رسوم بيانية تفاعلية</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-gray-50 p-4 rounded-xl">
                            <canvas id="demo-chart-1" width="300" height="200"></canvas>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-xl">
                            <canvas id="demo-chart-2" width="300" height="200"></canvas>
                        </div>
                    </div>
                    <div class="text-center mt-4 text-sm text-gray-600">
                        <p>📈 يمكنك تعديل البيانات والألوان في وضع التحرير</p>
                    </div>
                `;

                // إنشاء الرسوم البيانية
                setTimeout(() => {
                    // رسم بياني دائري
                    const ctx1 = document.getElementById('demo-chart-1');
                    if (ctx1) {
                        new Chart(ctx1, {
                            type: 'doughnut',
                            data: {
                                labels: ['كرة القدم', 'كرة السلة', 'التنس', 'السباحة'],
                                datasets: [{
                                    data: [40, 25, 20, 15],
                                    backgroundColor: ['#1e40af', '#10b981', '#f59e0b', '#8b5cf6']
                                }]
                            },
                            options: {
                                responsive: true,
                                plugins: {
                                    title: {
                                        display: true,
                                        text: 'توزيع الرياضات'
                                    }
                                }
                            }
                        });
                    }

                    // رسم بياني خطي
                    const ctx2 = document.getElementById('demo-chart-2');
                    if (ctx2) {
                        new Chart(ctx2, {
                            type: 'line',
                            data: {
                                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                                datasets: [{
                                    label: 'عدد اللاعبين',
                                    data: [120, 150, 180, 220, 280, 320],
                                    borderColor: '#1e40af',
                                    backgroundColor: 'rgba(30, 64, 175, 0.1)',
                                    tension: 0.4,
                                    fill: true
                                }]
                            },
                            options: {
                                responsive: true,
                                plugins: {
                                    title: {
                                        display: true,
                                        text: 'نمو عدد اللاعبين'
                                    }
                                }
                            }
                        });
                    }
                }, 100);
            }

            function createTypedInDemoArea() {
                const textDemo = document.getElementById('typed-demo-text');
                if (!textDemo || !window.Typed) return;

                textDemo.innerHTML = '<span id="typed-output"></span>';

                setTimeout(() => {
                    const typed = new Typed('#typed-output', {
                        strings: [
                            'مرحباً بك في أكاديمية 7C! 🏆',
                            'نحن نقدم أفضل التدريبات الرياضية ⚽',
                            'انضم إلينا لتحقيق أهدافك الرياضية 🎯',
                            'تقنيات حديثة وذكاء اصطناعي 🤖',
                            'مستقبل الرياضة يبدأ هنا! 🚀'
                        ],
                        typeSpeed: 60,
                        backSpeed: 40,
                        backDelay: 2000,
                        loop: true,
                        showCursor: true,
                        cursorChar: '|',
                        autoInsertCss: true
                    });
                }, 100);
            }

            // إنشاء لوحة تحكم مصغرة لكل مكتبة
            function createLibraryControlPanel(libraryName, libraryId) {
                // إزالة أي لوحة تحكم سابقة لنفس المكتبة
                const existingPanel = document.getElementById(`control-panel-${libraryId}`);
                if (existingPanel) existingPanel.remove();

                const controlPanel = document.createElement('div');
                controlPanel.id = `control-panel-${libraryId}`;
                controlPanel.style.cssText = `
                    position: fixed;
                    top: 120px;
                    left: 20px;
                    width: 280px;
                    background: linear-gradient(135deg, #1e40af, #8b5cf6);
                    color: white;
                    padding: 15px;
                    border-radius: 15px;
                    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
                    z-index: 9999;
                    font-size: 14px;
                    direction: rtl;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255,255,255,0.2);
                `;

                let panelContent = `
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h4 style="margin: 0; font-size: 16px; font-weight: bold;">🎛️ ${libraryName}</h4>
                        <button onclick="document.getElementById('control-panel-${libraryId}').remove()" style="background: rgba(255,255,255,0.2); color: white; border: none; border-radius: 50%; width: 25px; height: 25px; cursor: pointer; font-size: 14px;">×</button>
                    </div>
                `;

                // إضافة أدوات تحكم مخصصة حسب نوع المكتبة
                if (libraryName === 'jQuery') {
                    panelContent += `
                        <div style="margin-bottom: 10px;">
                            <button onclick="jQueryEffects('fadeToggle')" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; margin: 2px; font-size: 12px;">🌟 إخفاء/إظهار</button>
                            <button onclick="jQueryEffects('slideToggle')" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; margin: 2px; font-size: 12px;">📜 انزلاق</button>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <button onclick="jQueryEffects('bounce')" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; margin: 2px; font-size: 12px;">🏀 ارتداد</button>
                            <button onclick="jQueryEffects('pulse')" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; margin: 2px; font-size: 12px;">💓 نبضة</button>
                        </div>
                        <div>
                            <button onclick="jQueryEffects('colorChange')" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; margin: 2px; font-size: 12px;">🎨 تغيير لون</button>
                        </div>
                    `;
                }

                if (libraryName === 'SweetAlert2') {
                    panelContent += `
                        <div style="margin-bottom: 10px;">
                            <button onclick="showSweetAlert('success')" style="background: rgba(16, 185, 129, 0.8); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; margin: 2px; font-size: 12px;">✅ نجاح</button>
                            <button onclick="showSweetAlert('error')" style="background: rgba(239, 68, 68, 0.8); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; margin: 2px; font-size: 12px;">❌ خطأ</button>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <button onclick="showSweetAlert('warning')" style="background: rgba(245, 158, 11, 0.8); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; margin: 2px; font-size: 12px;">⚠️ تحذير</button>
                            <button onclick="showSweetAlert('info')" style="background: rgba(59, 130, 246, 0.8); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; margin: 2px; font-size: 12px;">ℹ️ معلومات</button>
                        </div>
                        <div>
                            <button onclick="showSweetAlert('question')" style="background: rgba(139, 92, 246, 0.8); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; margin: 2px; font-size: 12px;">❓ سؤال</button>
                            <button onclick="showSweetAlert('custom')" style="background: rgba(236, 72, 153, 0.8); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; margin: 2px; font-size: 12px;">🎨 مخصص</button>
                        </div>
                    `;
                }

                if (libraryName === 'Chart.js') {
                    panelContent += `
                        <div style="margin-bottom: 10px;">
                            <button onclick="createQuickChart('bar')" style="background: rgba(16, 185, 129, 0.8); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; margin: 2px; font-size: 12px;">📊 أعمدة</button>
                            <button onclick="createQuickChart('line')" style="background: rgba(59, 130, 246, 0.8); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; margin: 2px; font-size: 12px;">📈 خطي</button>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <button onclick="createQuickChart('pie')" style="background: rgba(245, 158, 11, 0.8); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; margin: 2px; font-size: 12px;">🥧 دائري</button>
                            <button onclick="createQuickChart('doughnut')" style="background: rgba(139, 92, 246, 0.8); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; margin: 2px; font-size: 12px;">🍩 حلقي</button>
                        </div>
                    `;
                }

                panelContent += `
                    <div style="margin-top: 15px; padding-top: 10px; border-top: 1px solid rgba(255,255,255,0.2); font-size: 12px; opacity: 0.8;">
                        💡 لوحة تحكم ${libraryName} - جرب الأزرار!
                    </div>
                `;

                controlPanel.innerHTML = panelContent;
                document.body.appendChild(controlPanel);

                // إزالة تلقائية بعد 30 ثانية
                setTimeout(() => {
                    if (controlPanel.parentNode) {
                        controlPanel.style.animation = 'slideOut 0.5s ease';
                        setTimeout(() => controlPanel.remove(), 500);
                    }
                }, 30000);
            }

            // فتح لوحة التحكم يدوياً
            function openLibraryControlPanel(libraryName, libraryId) {
                // التحقق من أن المكتبة محملة
                const lib = addedLibraries.find(l => l.id == libraryId);
                if (!lib || !lib.loaded) {
                    showNotification('❌ المكتبة غير محملة بعد!', 'error');
                    return;
                }

                // إنشاء لوحة التحكم
                createLibraryControlPanel(libraryName, libraryId);

                // إضافة تأثير بصري للإشارة إلى فتح اللوحة
                showNotification(`🎛️ تم فتح لوحة تحكم ${libraryName}`, 'info');
            }

            function checkLibraryLoaded(name, type) {
                let isLoaded = false;
                let message = '';

                if (name === 'jQuery') {
                    isLoaded = !!(window.jQuery || window.$);
                    message = isLoaded ? '✅ jQuery محمل ومتاح للاستخدام' : '❌ jQuery لم يتم تحميله بشكل صحيح';
                } else if (name === 'SweetAlert2') {
                    isLoaded = !!(window.Swal);
                    message = isLoaded ? '✅ SweetAlert2 محمل ومتاح للاستخدام' : '❌ SweetAlert2 لم يتم تحميله بشكل صحيح';
                } else if (name === 'Chart.js') {
                    isLoaded = !!(window.Chart);
                    message = isLoaded ? '✅ Chart.js محمل ومتاح للاستخدام' : '❌ Chart.js لم يتم تحميله بشكل صحيح';
                } else {
                    message = `✅ ${name} تم تحميله`;
                }

                if (message) {
                    showLibraryMessage(message, isLoaded ? 'success' : 'error');
                }
            }

            function updateAddedLibrariesDisplay() {
                const container = document.getElementById('added-libraries');

                if (addedLibraries.length === 0) {
                    container.innerHTML = '<div style="text-align:center;color:#666;padding:10px;font-size:13px;">لم يتم إضافة أي مكتبات بعد</div>';
                    return;
                }

                container.innerHTML = '';

                addedLibraries.forEach(lib => {
                    const libDiv = document.createElement('div');
                    libDiv.id = `lib-${lib.id}`;
                    libDiv.style.cssText = `
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 8px 12px;
                        margin-bottom: 6px;
                        background: #f8fafc;
                        border: 1px solid #e5e7eb;
                        border-radius: 6px;
                        font-size: 13px;
                    `;

                    libDiv.innerHTML = `
                        <div style="flex: 1;">
                            <div style="font-weight: bold; color: #1e40af;">
                                ${lib.name}
                                <span style="background:${lib.type === 'js' ? '#10b981' : '#8b5cf6'};color:white;padding:1px 4px;border-radius:3px;font-size:10px;margin-right:6px;">${lib.type.toUpperCase()}</span>
                            </div>
                            <div style="color: #666; font-size: 11px; margin-top: 2px;" class="lib-status">🔄 جاري التحميل...</div>
                        </div>
                        <div style="display: flex; gap: 4px;">
                            <button onclick="openLibraryControlPanel('${lib.name}', '${lib.id}')" style="background:#1e40af;color:white;border:none;border-radius:4px;padding:4px 8px;cursor:pointer;font-size:11px;display:none;" class="control-btn-${lib.id}" title="فتح لوحة التحكم">🎛️</button>
                            <button onclick="removeLibrary('${lib.id}')" style="background:#ef4444;color:white;border:none;border-radius:4px;padding:4px 8px;cursor:pointer;font-size:11px;">حذف</button>
                        </div>
                    `;

                    container.appendChild(libDiv);
                });
            }

            function updateLibraryStatus(libId, status) {
                const libElement = document.getElementById(`lib-${libId}`);
                if (!libElement) return;

                const statusElement = libElement.querySelector('.lib-status');
                const controlBtn = libElement.querySelector(`.control-btn-${libId}`);
                if (!statusElement) return;

                switch(status) {
                    case 'loaded':
                        statusElement.innerHTML = '✅ تم التحميل بنجاح';
                        statusElement.style.color = '#10b981';
                        // إظهار زر لوحة التحكم
                        if (controlBtn) {
                            controlBtn.style.display = 'block';
                            controlBtn.style.animation = 'fadeIn 0.5s ease';
                        }
                        break;
                    case 'error':
                        statusElement.innerHTML = '❌ فشل في التحميل';
                        statusElement.style.color = '#ef4444';
                        // إخفاء زر لوحة التحكم
                        if (controlBtn) {
                            controlBtn.style.display = 'none';
                        }
                        break;
                }
            }

            function removeLibrary(libId) {
                const libIndex = addedLibraries.findIndex(lib => lib.id == libId);
                if (libIndex === -1) return;

                const lib = addedLibraries[libIndex];

                // إزالة العنصر من DOM
                if (lib.element && lib.element.parentNode) {
                    lib.element.parentNode.removeChild(lib.element);
                }

                // إزالة من القائمة
                addedLibraries.splice(libIndex, 1);

                // تحديث العرض
                updateAddedLibrariesDisplay();

                showLibraryMessage(`تم حذف ${lib.name}`, 'info');
            }

            function showLibraryMessage(message, type) {
                const messageDiv = document.getElementById('library-message');
                messageDiv.style.display = 'block';
                messageDiv.textContent = message;

                const colors = {
                    success: '#10b981',
                    error: '#ef4444',
                    warning: '#f59e0b',
                    info: '#1e40af'
                };

                messageDiv.style.background = colors[type] || colors.info;
                messageDiv.style.color = 'white';

                setTimeout(() => {
                    messageDiv.style.display = 'none';
                }, 3000);
            }

            function saveLibrariesToStorage() {
                const librariesData = addedLibraries.map(lib => ({
                    name: lib.name,
                    url: lib.url,
                    type: lib.type
                }));
                localStorage.setItem('7c_added_libraries', JSON.stringify(librariesData));
            }

            function loadSavedLibraries() {
                const saved = localStorage.getItem('7c_added_libraries');
                if (!saved) return;

                try {
                    const librariesData = JSON.parse(saved);
                    librariesData.forEach(lib => {
                        const existing = addedLibraries.find(existing => existing.url === lib.url);
                        if (!existing) {
                            addLibraryToPage(lib.url, lib.type, lib.name);
                        }
                    });
                } catch (error) {
                    console.error('خطأ في تحميل المكتبات المحفوظة:', error);
                }
            }

            // وظائف إضافية للمكتبات
            function showCustomLibrary() {
                const customDiv = document.getElementById('custom-library');
                customDiv.style.display = customDiv.style.display === 'none' ? 'block' : 'none';
            }

            function addCustomLibrary() {
                const url = document.getElementById('custom-url').value.trim();
                if (!url) {
                    showLibraryMessage('يرجى إدخال رابط صحيح', 'error');
                    return;
                }

                const type = url.endsWith('.css') ? 'css' : url.endsWith('.js') ? 'js' : null;
                if (!type) {
                    showLibraryMessage('الرابط يجب أن ينتهي بـ .css أو .js', 'error');
                    return;
                }

                const name = url.split('/').pop().split('.')[0];
                addLibraryToPage(url, type, name);
                document.getElementById('custom-url').value = '';
            }

            // إضافة سريعة للمكتبات الشائعة
            function addQuickLibrary(libName) {
                const quickLibs = {
                    'jquery': {
                        name: 'jQuery',
                        url: 'https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js',
                        type: 'js'
                    },
                    'sweetalert2': {
                        name: 'SweetAlert2',
                        url: 'https://cdn.jsdelivr.net/npm/sweetalert2@11',
                        type: 'js'
                    },
                    'chart': {
                        name: 'Chart.js',
                        url: 'https://cdn.jsdelivr.net/npm/chart.js',
                        type: 'js'
                    },
                    'animate': {
                        name: 'Animate.css',
                        url: 'https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css',
                        type: 'css'
                    },
                    'swiper': [
                        {
                            name: 'Swiper CSS',
                            url: 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css',
                            type: 'css'
                        },
                        {
                            name: 'Swiper JS',
                            url: 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js',
                            type: 'js'
                        }
                    ],
                    'typed': {
                        name: 'Typed.js',
                        url: 'https://cdn.jsdelivr.net/npm/typed.js@2.1.0/dist/typed.umd.js',
                        type: 'js'
                    }
                };

                const lib = quickLibs[libName];
                if (lib) {
                    if (Array.isArray(lib)) {
                        // إضافة مكتبات متعددة (CSS + JS)
                        showLibraryMessage(`⚡ إضافة سريعة: ${lib[0].name.split(' ')[0]} (CSS + JS)`, 'info');
                        lib.forEach(l => {
                            addLibraryToPage(l.url, l.type, l.name);
                        });
                    } else {
                        // إضافة مكتبة واحدة
                        showLibraryMessage(`⚡ إضافة سريعة: ${lib.name}`, 'info');
                        addLibraryToPage(lib.url, lib.type, lib.name);
                    }
                }
            }

            // اختبار تأثيرات المكتبات
            function testLibraryEffects() {
                let results = [];
                let hasWorkingLibraries = false;

                console.log('🔍 بدء اختبار المكتبات...');

                // اختبار jQuery
                if (window.jQuery || window.$) {
                    results.push('✅ jQuery متاح ويعمل');
                    hasWorkingLibraries = true;
                    try {
                        const title = document.querySelector('h1');
                        if (title) {
                            // تأثير واضح ومرئي
                            $(title).animate({
                                fontSize: '+=20px',
                                color: '#10b981'
                            }, 1000).animate({
                                fontSize: '-=20px',
                                color: '#8B4513'
                            }, 1000);
                            results.push('🎨 تم تطبيق تأثير jQuery على العنوان الرئيسي');
                        }
                    } catch(e) {
                        results.push('❌ خطأ في تطبيق jQuery: ' + e.message);
                    }
                } else {
                    results.push('❌ jQuery غير متاح - أضف مكتبة jQuery أولاً');
                }

                // اختبار SweetAlert2
                if (window.Swal) {
                    results.push('✅ SweetAlert2 متاح ويعمل');
                    hasWorkingLibraries = true;
                    try {
                        Swal.fire({
                            title: '🎉 رائع!',
                            text: 'SweetAlert2 يعمل بشكل مثالي!',
                            icon: 'success',
                            timer: 3000,
                            timerProgressBar: true,
                            position: 'center',
                            showConfirmButton: false
                        });
                        results.push('🍭 تم عرض نافذة SweetAlert2 بنجاح');
                    } catch(e) {
                        results.push('❌ خطأ في SweetAlert2: ' + e.message);
                    }
                } else {
                    results.push('❌ SweetAlert2 غير متاح - أضف مكتبة SweetAlert2 أولاً');
                }

                // اختبار Chart.js
                if (window.Chart) {
                    results.push('✅ Chart.js متاح ويعمل');
                    hasWorkingLibraries = true;
                    results.push('📊 يمكنك إنشاء رسوم بيانية الآن');
                } else {
                    results.push('❌ Chart.js غير متاح - أضف مكتبة Chart.js للرسوم البيانية');
                }

                // إضافة تأثير بصري عام
                if (hasWorkingLibraries) {
                    createSuccessEffect();
                } else {
                    createWarningEffect();
                }

                // عرض النتائج
                const resultText = results.join('\n');
                console.log('نتائج الاختبار:', results);

                setTimeout(() => {
                    alert('🧪 نتائج اختبار المكتبات:\n\n' + resultText + '\n\n💡 تحقق من وحدة التحكم (F12) للمزيد من التفاصيل');
                }, hasWorkingLibraries ? 1000 : 100);
            }

            function createSuccessEffect() {
                const effect = document.createElement('div');
                effect.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(45deg, #10b981, #06b6d4);
                    color: white;
                    padding: 30px;
                    border-radius: 20px;
                    font-size: 24px;
                    font-weight: bold;
                    z-index: 10002;
                    box-shadow: 0 20px 60px rgba(16, 185, 129, 0.4);
                    animation: successBounce 1s ease;
                    text-align: center;
                    direction: rtl;
                `;

                effect.innerHTML = `
                    <div>🎉 المكتبات تعمل بنجاح!</div>
                    <div style="font-size: 16px; margin-top: 10px; opacity: 0.9;">
                        شاهد التأثيرات على الصفحة
                    </div>
                `;

                document.body.appendChild(effect);

                // إضافة الحركة
                const style = document.createElement('style');
                style.textContent = `
                    @keyframes successBounce {
                        0% { transform: translate(-50%, -50%) scale(0.3); opacity: 0; }
                        50% { transform: translate(-50%, -50%) scale(1.1); opacity: 1; }
                        100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
                    }
                `;
                document.head.appendChild(style);

                setTimeout(() => {
                    effect.remove();
                    style.remove();
                }, 3000);
            }

            function createWarningEffect() {
                const effect = document.createElement('div');
                effect.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(45deg, #f59e0b, #ef4444);
                    color: white;
                    padding: 30px;
                    border-radius: 20px;
                    font-size: 20px;
                    font-weight: bold;
                    z-index: 10002;
                    box-shadow: 0 20px 60px rgba(245, 158, 11, 0.4);
                    text-align: center;
                    direction: rtl;
                `;

                effect.innerHTML = `
                    <div>⚠️ لا توجد مكتبات للاختبار</div>
                    <div style="font-size: 14px; margin-top: 10px; opacity: 0.9;">
                        أضف مكتبات أولاً ثم اختبرها
                    </div>
                `;

                document.body.appendChild(effect);

                setTimeout(() => {
                    effect.remove();
                }, 3000);
            }

            function showLibraryExamples() {
                const examples = `
🎨 أمثلة للمكتبات المتاحة:

📊 للرسوم البيانية:
• Chart.js - رسوم بيانية تفاعلية

🎭 للحركات والتأثيرات:
• Animate.css - حركات CSS جاهزة
• AOS - حركات عند التمرير
• Typed.js - تأثير الكتابة

🎨 للتصميم:
• Bootstrap RTL - تصميم متجاوب
• SweetAlert2 - نوافذ جميلة

⚡ للتفاعل:
• jQuery - تفاعلات سهلة
• Alpine.js - تفاعلات حديثة

🔧 نصائح:
1. أضف المكتبة أولاً
2. اضغط "🧪 اختبار المكتبات"
3. شاهد التأثيرات تعمل!
                `;

                alert(examples);
            }

            // إضافة أنماط الحركة
            const animationStyles = document.createElement('style');
            animationStyles.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOut {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(animationStyles);

            // استرجاع المحتوى المحفوظ عند تحميل الصفحة
            window.addEventListener('DOMContentLoaded', function() {
                // استرجاع المحتوى
                const savedContent = localStorage.getItem('7c_academy_content');
                const savedTimestamp = localStorage.getItem('7c_academy_timestamp');

                if (savedContent && savedTimestamp) {
                    const saveDate = new Date(savedTimestamp);
                    const now = new Date();
                    const daysDiff = (now - saveDate) / (1000 * 60 * 60 * 24);

                    // إذا كان الحفظ خلال آخر 30 يوم
                    if (daysDiff <= 30) {
                        document.body.innerHTML = savedContent;
                        console.log('تم استرجاع المحتوى المحفوظ من:', saveDate.toLocaleString('ar'));
                    }
                }

                // استرجاع المكتبات المحفوظة
                setTimeout(() => {
                    loadSavedLibraries();
                }, 1000);

                // إضافة مستمع لمفتاح Enter في حقل كلمة المرور
                const passwordInput = document.getElementById('edit-password');
                if (passwordInput) {
                    passwordInput.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            verifyPassword();
                        }
                    });
                }
            });

            // وظائف التحكم في المكتبات
            function jQueryEffects(effect) {
                if (!window.jQuery) {
                    alert('jQuery غير متاح!');
                    return;
                }

                const elements = $('.feature-card, .stat-card, h1, h2, h3');

                switch(effect) {
                    case 'fadeToggle':
                        elements.fadeToggle(1000);
                        break;
                    case 'slideToggle':
                        elements.slideToggle(1000);
                        break;
                    case 'bounce':
                        elements.animate({
                            marginTop: '-=20px'
                        }, 300).animate({
                            marginTop: '+=40px'
                        }, 300).animate({
                            marginTop: '-=20px'
                        }, 300);
                        break;
                    case 'pulse':
                        elements.animate({
                            opacity: 0.5,
                            transform: 'scale(1.1)'
                        }, 500).animate({
                            opacity: 1,
                            transform: 'scale(1)'
                        }, 500);
                        break;
                    case 'colorChange':
                        const colors = ['#1e40af', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];
                        const randomColor = colors[Math.floor(Math.random() * colors.length)];
                        elements.animate({
                            color: randomColor
                        }, 1000);
                        break;
                }
            }

            function showSweetAlert(type) {
                if (!window.Swal) {
                    alert('SweetAlert2 غير متاح!');
                    return;
                }

                const alerts = {
                    success: {
                        title: '🎉 ممتاز!',
                        text: 'تم تنفيذ العملية بنجاح',
                        icon: 'success'
                    },
                    error: {
                        title: '❌ خطأ!',
                        text: 'حدث خطأ أثناء العملية',
                        icon: 'error'
                    },
                    warning: {
                        title: '⚠️ تحذير!',
                        text: 'يرجى الانتباه لهذا التحذير',
                        icon: 'warning'
                    },
                    info: {
                        title: 'ℹ️ معلومات',
                        text: 'هذه معلومات مفيدة لك',
                        icon: 'info'
                    },
                    question: {
                        title: '❓ سؤال',
                        text: 'هل تريد المتابعة؟',
                        icon: 'question',
                        showCancelButton: true,
                        confirmButtonText: 'نعم',
                        cancelButtonText: 'لا'
                    },
                    custom: {
                        title: '🎨 تصميم مخصص',
                        html: '<div style="color: #1e40af; font-size: 18px;">يمكنك تخصيص هذه النافذة بالكامل!</div>',
                        background: 'linear-gradient(45deg, #f0f9ff, #e0f2fe)',
                        confirmButtonColor: '#1e40af'
                    }
                };

                Swal.fire(alerts[type]);
            }

            function createQuickChart(type) {
                if (!window.Chart) {
                    alert('Chart.js غير متاح!');
                    return;
                }

                // إزالة أي رسم سابق
                const existingChart = document.getElementById('quick-chart-container');
                if (existingChart) existingChart.remove();

                // إنشاء حاوي الرسم
                const chartContainer = document.createElement('div');
                chartContainer.id = 'quick-chart-container';
                chartContainer.style.cssText = `
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    width: 350px;
                    height: 250px;
                    background: white;
                    padding: 15px;
                    border-radius: 15px;
                    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
                    z-index: 9998;
                `;

                chartContainer.innerHTML = `
                    <button onclick="document.getElementById('quick-chart-container').remove()" style="position: absolute; top: 10px; right: 15px; background: #ef4444; color: white; border: none; border-radius: 50%; width: 25px; height: 25px; cursor: pointer;">×</button>
                    <h4 style="margin: 0 0 15px 0; color: #1e40af; text-align: center;">📊 ${type.toUpperCase()}</h4>
                    <canvas id="quick-chart" width="320" height="180"></canvas>
                `;

                document.body.appendChild(chartContainer);

                // بيانات تجريبية
                const data = {
                    labels: ['كرة القدم', 'كرة السلة', 'التنس', 'السباحة'],
                    datasets: [{
                        label: 'عدد اللاعبين',
                        data: [120, 85, 60, 45],
                        backgroundColor: ['#1e40af', '#10b981', '#f59e0b', '#8b5cf6']
                    }]
                };

                // إنشاء الرسم
                setTimeout(() => {
                    const ctx = document.getElementById('quick-chart');
                    new Chart(ctx, {
                        type: type,
                        data: data,
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    display: type !== 'pie' && type !== 'doughnut'
                                }
                            }
                        }
                    });
                }, 100);

                // إزالة تلقائية بعد 15 ثانية
                setTimeout(() => {
                    if (chartContainer.parentNode) {
                        chartContainer.remove();
                    }
                }, 15000);
            }

            // وظائف معاينة ومعلومات المكتبات
            function previewLibraryFeatures(libraryName) {
                const features = {
                    'jQuery': {
                        title: '⚡ jQuery - مكتبة JavaScript الشهيرة',
                        features: [
                            '🎯 تحديد العناصر بسهولة',
                            '🎨 تأثيرات وحركات متقدمة',
                            '📡 طلبات AJAX مبسطة',
                            '🎭 معالجة الأحداث',
                            '🔄 تلاعب بـ DOM'
                        ],
                        demo: 'سيتم تطبيق تأثيرات على العنوان والأزرار'
                    },
                    'SweetAlert2': {
                        title: '🍭 SweetAlert2 - نوافذ تنبيه جميلة',
                        features: [
                            '✅ نوافذ نجاح وخطأ',
                            '🎨 تصميم عصري وجذاب',
                            '⚙️ قابل للتخصيص بالكامل',
                            '📱 متجاوب مع الجوال',
                            '🎭 حركات وتأثيرات'
                        ],
                        demo: 'ستظهر نافذة ترحيب جميلة'
                    },
                    'Chart.js': {
                        title: '📊 Chart.js - رسوم بيانية تفاعلية',
                        features: [
                            '📈 رسوم خطية ودائرية',
                            '📊 رسوم أعمدة وشريطية',
                            '🎯 تفاعلية ومتحركة',
                            '📱 متجاوبة مع الشاشات',
                            '🎨 ألوان وأنماط مخصصة'
                        ],
                        demo: 'سيتم إنشاء رسوم بيانية في منطقة التجريب'
                    },
                    'Swiper JS': {
                        title: '🖼️ Swiper - سلايدر متطور',
                        features: [
                            '📱 يدعم اللمس والسحب',
                            '🎯 تأثيرات انتقال متعددة',
                            '🔄 تشغيل تلقائي',
                            '📐 متجاوب ومرن',
                            '🎨 قابل للتخصيص'
                        ],
                        demo: 'سيتم إنشاء سلايدر تفاعلي في منطقة التجريب'
                    },
                    'Animate.css': {
                        title: '🎭 Animate.css - حركات CSS جاهزة',
                        features: [
                            '🏀 حركات ارتداد ونبضة',
                            '🌅 تأثيرات ظهور واختفاء',
                            '🔄 حركات دوران وتكبير',
                            '⚡ سريعة وخفيفة',
                            '🎨 سهلة الاستخدام'
                        ],
                        demo: 'ستتحرك البطاقات بحركات جميلة'
                    },
                    'Typed.js': {
                        title: '⌨️ Typed.js - تأثير الكتابة',
                        features: [
                            '⌨️ تأثير كتابة واقعي',
                            '🔄 تكرار ومسح النص',
                            '⚡ سرعة قابلة للتحكم',
                            '🎯 نصوص متعددة',
                            '🎨 مؤشر قابل للتخصيص'
                        ],
                        demo: 'سيظهر تأثير كتابة في منطقة التجريب'
                    }
                };

                const libInfo = features[libraryName] || {
                    title: `📚 ${libraryName}`,
                    features: ['مكتبة مفيدة للتطوير'],
                    demo: 'ستظهر تأثيرات عند إضافة المكتبة'
                };

                if (window.Swal) {
                    Swal.fire({
                        title: libInfo.title,
                        html: `
                            <div style="text-align: right; direction: rtl;">
                                <h4 style="color: #1e40af; margin-bottom: 15px;">🌟 المميزات:</h4>
                                <ul style="list-style: none; padding: 0;">
                                    ${libInfo.features.map(feature => `<li style="padding: 5px 0; border-bottom: 1px solid #f0f0f0;">${feature}</li>`).join('')}
                                </ul>
                                <div style="margin-top: 20px; padding: 15px; background: #f0f9ff; border-radius: 8px; border-right: 4px solid #1e40af;">
                                    <strong>🎬 التجربة:</strong><br>
                                    ${libInfo.demo}
                                </div>
                            </div>
                        `,
                        width: 500,
                        confirmButtonText: 'إضافة المكتبة',
                        showCancelButton: true,
                        cancelButtonText: 'إغلاق',
                        confirmButtonColor: '#1e40af'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // البحث عن المكتبة وإضافتها
                            const lib = LIBRARIES.find(l => l.name === libraryName);
                            if (lib) {
                                selectedLibrary = lib;
                                addSelectedLibrary();
                            }
                        }
                    });
                } else {
                    alert(`${libInfo.title}\n\nالمميزات:\n${libInfo.features.join('\n')}\n\nالتجربة: ${libInfo.demo}`);
                }
            }

            function showLibraryInfo(libraryName) {
                const info = {
                    'jQuery': {
                        version: '3.7.1',
                        size: '~87KB',
                        license: 'MIT',
                        website: 'https://jquery.com',
                        description: 'مكتبة JavaScript سريعة وخفيفة تبسط التلاعب بـ HTML وCSS والأحداث'
                    },
                    'SweetAlert2': {
                        version: '11.x',
                        size: '~159KB',
                        license: 'MIT',
                        website: 'https://sweetalert2.github.io',
                        description: 'مكتبة لإنشاء نوافذ تنبيه جميلة ومتجاوبة بديلة عن alert() التقليدية'
                    },
                    'Chart.js': {
                        version: '4.x',
                        size: '~186KB',
                        license: 'MIT',
                        website: 'https://www.chartjs.org',
                        description: 'مكتبة رسوم بيانية مرنة وقوية لمطوري الويب'
                    },
                    'Swiper JS': {
                        version: '11.x',
                        size: '~150KB',
                        license: 'MIT',
                        website: 'https://swiperjs.com',
                        description: 'أقوى سلايدر للجوال واللمس مع تسارع الأجهزة'
                    },
                    'Animate.css': {
                        version: '4.1.1',
                        size: '~56KB',
                        license: 'MIT',
                        website: 'https://animate.style',
                        description: 'مكتبة حركات CSS جاهزة للاستخدام'
                    },
                    'Typed.js': {
                        version: '2.1.0',
                        size: '~25KB',
                        license: 'MIT',
                        website: 'https://mattboldt.com/demos/typed-js',
                        description: 'مكتبة JavaScript لإنشاء تأثير الكتابة'
                    }
                };

                const libInfo = info[libraryName] || {
                    version: 'غير محدد',
                    size: 'غير محدد',
                    license: 'غير محدد',
                    website: '#',
                    description: 'معلومات غير متوفرة'
                };

                if (window.Swal) {
                    Swal.fire({
                        title: `ℹ️ معلومات ${libraryName}`,
                        html: `
                            <div style="text-align: right; direction: rtl;">
                                <div style="background: #f8fafc; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                    <p><strong>📝 الوصف:</strong><br>${libInfo.description}</p>
                                </div>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; text-align: center;">
                                    <div style="background: #eff6ff; padding: 10px; border-radius: 6px;">
                                        <strong>📦 الإصدار</strong><br>
                                        <span style="color: #1e40af;">${libInfo.version}</span>
                                    </div>
                                    <div style="background: #f0fdf4; padding: 10px; border-radius: 6px;">
                                        <strong>📏 الحجم</strong><br>
                                        <span style="color: #16a34a;">${libInfo.size}</span>
                                    </div>
                                    <div style="background: #fefce8; padding: 10px; border-radius: 6px;">
                                        <strong>⚖️ الرخصة</strong><br>
                                        <span style="color: #ca8a04;">${libInfo.license}</span>
                                    </div>
                                    <div style="background: #fdf2f8; padding: 10px; border-radius: 6px;">
                                        <strong>🌐 الموقع</strong><br>
                                        <a href="${libInfo.website}" target="_blank" style="color: #be185d;">زيارة</a>
                                    </div>
                                </div>
                            </div>
                        `,
                        width: 500,
                        confirmButtonText: 'إضافة المكتبة',
                        showCancelButton: true,
                        cancelButtonText: 'إغلاق',
                        confirmButtonColor: '#1e40af'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            const lib = LIBRARIES.find(l => l.name === libraryName);
                            if (lib) {
                                selectedLibrary = lib;
                                addSelectedLibrary();
                            }
                        }
                    });
                } else {
                    alert(`معلومات ${libraryName}:\n\nالوصف: ${libInfo.description}\nالإصدار: ${libInfo.version}\nالحجم: ${libInfo.size}\nالرخصة: ${libInfo.license}`);
                }
            }
        </script>

        <!-- نظام تحرير الصفحة المتقدم -->
        <div id="edit-overlay" style="display:none;position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.8);z-index:10000;backdrop-filter:blur(10px);">
            <div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:#fff;padding:30px;border-radius:20px;box-shadow:0 20px 60px rgba(0,0,0,0.3);min-width:400px;text-align:center;direction:rtl;">
                <h3 style="color:#1e40af;margin-bottom:20px;font-size:24px;">🔐 تحرير الصفحة الرئيسية</h3>
                <p style="color:#666;margin-bottom:20px;">أدخل كلمة المرور للمتابعة:</p>
                <input type="password" id="edit-password" placeholder="كلمة المرور..." style="width:100%;padding:15px;border:2px solid #e5e7eb;border-radius:10px;font-size:16px;margin-bottom:20px;text-align:center;">
                <div style="display:flex;gap:10px;justify-content:center;">
                    <button onclick="verifyPassword()" style="background:#1e40af;color:#fff;padding:12px 24px;border:none;border-radius:10px;cursor:pointer;font-size:16px;">تأكيد</button>
                    <button onclick="closeEditMode()" style="background:#ef4444;color:#fff;padding:12px 24px;border:none;border-radius:10px;cursor:pointer;font-size:16px;">إلغاء</button>
                </div>
            </div>
        </div>

        <!-- شريط أدوات التحرير -->
        <div id="edit-toolbar" style="display:none;position:fixed;top:20px;left:50%;transform:translateX(-50%);background:rgba(30,64,175,0.95);color:#fff;padding:15px 25px;border-radius:15px;z-index:9999;backdrop-filter:blur(10px);box-shadow:0 10px 30px rgba(0,0,0,0.3);">
            <div style="display:flex;align-items:center;gap:15px;font-size:14px;flex-wrap:wrap;">
                <span>🎨 وضع التحرير نشط</span>
                <button onclick="showLibraryPanel()" style="background:#8b5cf6;color:#fff;padding:8px 16px;border:none;border-radius:8px;cursor:pointer;">📚 مكتبات</button>
                <button onclick="saveChanges()" style="background:#10b981;color:#fff;padding:8px 16px;border:none;border-radius:8px;cursor:pointer;">💾 حفظ</button>
                <button onclick="exitEditMode()" style="background:#ef4444;color:#fff;padding:8px 16px;border:none;border-radius:8px;cursor:pointer;">❌ خروج</button>
            </div>
        </div>

        <!-- نافذة المكتبات -->
        <div id="library-panel" style="display:none;position:fixed;top:100px;right:20px;width:400px;max-height:500px;background:#fff;color:#222;padding:20px;border-radius:16px;box-shadow:0 20px 60px rgba(0,0,0,0.3);z-index:10001;direction:rtl;overflow-y:auto;">
            <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:15px;">
                <h3 style="margin:0;color:#1e40af;font-size:20px;">📚 مكتبة المكونات</h3>
                <button onclick="hideLibraryPanel()" style="background:#ef4444;color:white;border:none;font-size:16px;cursor:pointer;padding:5px 10px;border-radius:6px;font-weight:bold;">✕ إغلاق</button>
            </div>

            <div style="margin-bottom:15px;">
                <input id="library-search" type="text" placeholder="ابحث عن مكتبة... (مثال: charts, icons, fonts)" style="width:100%;padding:12px;border:2px solid #e5e7eb;border-radius:8px;font-size:14px;">
            </div>

            <div id="library-results" style="max-height:200px;overflow-y:auto;margin-bottom:15px;border:1px solid #e5e7eb;border-radius:8px;padding:10px;">
                <div style="text-align:center;color:#666;padding:20px;">اكتب في البحث لعرض المكتبات المتاحة</div>
            </div>

            <div style="display:flex;gap:10px;margin-bottom:15px;">
                <button id="add-library-btn" onclick="addSelectedLibrary()" style="flex:1;background:#1e40af;color:#fff;padding:12px;border:none;border-radius:8px;cursor:pointer;font-size:14px;" disabled>إضافة المكتبة</button>
                <button onclick="showCustomLibrary()" style="background:#8b5cf6;color:#fff;padding:12px 16px;border:none;border-radius:8px;cursor:pointer;font-size:14px;">مخصص</button>
            </div>

            <div style="display:flex;gap:10px;margin-bottom:15px;">
                <button onclick="testLibraryEffects()" style="flex:1;background:#10b981;color:#fff;padding:10px;border:none;border-radius:8px;cursor:pointer;font-size:13px;">🧪 اختبار المكتبات</button>
                <button onclick="showLibraryExamples()" style="background:#f59e0b;color:#fff;padding:10px 16px;border:none;border-radius:8px;cursor:pointer;font-size:13px;">💡 أمثلة</button>
            </div>

            <div style="display:flex;gap:5px;margin-bottom:15px;flex-wrap:wrap;">
                <button onclick="addQuickLibrary('jquery')" style="background:#0066cc;color:#fff;padding:6px 10px;border:none;border-radius:6px;cursor:pointer;font-size:11px;">⚡ jQuery</button>
                <button onclick="addQuickLibrary('sweetalert2')" style="background:#ff6b6b;color:#fff;padding:6px 10px;border:none;border-radius:6px;cursor:pointer;font-size:11px;">🍭 Sweet</button>
                <button onclick="addQuickLibrary('chart')" style="background:#4ecdc4;color:#fff;padding:6px 10px;border:none;border-radius:6px;cursor:pointer;font-size:11px;">📊 Chart</button>
                <button onclick="addQuickLibrary('animate')" style="background:#a8e6cf;color:#333;padding:6px 10px;border:none;border-radius:6px;cursor:pointer;font-size:11px;">🎭 Animate</button>
                <button onclick="addQuickLibrary('swiper')" style="background:#6366f1;color:#fff;padding:6px 10px;border:none;border-radius:6px;cursor:pointer;font-size:11px;">🖼️ Swiper</button>
                <button onclick="addQuickLibrary('typed')" style="background:#8b5cf6;color:#fff;padding:6px 10px;border:none;border-radius:6px;cursor:pointer;font-size:11px;">⌨️ Typed</button>
            </div>

            <div id="custom-library" style="display:none;margin-top:15px;padding:15px;background:#f8fafc;border-radius:8px;">
                <label style="display:block;margin-bottom:8px;font-weight:bold;">رابط مخصص:</label>
                <input id="custom-url" type="text" placeholder="https://cdn.jsdelivr.net/npm/..." style="width:100%;padding:10px;border:1px solid #d1d5db;border-radius:6px;margin-bottom:10px;">
                <button onclick="addCustomLibrary()" style="background:#10b981;color:#fff;padding:8px 16px;border:none;border-radius:6px;cursor:pointer;">إضافة</button>
            </div>

            <!-- قائمة المكتبات المضافة -->
            <div style="border-top:2px solid #e5e7eb;padding-top:15px;">
                <h4 style="margin:0 0 10px 0;color:#1e40af;font-size:16px;">📦 المكتبات المضافة:</h4>
                <div id="added-libraries" style="max-height:150px;overflow-y:auto;">
                    <div style="text-align:center;color:#666;padding:10px;font-size:13px;">لم يتم إضافة أي مكتبات بعد</div>
                </div>
            </div>

            <div id="library-message" style="margin-top:10px;padding:10px;border-radius:6px;font-size:13px;display:none;"></div>
        </div>
    </body>

</html>
