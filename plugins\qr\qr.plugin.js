// ==================== Plugin: نظام QR ====================
export const QRSystemPlugin = {
    id: 'qr',
    name: 'نظام QR',
    init() {
        if (!document.getElementById('qr-plugin-style')) {
            const style = document.createElement('style');
            style.id = 'qr-plugin-style';
            style.innerHTML = `
                .plugin-section {background: #181f2a; color: #fff; border-radius: 18px; box-shadow: 0 2px 16px #0002; max-width: 900px; margin: 32px auto 0; padding: 32px 24px 24px; font-family: 'Cairo',sans-serif;}
                .plugin-header {display: flex; align-items: center; justify-content: space-between; margin-bottom: 18px;}
                .plugin-header h2 {font-size: 1.5rem; font-weight: bold; margin: 0;}
                .plugin-btn {background: linear-gradient(90deg,#4f8cff,#6f6bff); color: #fff; border: none; border-radius: 8px; padding: 10px 22px; font-size: 1rem; font-weight: 600; cursor: pointer; transition: background 0.2s;}
                .plugin-btn:hover {background: linear-gradient(90deg,#6f6bff,#4f8cff);}
                .plugin-table {width: 100%; border-collapse: collapse; background: #232b3b; border-radius: 12px; overflow: hidden; margin-top: 10px;}
                .plugin-table th, .plugin-table td {padding: 12px 8px; text-align: center;}
                .plugin-table th {background: #232b3b; color: #8bb4ff; font-weight: 700; border-bottom: 2px solid #2d3950;}
                .plugin-table tr {transition: background 0.2s;}
                .plugin-table tr:hover {background: #232b3bcc;}
                .plugin-table td {border-bottom: 1px solid #232b3b;}
                .plugin-action-btn {background: #2d3950; color: #8bb4ff; border: none; border-radius: 6px; padding: 6px 16px; margin: 0 2px; font-size: 0.95rem; cursor: pointer; transition: background 0.2s;}
                .plugin-action-btn:hover {background: #4f8cff; color: #fff;}
                .plugin-modal-bg {position: fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.45); z-index: 9999; display: flex; align-items: center; justify-content: center;}
                .plugin-modal {background: #232b3b; border-radius: 16px; padding: 32px 24px 24px; min-width: 320px; max-width: 95vw; box-shadow: 0 4px 32px #0005;}
                .plugin-modal h3 {margin-top:0; color:#8bb4ff; font-size:1.2rem; margin-bottom:18px;}
                .plugin-modal label {display:block; margin-bottom:6px; color:#b6cfff; font-size:1rem;}
                .plugin-modal input {width:100%; padding:8px 10px; border-radius:6px; border:none; background:#181f2a; color:#fff; margin-bottom:16px; font-size:1rem;}
                .plugin-modal .modal-actions {display:flex; justify-content:flex-end; gap:10px;}
                .plugin-modal .plugin-btn {padding:8px 18px; font-size:1rem;}
            `;
            document.head.appendChild(style);
        }
        if (!document.getElementById('qr-plugin-container')) {
            const container = document.createElement('section');
            container.id = 'qr-plugin-container';
            container.className = 'plugin-section';
            container.innerHTML = `
                <div class="plugin-header">
                    <h2>نظام QR</h2>
                    <button id="add-qr-btn" class="plugin-btn">توليد كود QR</button>
                </div>
                <table class="plugin-table" id="qr-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>القيمة</th>
                            <th>الكود</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="qr-table-body">
                        <!-- سيتم تعبئة الأكواد هنا -->
                    </tbody>
                </table>
            `;
            document.body.prepend(container);
        }
        this.renderQRs();
        document.getElementById('add-qr-btn').onclick = () => this.openQRModal();
    },
    destroy() {
        const container = document.getElementById('qr-plugin-container');
        if (container) container.remove();
        const style = document.getElementById('qr-plugin-style');
        if (style) style.remove();
        this.closeModal();
    },
    renderQRs() {
        const qrs = JSON.parse(localStorage.getItem('plugin_qrs') || '[]');
        const tbody = document.getElementById('qr-table-body');
        if (!tbody) return;
        tbody.innerHTML = qrs.length ? qrs.map((q, i) => `
            <tr>
                <td>${q.name || ''}</td>
                <td>${q.value || ''}</td>
                <td>${q.value ? `<img src='https://api.qrserver.com/v1/create-qr-code/?size=80x80&data=${encodeURIComponent(q.value)}' alt='QR'/>` : ''}</td>
                <td>
                    <button class="plugin-action-btn" onclick="window.PluginManager.plugins.qr.openQRModal(${i})">تعديل</button>
                    <button class="plugin-action-btn" onclick="window.PluginManager.plugins.qr.deleteQR(${i})">حذف</button>
                </td>
            </tr>
        `).join('') : '<tr><td colspan="4">لا يوجد أكواد QR</td></tr>';
    },
    openQRModal(index = null) {
        this.closeModal();
        const qrs = JSON.parse(localStorage.getItem('plugin_qrs') || '[]');
        const qr = index !== null ? qrs[index] : { name: '', value: '' };
        const modalBg = document.createElement('div');
        modalBg.className = 'plugin-modal-bg';
        modalBg.id = 'qr-plugin-modal-bg';
        modalBg.innerHTML = `
            <div class="plugin-modal">
                <h3>${index !== null ? 'تعديل كود QR' : 'توليد كود QR جديد'}</h3>
                <label>اسم الكود</label>
                <input id="modal-qr-name" type="text" value="${qr.name || ''}" placeholder="مثال: دخول الملعب" />
                <label>القيمة/الرابط</label>
                <input id="modal-qr-value" type="text" value="${qr.value || ''}" placeholder="مثال: https://... أو رقم" />
                <div class="modal-actions">
                    <button class="plugin-btn" id="save-qr-btn">${index !== null ? 'حفظ التعديلات' : 'توليد'}</button>
                    <button class="plugin-btn" id="cancel-qr-btn" style="background:#2d3950;">إلغاء</button>
                </div>
            </div>
        `;
        document.body.appendChild(modalBg);
        document.getElementById('cancel-qr-btn').onclick = () => this.closeModal();
        document.getElementById('save-qr-btn').onclick = () => {
            const name = document.getElementById('modal-qr-name').value.trim();
            const value = document.getElementById('modal-qr-value').value.trim();
            if (!name || !value) {
                alert('يرجى تعبئة جميع الحقول');
                return;
            }
            if (index !== null) {
                qrs[index] = { name, value };
            } else {
                qrs.push({ name, value });
            }
            localStorage.setItem('plugin_qrs', JSON.stringify(qrs));
            this.closeModal();
            this.renderQRs();
        };
        setTimeout(() => {
            document.getElementById('modal-qr-name').focus();
        }, 100);
    },
    closeModal() {
        const modalBg = document.getElementById('qr-plugin-modal-bg');
        if (modalBg) modalBg.remove();
    },
    deleteQR(index) {
        if (!confirm('هل أنت متأكد من حذف الكود؟')) return;
        const qrs = JSON.parse(localStorage.getItem('plugin_qrs') || '[]');
        qrs.splice(index, 1);
        localStorage.setItem('plugin_qrs', JSON.stringify(qrs));
        this.renderQRs();
    }
};
