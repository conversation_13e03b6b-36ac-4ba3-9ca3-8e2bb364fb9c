<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأخطاء</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            direction: rtl;
        }
        .error {
            background: #dc2626;
            color: white;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #16a34a;
            color: white;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .warning {
            background: #f59e0b;
            color: white;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>اختبار أخطاء JavaScript</h1>
    <button onclick="testMainFile()">اختبار الملف الرئيسي</button>
    <div id="results"></div>

    <script>
        function testMainFile() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="warning">جاري الاختبار...</div>';
            
            // إنشاء iframe لتحميل الملف الرئيسي
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = 'admin-advanced.html';
            
            iframe.onload = function() {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const errors = [];
                    
                    // التحقق من وجود أخطاء JavaScript
                    const originalConsoleError = console.error;
                    console.error = function(...args) {
                        errors.push(args.join(' '));
                        originalConsoleError.apply(console, args);
                    };
                    
                    // تشغيل بعض الاختبارات
                    setTimeout(() => {
                        let resultHTML = '';
                        
                        if (errors.length === 0) {
                            resultHTML += '<div class="success">✅ لم يتم العثور على أخطاء JavaScript</div>';
                        } else {
                            resultHTML += '<div class="error">❌ تم العثور على أخطاء:</div>';
                            errors.forEach(error => {
                                resultHTML += '<div class="error">' + error + '</div>';
                            });
                        }
                        
                        // التحقق من تحميل المكتبات
                        const iframeWindow = iframe.contentWindow;
                        if (typeof iframeWindow.Swal !== 'undefined') {
                            resultHTML += '<div class="success">✅ SweetAlert2 محمل بنجاح</div>';
                        } else {
                            resultHTML += '<div class="warning">⚠️ SweetAlert2 غير محمل</div>';
                        }
                        
                        if (typeof iframeWindow.Chart !== 'undefined') {
                            resultHTML += '<div class="success">✅ Chart.js محمل بنجاح</div>';
                        } else {
                            resultHTML += '<div class="warning">⚠️ Chart.js غير محمل</div>';
                        }
                        
                        results.innerHTML = resultHTML;
                        
                        // إزالة الـ iframe
                        document.body.removeChild(iframe);
                        
                        // استعادة console.error
                        console.error = originalConsoleError;
                    }, 3000);
                    
                } catch (error) {
                    results.innerHTML = '<div class="error">❌ خطأ في الاختبار: ' + error.message + '</div>';
                    document.body.removeChild(iframe);
                }
            };
            
            iframe.onerror = function() {
                results.innerHTML = '<div class="error">❌ فشل في تحميل الملف</div>';
                document.body.removeChild(iframe);
            };
            
            document.body.appendChild(iframe);
        }
        
        // التحقق من أخطاء الصفحة الحالية
        window.addEventListener('error', function(e) {
            console.error('خطأ JavaScript:', e.error);
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Promise مرفوض:', e.reason);
        });
    </script>
</body>
</html>
