<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المباريات والبطولات المتطور - أكاديمية 7C الرياضية</title>

    <!-- External Libraries -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

    <!-- AI and Advanced Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/ml-matrix@6.10.4/lib/index.min.js"></script>
    <style>
        :root {
            /* Sports Color Palette */
            --primary-dark: #0f172a;
            --primary-blue: #1e3a8a;
            --accent-blue: #3b82f6;
            --light-blue: #60a5fa;
            --silver: #e2e8f0;
            --white: #ffffff;
            --dark-silver: #94a3b8;

            /* Academy Colors */
            --academy-primary: #8B4513;
            --academy-secondary: #A0522D;
            --academy-accent: #CD853F;
            --success-color: #228B22;
            --warning-color: #DAA520;
            --danger-color: #B22222;
            --text-dark: #2D1B0E;
            --text-light: #8B4513;

            /* Sports Gradients */
            --gradient-primary: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #3b82f6 100%);
            --gradient-academy: linear-gradient(135deg, var(--academy-primary), var(--academy-secondary));
            --gradient-field: linear-gradient(180deg, #16a34a 0%, #15803d 50%, #166534 100%);
            --gradient-silver: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
            --gradient-accent: linear-gradient(45deg, #3b82f6, #60a5fa, #93c5fd);

            /* Glass Effects */
            --glass-bg: rgba(255, 255, 255, 0.9);
            --glass-border: rgba(139, 69, 19, 0.1);
            --glass-shadow: rgba(139, 69, 19, 0.1);

            /* Sports Shadows */
            --shadow-blue: 0 10px 30px rgba(59, 130, 246, 0.3);
            --shadow-academy: 0 8px 32px rgba(139, 69, 19, 0.1);
            --shadow-silver: 0 8px 25px rgba(148, 163, 184, 0.2);
            --shadow-field: 0 15px 35px rgba(22, 101, 52, 0.4);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #ffffff 0%, #fefefe 50%, #ffffff 100%);
            color: var(--text-dark);
            min-height: 100vh;
            direction: rtl;
            position: relative;
            overflow-x: hidden;
        }

        /* Sports Pattern Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(139, 69, 19, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(160, 82, 45, 0.05) 0%, transparent 50%);
            z-index: -2;
        }

        /* Floating Sports Elements */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%238B4513' fill-opacity='0.02'%3E%3Ccircle cx='50' cy='50' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            z-index: -1;
            opacity: 0.3;
            animation: patternFloat 30s ease-in-out infinite;
        }

        @keyframes patternFloat {
            0%, 100% { transform: translateX(0px) translateY(0px); }
            33% { transform: translateX(10px) translateY(-5px); }
            66% { transform: translateX(-5px) translateY(10px); }
        }

        /* Animated Soccer Ball */
        .soccer-ball {
            position: fixed;
            font-size: 2rem;
            color: var(--academy-primary);
            opacity: 0.1;
            animation: ballFloat 15s ease-in-out infinite;
            z-index: -1;
        }

        .soccer-ball:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .soccer-ball:nth-child(2) { top: 20%; right: 15%; animation-delay: 5s; }
        .soccer-ball:nth-child(3) { bottom: 30%; left: 20%; animation-delay: 10s; }

        @keyframes ballFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            position: relative;
            z-index: 1;
        }

        /* Enhanced Sports Header */
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 3rem 2rem;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 25px;
            box-shadow: var(--shadow-academy);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(139, 69, 19, 0.1), transparent);
            animation: headerShine 4s infinite;
        }

        @keyframes headerShine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 900;
            margin-bottom: 1rem;
            background: var(--gradient-academy);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 2;
            animation: titleFloat 6s ease-in-out infinite;
        }

        @keyframes titleFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        .header p {
            font-size: 1.3rem;
            color: var(--text-light);
            position: relative;
            z-index: 2;
            font-weight: 600;
        }

        /* Academy Logo in Header */
        .header::after {
            content: '🏆';
            position: absolute;
            top: 20px;
            right: 30px;
            font-size: 3rem;
            opacity: 0.1;
            animation: trophyGlow 3s ease-in-out infinite;
        }

        @keyframes trophyGlow {
            0%, 100% { opacity: 0.1; transform: scale(1); }
            50% { opacity: 0.2; transform: scale(1.1); }
        }

        /* AI Smart Dashboard */
        .ai-dashboard {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-academy);
            position: relative;
            overflow: hidden;
        }

        .ai-dashboard::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: var(--gradient-academy);
            border-radius: 50%;
            transform: translate(50%, -50%);
            opacity: 0.1;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .card-header h3 {
            color: var(--academy-primary);
            font-size: 1.5rem;
            font-weight: 700;
        }

        .ai-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--success-color);
            font-weight: 600;
        }

        .ai-indicator {
            width: 8px;
            height: 8px;
            background: var(--success-color);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }

        /* Quick Stats Grid */
        .quick-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .stat-card {
            background: white;
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-academy);
        }

        .stat-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 35px rgba(139, 69, 19, 0.2);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            background: var(--gradient-academy);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            animation: iconFloat 3s ease-in-out infinite;
        }

        @keyframes iconFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-3px); }
        }

        .stat-content {
            flex: 1;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 900;
            color: var(--academy-primary);
            line-height: 1;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            color: var(--text-light);
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .stat-trend {
            font-size: 0.8rem;
            font-weight: 600;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            display: inline-block;
        }

        .stat-trend.positive {
            background: rgba(34, 139, 34, 0.1);
            color: var(--success-color);
        }

        .stat-trend.negative {
            background: rgba(178, 34, 34, 0.1);
            color: var(--danger-color);
        }
    </style>
</head>
<body>
    <!-- Floating Sports Elements -->
    <div class="soccer-ball">⚽</div>
    <div class="soccer-ball">🏆</div>
    <div class="soccer-ball">⚽</div>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-futbol"></i> نظام إدارة المباريات والبطولات المتطور</h1>
            <p>أكاديمية 7C للتدريب الرياضي - مدعوم بالذكاء الاصطناعي والتحليل المتقدم</p>
        </div>

        <!-- AI Smart Dashboard -->
        <div class="ai-dashboard">
            <div class="card-header">
                <h3><i class="fas fa-brain"></i> لوحة التحكم الذكية</h3>
                <div class="ai-status">
                    <span class="ai-indicator"></span>
                    <span>الذكاء الاصطناعي نشط</span>
                </div>
            </div>
            
            <!-- Quick Stats Grid -->
            <div class="quick-stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="stat-number" data-target="24">0</h4>
                        <p class="stat-label">المباريات هذا الشهر</p>
                        <span class="stat-trend positive">+15%</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="stat-number" data-target="156">0</h4>
                        <p class="stat-label">اللاعبون النشطون</p>
                        <span class="stat-trend positive">+8%</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="stat-number" data-target="87">0</h4>
                        <p class="stat-label">معدل الفوز %</p>
                        <span class="stat-trend positive">+12%</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="stat-number" data-target="94">0</h4>
                        <p class="stat-label">دقة التوقعات %</p>
                        <span class="stat-trend positive">+5%</span>
                    </div>
                </div>
            </div>

            <h2 style="text-align: center; color: var(--academy-primary); margin: 2rem 0;">
                🎉 تم تطوير النظام بنجاح!
            </h2>
            <p style="text-align: center; font-size: 1.2rem; margin-bottom: 2rem; color: var(--text-light);">
                نظام إدارة المباريات والبطولات المتطور مع الذكاء الاصطناعي جاهز للاستخدام
            </p>
        </div>
    </div>

    <script>
        // Animate Statistics Counters
        function animateCounters() {
            document.querySelectorAll('.stat-number').forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const increment = target / 100;
                let current = 0;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    counter.textContent = Math.floor(current);
                }, 20);
            });
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(animateCounters, 1000);
            
            // Add smooth scrolling
            document.documentElement.style.scrollBehavior = 'smooth';
            
            // Add loading animation
            setTimeout(() => {
                document.body.style.opacity = '1';
                document.body.style.transform = 'translateY(0)';
            }, 100);
        });

        // Add initial loading state
        document.body.style.opacity = '0';
        document.body.style.transform = 'translateY(20px)';
        document.body.style.transition = 'all 0.6s ease';
    </script>
</body>
</html>
