<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام التعرف على الوجه المحسن</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js"></script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ffffff;
        }
        .test-card {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border: 1px solid #475569;
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            transition: all 0.3s ease;
        }
        .test-card:hover {
            border-color: #60a5fa;
            box-shadow: 0 8px 25px rgba(96, 165, 250, 0.15);
        }
        .test-btn {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 6px;
            font-size: 14px;
        }
        .success { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
        .error { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
        .warning { background: rgba(245, 158, 11, 0.2); color: #f59e0b; }
        .info { background: rgba(59, 130, 246, 0.2); color: #3b82f6; }
        .video-container {
            position: relative;
            max-width: 640px;
            margin: 0 auto;
        }
        #testVideo {
            width: 100%;
            height: auto;
            border-radius: 12px;
            border: 2px solid #3b82f6;
        }
        .status-overlay {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
        }
        .log-container {
            max-height: 200px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        .log-entry {
            margin-bottom: 5px;
            font-size: 13px;
        }
        .cdn-status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .cdn-success { background: #22c55e; }
        .cdn-error { background: #ef4444; }
        .cdn-loading { background: #f59e0b; animation: pulse 1s infinite; }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="container mx-auto p-6">
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-blue-400 mb-4">
                <i class="fas fa-user-check text-3xl ml-3"></i>
                اختبار نظام التعرف على الوجه المحسن
            </h1>
            <p class="text-gray-400">اختبار شامل لحل مشكلة CORS ونماذج face-api.js</p>
        </div>

        <!-- حالة CDN -->
        <div class="test-card">
            <h3 class="text-xl font-bold mb-4 text-green-400">
                <i class="fas fa-cloud ml-2"></i>
                حالة مصادر CDN
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="p-4 bg-white/5 rounded-lg">
                    <div class="flex items-center justify-between">
                        <span>jsDelivr CDN</span>
                        <span class="cdn-status cdn-loading" id="jsdelivr-status"></span>
                    </div>
                    <div class="text-sm text-gray-400 mt-1">cdn.jsdelivr.net</div>
                </div>
                <div class="p-4 bg-white/5 rounded-lg">
                    <div class="flex items-center justify-between">
                        <span>unpkg CDN</span>
                        <span class="cdn-status cdn-loading" id="unpkg-status"></span>
                    </div>
                    <div class="text-sm text-gray-400 mt-1">unpkg.com</div>
                </div>
                <div class="p-4 bg-white/5 rounded-lg">
                    <div class="flex items-center justify-between">
                        <span>Skypack CDN</span>
                        <span class="cdn-status cdn-loading" id="skypack-status"></span>
                    </div>
                    <div class="text-sm text-gray-400 mt-1">cdn.skypack.dev</div>
                </div>
            </div>
            <div class="mt-4">
                <button onclick="testAllCDNs()" class="test-btn">
                    <i class="fas fa-sync ml-2"></i>
                    اختبار جميع المصادر
                </button>
            </div>
            <div id="cdnTestResults" class="test-result"></div>
        </div>

        <!-- اختبار تحميل النماذج -->
        <div class="test-card">
            <h3 class="text-xl font-bold mb-4 text-blue-400">
                <i class="fas fa-download ml-2"></i>
                اختبار تحميل النماذج
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="testModelLoading()" class="test-btn">
                    <i class="fas fa-brain ml-2"></i>
                    تحميل النماذج
                </button>
                <button onclick="testFaceAPIAvailability()" class="test-btn">
                    <i class="fas fa-check-circle ml-2"></i>
                    توفر face-api
                </button>
                <button onclick="testModelSizes()" class="test-btn">
                    <i class="fas fa-weight-hanging ml-2"></i>
                    أحجام النماذج
                </button>
            </div>
            <div id="modelTestResults" class="test-result"></div>
        </div>

        <!-- اختبار الكاميرا والتعرف -->
        <div class="test-card">
            <h3 class="text-xl font-bold mb-4 text-purple-400">
                <i class="fas fa-video ml-2"></i>
                اختبار الكاميرا والتعرف
            </h3>
            <div class="video-container mb-4">
                <video id="testVideo" autoplay muted playsinline></video>
                <div class="status-overlay" id="videoStatus">جاري التحضير...</div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <button onclick="testCameraAccess()" class="test-btn bg-green-600">
                    <i class="fas fa-camera ml-2"></i>
                    تشغيل الكاميرا
                </button>
                <button onclick="testFaceDetection()" class="test-btn bg-blue-600">
                    <i class="fas fa-search ml-2"></i>
                    كشف الوجوه
                </button>
                <button onclick="testFaceRecognition()" class="test-btn bg-purple-600">
                    <i class="fas fa-user-check ml-2"></i>
                    التعرف على الوجه
                </button>
                <button onclick="stopAllTests()" class="test-btn bg-red-600">
                    <i class="fas fa-stop ml-2"></i>
                    إيقاف الاختبار
                </button>
            </div>
            <div id="cameraTestResults" class="test-result"></div>
            <div class="log-container" id="testLog"></div>
        </div>

        <!-- اختبار الأداء -->
        <div class="test-card">
            <h3 class="text-xl font-bold mb-4 text-orange-400">
                <i class="fas fa-tachometer-alt ml-2"></i>
                اختبار الأداء
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="testPerformance()" class="test-btn bg-orange-600">
                    <i class="fas fa-stopwatch ml-2"></i>
                    قياس الأداء
                </button>
                <button onclick="testMemoryUsage()" class="test-btn bg-orange-600">
                    <i class="fas fa-memory ml-2"></i>
                    استخدام الذاكرة
                </button>
                <button onclick="testBrowserCompatibility()" class="test-btn bg-orange-600">
                    <i class="fas fa-browser ml-2"></i>
                    توافق المتصفح
                </button>
            </div>
            <div id="performanceTestResults" class="test-result"></div>
        </div>

        <!-- ملخص النتائج -->
        <div class="test-card">
            <h3 class="text-xl font-bold mb-4 text-cyan-400">
                <i class="fas fa-chart-bar ml-2"></i>
                ملخص النتائج
            </h3>
            <div id="testSummary" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-green-900 bg-opacity-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-400" id="passedTests">0</div>
                    <div class="text-sm text-gray-400">اختبارات نجحت</div>
                </div>
                <div class="text-center p-4 bg-red-900 bg-opacity-50 rounded-lg">
                    <div class="text-2xl font-bold text-red-400" id="failedTests">0</div>
                    <div class="text-sm text-gray-400">اختبارات فشلت</div>
                </div>
                <div class="text-center p-4 bg-yellow-900 bg-opacity-50 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-400" id="warningTests">0</div>
                    <div class="text-sm text-gray-400">تحذيرات</div>
                </div>
                <div class="text-center p-4 bg-blue-900 bg-opacity-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-400" id="totalTests">0</div>
                    <div class="text-sm text-gray-400">إجمالي الاختبارات</div>
                </div>
            </div>
        </div>

        <!-- أزرار إضافية -->
        <div class="text-center mt-8">
            <button onclick="runFullTest()" class="test-btn bg-green-600 text-lg px-8 py-4">
                <i class="fas fa-play-circle ml-2"></i>
                تشغيل الاختبار الشامل
            </button>
            <button onclick="clearResults()" class="test-btn bg-gray-600 text-lg px-8 py-4">
                <i class="fas fa-trash ml-2"></i>
                مسح النتائج
            </button>
            <button onclick="exportResults()" class="test-btn bg-blue-600 text-lg px-8 py-4">
                <i class="fas fa-download ml-2"></i>
                تصدير النتائج
            </button>
        </div>
    </div>

    <script>
        // متغيرات الاختبار
        let testResults = {
            passed: 0,
            failed: 0,
            warnings: 0,
            total: 0
        };

        let testVideo = null;
        let isTestRunning = false;

        // دوال مساعدة
        function showResult(containerId, message, type) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<i class="fas fa-${getIcon(type)} ml-2"></i>${message}`;
            container.appendChild(resultDiv);
            
            updateTestStats(type);
            addToLog(message, type);
        }

        function getIcon(type) {
            switch(type) {
                case 'success': return 'check-circle';
                case 'error': return 'times-circle';
                case 'warning': return 'exclamation-triangle';
                case 'info': return 'info-circle';
                default: return 'circle';
            }
        }

        function updateTestStats(type) {
            testResults.total++;
            if (type === 'success') testResults.passed++;
            else if (type === 'error') testResults.failed++;
            else if (type === 'warning') testResults.warnings++;
            
            document.getElementById('passedTests').textContent = testResults.passed;
            document.getElementById('failedTests').textContent = testResults.failed;
            document.getElementById('warningTests').textContent = testResults.warnings;
            document.getElementById('totalTests').textContent = testResults.total;
        }

        function addToLog(message, type = 'info') {
            const logContainer = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry text-${type === 'success' ? 'green' : type === 'error' ? 'red' : type === 'warning' ? 'yellow' : 'blue'}-400`;
            logEntry.innerHTML = `<span class="text-gray-500">[${timestamp}]</span> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateVideoStatus(status) {
            const statusElement = document.getElementById('videoStatus');
            if (statusElement) {
                statusElement.textContent = status;
            }
        }

        // اختبار مصادر CDN
        async function testAllCDNs() {
            const cdns = [
                { name: 'jsdelivr', url: 'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights/', id: 'jsdelivr-status' },
                { name: 'unpkg', url: 'https://unpkg.com/face-api.js@0.22.2/weights/', id: 'unpkg-status' },
                { name: 'skypack', url: 'https://cdn.skypack.dev/face-api.js@0.22.2/weights/', id: 'skypack-status' }
            ];

            showResult('cdnTestResults', 'بدء اختبار مصادر CDN...', 'info');

            for (const cdn of cdns) {
                try {
                    const statusElement = document.getElementById(cdn.id);
                    statusElement.className = 'cdn-status cdn-loading';

                    // اختبار تحميل ملف manifest
                    const response = await fetch(cdn.url + 'tiny_face_detector_model-weights_manifest.json', {
                        method: 'HEAD',
                        mode: 'cors'
                    });

                    if (response.ok) {
                        statusElement.className = 'cdn-status cdn-success';
                        showResult('cdnTestResults', `✅ ${cdn.name} CDN متاح`, 'success');
                    } else {
                        statusElement.className = 'cdn-status cdn-error';
                        showResult('cdnTestResults', `❌ ${cdn.name} CDN غير متاح (${response.status})`, 'error');
                    }
                } catch (error) {
                    const statusElement = document.getElementById(cdn.id);
                    statusElement.className = 'cdn-status cdn-error';
                    showResult('cdnTestResults', `❌ ${cdn.name} CDN خطأ: ${error.message}`, 'error');
                }
            }
        }

        // اختبار توفر face-api
        function testFaceAPIAvailability() {
            if (typeof faceapi !== 'undefined') {
                showResult('modelTestResults', '✅ مكتبة face-api.js متاحة', 'success');
                showResult('modelTestResults', `📦 إصدار face-api: ${faceapi.version || 'غير محدد'}`, 'info');
            } else {
                showResult('modelTestResults', '❌ مكتبة face-api.js غير متاحة', 'error');
            }
        }

        // اختبار تحميل النماذج
        async function testModelLoading() {
            if (typeof faceapi === 'undefined') {
                showResult('modelTestResults', '❌ face-api غير متاح', 'error');
                return;
            }

            showResult('modelTestResults', '🔄 بدء تحميل النماذج...', 'info');

            try {
                const modelPath = 'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights/';
                
                const startTime = Date.now();
                
                await Promise.all([
                    faceapi.nets.tinyFaceDetector.loadFromUri(modelPath),
                    faceapi.nets.faceLandmark68Net.loadFromUri(modelPath),
                    faceapi.nets.faceRecognitionNet.loadFromUri(modelPath)
                ]);

                const loadTime = Date.now() - startTime;
                showResult('modelTestResults', `✅ تم تحميل جميع النماذج في ${loadTime}ms`, 'success');
                
            } catch (error) {
                showResult('modelTestResults', `❌ فشل تحميل النماذج: ${error.message}`, 'error');
            }
        }

        // اختبار الكاميرا
        async function testCameraAccess() {
            try {
                updateVideoStatus('جاري تشغيل الكاميرا...');
                
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 640, height: 480 }
                });

                testVideo = document.getElementById('testVideo');
                testVideo.srcObject = stream;
                
                updateVideoStatus('الكاميرا نشطة');
                showResult('cameraTestResults', '✅ تم تشغيل الكاميرا بنجاح', 'success');
                
            } catch (error) {
                updateVideoStatus('خطأ في الكاميرا');
                showResult('cameraTestResults', `❌ فشل تشغيل الكاميرا: ${error.message}`, 'error');
            }
        }

        // اختبار كشف الوجوه
        async function testFaceDetection() {
            if (!testVideo || !testVideo.srcObject) {
                showResult('cameraTestResults', '❌ يجب تشغيل الكاميرا أولاً', 'error');
                return;
            }

            if (typeof faceapi === 'undefined') {
                showResult('cameraTestResults', '❌ face-api غير متاح', 'error');
                return;
            }

            try {
                updateVideoStatus('جاري كشف الوجوه...');

                const detections = await faceapi.detectAllFaces(testVideo, new faceapi.TinyFaceDetectorOptions());

                if (detections.length > 0) {
                    updateVideoStatus(`تم كشف ${detections.length} وجه`);
                    showResult('cameraTestResults', `✅ تم كشف ${detections.length} وجه`, 'success');
                } else {
                    updateVideoStatus('لم يتم كشف وجوه');
                    showResult('cameraTestResults', '⚠️ لم يتم كشف أي وجوه', 'warning');
                }

            } catch (error) {
                updateVideoStatus('خطأ في الكشف');
                showResult('cameraTestResults', `❌ خطأ في كشف الوجوه: ${error.message}`, 'error');
            }
        }

        // اختبار التعرف على الوجه الكامل
        async function testFaceRecognition() {
            if (!testVideo || !testVideo.srcObject) {
                showResult('cameraTestResults', '❌ يجب تشغيل الكاميرا أولاً', 'error');
                return;
            }

            if (typeof faceapi === 'undefined') {
                showResult('cameraTestResults', '❌ face-api غير متاح', 'error');
                return;
            }

            try {
                updateVideoStatus('جاري التعرف على الوجه...');

                const detections = await faceapi.detectAllFaces(testVideo, new faceapi.TinyFaceDetectorOptions())
                    .withFaceLandmarks()
                    .withFaceDescriptors();

                if (detections.length > 0) {
                    updateVideoStatus(`تم التعرف على ${detections.length} وجه`);
                    showResult('cameraTestResults', `✅ تم التعرف على ${detections.length} وجه مع الخصائص`, 'success');

                    // عرض تفاصيل إضافية
                    const detection = detections[0];
                    if (detection.landmarks) {
                        showResult('cameraTestResults', `📍 تم كشف ${detection.landmarks.positions.length} نقطة مرجعية`, 'info');
                    }
                    if (detection.descriptor) {
                        showResult('cameraTestResults', `🧬 تم إنشاء بصمة الوجه (${detection.descriptor.length} خاصية)`, 'info');
                    }
                } else {
                    updateVideoStatus('لم يتم التعرف على وجوه');
                    showResult('cameraTestResults', '⚠️ لم يتم التعرف على أي وجوه', 'warning');
                }

            } catch (error) {
                updateVideoStatus('خطأ في التعرف');
                showResult('cameraTestResults', `❌ خطأ في التعرف على الوجه: ${error.message}`, 'error');
            }
        }

        // اختبار أحجام النماذج
        async function testModelSizes() {
            showResult('modelTestResults', '🔄 جاري فحص أحجام النماذج...', 'info');

            const models = [
                'tiny_face_detector_model-weights_manifest.json',
                'face_landmark_68_model-weights_manifest.json',
                'face_recognition_model-weights_manifest.json'
            ];

            const modelPath = 'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights/';

            for (const model of models) {
                try {
                    const response = await fetch(modelPath + model);
                    if (response.ok) {
                        const data = await response.json();
                        const totalSize = data.weightsManifest.reduce((sum, item) => {
                            return sum + item.paths.reduce((pathSum, path) => pathSum + (item.weights || 0), 0);
                        }, 0);

                        showResult('modelTestResults', `📦 ${model}: ${(totalSize / 1024 / 1024).toFixed(2)} MB`, 'info');
                    } else {
                        showResult('modelTestResults', `❌ لا يمكن الوصول إلى ${model}`, 'error');
                    }
                } catch (error) {
                    showResult('modelTestResults', `❌ خطأ في فحص ${model}: ${error.message}`, 'error');
                }
            }
        }

        // اختبار الأداء
        async function testPerformance() {
            if (!testVideo || !testVideo.srcObject) {
                showResult('performanceTestResults', '❌ يجب تشغيل الكاميرا أولاً', 'error');
                return;
            }

            if (typeof faceapi === 'undefined') {
                showResult('performanceTestResults', '❌ face-api غير متاح', 'error');
                return;
            }

            showResult('performanceTestResults', '🔄 بدء اختبار الأداء...', 'info');

            const iterations = 10;
            const times = [];

            for (let i = 0; i < iterations; i++) {
                const startTime = performance.now();

                try {
                    await faceapi.detectAllFaces(testVideo, new faceapi.TinyFaceDetectorOptions());
                    const endTime = performance.now();
                    times.push(endTime - startTime);
                } catch (error) {
                    showResult('performanceTestResults', `❌ خطأ في التكرار ${i + 1}: ${error.message}`, 'error');
                    return;
                }
            }

            const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
            const minTime = Math.min(...times);
            const maxTime = Math.max(...times);

            showResult('performanceTestResults', `⏱️ متوسط وقت الكشف: ${avgTime.toFixed(2)}ms`, 'success');
            showResult('performanceTestResults', `🚀 أسرع كشف: ${minTime.toFixed(2)}ms`, 'info');
            showResult('performanceTestResults', `🐌 أبطأ كشف: ${maxTime.toFixed(2)}ms`, 'info');
            showResult('performanceTestResults', `📊 معدل الإطارات المتوقع: ${(1000 / avgTime).toFixed(1)} FPS`, 'info');
        }

        // اختبار استخدام الذاكرة
        function testMemoryUsage() {
            if ('memory' in performance) {
                const memory = performance.memory;
                showResult('performanceTestResults', `💾 الذاكرة المستخدمة: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`, 'info');
                showResult('performanceTestResults', `📈 إجمالي الذاكرة: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`, 'info');
                showResult('performanceTestResults', `🔒 حد الذاكرة: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`, 'info');
            } else {
                showResult('performanceTestResults', '⚠️ معلومات الذاكرة غير متاحة في هذا المتصفح', 'warning');
            }
        }

        // اختبار توافق المتصفح
        function testBrowserCompatibility() {
            const features = {
                'WebRTC': !!navigator.mediaDevices?.getUserMedia,
                'Canvas 2D': !!document.createElement('canvas').getContext('2d'),
                'WebGL': !!document.createElement('canvas').getContext('webgl'),
                'Web Workers': typeof Worker !== 'undefined',
                'IndexedDB': 'indexedDB' in window,
                'Local Storage': 'localStorage' in window,
                'Session Storage': 'sessionStorage' in window,
                'Fetch API': 'fetch' in window,
                'Promise': 'Promise' in window,
                'ES6 Classes': (function() { try { eval('class Test {}'); return true; } catch(e) { return false; } })()
            };

            Object.entries(features).forEach(([feature, supported]) => {
                const type = supported ? 'success' : 'error';
                const icon = supported ? '✅' : '❌';
                showResult('performanceTestResults', `${icon} ${feature}: ${supported ? 'مدعوم' : 'غير مدعوم'}`, type);
            });

            showResult('performanceTestResults', `🌐 المتصفح: ${navigator.userAgent.split(' ').pop()}`, 'info');
        }

        // إيقاف جميع الاختبارات
        function stopAllTests() {
            if (testVideo && testVideo.srcObject) {
                const tracks = testVideo.srcObject.getTracks();
                tracks.forEach(track => track.stop());
                testVideo.srcObject = null;
            }
            
            updateVideoStatus('تم الإيقاف');
            showResult('cameraTestResults', '⏹️ تم إيقاف جميع الاختبارات', 'info');
            isTestRunning = false;
        }

        // تشغيل الاختبار الشامل
        async function runFullTest() {
            if (isTestRunning) return;
            
            isTestRunning = true;
            clearResults();
            
            addToLog('🚀 بدء الاختبار الشامل للنظام');
            
            // اختبار CDN
            await testAllCDNs();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // اختبار face-api
            testFaceAPIAvailability();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // اختبار تحميل النماذج
            await testModelLoading();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // اختبار الكاميرا
            await testCameraAccess();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // اختبار كشف الوجوه
            await testFaceDetection();
            
            addToLog('✅ اكتمل الاختبار الشامل');
            isTestRunning = false;
            
            // إظهار ملخص
            Swal.fire({
                icon: 'success',
                title: 'اكتمل الاختبار الشامل',
                text: `نجح ${testResults.passed} من ${testResults.total} اختبار`,
                background: '#1a1a1a',
                color: '#e2e8f0'
            });
        }

        // مسح النتائج
        function clearResults() {
            const resultContainers = ['cdnTestResults', 'modelTestResults', 'cameraTestResults', 'performanceTestResults'];
            resultContainers.forEach(id => {
                const container = document.getElementById(id);
                if (container) container.innerHTML = '';
            });
            
            const logContainer = document.getElementById('testLog');
            if (logContainer) logContainer.innerHTML = '';
            
            testResults = { passed: 0, failed: 0, warnings: 0, total: 0 };
            updateTestStats('');
        }

        // تصدير النتائج
        function exportResults() {
            const results = {
                timestamp: new Date().toISOString(),
                summary: testResults,
                userAgent: navigator.userAgent,
                faceApiAvailable: typeof faceapi !== 'undefined',
                webRTCSupported: !!navigator.mediaDevices?.getUserMedia
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `face-recognition-test-${Date.now()}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addToLog('✅ تم تحميل صفحة اختبار التعرف على الوجه');
            testFaceAPIAvailability();
        });
    </script>
</body>
</html>
