<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معرض القوالب - أكاديمية 7C</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="advanced-templates.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D2691E;
            --background-dark: #1a1a1a;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--background-dark);
            color: #ffffff;
            overflow-x: hidden;
        }

        .showcase-header {
            background: linear-gradient(135deg, var(--background-dark) 0%, #2d2d2d 100%);
            border-bottom: 2px solid var(--primary-color);
            padding: 2rem;
            text-align: center;
        }

        .template-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .template-preview {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .template-preview:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--primary-color);
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .template-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            border-radius: 8px;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: var(--primary-color);
        }

        .template-title {
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #ffffff;
        }

        .template-description {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }

        .template-features {
            list-style: none;
            padding: 0;
            margin-bottom: 1rem;
        }

        .template-features li {
            padding: 0.25rem 0;
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.6);
        }

        .template-features li::before {
            content: '✓';
            color: var(--primary-color);
            margin-left: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            width: 100%;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
        }

        .demo-section {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .demo-container {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 2rem;
            margin-top: 2rem;
        }

        .color-themes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .color-theme {
            padding: 1rem;
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .color-theme:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .color-theme.active {
            border-color: white;
        }

        .color-preview {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 0 auto 0.5rem;
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="showcase-header">
        <h1 class="text-4xl font-bold mb-4">معرض قوالب ملف اللاعب</h1>
        <p class="text-xl text-gray-300 mb-6">اختر القالب المناسب لعرض ملفك الشخصي</p>
        <div class="flex justify-center gap-4">
            <a href="player-profile.html" class="btn btn-primary">
                <i class="fas fa-arrow-right"></i>
                العودة للملف الرئيسي
            </a>
        </div>
    </header>

    <!-- Templates Grid -->
    <div class="template-grid fade-in">
        <!-- Default Template -->
        <div class="template-preview" onclick="previewTemplate('default')">
            <div class="template-image">
                <i class="fas fa-th-large"></i>
            </div>
            <h3 class="template-title">القالب الافتراضي</h3>
            <p class="template-description">التصميم الأساسي مع التبويبات التفاعلية والعرض المنظم للمعلومات</p>
            <ul class="template-features">
                <li>تبويبات تفاعلية</li>
                <li>عرض شامل للمعلومات</li>
                <li>تصميم متجاوب</li>
                <li>سهولة التنقل</li>
            </ul>
            <button class="btn btn-primary">
                <i class="fas fa-eye"></i>
                معاينة القالب
            </button>
        </div>

        <!-- Cards Template -->
        <div class="template-preview" onclick="previewTemplate('cards')">
            <div class="template-image">
                <i class="fas fa-th"></i>
            </div>
            <h3 class="template-title">قالب البطاقات</h3>
            <p class="template-description">عرض المعلومات في بطاقات مرتبة عمودياً لسهولة المراجعة السريعة</p>
            <ul class="template-features">
                <li>بطاقات منظمة</li>
                <li>عرض مضغوط</li>
                <li>مراجعة سريعة</li>
                <li>تأثيرات بصرية</li>
            </ul>
            <button class="btn btn-primary">
                <i class="fas fa-eye"></i>
                معاينة القالب
            </button>
        </div>

        <!-- Compact Template -->
        <div class="template-preview" onclick="previewTemplate('compact')">
            <div class="template-image">
                <i class="fas fa-compress-alt"></i>
            </div>
            <h3 class="template-title">القالب المضغوط</h3>
            <p class="template-description">عرض مكثف للمعلومات الأساسية في مساحة محدودة</p>
            <ul class="template-features">
                <li>عرض مكثف</li>
                <li>توفير المساحة</li>
                <li>معلومات أساسية</li>
                <li>تحميل سريع</li>
            </ul>
            <button class="btn btn-primary">
                <i class="fas fa-eye"></i>
                معاينة القالب
            </button>
        </div>

        <!-- Professional Template -->
        <div class="template-preview" onclick="previewTemplate('professional')">
            <div class="template-image">
                <i class="fas fa-briefcase"></i>
            </div>
            <h3 class="template-title">القالب الاحترافي</h3>
            <p class="template-description">تصميم رسمي مناسب للطباعة والعرض الرسمي</p>
            <ul class="template-features">
                <li>تصميم رسمي</li>
                <li>قابل للطباعة</li>
                <li>ألوان محايدة</li>
                <li>تنسيق احترافي</li>
            </ul>
            <button class="btn btn-primary">
                <i class="fas fa-eye"></i>
                معاينة القالب
            </button>
        </div>

        <!-- FIFA Template -->
        <div class="template-preview" onclick="previewTemplate('fifa')">
            <div class="template-image">
                <i class="fas fa-futbol"></i>
            </div>
            <h3 class="template-title">قالب FIFA</h3>
            <p class="template-description">تصميم يحاكي بطاقات FIFA مع إحصائيات مفصلة وتأثيرات ثلاثية الأبعاد</p>
            <ul class="template-features">
                <li>تصميم FIFA</li>
                <li>تأثيرات ثلاثية الأبعاد</li>
                <li>إحصائيات مفصلة</li>
                <li>تفاعل متقدم</li>
            </ul>
            <button class="btn btn-primary">
                <i class="fas fa-eye"></i>
                معاينة القالب
            </button>
        </div>
    </div>

    <!-- Color Themes Section -->
    <div class="demo-section fade-in">
        <div class="demo-container">
            <h2 class="text-2xl font-bold mb-4 text-center">مجموعات الألوان المتاحة</h2>
            <p class="text-center text-gray-300 mb-6">اختر مجموعة الألوان المفضلة لديك</p>
            
            <div class="color-themes">
                <div class="color-theme active" data-theme="default">
                    <div class="color-preview" style="background: linear-gradient(135deg, #8B4513, #D2691E);"></div>
                    <span>الافتراضي</span>
                </div>
                <div class="color-theme" data-theme="blue">
                    <div class="color-preview" style="background: linear-gradient(135deg, #3b82f6, #60a5fa);"></div>
                    <span>أزرق</span>
                </div>
                <div class="color-theme" data-theme="green">
                    <div class="color-preview" style="background: linear-gradient(135deg, #10b981, #34d399);"></div>
                    <span>أخضر</span>
                </div>
                <div class="color-theme" data-theme="purple">
                    <div class="color-preview" style="background: linear-gradient(135deg, #8b5cf6, #a78bfa);"></div>
                    <span>بنفسجي</span>
                </div>
                <div class="color-theme" data-theme="red">
                    <div class="color-preview" style="background: linear-gradient(135deg, #ef4444, #f87171);"></div>
                    <span>أحمر</span>
                </div>
                <div class="color-theme" data-theme="orange">
                    <div class="color-preview" style="background: linear-gradient(135deg, #f59e0b, #fbbf24);"></div>
                    <span>برتقالي</span>
                </div>
                <div class="color-theme" data-theme="pink">
                    <div class="color-preview" style="background: linear-gradient(135deg, #ec4899, #f472b6);"></div>
                    <span>وردي</span>
                </div>
                <div class="color-theme" data-theme="indigo">
                    <div class="color-preview" style="background: linear-gradient(135deg, #6366f1, #818cf8);"></div>
                    <span>نيلي</span>
                </div>
                <div class="color-theme" data-theme="teal">
                    <div class="color-preview" style="background: linear-gradient(135deg, #14b8a6, #2dd4bf);"></div>
                    <span>تركوازي</span>
                </div>
                <div class="color-theme" data-theme="gray">
                    <div class="color-preview" style="background: linear-gradient(135deg, #6b7280, #9ca3af);"></div>
                    <span>رمادي</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="demo-section fade-in">
        <div class="demo-container">
            <h2 class="text-2xl font-bold mb-6 text-center">الميزات المتقدمة</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="text-center">
                    <i class="fas fa-edit text-4xl text-blue-400 mb-4"></i>
                    <h3 class="text-lg font-bold mb-2">تحرير فوري</h3>
                    <p class="text-sm text-gray-300">إمكانية تحرير جميع المعلومات مع حفظ تلقائي</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-download text-4xl text-green-400 mb-4"></i>
                    <h3 class="text-lg font-bold mb-2">تصدير شامل</h3>
                    <p class="text-sm text-gray-300">تصدير بصيغ PDF, Word, Excel, وصورة</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-share-alt text-4xl text-purple-400 mb-4"></i>
                    <h3 class="text-lg font-bold mb-2">مشاركة ذكية</h3>
                    <p class="text-sm text-gray-300">نشر الملف مع إعدادات خصوصية متقدمة</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-chart-bar text-4xl text-orange-400 mb-4"></i>
                    <h3 class="text-lg font-bold mb-2">إحصائيات مفصلة</h3>
                    <p class="text-sm text-gray-300">تتبع المشاهدات والزيارات مع تحليل البيانات</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-palette text-4xl text-pink-400 mb-4"></i>
                    <h3 class="text-lg font-bold mb-2">تخصيص الألوان</h3>
                    <p class="text-sm text-gray-300">10 مجموعات لونية + ألوان مخصصة</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-mobile-alt text-4xl text-teal-400 mb-4"></i>
                    <h3 class="text-lg font-bold mb-2">تجاوب كامل</h3>
                    <p class="text-sm text-gray-300">يعمل بشكل مثالي على جميع الأجهزة</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Preview template function
        function previewTemplate(templateName) {
            // Save selected template
            localStorage.setItem('preview_template', templateName);
            
            // Open player profile with selected template
            const url = `player-profile.html?template=${templateName}`;
            window.open(url, '_blank');
        }

        // Color theme selection
        document.querySelectorAll('.color-theme').forEach(theme => {
            theme.addEventListener('click', function() {
                // Remove active class from all themes
                document.querySelectorAll('.color-theme').forEach(t => t.classList.remove('active'));
                
                // Add active class to clicked theme
                this.classList.add('active');
                
                // Apply theme to body
                const themeName = this.dataset.theme;
                applyColorTheme(themeName);
                
                // Save preference
                localStorage.setItem('preview_color_theme', themeName);
            });
        });

        function applyColorTheme(theme) {
            const body = document.body;
            
            // Remove existing theme classes
            body.classList.remove('theme-default', 'theme-blue', 'theme-green', 'theme-purple', 'theme-red', 'theme-orange', 'theme-pink', 'theme-indigo', 'theme-teal', 'theme-gray');
            
            // Add new theme class
            if (theme !== 'default') {
                body.classList.add(`theme-${theme}`);
            }
        }

        // Load saved preferences
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('preview_color_theme');
            if (savedTheme) {
                document.querySelectorAll('.color-theme').forEach(theme => {
                    theme.classList.remove('active');
                    if (theme.dataset.theme === savedTheme) {
                        theme.classList.add('active');
                    }
                });
                applyColorTheme(savedTheme);
            }
        });

        console.log('🎨 تم تحميل معرض القوالب بنجاح');
    </script>
</body>
</html>
