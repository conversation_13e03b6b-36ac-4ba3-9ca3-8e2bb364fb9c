document.getElementById('installForm').onsubmit = async function(e) {
  e.preventDefault();
  const adminName = document.getElementById('adminName').value;
  const adminPass = document.getElementById('adminPass').value;
  const adminEmail = document.getElementById('adminEmail').value;
  const msg = document.getElementById('msg');
  msg.textContent = '';

  // حفظ الإعدادات في ملف (محاكاة - يجب ربطها بـ backend فعلي في الإنتاج)
  try {
    const res = await fetch('/api/install', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ adminName, adminPass, adminEmail })
    });
    const data = await res.json();
    if (data.success) {
      msg.innerHTML = '<div class="success">تم التثبيت بنجاح! يمكنك الآن استخدام المساعد الذكي.</div>';
      document.getElementById('installForm').reset();
    } else {
      msg.innerHTML = '<div class="error">' + (data.error || 'حدث خطأ أثناء التثبيت') + '</div>';
    }
  } catch (err) {
    msg.innerHTML = '<div class="error">تعذر الاتصال بالخادم. تأكد أن backend يعمل.</div>';
  }
};
