/* Advanced Player Profile Templates */

/* ==================== FIFA Template Styles ==================== */
.template-fifa {
    background: linear-gradient(135deg, #0f1419 0%, #1a1a1a 50%, #2d2d2d 100%);
    position: relative;
    overflow: hidden;
}

.template-fifa::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(139, 69, 19, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(210, 105, 30, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.fifa-ultimate-card {
    width: 400px;
    height: 600px;
    background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
    border-radius: 20px;
    position: relative;
    margin: 2rem auto;
    overflow: hidden;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 3px solid var(--primary-color);
    transform-style: preserve-3d;
    transition: all 0.3s ease;
}

.fifa-ultimate-card:hover {
    transform: rotateY(5deg) rotateX(5deg);
    box-shadow: 
        0 30px 60px rgba(0, 0, 0, 0.7),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.fifa-card-header {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    z-index: 2;
}

.fifa-rating {
    font-size: 4rem;
    font-weight: 900;
    color: var(--primary-color);
    text-shadow: 
        2px 2px 4px rgba(0, 0, 0, 0.8),
        0 0 10px rgba(139, 69, 19, 0.5);
    font-family: 'Arial Black', sans-serif;
}

.fifa-position {
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
    margin-top: 1rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.fifa-flag {
    font-size: 2rem;
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.5));
}

.fifa-player-image {
    position: absolute;
    top: 120px;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 200px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid var(--primary-color);
    box-shadow: 
        0 0 20px rgba(139, 69, 19, 0.5),
        inset 0 0 20px rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.fifa-player-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: contrast(1.1) brightness(1.05);
}

.fifa-player-info {
    position: absolute;
    top: 340px;
    left: 0;
    right: 0;
    text-align: center;
    z-index: 2;
}

.fifa-player-name {
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
    margin-bottom: 0.5rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.fifa-player-surname {
    font-size: 2rem;
    font-weight: 900;
    color: var(--primary-color);
    text-transform: uppercase;
    text-shadow: 
        2px 2px 4px rgba(0, 0, 0, 0.8),
        0 0 10px rgba(139, 69, 19, 0.3);
    font-family: 'Arial Black', sans-serif;
}

.fifa-stats {
    position: absolute;
    bottom: 80px;
    left: 20px;
    right: 20px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    z-index: 2;
}

.fifa-stat {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 0.5rem;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.fifa-stat:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.fifa-stat .stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.fifa-stat .stat-name {
    display: block;
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.fifa-club {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 2;
}

.fifa-club img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
}

.fifa-club span {
    color: white;
    font-weight: bold;
    font-size: 0.875rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* ==================== Professional Template Styles ==================== */
.template-professional {
    background: #ffffff;
    color: #333333;
    font-family: 'Times New Roman', serif;
}

.template-professional .header {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
}

.template-professional .card {
    background: white;
    border: 2px solid #e0e0e0;
    color: #333;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.template-professional .profile-header {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
}

.template-professional .nav-tabs {
    border-bottom: 2px solid #e0e0e0;
}

.template-professional .nav-tab {
    background: #f8f9fa;
    color: #333;
    border: 1px solid #e0e0e0;
}

.template-professional .nav-tab.active {
    background: #2c3e50;
    color: white;
}

.template-professional .btn-primary {
    background: #2c3e50;
    border-color: #2c3e50;
}

.template-professional .stat-value {
    color: #2c3e50;
}

/* ==================== Compact Template Styles ==================== */
.template-compact {
    font-size: 0.875rem;
}

.template-compact .container {
    max-width: 1000px;
}

.template-compact .card {
    padding: 1rem;
    margin-bottom: 1rem;
}

.template-compact .profile-header {
    padding: 1rem;
    margin-bottom: 1rem;
}

.template-compact .nav-tabs {
    display: none;
}

.template-compact .tab-content {
    display: block !important;
}

.template-compact #tabContents {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.template-compact .stat-value {
    font-size: 1.5rem;
}

.template-compact h1 {
    font-size: 1.5rem;
}

.template-compact h2 {
    font-size: 1.25rem;
}

.template-compact h3 {
    font-size: 1.125rem;
}

/* ==================== Cards Template Styles ==================== */
.template-cards .nav-tabs {
    display: none;
}

.template-cards .tab-content {
    display: block !important;
}

.template-cards #tabContents {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.template-cards .card {
    height: fit-content;
    transition: all 0.3s ease;
}

.template-cards .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* ==================== Animation Effects ==================== */
@keyframes cardFlip {
    0% { transform: rotateY(0deg); }
    50% { transform: rotateY(180deg); }
    100% { transform: rotateY(0deg); }
}

@keyframes glowPulse {
    0%, 100% { box-shadow: 0 0 5px rgba(139, 69, 19, 0.5); }
    50% { box-shadow: 0 0 20px rgba(139, 69, 19, 0.8); }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* ==================== Interactive Elements ==================== */
.interactive-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.interactive-card:hover {
    animation: glowPulse 2s infinite;
}

.flip-card {
    perspective: 1000px;
}

.flip-card:hover .fifa-ultimate-card {
    animation: cardFlip 1s ease-in-out;
}

/* ==================== Responsive Enhancements ==================== */
@media (max-width: 768px) {
    .fifa-ultimate-card {
        width: 300px;
        height: 450px;
        margin: 1rem auto;
    }
    
    .fifa-rating {
        font-size: 3rem;
    }
    
    .fifa-player-image {
        width: 150px;
        height: 150px;
        top: 100px;
    }
    
    .fifa-player-info {
        top: 270px;
    }
    
    .fifa-stats {
        bottom: 60px;
        grid-template-columns: repeat(2, 1fr);
    }
    
    .template-cards #tabContents {
        grid-template-columns: 1fr;
    }
    
    .template-compact #tabContents {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .fifa-ultimate-card {
        width: 250px;
        height: 380px;
    }
    
    .fifa-rating {
        font-size: 2.5rem;
    }
    
    .fifa-player-image {
        width: 120px;
        height: 120px;
        top: 80px;
    }
    
    .fifa-player-info {
        top: 220px;
    }
    
    .fifa-player-name {
        font-size: 1.25rem;
    }
    
    .fifa-player-surname {
        font-size: 1.5rem;
    }
}

/* ==================== Print Styles ==================== */
@media print {
    .template-professional {
        background: white !important;
        color: black !important;
    }
    
    .template-professional .card {
        border: 1px solid #ccc !important;
        box-shadow: none !important;
        break-inside: avoid;
    }
    
    .header-actions,
    .nav-tabs,
    .social-share {
        display: none !important;
    }
    
    .fifa-ultimate-card {
        break-inside: avoid;
    }
}

/* ==================== Dark Mode Enhancements ==================== */
@media (prefers-color-scheme: dark) {
    .template-professional {
        background: #1a1a1a;
        color: #ffffff;
    }
    
    .template-professional .card {
        background: #2d2d2d;
        border-color: #444;
        color: #ffffff;
    }
}
