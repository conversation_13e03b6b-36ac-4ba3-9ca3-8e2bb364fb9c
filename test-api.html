<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API المدربين</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #e2e8f0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #2d3748;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .result {
            background: #374151;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #3b82f6;
        }
        .success {
            border-left-color: #10b981;
            background: #064e3b;
        }
        .error {
            border-left-color: #ef4444;
            background: #7f1d1d;
        }
        pre {
            background: #1f2937;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار API نظام إدارة المدربين</h1>
        
        <div>
            <button class="test-button" onclick="testBasicConnection()">اختبار الاتصال الأساسي</button>
            <button class="test-button" onclick="testGetCoaches()">جلب المدربين</button>
            <button class="test-button" onclick="testDatabaseConnection()">اختبار قاعدة البيانات</button>
            <button class="test-button" onclick="checkTables()">فحص الجداول</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const API_BASE_URL = `${window.location.protocol}//${window.location.host}`;
        const API_URL = `${API_BASE_URL}/api/coaches_management.php`;

        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(content, null, 2)}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        async function testBasicConnection() {
            try {
                console.log('Testing basic connection...');
                const response = await fetch(`${API_URL}?test=1`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult('✅ اختبار الاتصال الأساسي', data, 'success');
                } else {
                    addResult('❌ فشل الاتصال الأساسي', data, 'error');
                }
            } catch (error) {
                addResult('❌ خطأ في الاتصال', {
                    error: error.message,
                    url: API_URL
                }, 'error');
            }
        }

        async function testGetCoaches() {
            try {
                console.log('Testing get coaches...');
                const response = await fetch(`${API_URL}?page=1&limit=5`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult('✅ جلب المدربين', data, 'success');
                } else {
                    addResult('❌ فشل جلب المدربين', data, 'error');
                }
            } catch (error) {
                addResult('❌ خطأ في جلب المدربين', {
                    error: error.message,
                    url: API_URL
                }, 'error');
            }
        }

        async function testDatabaseConnection() {
            try {
                console.log('Testing database connection...');
                const response = await fetch(`${API_URL}?action=test_db`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult('✅ اختبار قاعدة البيانات', data, 'success');
                } else {
                    addResult('❌ فشل اختبار قاعدة البيانات', data, 'error');
                }
            } catch (error) {
                addResult('❌ خطأ في اختبار قاعدة البيانات', {
                    error: error.message,
                    url: API_URL
                }, 'error');
            }
        }

        async function checkTables() {
            try {
                console.log('Checking tables...');
                const response = await fetch(`${API_URL}?action=check_tables`);
                const data = await response.json();
                
                if (response.ok) {
                    addResult('📊 فحص الجداول', data, data.success ? 'success' : 'error');
                } else {
                    addResult('❌ فشل فحص الجداول', data, 'error');
                }
            } catch (error) {
                addResult('❌ خطأ في فحص الجداول', {
                    error: error.message,
                    url: API_URL
                }, 'error');
            }
        }

        // تشغيل اختبار أساسي عند تحميل الصفحة
        window.addEventListener('load', () => {
            addResult('🔗 معلومات الاتصال', {
                protocol: window.location.protocol,
                host: window.location.host,
                api_url: API_URL
            });
        });
    </script>
</body>
</html>
