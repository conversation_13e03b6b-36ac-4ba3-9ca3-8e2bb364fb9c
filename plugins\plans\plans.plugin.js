// ==================== Plugin: إدارة خطط الاشتراك ====================
export const PlansManagementPlugin = {
    id: 'plans',
    name: 'إدارة خطط الاشتراك',
    init() {
        if (!document.getElementById('plans-plugin-style')) {
            const style = document.createElement('style');
            style.id = 'plans-plugin-style';
            style.innerHTML = `
                .plugin-section {background: #181f2a; color: #fff; border-radius: 18px; box-shadow: 0 2px 16px #0002; max-width: 900px; margin: 32px auto 0; padding: 32px 24px 24px; font-family: 'Cairo',sans-serif;}
                .plugin-header {display: flex; align-items: center; justify-content: space-between; margin-bottom: 18px;}
                .plugin-header h2 {font-size: 1.5rem; font-weight: bold; margin: 0;}
                .plugin-btn {background: linear-gradient(90deg,#4f8cff,#6f6bff); color: #fff; border: none; border-radius: 8px; padding: 10px 22px; font-size: 1rem; font-weight: 600; cursor: pointer; transition: background 0.2s;}
                .plugin-btn:hover {background: linear-gradient(90deg,#6f6bff,#4f8cff);}
                .plugin-table {width: 100%; border-collapse: collapse; background: #232b3b; border-radius: 12px; overflow: hidden; margin-top: 10px;}
                .plugin-table th, .plugin-table td {padding: 12px 8px; text-align: center;}
                .plugin-table th {background: #232b3b; color: #8bb4ff; font-weight: 700; border-bottom: 2px solid #2d3950;}
                .plugin-table tr {transition: background 0.2s;}
                .plugin-table tr:hover {background: #232b3bcc;}
                .plugin-table td {border-bottom: 1px solid #232b3b;}
                .plugin-action-btn {background: #2d3950; color: #8bb4ff; border: none; border-radius: 6px; padding: 6px 16px; margin: 0 2px; font-size: 0.95rem; cursor: pointer; transition: background 0.2s;}
                .plugin-action-btn:hover {background: #4f8cff; color: #fff;}
                .plugin-modal-bg {position: fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.45); z-index: 9999; display: flex; align-items: center; justify-content: center;}
                .plugin-modal {background: #232b3b; border-radius: 16px; padding: 32px 24px 24px; min-width: 320px; max-width: 95vw; box-shadow: 0 4px 32px #0005;}
                .plugin-modal h3 {margin-top:0; color:#8bb4ff; font-size:1.2rem; margin-bottom:18px;}
                .plugin-modal label {display:block; margin-bottom:6px; color:#b6cfff; font-size:1rem;}
                .plugin-modal input {width:100%; padding:8px 10px; border-radius:6px; border:none; background:#181f2a; color:#fff; margin-bottom:16px; font-size:1rem;}
                .plugin-modal .modal-actions {display:flex; justify-content:flex-end; gap:10px;}
                .plugin-modal .plugin-btn {padding:8px 18px; font-size:1rem;}
            `;
            document.head.appendChild(style);
        }
        if (!document.getElementById('plans-plugin-container')) {
            const container = document.createElement('section');
            container.id = 'plans-plugin-container';
            container.className = 'plugin-section';
            container.innerHTML = `
                <div class="plugin-header">
                    <h2>إدارة خطط الاشتراك</h2>
                    <button id="add-plan-btn" class="plugin-btn">إضافة خطة</button>
                </div>
                <table class="plugin-table" id="plans-table">
                    <thead>
                        <tr>
                            <th>اسم الخطة</th>
                            <th>المدة</th>
                            <th>السعر</th>
                            <th>الوصف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="plans-table-body">
                        <!-- سيتم تعبئة الخطط هنا -->
                    </tbody>
                </table>
            `;
            document.body.prepend(container);
        }
        this.renderPlans();
        document.getElementById('add-plan-btn').onclick = () => this.openPlanModal();
    },
    destroy() {
        const container = document.getElementById('plans-plugin-container');
        if (container) container.remove();
        const style = document.getElementById('plans-plugin-style');
        if (style) style.remove();
        this.closeModal();
    },
    renderPlans() {
        const plans = JSON.parse(localStorage.getItem('plugin_plans') || '[]');
        const tbody = document.getElementById('plans-table-body');
        if (!tbody) return;
        tbody.innerHTML = plans.length ? plans.map((p, i) => `
            <tr>
                <td>${p.name || ''}</td>
                <td>${p.duration || ''}</td>
                <td>${p.price || ''}</td>
                <td>${p.desc || ''}</td>
                <td>
                    <button class="plugin-action-btn" onclick="window.PluginManager.plugins.plans.openPlanModal(${i})">تعديل</button>
                    <button class="plugin-action-btn" onclick="window.PluginManager.plugins.plans.deletePlan(${i})">حذف</button>
                </td>
            </tr>
        `).join('') : '<tr><td colspan="5">لا يوجد خطط</td></tr>';
    },
    openPlanModal(index = null) {
        this.closeModal();
        const plans = JSON.parse(localStorage.getItem('plugin_plans') || '[]');
        const plan = index !== null ? plans[index] : { name: '', duration: '', price: '', desc: '' };
        const modalBg = document.createElement('div');
        modalBg.className = 'plugin-modal-bg';
        modalBg.id = 'plans-plugin-modal-bg';
        modalBg.innerHTML = `
            <div class="plugin-modal">
                <h3>${index !== null ? 'تعديل خطة' : 'إضافة خطة جديدة'}</h3>
                <label>اسم الخطة</label>
                <input id="modal-plan-name" type="text" value="${plan.name || ''}" placeholder="مثال: خطة شهرية" />
                <label>المدة</label>
                <input id="modal-plan-duration" type="text" value="${plan.duration || ''}" placeholder="مثال: شهر/3 أشهر/سنة" />
                <label>السعر</label>
                <input id="modal-plan-price" type="number" value="${plan.price || ''}" placeholder="مثال: 200" />
                <label>الوصف</label>
                <input id="modal-plan-desc" type="text" value="${plan.desc || ''}" placeholder="ملاحظات أو تفاصيل إضافية" />
                <div class="modal-actions">
                    <button class="plugin-btn" id="save-plan-btn">${index !== null ? 'حفظ التعديلات' : 'إضافة'}</button>
                    <button class="plugin-btn" id="cancel-plan-btn" style="background:#2d3950;">إلغاء</button>
                </div>
            </div>
        `;
        document.body.appendChild(modalBg);
        document.getElementById('cancel-plan-btn').onclick = () => this.closeModal();
        document.getElementById('save-plan-btn').onclick = () => {
            const name = document.getElementById('modal-plan-name').value.trim();
            const duration = document.getElementById('modal-plan-duration').value.trim();
            const price = document.getElementById('modal-plan-price').value.trim();
            const desc = document.getElementById('modal-plan-desc').value.trim();
            if (!name || !duration || !price) {
                alert('يرجى تعبئة جميع الحقول المطلوبة');
                return;
            }
            if (index !== null) {
                plans[index] = { name, duration, price, desc };
            } else {
                plans.push({ name, duration, price, desc });
            }
            localStorage.setItem('plugin_plans', JSON.stringify(plans));
            this.closeModal();
            this.renderPlans();
        };
        setTimeout(() => {
            document.getElementById('modal-plan-name').focus();
        }, 100);
    },
    closeModal() {
        const modalBg = document.getElementById('plans-plugin-modal-bg');
        if (modalBg) modalBg.remove();
    },
    deletePlan(index) {
        if (!confirm('هل أنت متأكد من حذف الخطة؟')) return;
        const plans = JSON.parse(localStorage.getItem('plugin_plans') || '[]');
        plans.splice(index, 1);
        localStorage.setItem('plugin_plans', JSON.stringify(plans));
        this.renderPlans();
    }
};
