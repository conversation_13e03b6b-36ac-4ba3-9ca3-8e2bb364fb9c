<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محمد الزهراني - أكاديمية 7C</title>
    <meta name="description" content="ملف اللاعب محمد الزهراني في أكاديمية 7C للتدريب الرياضي">
    <meta property="og:title" content="محمد الزهراني - أكاديمية 7C">
    <meta property="og:description" content="شاهد ملف اللاعب محمد الزهراني وإنجازاته في أكاديمية 7C">
    <meta property="og:image" content="https://via.placeholder.com/400x300/8B4513/FFFFFF?text=Player+Card">
    <meta property="og:url" content="https://academy7c.com/player/7C-2024-001">
    
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D2691E;
        }

        body { 
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            padding: 3rem 0;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></svg>');
            opacity: 0.3;
        }

        .player-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 1;
        }

        .player-avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            border: 5px solid rgba(255,255,255,0.3);
            object-fit: cover;
            margin: 0 auto;
            display: block;
        }

        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            background: rgba(255,255,255,0.15);
            transform: translateY(-2px);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: white;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.875rem;
        }

        .achievement {
            background: rgba(255,215,0,0.2);
            border: 1px solid rgba(255,215,0,0.3);
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            display: inline-block;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }

        .achievement:hover {
            background: rgba(255,215,0,0.3);
            transform: scale(1.05);
        }

        .info-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .fifa-mini-card {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            border: 2px solid var(--primary-color);
            border-radius: 15px;
            padding: 1.5rem;
            max-width: 300px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }

        .fifa-rating {
            position: absolute;
            top: 1rem;
            left: 1rem;
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .fifa-position {
            position: absolute;
            top: 4rem;
            left: 1rem;
            font-size: 1rem;
            color: white;
        }

        .social-share {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            z-index: 1000;
        }

        .share-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .share-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
        }

        .whatsapp { background: #25D366; }
        .twitter { background: #1DA1F2; }
        .facebook { background: #1877F2; }
        .copy-link { background: var(--primary-color); }

        .academy-footer {
            background: rgba(0, 0, 0, 0.3);
            padding: 2rem 0;
            text-align: center;
            margin-top: 3rem;
        }

        .view-counter {
            position: fixed;
            top: 1rem;
            left: 1rem;
            background: rgba(0, 0, 0, 0.7);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            z-index: 1000;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in {
            animation: fadeInUp 0.8s ease-out;
        }

        .fade-in-delay {
            animation: fadeInUp 0.8s ease-out 0.3s both;
        }

        @media (max-width: 768px) {
            .hero-section {
                padding: 2rem 0;
            }
            
            .player-card {
                margin: 0 1rem;
                padding: 1.5rem;
            }
            
            .social-share {
                bottom: 1rem;
                right: 1rem;
            }
            
            .stat-item {
                padding: 1rem;
            }
            
            .stat-value {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- View Counter -->
    <div class="view-counter">
        <i class="fas fa-eye ml-1"></i>
        <span id="viewCount">1</span> مشاهدة
    </div>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container mx-auto px-4">
            <div class="player-card fade-in">
                <div class="text-center mb-6">
                    <img src="https://via.placeholder.com/150x150/8B4513/FFFFFF?text=Player" alt="صورة اللاعب" class="player-avatar mb-4">
                    <h1 class="text-4xl font-bold mb-2">محمد أحمد الزهراني</h1>
                    <p class="text-xl opacity-90 mb-1">7C-2024-001</p>
                    <p class="text-lg opacity-75">ناشئين - وسط ميدان</p>
                    <div class="flex justify-center items-center gap-2 mt-3">
                        <span class="text-2xl">🇸🇦</span>
                        <span class="text-sm opacity-75">المملكة العربية السعودية</span>
                    </div>
                </div>
                
                <!-- FIFA Mini Card -->
                <div class="fifa-mini-card mb-6 fade-in-delay">
                    <div class="fifa-rating">85</div>
                    <div class="fifa-position">MID</div>
                    <div class="text-center pt-16">
                        <h3 class="text-xl font-bold">محمد الزهراني</h3>
                        <p class="text-sm opacity-75">أكاديمية 7C</p>
                    </div>
                </div>
                
                <!-- Stats Grid -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6 fade-in-delay">
                    <div class="stat-item">
                        <div class="stat-value">85</div>
                        <div class="stat-label">التقييم</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">16</div>
                        <div class="stat-label">العمر</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">1250</div>
                        <div class="stat-label">نقاط الولاء</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">95%</div>
                        <div class="stat-label">الحضور</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Achievements Section -->
    <section class="info-section container mx-auto px-4 fade-in-delay">
        <h2 class="text-2xl font-bold text-center mb-6">🏆 الإنجازات والشهادات</h2>
        <div class="text-center">
            <span class="achievement">⭐ أفضل لاعب في الشهر</span>
            <span class="achievement">🎯 حضور مثالي</span>
            <span class="achievement">⚽ هداف البطولة</span>
            <span class="achievement">🤝 روح الفريق</span>
            <span class="achievement">🏃‍♂️ أسرع لاعب</span>
            <span class="achievement">🎖️ لاعب الأسبوع</span>
        </div>
    </section>

    <!-- Performance Section -->
    <section class="info-section container mx-auto px-4 fade-in-delay">
        <h2 class="text-2xl font-bold text-center mb-6">📊 الأداء والإحصائيات</h2>
        <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div class="text-center">
                <div class="text-3xl font-bold text-blue-400">85%</div>
                <div class="text-sm opacity-75">المهارات التقنية</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-green-400">78%</div>
                <div class="text-sm opacity-75">اللياقة البدنية</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-purple-400">82%</div>
                <div class="text-sm opacity-75">الفهم التكتيكي</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-orange-400">88%</div>
                <div class="text-sm opacity-75">القوة الذهنية</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-red-400">90%</div>
                <div class="text-sm opacity-75">روح الفريق</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-yellow-400">156</div>
                <div class="text-sm opacity-75">أيام حضور</div>
            </div>
        </div>
    </section>

    <!-- Academy Footer -->
    <footer class="academy-footer">
        <div class="container mx-auto px-4">
            <div class="flex flex-col items-center">
                <img src="https://via.placeholder.com/80x80/8B4513/FFFFFF?text=7C" alt="شعار الأكاديمية" class="w-16 h-16 mb-4 rounded-lg">
                <h3 class="text-xl font-bold mb-2">أكاديمية 7C للتدريب الرياضي</h3>
                <p class="text-sm opacity-75 mb-4">تطوير المواهب الرياضية وبناء الأبطال</p>
                <div class="flex gap-4 text-sm opacity-50">
                    <span>تم إنشاء هذا الملف في: <span id="creationDate"></span></span>
                    <span>•</span>
                    <span>آخر تحديث: <span id="lastUpdate"></span></span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Social Share Buttons -->
    <div class="social-share">
        <a href="#" onclick="shareToWhatsApp()" class="share-btn whatsapp" title="مشاركة على واتساب">
            <i class="fab fa-whatsapp"></i>
        </a>
        <a href="#" onclick="shareToTwitter()" class="share-btn twitter" title="مشاركة على تويتر">
            <i class="fab fa-twitter"></i>
        </a>
        <a href="#" onclick="shareToFacebook()" class="share-btn facebook" title="مشاركة على فيسبوك">
            <i class="fab fa-facebook"></i>
        </a>
        <a href="#" onclick="copyLink()" class="share-btn copy-link" title="نسخ الرابط">
            <i class="fas fa-link"></i>
        </a>
    </div>

    <script>
        // Player data
        const playerData = {
            id: '7C-2024-001',
            name: 'محمد أحمد الزهراني',
            academicNumber: '7C-2024-001',
            position: 'وسط ميدان',
            category: 'ناشئين',
            rating: 85,
            age: 16,
            loyaltyPoints: 1250,
            attendance: 95
        };

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            trackPageView();
            updateDates();
            addScrollAnimations();
        });

        // Track page views
        function trackPageView() {
            let views = parseInt(localStorage.getItem(`public_views_${playerData.id}`) || '0');
            views++;
            localStorage.setItem(`public_views_${playerData.id}`, views.toString());
            document.getElementById('viewCount').textContent = views;

            // Track visit data
            const visitData = {
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                referrer: document.referrer,
                page: 'public_player_page'
            };

            const visits = JSON.parse(localStorage.getItem(`public_visits_${playerData.id}`) || '[]');
            visits.push(visitData);
            localStorage.setItem(`public_visits_${playerData.id}`, JSON.stringify(visits));
        }

        // Update dates
        function updateDates() {
            const now = new Date();
            document.getElementById('creationDate').textContent = now.toLocaleDateString('ar-SA');
            document.getElementById('lastUpdate').textContent = now.toLocaleDateString('ar-SA');
        }

        // Add scroll animations
        function addScrollAnimations() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });

            document.querySelectorAll('.info-section').forEach(section => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(30px)';
                section.style.transition = 'all 0.6s ease';
                observer.observe(section);
            });
        }

        // Share functions
        function shareToWhatsApp() {
            const text = `شاهد ملف اللاعب ${playerData.name} في أكاديمية 7C`;
            const url = window.location.href;
            window.open(`https://wa.me/?text=${encodeURIComponent(text + '\n' + url)}`, '_blank');
        }

        function shareToTwitter() {
            const text = `شاهد ملف اللاعب ${playerData.name} في أكاديمية 7C`;
            const url = window.location.href;
            window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`, '_blank');
        }

        function shareToFacebook() {
            const url = window.location.href;
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
        }

        function copyLink() {
            navigator.clipboard.writeText(window.location.href).then(() => {
                // Show success message
                const btn = event.target.closest('.share-btn');
                const originalIcon = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i>';
                btn.style.background = '#10b981';
                
                setTimeout(() => {
                    btn.innerHTML = originalIcon;
                    btn.style.background = 'var(--primary-color)';
                }, 2000);
            });
        }

        // Add some interactive effects
        document.querySelectorAll('.stat-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.05)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        console.log('🌟 تم تحميل الصفحة العامة للاعب بنجاح');
    </script>
</body>
</html>
