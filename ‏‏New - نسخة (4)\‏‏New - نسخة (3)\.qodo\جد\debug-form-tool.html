<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>أداة تشخيص النماذج - Debug Form Tool</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { background: #0f172a; color: #fff; font-family: 'Cairo', Tahoma, Arial, sans-serif; margin: 0; padding: 0; }
        .debug-container { max-width: 480px; margin: 40px auto; background: #1e293b; border-radius: 18px; box-shadow: 0 4px 24px #0005; padding: 32px 28px; }
        h2 { color: #fbbf24; margin-bottom: 18px; }
        label { display: block; margin: 12px 0 4px; color: #38bdf8; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px 10px; border-radius: 7px; border: none; background: #334155; color: #fff; margin-bottom: 10px; font-size: 15px; }
        button { background: #f59e0b; color: #fff; border: none; padding: 10px 22px; border-radius: 8px; font-weight: bold; font-size: 16px; cursor: pointer; margin-top: 10px; }
        button:hover { background: #d97706; }
        .result-box { background: #222c3d; border-radius: 10px; padding: 16px; margin-top: 18px; font-size: 15px; line-height: 1.7; }
        .field-list { background: #334155; border-radius: 8px; padding: 10px 12px; margin-bottom: 10px; }
        .field-list span { color: #fbbf24; font-weight: bold; }
        .json-valid { color: #22d3ee; }
        .json-invalid { color: #f87171; }
        .success { color: #22c55e; }
        .fail { color: #ef4444; }
        .small { font-size: 13px; color: #94a3b8; }
    </style>
</head>
<body>
<div class="debug-container">
    <h2>🛠️ أداة تشخيص النماذج</h2>
    <label>Endpoint (API):</label>
    <input id="endpoint" type="text" value="registration_api.php">
    <label>أسماء الحقول (مفصولة بفاصلة):</label>
    <input id="fields" type="text" value="fullName,birthDate,nationalId,gender,category,referralPhone,phone,email,address,guardianName,guardianPhone,relationship,guardianEmail,subscriptionPlan,paymentMethod,faceImage">
    <button onclick="scanFields()">🔍 فحص الحقول في الصفحة</button>
    <div id="fieldList" class="field-list"></div>
    <button onclick="sendTest()">🚀 إرسال بيانات اختبار</button>
    <div id="resultBox" class="result-box"></div>
    <div class="small" style="margin-top:18px;">ضع هذه الأداة بجانب أي نموذج، أو افتحها في نافذة جديدة، ويمكنك تعديل أسماء الحقول أو endpoint حسب الحاجة.</div>
</div>
<script>
function scanFields() {
    let fieldNames = document.getElementById('fields').value.split(',').map(f=>f.trim());
    let html = '';
    fieldNames.forEach(f => {
        let el = window.opener ? window.opener.document.getElementById(f) : document.getElementById(f);
        let v = el ? el.value : '[غير موجود]';
        html += `<div>${f}: <span>${v}</span></div>`;
    });
    document.getElementById('fieldList').innerHTML = html;
}
async function sendTest() {
    let endpoint = document.getElementById('endpoint').value;
    let fieldNames = document.getElementById('fields').value.split(',').map(f=>f.trim());
    let data = {};
    fieldNames.forEach(f => {
        let el = window.opener ? window.opener.document.getElementById(f) : document.getElementById(f);
        data[f] = el ? el.value : '';
    });
    document.getElementById('resultBox').innerHTML = '...جاري الإرسال';
    try {
        let res = await fetch(endpoint, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });
        let txt = await res.text();
        let html = `<b>استجابة السيرفر:</b><br><pre style='color:#f87171;background:#222;padding:8px;border-radius:6px;max-height:120px;overflow:auto'>${txt}</pre>`;
        try {
            let js = JSON.parse(txt);
            html += '<span class="json-valid">✔ JSON صالح</span><br>';
            if(js.success) html += '<span class="success">تمت العملية بنجاح</span>';
            else html += '<span class="fail">فشل العملية</span>';
        } catch {
            html += '<span class="json-invalid">❌ ليس JSON صالح</span>';
        }
        document.getElementById('resultBox').innerHTML = html;
    } catch (err) {
        document.getElementById('resultBox').innerHTML = `<span class='fail'>خطأ في الاتصال: ${err.message}</span>`;
    }
}
// عند الفتح، افحص الحقول تلقائيًا
scanFields();
</script>
</body>
</html>
