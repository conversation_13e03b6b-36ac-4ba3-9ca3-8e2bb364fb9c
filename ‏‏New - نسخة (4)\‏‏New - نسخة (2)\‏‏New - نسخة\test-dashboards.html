<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار لوحات التحكم - أكاديمية 7C</title>
    
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-bg: #1a1a1a;
            --brand-primary: #8B4513;
            --brand-secondary: #D2691E;
            --accent-dark: #2F4F4F;
            --success: #28a745;
            --danger: #dc3545;
            --warning: #ffc107;
            --info: #17a2b8;
            --glass: rgba(255,255,255,0.1);
            --border: rgba(255,255,255,0.2);
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --shadow-dark: 0 8px 32px rgba(0, 0, 0, 0.3);
            --shadow-glow: 0 0 20px rgba(139, 69, 19, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--primary-bg) 0%, var(--accent-dark) 100%);
            min-height: 100vh;
            color: var(--text-primary);
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .logo {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2.5rem;
            color: white;
            box-shadow: var(--shadow-glow);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, var(--brand-primary), var(--brand-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            color: var(--text-secondary);
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }

        .dashboards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .dashboard-card {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--brand-primary), var(--brand-secondary));
            border-radius: 20px 20px 0 0;
        }

        .dashboard-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-dark);
        }

        .dashboard-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            color: white;
            box-shadow: var(--shadow-glow);
        }

        .dashboard-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .dashboard-desc {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .dashboard-btn {
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            padding: 0.8rem 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            text-decoration: none;
            display: inline-block;
            position: relative;
            overflow: hidden;
        }

        .dashboard-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .dashboard-btn:hover::before {
            left: 100%;
        }

        .dashboard-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.4);
        }

        .credentials {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 2rem;
            margin-top: 2rem;
        }

        .credentials h3 {
            color: var(--brand-primary);
            margin-bottom: 1rem;
            text-align: center;
        }

        .cred-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .cred-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 1rem;
        }

        .cred-role {
            font-weight: 700;
            color: var(--brand-primary);
            margin-bottom: 0.5rem;
        }

        .cred-details {
            font-size: 0.9rem;
            color: var(--text-secondary);
            line-height: 1.4;
        }

        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .dashboards-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .dashboard-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <i class="fas fa-dumbbell"></i>
            </div>
            <h1 class="title">أكاديمية 7C الرياضية</h1>
            <p class="subtitle">اختبار لوحات التحكم المتخصصة</p>
        </div>

        <div class="dashboards-grid">
            <div class="dashboard-card">
                <div class="dashboard-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <h3 class="dashboard-title">لوحة تحكم المدير</h3>
                <p class="dashboard-desc">إدارة شاملة للأكاديمية مع تحليلات الذكاء الاصطناعي والتقارير المتقدمة</p>
                <a href="admin-dashboard-7c.html" class="dashboard-btn">
                    <i class="fas fa-arrow-left" style="margin-right: 0.5rem;"></i>
                    دخول لوحة المدير
                </a>
            </div>

            <div class="dashboard-card">
                <div class="dashboard-icon">
                    <i class="fas fa-user-tie"></i>
                </div>
                <h3 class="dashboard-title">لوحة تحكم المدرب</h3>
                <p class="dashboard-desc">إدارة اللاعبين والحصص التدريبية مع نظام تقييم متطور</p>
                <a href="coach-dashboard.html" class="dashboard-btn">
                    <i class="fas fa-arrow-left" style="margin-right: 0.5rem;"></i>
                    دخول لوحة المدرب
                </a>
            </div>

            <div class="dashboard-card">
                <div class="dashboard-icon">
                    <i class="fas fa-user-friends"></i>
                </div>
                <h3 class="dashboard-title">لوحة تحكم ولي الأمر</h3>
                <p class="dashboard-desc">متابعة تقدم الأطفال والتواصل مع المدربين ومراقبة الأداء</p>
                <a href="parent-dashboard.html" class="dashboard-btn">
                    <i class="fas fa-arrow-left" style="margin-right: 0.5rem;"></i>
                    دخول لوحة ولي الأمر
                </a>
            </div>

            <div class="dashboard-card">
                <div class="dashboard-icon">
                    <i class="fas fa-running"></i>
                </div>
                <h3 class="dashboard-title">لوحة تحكم اللاعب</h3>
                <p class="dashboard-desc">عرض الأداء الشخصي والجدول الزمني والإنجازات (قيد التطوير)</p>
                <a href="player-dashboard.html" class="dashboard-btn" style="opacity: 0.6;">
                    <i class="fas fa-tools" style="margin-right: 0.5rem;"></i>
                    قيد التطوير
                </a>
            </div>

            <div class="dashboard-card">
                <div class="dashboard-icon">
                    <i class="fas fa-clipboard-check"></i>
                </div>
                <h3 class="dashboard-title">لوحة تحكم المشرف</h3>
                <p class="dashboard-desc">مراقبة العمليات والتقارير الإشرافية (قيد التطوير)</p>
                <a href="supervisor-dashboard.html" class="dashboard-btn" style="opacity: 0.6;">
                    <i class="fas fa-tools" style="margin-right: 0.5rem;"></i>
                    قيد التطوير
                </a>
            </div>

            <div class="dashboard-card">
                <div class="dashboard-icon">
                    <i class="fas fa-sign-in-alt"></i>
                </div>
                <h3 class="dashboard-title">نظام تسجيل الدخول</h3>
                <p class="dashboard-desc">مصادقة متقدمة مع دعم البيومترية والأمان المتطور</p>
                <a href="login.html" class="dashboard-btn">
                    <i class="fas fa-arrow-left" style="margin-right: 0.5rem;"></i>
                    صفحة تسجيل الدخول
                </a>
            </div>
        </div>

        <div class="credentials">
            <h3>بيانات الاختبار</h3>
            <div class="cred-grid">
                <div class="cred-item">
                    <div class="cred-role">المدير</div>
                    <div class="cred-details">
                        البريد: <EMAIL><br>
                        كلمة المرور: admin123<br>
                        الجوال: 0501234567
                    </div>
                </div>
                <div class="cred-item">
                    <div class="cred-role">المدرب</div>
                    <div class="cred-details">
                        البريد: <EMAIL><br>
                        كلمة المرور: coach123<br>
                        الجوال: 0509876543
                    </div>
                </div>
                <div class="cred-item">
                    <div class="cred-role">ولي الأمر</div>
                    <div class="cred-details">
                        البريد: <EMAIL><br>
                        كلمة المرور: parent123<br>
                        الجوال: 0555544332
                    </div>
                </div>
                <div class="cred-item">
                    <div class="cred-role">اللاعب</div>
                    <div class="cred-details">
                        البريد: <EMAIL><br>
                        كلمة المرور: player123<br>
                        الجوال: 0551122334
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
