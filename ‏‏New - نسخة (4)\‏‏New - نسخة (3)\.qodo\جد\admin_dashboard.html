<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8">
  <title>لوحة الإدارة - 7C Academy</title>
  <style>
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; background: #f9f9f9; direction: rtl; }
    header { background: #1e88e5; padding: 20px; color: white; text-align: center; }
    nav { background: #fff; padding: 15px; display: flex; justify-content: space-around; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
    nav a { text-decoration: none; color: #333; font-weight: bold; }
    .container { padding: 30px; }
    .card-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 20px; }
    .card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 6px rgba(0,0,0,0.07); transition: 0.3s ease; text-align: center; }
    .card:hover { box-shadow: 0 4px 10px rgba(0,0,0,0.1); }
    .card h3 { margin: 0 0 10px; color: #007BFF; }
    .logout { position: absolute; left: 20px; top: 20px; background: #e53935; color: white; padding: 8px 16px; border: none; border-radius: 6px; text-decoration: none; }
  </style>
</head>
<body>
  <header>
    <h1>لوحة التحكم - الإدارة</h1>
    <a href="../index.html" class="logout">🔓 تسجيل الخروج</a>
  </header>
  <nav>
    <a href="#">الإحصائيات</a>
    <a href="#">إدارة المستخدمين</a>
    <a href="#">المدفوعات</a>
    <a href="#">الإشعارات</a>
    <a href="#">الفرق واللاعبين</a>
  </nav>
  <div class="container">
    <div class="card-grid">
      <div class="card">
        <h3>📊 تقارير الأداء</h3>
        <p>عرض ومتابعة المؤشرات الرئيسية للاعبين والفرق.</p>
      </div>
      <div class="card">
        <h3>👨‍🏫 المدربون</h3>
        <p>إدارة بيانات المدربين وتوزيع المهام.</p>
      </div>
      <div class="card">
        <h3>👨‍👩‍👧 أولياء الأمور</h3>
        <p>ربط الأولياء باللاعبين ومتابعة مشاركاتهم.</p>
      </div>
      <div class="card">
        <h3>🕒 الحضور والانضباط</h3>
        <p>مراقبة الغياب والمخالفات.</p>
      </div>
    </div>
  </div>
</body>
</html>
