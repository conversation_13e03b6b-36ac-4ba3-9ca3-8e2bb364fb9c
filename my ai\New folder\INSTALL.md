# خطوات تنصيب مشروع المساعد الذكي

1. فك ضغط ملفات المشروع في مجلد على الاستضافة.
2. ادخل إلى مجلد backend وثبّت الحزم:
   ```sh
   cd backend
   npm install
   ```
3. شغّل الخادم:
   ```sh
   node index.js &
   ```
   أو يفضل استخدام pm2 لإدارة الخادم:
   ```sh
   npm install -g pm2
   pm2 start index.js --name ai-backend
   ```
4. ادخل إلى مجلد frontend وثبّت الحزم:
   ```sh
   cd ../frontend
   npm install
   ```
5. لبناء الواجهة الأمامية كملفات ثابتة (اختياري):
   ```sh
   npm run build
   ```
   أو للتشغيل المحلي:
   ```sh
   npm start
   ```
6. اربط الواجهة الأمامية مع الواجهة الخلفية حسب الحاجة (تعديل endpoint في App.js إذا تغير المسار).

---

## ملاحظات:
- يجب أن تكون الاستضافة تدعم Node.js وnpm.
- يفضل استخدام pm2 لإدارة الخادم.
- إذا واجهت أي مشكلة، انسخ رسالة الخطأ هنا وسأساعدك فورًا.
