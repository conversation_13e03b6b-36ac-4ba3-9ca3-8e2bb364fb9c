// ==================== Plugin: تحليلات الزوار ====================
export const AnalyticsPlugin = {
    id: 'analytics',
    name: 'تحليلات الزوار',
    init() {
        if (!document.getElementById('analytics-plugin-style')) {
            const style = document.createElement('style');
            style.id = 'analytics-plugin-style';
            style.innerHTML = `
                .plugin-section {background: #181f2a; color: #fff; border-radius: 18px; box-shadow: 0 2px 16px #0002; max-width: 900px; margin: 32px auto 0; padding: 32px 24px 24px; font-family: 'Cairo',sans-serif;}
                .plugin-header {display: flex; align-items: center; justify-content: space-between; margin-bottom: 18px;}
                .plugin-header h2 {font-size: 1.5rem; font-weight: bold; margin: 0;}
                .plugin-btn {background: linear-gradient(90deg,#4f8cff,#6f6bff); color: #fff; border: none; border-radius: 8px; padding: 10px 22px; font-size: 1rem; font-weight: 600; cursor: pointer; transition: background 0.2s;}
                .plugin-btn:hover {background: linear-gradient(90deg,#6f6bff,#4f8cff);}
                .analytics-cards {display: flex; gap: 18px; margin-bottom: 24px; flex-wrap: wrap;}
                .analytics-card {flex: 1; min-width: 180px; background: #232b3b; border-radius: 12px; padding: 18px 12px; text-align: center; color: #8bb4ff; font-size: 1.2rem; font-weight: 700; box-shadow: 0 2px 8px #0002;}
                .analytics-chart {background: #232b3b; border-radius: 12px; padding: 18px; margin-top: 18px; text-align: center;}
            `;
            document.head.appendChild(style);
        }
        if (!document.getElementById('analytics-plugin-container')) {
            const container = document.createElement('section');
            container.id = 'analytics-plugin-container';
            container.className = 'plugin-section';
            container.innerHTML = `
                <div class="plugin-header">
                    <h2>تحليلات الزوار</h2>
                    <button id="add-visit-btn" class="plugin-btn">إضافة زيارة تجريبية</button>
                </div>
                <div class="analytics-cards">
                    <div class="analytics-card" id="analytics-total">إجمالي الزيارات: 0</div>
                    <div class="analytics-card" id="analytics-unique">الزوار الفريدون: 0</div>
                    <div class="analytics-card" id="analytics-today">زيارات اليوم: 0</div>
                </div>
                <div class="analytics-chart" id="analytics-chart">(رسم بياني تجريبي)</div>
            `;
            document.body.prepend(container);
        }
        this.renderAnalytics();
        document.getElementById('add-visit-btn').onclick = () => this.addVisit();
    },
    destroy() {
        const container = document.getElementById('analytics-plugin-container');
        if (container) container.remove();
        const style = document.getElementById('analytics-plugin-style');
        if (style) style.remove();
    },
    renderAnalytics() {
        // بيانات تجريبية
        const visits = JSON.parse(localStorage.getItem('plugin_analytics') || '[]');
        const total = visits.length;
        const unique = new Set(visits.map(v => v.visitor)).size;
        const today = visits.filter(v => v.date === this.getToday()).length;
        document.getElementById('analytics-total').textContent = `إجمالي الزيارات: ${total}`;
        document.getElementById('analytics-unique').textContent = `الزوار الفريدون: ${unique}`;
        document.getElementById('analytics-today').textContent = `زيارات اليوم: ${today}`;
        // رسم بياني تجريبي (نص فقط)
        document.getElementById('analytics-chart').textContent = `توزيع الزيارات حسب الأيام (تجريبي):\n` +
            this.getVisitsByDay(visits).map(([day, count]) => `${day}: ${count}`).join(' | ');
    },
    addVisit() {
        const visits = JSON.parse(localStorage.getItem('plugin_analytics') || '[]');
        const visitor = 'زائر-' + Math.floor(Math.random()*1000);
        visits.push({ visitor, date: this.getToday() });
        localStorage.setItem('plugin_analytics', JSON.stringify(visits));
        this.renderAnalytics();
    },
    getToday() {
        const d = new Date();
        return d.toISOString().split('T')[0];
    },
    getVisitsByDay(visits) {
        const days = {};
        visits.forEach(v => { days[v.date] = (days[v.date]||0)+1; });
        return Object.entries(days);
    }
};
