import React, { useState } from 'react';

function App() {
  const [question, setQuestion] = useState('');
  const [answer, setAnswer] = useState('');
  const [loading, setLoading] = useState(false);

  const askAI = async () => {
    setLoading(true);
    setAnswer('');
    const res = await fetch('http://localhost:5000/api/ask', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ question })
    });
    const data = await res.json();
    setAnswer(data.answer);
    setLoading(false);
  };

  return (
    <div style={{ maxWidth: 500, margin: '50px auto', padding: 20, boxShadow: '0 0 10px #ccc', borderRadius: 10 }}>
      <h2>مساعدك الذكي المجاني</h2>
      <input
        type="text"
        value={question}
        onChange={e => setQuestion(e.target.value)}
        placeholder="اكتب سؤالك هنا..."
        style={{ width: '100%', padding: 10, marginBottom: 10 }}
      />
      <button onClick={askAI} disabled={loading || !question} style={{ width: '100%', padding: 10 }}>
        {loading ? 'جاري التفكير...' : 'اسأل'}
      </button>
      {answer && (
        <div style={{ marginTop: 20, background: '#f9f9f9', padding: 15, borderRadius: 8 }}>
          <b>الإجابة:</b>
          <div>{answer}</div>
        </div>
      )}
    </div>
  );
}

export default App;
