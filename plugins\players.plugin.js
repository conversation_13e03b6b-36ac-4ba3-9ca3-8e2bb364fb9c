// ==================== Plugin: إدارة اللاعبين ====================
export const PlayersManagementPlugin = {
    id: 'players',
    name: 'إدارة اللاعبين',
    init() {
        // إنشاء واجهة إدارة اللاعبين إذا لم تكن موجودة
        if (!document.getElementById('players-plugin-container')) {
            const container = document.createElement('section');
            container.id = 'players-plugin-container';
            container.className = 'plugin-section';
            container.innerHTML = `
                <div class="plugin-header">
                    <h2>إدارة اللاعبين</h2>
                    <button id="add-player-btn" class="plugin-btn">إضافة لاعب</button>
                </div>
                <table class="plugin-table" id="players-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الفئة</th>
                            <th>الهاتف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="players-table-body">
                        <!-- سيتم تعبئة اللاعبين هنا -->
                    </tbody>
                </table>
            `;
            document.body.prepend(container);
        }
        this.renderPlayers();
        document.getElementById('add-player-btn').onclick = () => this.openAddPlayerModal();
    },
    destroy() {
        // إزالة واجهة إدارة اللاعبين وCSS
        const container = document.getElementById('players-plugin-container');
        if (container) container.remove();
        const style = document.getElementById('players-plugin-style');
        if (style) style.remove();
        this.closeModal();
    },
    renderPlayers() {
        // بيانات تجريبية (يمكن ربطها بقاعدة البيانات لاحقاً)
        const players = JSON.parse(localStorage.getItem('plugin_players') || '[]');
        const tbody = document.getElementById('players-table-body');
        if (!tbody) return;
        tbody.innerHTML = players.map((p, i) => `
            <tr>
                <td>${p.name}</td>
                <td>${p.category}</td>
                <td>${p.phone}</td>
                <td>
                    <button onclick="window.PluginManager.plugins.players.editPlayer(${i})">تعديل</button>
                    <button onclick="window.PluginManager.plugins.players.deletePlayer(${i})">حذف</button>
                </td>
            </tr>
        `).join('') || '<tr><td colspan="4">لا يوجد لاعبين</td></tr>';
    },
    openPlayerModal(index = null) {
        this.closeModal();
        const players = JSON.parse(localStorage.getItem('plugin_players') || '[]');
        const player = index !== null ? players[index] : { name: '', category: '', phone: '' };
        const modalBg = document.createElement('div');
        modalBg.className = 'plugin-modal-bg';
        modalBg.id = 'players-plugin-modal-bg';
        modalBg.innerHTML = `
            <div class="plugin-modal">
                <h3>${index !== null ? 'تعديل لاعب' : 'إضافة لاعب جديد'}</h3>
                <label>اسم اللاعب</label>
                <input id="modal-player-name" type="text" value="${player.name || ''}" placeholder="مثال: محمد أحمد" />
                <label>فئة اللاعب</label>
                <input id="modal-player-category" type="text" value="${player.category || ''}" placeholder="مثال: ناشئين" />
                <label>رقم الهاتف</label>
                <input id="modal-player-phone" type="text" value="${player.phone || ''}" placeholder="05xxxxxxxx" />
                <div class="modal-actions">
                    <button class="plugin-btn" id="save-player-btn">${index !== null ? 'حفظ التعديلات' : 'إضافة'}</button>
                    <button class="plugin-btn" id="cancel-player-btn" style="background:#2d3950;">إلغاء</button>
                </div>
            </div>
        `;
        document.body.appendChild(modalBg);
        document.getElementById('cancel-player-btn').onclick = () => this.closeModal();
        document.getElementById('save-player-btn').onclick = () => {
            const name = document.getElementById('modal-player-name').value.trim();
            const category = document.getElementById('modal-player-category').value.trim();
            const phone = document.getElementById('modal-player-phone').value.trim();
            if (!name || !category || !phone) {
                alert('يرجى تعبئة جميع الحقول');
                return;
            }
            if (index !== null) {
                players[index] = { name, category, phone };
            } else {
                players.push({ name, category, phone });
            }
            localStorage.setItem('plugin_players', JSON.stringify(players));
            this.closeModal();
            this.renderPlayers();
        };
        setTimeout(() => {
            document.getElementById('modal-player-name').focus();
        }, 100);
    },
    editPlayer(index) {
        const players = JSON.parse(localStorage.getItem('plugin_players') || '[]');
        const player = players[index];
        if (!player) return;
        const name = prompt('اسم اللاعب:', player.name);
        if (!name) return;
        const category = prompt('فئة اللاعب:', player.category);
        if (!category) return;
        const phone = prompt('رقم الهاتف:', player.phone);
        if (!phone) return;
        players[index] = { name, category, phone };
        localStorage.setItem('plugin_players', JSON.stringify(players));
        this.renderPlayers();
    },
    deletePlayer(index) {
        if (!confirm('هل أنت متأكد من حذف اللاعب؟')) return;
        const players = JSON.parse(localStorage.getItem('plugin_players') || '[]');
        players.splice(index, 1);
        localStorage.setItem('plugin_players', JSON.stringify(players));
        this.renderPlayers();
    }
};

// ملاحظة: يمكن تطوير الواجهة لاحقاً لتكون أكثر احترافية وربطها بقاعدة بيانات فعلية.
