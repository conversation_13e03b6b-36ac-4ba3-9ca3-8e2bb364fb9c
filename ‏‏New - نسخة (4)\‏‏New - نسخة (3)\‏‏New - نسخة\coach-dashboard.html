<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المدرب - أكاديمية 7C الرياضية</title>
    
    <!-- External Libraries -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.0/dist/sweetalert2.all.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    
    <style>
        /* ==================== متغيرات الألوان الأساسية ==================== */
        :root {
            --primary-bg: #1a1a1a;
            --brand-primary: #8B4513;
            --brand-secondary: #D2691E;
            --accent-dark: #2F4F4F;
            --success: #28a745;
            --danger: #dc3545;
            --warning: #ffc107;
            --info: #17a2b8;
            --glass: rgba(255,255,255,0.1);
            --border: rgba(255,255,255,0.2);
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --shadow-dark: 0 8px 32px rgba(0, 0, 0, 0.3);
            --shadow-glow: 0 0 20px rgba(139, 69, 19, 0.3);
        }

        /* ==================== إعدادات أساسية ==================== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--primary-bg);
            color: var(--text-primary);
            overflow-x: hidden;
        }

        /* ==================== التخطيط الرئيسي ==================== */
        .main-layout {
            display: grid;
            grid-template-columns: 280px 1fr;
            grid-template-rows: 70px 1fr;
            grid-template-areas: 
                "sidebar header"
                "sidebar content";
            min-height: 100vh;
        }

        /* ==================== الشريط الجانبي ==================== */
        .sidebar {
            grid-area: sidebar;
            background: linear-gradient(180deg, var(--brand-primary) 0%, var(--accent-dark) 100%);
            border-left: 1px solid var(--border);
            overflow-y: auto;
            transition: all 0.3s ease;
            position: relative;
        }

        .sidebar-header {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid var(--border);
            background: rgba(0, 0, 0, 0.2);
        }

        .coach-avatar {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--brand-secondary), #ff8c42);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            font-size: 2rem;
            color: white;
            box-shadow: var(--shadow-glow);
            animation: pulse 3s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .coach-name {
            font-size: 1.1rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.25rem;
        }

        .coach-title {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.7);
            background: rgba(255, 255, 255, 0.1);
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            display: inline-block;
        }

        /* ==================== قائمة التنقل ==================== */
        .nav-menu {
            padding: 1rem 0;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
            position: relative;
            cursor: pointer;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-right-color: var(--brand-secondary);
            transform: translateX(-5px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border-right-color: var(--brand-secondary);
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
        }

        .nav-icon {
            width: 20px;
            margin-left: 1rem;
            font-size: 1.1rem;
        }

        .nav-text {
            font-weight: 600;
            font-size: 0.95rem;
        }

        .nav-badge {
            margin-right: auto;
            background: var(--brand-secondary);
            color: white;
            font-size: 0.7rem;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            font-weight: 600;
        }

        /* ==================== رأس الصفحة ==================== */
        .header {
            grid-area: header;
            background: var(--glass);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 2rem;
            box-shadow: var(--shadow-dark);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--brand-primary);
        }

        .breadcrumb {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-btn {
            background: var(--glass);
            border: 1px solid var(--border);
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .header-btn:hover {
            background: var(--brand-primary);
            transform: translateY(-2px);
        }

        .notification-btn {
            position: relative;
        }

        .notification-count {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--danger);
            color: white;
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
            border-radius: 50%;
            min-width: 18px;
            text-align: center;
        }

        /* ==================== المحتوى الرئيسي ==================== */
        .content {
            grid-area: content;
            padding: 2rem;
            overflow-y: auto;
            background: linear-gradient(135deg, var(--primary-bg) 0%, #1e1e2e 100%);
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* ==================== بطاقات الإحصائيات ==================== */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-dark);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--brand-primary), var(--brand-secondary));
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 600;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stat-change {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .stat-change.positive {
            color: var(--success);
        }

        .stat-change.negative {
            color: var(--danger);
        }

        .stat-change.neutral {
            color: var(--text-secondary);
        }

        /* ==================== البطاقات العامة ==================== */
        .card {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .card-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border);
            border-radius: 8px;
            background: var(--glass);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn:hover {
            background: var(--brand-primary);
            transform: translateY(-2px);
        }

        .btn.primary {
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            border-color: var(--brand-primary);
        }

        /* ==================== تصميم متجاوب ==================== */
        @media (max-width: 1024px) {
            .main-layout {
                grid-template-columns: 1fr;
                grid-template-areas: 
                    "header"
                    "content";
            }
            
            .sidebar {
                position: fixed;
                left: -280px;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            
            .sidebar.open {
                left: 0;
            }
        }

        @media (max-width: 768px) {
            .content {
                padding: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .header {
                padding: 0 1rem;
            }
            
            .page-title {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-layout">
        <!-- الشريط الجانبي -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="coach-avatar" id="coachAvatar">
                    <i class="fas fa-user-tie"></i>
                </div>
                <div class="coach-name" id="coachName">كابتن أحمد المدرب</div>
                <div class="coach-title" id="coachTitle">مدرب كرة القدم</div>
            </div>

            <nav class="nav-menu">
                <div class="nav-item">
                    <div class="nav-link active" onclick="showSection('overview')">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">نظرة عامة</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('players')">
                        <i class="fas fa-users nav-icon"></i>
                        <span class="nav-text">إدارة اللاعبين</span>
                        <span class="nav-badge" id="totalPlayersCount">0</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('schedule')">
                        <i class="fas fa-calendar-alt nav-icon"></i>
                        <span class="nav-text">جدولة التدريبات</span>
                        <span class="nav-badge" id="upcomingSessionsCount">0</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('performance')">
                        <i class="fas fa-chart-line nav-icon"></i>
                        <span class="nav-text">تقييم الأداء</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('reports')">
                        <i class="fas fa-file-alt nav-icon"></i>
                        <span class="nav-text">التقارير</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('messages')">
                        <i class="fas fa-envelope nav-icon"></i>
                        <span class="nav-text">الرسائل</span>
                        <span class="nav-badge" id="unreadMessagesCount">0</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('settings')">
                        <i class="fas fa-cog nav-icon"></i>
                        <span class="nav-text">الإعدادات</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="logout()">
                        <i class="fas fa-sign-out-alt nav-icon"></i>
                        <span class="nav-text">تسجيل الخروج</span>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- رأس الصفحة -->
        <header class="header">
            <div class="header-left">
                <button class="header-btn" onclick="toggleSidebar()" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div>
                    <h1 class="page-title" id="pageTitle">نظرة عامة</h1>
                    <div class="breadcrumb" id="breadcrumb">الرئيسية / نظرة عامة</div>
                </div>
            </div>

            <div class="header-right">
                <button class="header-btn notification-btn" onclick="showNotifications()">
                    <i class="fas fa-bell"></i>
                    <span class="notification-count" id="notificationCount">3</span>
                </button>

                <button class="header-btn" onclick="showQuickActions()">
                    <i class="fas fa-plus"></i>
                    إجراء سريع
                </button>

                <button class="header-btn" onclick="exportCoachReport()">
                    <i class="fas fa-download"></i>
                    تصدير التقرير
                </button>
            </div>
        </header>

        <!-- المحتوى الرئيسي -->
        <main class="content">
            <!-- قسم النظرة العامة -->
            <div id="overviewSection" class="content-section active">
                <!-- بطاقات الإحصائيات -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">إجمالي اللاعبين</div>
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="totalPlayers">0</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+2 هذا الشهر</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">الحصص هذا الأسبوع</div>
                            <div class="stat-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="weekSessions">0</div>
                        <div class="stat-change neutral">
                            <i class="fas fa-minus"></i>
                            <span>حسب الجدول</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">متوسط الحضور</div>
                            <div class="stat-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="avgAttendance">0%</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+5% من الشهر الماضي</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">التقييمات المعلقة</div>
                            <div class="stat-icon">
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="pendingEvaluations">0</div>
                        <div class="stat-change negative">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>يحتاج متابعة</span>
                        </div>
                    </div>
                </div>

                <!-- رسم بياني للأداء -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">أداء اللاعبين (آخر 6 أشهر)</h3>
                        <div class="card-actions">
                            <button class="btn" onclick="changeChartPeriod('3months')">3 أشهر</button>
                            <button class="btn primary" onclick="changeChartPeriod('6months')">6 أشهر</button>
                            <button class="btn" onclick="changeChartPeriod('1year')">سنة</button>
                        </div>
                    </div>
                    <div style="position: relative; height: 300px;">
                        <canvas id="performanceChart"></canvas>
                    </div>
                </div>

                <!-- الحصص القادمة -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">الحصص القادمة</h3>
                        <div class="card-actions">
                            <button class="btn primary" onclick="addNewSession()">
                                <i class="fas fa-plus"></i>
                                إضافة حصة جديدة
                            </button>
                        </div>
                    </div>
                    <div id="upcomingSessionsList">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <!-- الأقسام الأخرى -->
            <div id="playersSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">إدارة اللاعبين</h3>
                        <div class="card-actions">
                            <button class="btn primary" onclick="addNewPlayer()">
                                <i class="fas fa-user-plus"></i>
                                إضافة لاعب جديد
                            </button>
                        </div>
                    </div>
                    <div id="playersContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <div id="scheduleSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">جدولة التدريبات</h3>
                        <div class="card-actions">
                            <button class="btn primary" onclick="addNewSession()">
                                <i class="fas fa-calendar-plus"></i>
                                إضافة حصة جديدة
                            </button>
                        </div>
                    </div>
                    <div id="scheduleContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <div id="performanceSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">تقييم الأداء</h3>
                    </div>
                    <div id="performanceContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <div id="reportsSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">التقارير</h3>
                    </div>
                    <div id="reportsContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <div id="messagesSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">الرسائل</h3>
                    </div>
                    <div id="messagesContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <div id="settingsSection" class="content-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">الإعدادات</h3>
                    </div>
                    <div id="settingsContent">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // ==================== متغيرات عامة ====================
        let currentCoach = null;
        let coachData = {};
        let performanceChart = null;

        // ==================== بيانات المدربين التجريبية ====================
        const coachesDatabase = {
            'C001': {
                id: 'C001',
                name: 'كابتن أحمد المدرب',
                email: '<EMAIL>',
                phone: '0509876543',
                specialization: 'كرة القدم',
                experience: 8,
                certification: 'مدرب معتمد من الاتحاد السعودي',
                joinDate: '2020-01-15',
                avatar: null,
                assignedPlayers: ['P001', 'P002', 'P003'],
                totalSessions: 156,
                avgAttendance: 87,
                rating: 4.8
            },
            'C002': {
                id: 'C002',
                name: 'كابتن سارة المدربة',
                email: '<EMAIL>',
                phone: '0551234567',
                specialization: 'كرة السلة',
                experience: 5,
                certification: 'مدربة معتمدة دولياً',
                joinDate: '2021-03-10',
                avatar: null,
                assignedPlayers: ['P004', 'P005'],
                totalSessions: 98,
                avgAttendance: 92,
                rating: 4.9
            },
            'C003': {
                id: 'C003',
                name: 'كابتن محمد الرياضي',
                email: '<EMAIL>',
                phone: '0556789012',
                specialization: 'السباحة',
                experience: 12,
                certification: 'مدرب أولمبي سابق',
                joinDate: '2019-06-20',
                avatar: null,
                assignedPlayers: ['P006', 'P007', 'P008'],
                totalSessions: 234,
                avgAttendance: 85,
                rating: 4.7
            }
        };

        // ==================== بيانات اللاعبين المخصصين للمدرب ====================
        const playersDatabase = {
            'P001': {
                id: 'P001',
                name: 'محمد اللاعب',
                email: '<EMAIL>',
                phone: '0551122334',
                age: 18,
                coachId: 'C001',
                parentId: 'PAR001',
                level: 'متقدم',
                joinDate: '2024-01-15',
                attendanceRate: 85,
                performanceScore: 92,
                lastEvaluation: '2024-06-20',
                nextEvaluation: '2024-07-20',
                strengths: ['التسديد', 'السرعة', 'التحكم بالكرة'],
                weaknesses: ['التمرير الطويل', 'اللعب الجماعي'],
                goals: ['تحسين دقة التسديد', 'زيادة السرعة']
            },
            'P002': {
                id: 'P002',
                name: 'فاطمة سعد الدين',
                email: '<EMAIL>',
                phone: '0559876543',
                age: 14,
                coachId: 'C001',
                parentId: 'PAR002',
                level: 'متوسط',
                joinDate: '2024-02-10',
                attendanceRate: 78,
                performanceScore: 88,
                lastEvaluation: '2024-06-15',
                nextEvaluation: '2024-07-15',
                strengths: ['المراوغة', 'الانضباط'],
                weaknesses: ['القوة البدنية', 'التسديد'],
                goals: ['تطوير القوة البدنية', 'تحسين التسديد']
            },
            'P003': {
                id: 'P003',
                name: 'أحمد عبدالله',
                email: '<EMAIL>',
                phone: '0555555555',
                age: 16,
                coachId: 'C001',
                parentId: 'PAR003',
                level: 'مبتدئ',
                joinDate: '2024-03-05',
                attendanceRate: 65,
                performanceScore: 75,
                lastEvaluation: '2024-06-10',
                nextEvaluation: '2024-07-10',
                strengths: ['الحماس', 'التعلم السريع'],
                weaknesses: ['المهارات الأساسية', 'اللياقة البدنية'],
                goals: ['إتقان المهارات الأساسية', 'تحسين اللياقة']
            }
        };

        // ==================== بيانات الحصص التدريبية ====================
        const sessionsDatabase = [
            {
                id: 'S001',
                coachId: 'C001',
                title: 'تدريب كرة القدم - تقنيات الهجوم',
                date: '2024-06-25',
                time: '16:00',
                duration: 90,
                location: 'الملعب الرئيسي',
                type: 'جماعي',
                status: 'مجدولة',
                participants: ['P001', 'P002', 'P003'],
                description: 'تدريب على تقنيات الهجوم والتسديد',
                objectives: ['تحسين دقة التسديد', 'تطوير اللعب الجماعي'],
                equipment: ['كرات قدم', 'أقماع', 'مرمى متنقل']
            },
            {
                id: 'S002',
                coachId: 'C001',
                title: 'تدريب اللياقة البدنية',
                date: '2024-06-23',
                time: '17:00',
                duration: 60,
                location: 'صالة اللياقة',
                type: 'فردي',
                status: 'مكتملة',
                participants: ['P001'],
                description: 'تدريب على تحسين اللياقة البدنية والقوة',
                objectives: ['زيادة القوة', 'تحسين التحمل'],
                equipment: ['أوزان', 'حبال'],
                evaluation: {
                    attendance: ['P001'],
                    performance: { 'P001': 9 },
                    notes: 'أداء ممتاز، تحسن ملحوظ في القوة'
                }
            }
        ];

        // ==================== بيانات التقييمات ====================
        const evaluationsDatabase = [
            {
                id: 'E001',
                playerId: 'P001',
                coachId: 'C001',
                date: '2024-06-20',
                type: 'تقييم شهري',
                scores: {
                    technical: 9,
                    physical: 8,
                    tactical: 7,
                    mental: 9,
                    overall: 8.25
                },
                strengths: ['التسديد الدقيق', 'السرعة العالية', 'الروح القتالية'],
                weaknesses: ['التمرير الطويل', 'اللعب الدفاعي'],
                recommendations: ['التركيز على التمرير', 'تدريبات دفاعية إضافية'],
                nextGoals: ['تحسين دقة التمرير بنسبة 20%', 'تطوير المهارات الدفاعية'],
                notes: 'لاعب موهوب يحتاج لتطوير الجانب التكتيكي'
            },
            {
                id: 'E002',
                playerId: 'P002',
                coachId: 'C001',
                date: '2024-06-15',
                type: 'تقييم شهري',
                scores: {
                    technical: 8,
                    physical: 6,
                    tactical: 8,
                    mental: 9,
                    overall: 7.75
                },
                strengths: ['المراوغة الممتازة', 'الذكاء التكتيكي', 'الانضباط'],
                weaknesses: ['القوة البدنية', 'التسديد من المسافات البعيدة'],
                recommendations: ['برنامج تقوية بدنية', 'تدريبات تسديد مكثفة'],
                nextGoals: ['زيادة القوة البدنية', 'تحسين دقة التسديد'],
                notes: 'لاعبة ذكية تحتاج لتطوير الجانب البدني'
            }
        ];

        // ==================== التهيئة عند تحميل الصفحة ====================
        document.addEventListener('DOMContentLoaded', function() {
            initializeCoachDashboard();
            loadCoachSession();
            updateDashboard();
            setupEventListeners();

            console.log('👨‍🏫 لوحة تحكم المدرب جاهزة!');
        });

        function initializeCoachDashboard() {
            // تحميل البيانات من localStorage أو إنشاء بيانات تجريبية
            const savedData = localStorage.getItem('coachDashboardData');
            if (savedData) {
                coachData = JSON.parse(savedData);
            } else {
                // إنشاء بيانات تجريبية
                coachData = {
                    coaches: coachesDatabase,
                    players: playersDatabase,
                    sessions: sessionsDatabase,
                    evaluations: evaluationsDatabase
                };
                saveCoachData();
            }
        }

        function loadCoachSession() {
            // تحميل بيانات المدرب من الجلسة
            const sessionData = sessionStorage.getItem('userSession');
            if (sessionData) {
                try {
                    const decryptedData = decryptData(sessionData);
                    if (decryptedData && decryptedData.user.role === 'coach') {
                        currentCoach = findCoachByEmail(decryptedData.user.email);
                        if (currentCoach) {
                            updateCoachInfo();
                        }
                    }
                } catch (error) {
                    console.error('خطأ في تحميل بيانات الجلسة:', error);
                    // للاختبار - تحميل بيانات افتراضية
                    currentCoach = coachData.coaches['C001'];
                    updateCoachInfo();
                }
            } else {
                // للاختبار - تحميل بيانات افتراضية
                currentCoach = coachData.coaches['C001'];
                updateCoachInfo();
            }
        }

        function findCoachByEmail(email) {
            for (let coachId in coachData.coaches) {
                if (coachData.coaches[coachId].email === email) {
                    return coachData.coaches[coachId];
                }
            }
            return null;
        }

        function decryptData(encryptedData) {
            try {
                const bytes = CryptoJS.AES.decrypt(encryptedData, 'academy7c_secret');
                return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
            } catch (e) {
                throw new Error('فشل في فك التشفير');
            }
        }

        function saveCoachData() {
            localStorage.setItem('coachDashboardData', JSON.stringify(coachData));
        }

        // ==================== تحديث معلومات المدرب ====================
        function updateCoachInfo() {
            if (!currentCoach) return;

            document.getElementById('coachName').textContent = currentCoach.name;
            document.getElementById('coachTitle').textContent = `مدرب ${currentCoach.specialization}`;

            // تحديث الإحصائيات
            const assignedPlayers = currentCoach.assignedPlayers || [];
            const coachSessions = coachData.sessions.filter(s => s.coachId === currentCoach.id);
            const weekSessions = coachSessions.filter(s => {
                const sessionDate = new Date(s.date);
                const today = new Date();
                const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
                const weekEnd = new Date(weekStart);
                weekEnd.setDate(weekEnd.getDate() + 6);
                return sessionDate >= weekStart && sessionDate <= weekEnd;
            });

            const pendingEvaluations = assignedPlayers.filter(playerId => {
                const player = coachData.players[playerId];
                if (!player) return false;
                const nextEval = new Date(player.nextEvaluation);
                return nextEval <= new Date();
            });

            document.getElementById('totalPlayers').textContent = assignedPlayers.length;
            document.getElementById('weekSessions').textContent = weekSessions.length;
            document.getElementById('avgAttendance').textContent = currentCoach.avgAttendance + '%';
            document.getElementById('pendingEvaluations').textContent = pendingEvaluations.length;

            // تحديث الشارات
            updateNavigationBadges();
        }

        function updateNavigationBadges() {
            if (!currentCoach) return;

            const assignedPlayers = currentCoach.assignedPlayers || [];
            document.getElementById('totalPlayersCount').textContent = assignedPlayers.length;

            const upcomingSessions = coachData.sessions.filter(s =>
                s.coachId === currentCoach.id && s.status === 'مجدولة'
            );
            document.getElementById('upcomingSessionsCount').textContent = upcomingSessions.length;

            // حساب الرسائل غير المقروءة (محاكاة)
            document.getElementById('unreadMessagesCount').textContent = '2';
        }

        // ==================== تحديث لوحة المعلومات ====================
        function updateDashboard() {
            updateUpcomingSessionsList();
            updatePlayersContent();
            updateScheduleContent();
            updatePerformanceContent();
            updateReportsContent();
            updateMessagesContent();
            updateSettingsContent();
            createPerformanceChart();
        }

        function updateUpcomingSessionsList() {
            if (!currentCoach) return;

            const upcomingSessions = coachData.sessions
                .filter(s => s.coachId === currentCoach.id && s.status === 'مجدولة')
                .sort((a, b) => new Date(a.date + ' ' + a.time) - new Date(b.date + ' ' + b.time))
                .slice(0, 3);

            const container = document.getElementById('upcomingSessionsList');

            if (upcomingSessions.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">لا توجد حصص قادمة</p>';
                return;
            }

            container.innerHTML = upcomingSessions.map(session => `
                <div style="
                    background: rgba(255,255,255,0.05);
                    border: 1px solid var(--border);
                    border-radius: 12px;
                    padding: 1rem;
                    margin-bottom: 1rem;
                    transition: all 0.3s ease;
                " onmouseover="this.style.background='rgba(255,255,255,0.1)'"
                   onmouseout="this.style.background='rgba(255,255,255,0.05)'">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                        <h4 style="color: var(--text-primary); font-size: 1rem;">${session.title}</h4>
                        <span style="
                            background: var(--brand-primary);
                            color: white;
                            padding: 0.2rem 0.5rem;
                            border-radius: 6px;
                            font-size: 0.8rem;
                        ">${session.type}</span>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; font-size: 0.9rem; color: var(--text-secondary);">
                        <div><i class="fas fa-calendar" style="margin-left: 0.5rem;"></i>${formatDate(session.date)}</div>
                        <div><i class="fas fa-clock" style="margin-left: 0.5rem;"></i>${session.time}</div>
                        <div><i class="fas fa-map-marker-alt" style="margin-left: 0.5rem;"></i>${session.location}</div>
                        <div><i class="fas fa-users" style="margin-left: 0.5rem;"></i>${session.participants.length} لاعب</div>
                    </div>
                    <div style="margin-top: 1rem;">
                        <button onclick="editSession('${session.id}')" style="
                            background: var(--info);
                            color: white;
                            border: none;
                            padding: 0.4rem 0.8rem;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 0.8rem;
                            margin-left: 0.5rem;
                        ">تعديل</button>
                        <button onclick="startSession('${session.id}')" style="
                            background: var(--success);
                            color: white;
                            border: none;
                            padding: 0.4rem 0.8rem;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 0.8rem;
                        ">بدء الحصة</button>
                    </div>
                </div>
            `).join('');
        }

        // ==================== تحديث محتوى إدارة اللاعبين ====================
        function updatePlayersContent() {
            if (!currentCoach) return;

            const assignedPlayers = currentCoach.assignedPlayers || [];
            const players = assignedPlayers.map(id => coachData.players[id]).filter(p => p);

            const container = document.getElementById('playersContent');

            if (players.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                        <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>لا يوجد لاعبون مخصصون لك بعد</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = players.map(player => `
                <div style="
                    background: rgba(255,255,255,0.05);
                    border: 1px solid var(--border);
                    border-radius: 12px;
                    padding: 1.5rem;
                    margin-bottom: 1rem;
                    transition: all 0.3s ease;
                " onmouseover="this.style.background='rgba(255,255,255,0.1)'"
                   onmouseout="this.style.background='rgba(255,255,255,0.05)'">

                    <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 1rem;">
                        <div style="display: flex; align-items: center; gap: 1rem;">
                            <div style="
                                width: 60px;
                                height: 60px;
                                background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 1.5rem;
                                color: white;
                            ">
                                <i class="fas fa-user"></i>
                            </div>
                            <div>
                                <h4 style="color: var(--text-primary); font-size: 1.1rem; margin-bottom: 0.5rem;">
                                    ${player.name}
                                </h4>
                                <div style="display: flex; align-items: center; gap: 0.5rem;">
                                    <span style="
                                        background: var(--brand-primary);
                                        color: white;
                                        padding: 0.2rem 0.6rem;
                                        border-radius: 12px;
                                        font-size: 0.8rem;
                                    ">${player.level}</span>
                                    <span style="color: var(--text-secondary); font-size: 0.9rem;">
                                        ${player.age} سنة
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: left;">
                            <div style="color: var(--success); font-size: 1.2rem; font-weight: bold;">
                                ${player.performanceScore}/100
                            </div>
                            <div style="color: var(--text-secondary); font-size: 0.8rem;">
                                درجة الأداء
                            </div>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                        <div style="text-align: center; padding: 0.5rem; background: rgba(40, 167, 69, 0.1); border-radius: 8px;">
                            <div style="color: #28a745; font-weight: bold;">${player.attendanceRate}%</div>
                            <div style="color: var(--text-secondary); font-size: 0.8rem;">معدل الحضور</div>
                        </div>
                        <div style="text-align: center; padding: 0.5rem; background: rgba(23, 162, 184, 0.1); border-radius: 8px;">
                            <div style="color: #17a2b8; font-weight: bold;">${formatDate(player.lastEvaluation)}</div>
                            <div style="color: var(--text-secondary); font-size: 0.8rem;">آخر تقييم</div>
                        </div>
                        <div style="text-align: center; padding: 0.5rem; background: rgba(255, 193, 7, 0.1); border-radius: 8px;">
                            <div style="color: #ffc107; font-weight: bold;">${formatDate(player.nextEvaluation)}</div>
                            <div style="color: var(--text-secondary); font-size: 0.8rem;">التقييم القادم</div>
                        </div>
                    </div>

                    <div style="margin-bottom: 1rem;">
                        <div style="margin-bottom: 0.5rem;">
                            <strong style="color: var(--text-primary);">نقاط القوة:</strong>
                            <div style="margin-top: 0.3rem;">
                                ${player.strengths.map(s => `
                                    <span style="
                                        background: rgba(40, 167, 69, 0.2);
                                        color: #28a745;
                                        padding: 0.2rem 0.5rem;
                                        border-radius: 8px;
                                        font-size: 0.8rem;
                                        margin-left: 0.3rem;
                                        display: inline-block;
                                        margin-bottom: 0.3rem;
                                    ">${s}</span>
                                `).join('')}
                            </div>
                        </div>
                        <div>
                            <strong style="color: var(--text-primary);">نقاط التحسين:</strong>
                            <div style="margin-top: 0.3rem;">
                                ${player.weaknesses.map(w => `
                                    <span style="
                                        background: rgba(220, 53, 69, 0.2);
                                        color: #dc3545;
                                        padding: 0.2rem 0.5rem;
                                        border-radius: 8px;
                                        font-size: 0.8rem;
                                        margin-left: 0.3rem;
                                        display: inline-block;
                                        margin-bottom: 0.3rem;
                                    ">${w}</span>
                                `).join('')}
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                        <button onclick="evaluatePlayer('${player.id}')" style="
                            background: var(--brand-primary);
                            color: white;
                            border: none;
                            padding: 0.5rem 1rem;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 0.9rem;
                            display: flex;
                            align-items: center;
                            gap: 0.3rem;
                        ">
                            <i class="fas fa-star"></i>
                            تقييم الأداء
                        </button>

                        <button onclick="viewPlayerDetails('${player.id}')" style="
                            background: var(--info);
                            color: white;
                            border: none;
                            padding: 0.5rem 1rem;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 0.9rem;
                            display: flex;
                            align-items: center;
                            gap: 0.3rem;
                        ">
                            <i class="fas fa-eye"></i>
                            عرض التفاصيل
                        </button>

                        <button onclick="sendMessageToParent('${player.parentId}')" style="
                            background: var(--success);
                            color: white;
                            border: none;
                            padding: 0.5rem 1rem;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 0.9rem;
                            display: flex;
                            align-items: center;
                            gap: 0.3rem;
                        ">
                            <i class="fas fa-envelope"></i>
                            رسالة لولي الأمر
                        </button>

                        <button onclick="generatePlayerReport('${player.id}')" style="
                            background: transparent;
                            color: var(--text-secondary);
                            border: 1px solid var(--border);
                            padding: 0.5rem 1rem;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 0.9rem;
                            display: flex;
                            align-items: center;
                            gap: 0.3rem;
                        ">
                            <i class="fas fa-file-alt"></i>
                            تقرير شامل
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // ==================== تحديث محتوى الجدولة ====================
        function updateScheduleContent() {
            if (!currentCoach) return;

            const coachSessions = coachData.sessions.filter(s => s.coachId === currentCoach.id);
            coachSessions.sort((a, b) => new Date(b.date + ' ' + b.time) - new Date(a.date + ' ' + a.time));

            const container = document.getElementById('scheduleContent');

            if (coachSessions.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                        <i class="fas fa-calendar-times" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>لا توجد حصص مجدولة</p>
                        <button onclick="addNewSession()" style="
                            background: var(--brand-primary);
                            color: white;
                            border: none;
                            padding: 0.8rem 1.5rem;
                            border-radius: 8px;
                            cursor: pointer;
                            margin-top: 1rem;
                        ">
                            <i class="fas fa-plus"></i> إضافة حصة جديدة
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = coachSessions.map(session => {
                const statusColor = session.status === 'مجدولة' ? '#17a2b8' :
                                  session.status === 'مكتملة' ? '#28a745' : '#dc3545';
                const statusIcon = session.status === 'مجدولة' ? 'clock' :
                                 session.status === 'مكتملة' ? 'check-circle' : 'times-circle';

                return `
                    <div style="
                        background: rgba(255,255,255,0.05);
                        border: 1px solid var(--border);
                        border-radius: 12px;
                        padding: 1.5rem;
                        margin-bottom: 1rem;
                        transition: all 0.3s ease;
                        border-right: 4px solid ${statusColor};
                    " onmouseover="this.style.background='rgba(255,255,255,0.1)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.05)'">

                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 1rem;">
                            <div>
                                <h4 style="color: var(--text-primary); font-size: 1.1rem; margin-bottom: 0.5rem;">
                                    ${session.title}
                                </h4>
                                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                                    <span style="
                                        background: ${statusColor};
                                        color: white;
                                        padding: 0.2rem 0.6rem;
                                        border-radius: 12px;
                                        font-size: 0.8rem;
                                        display: flex;
                                        align-items: center;
                                        gap: 0.3rem;
                                    ">
                                        <i class="fas fa-${statusIcon}"></i>
                                        ${session.status}
                                    </span>
                                    <span style="
                                        background: var(--brand-primary);
                                        color: white;
                                        padding: 0.2rem 0.6rem;
                                        border-radius: 12px;
                                        font-size: 0.8rem;
                                    ">${session.type}</span>
                                </div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                            <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                                <i class="fas fa-calendar" style="color: var(--brand-primary);"></i>
                                <span>${formatDate(session.date)}</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                                <i class="fas fa-clock" style="color: var(--brand-primary);"></i>
                                <span>${session.time} (${session.duration} دقيقة)</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                                <i class="fas fa-map-marker-alt" style="color: var(--brand-primary);"></i>
                                <span>${session.location}</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                                <i class="fas fa-users" style="color: var(--brand-primary);"></i>
                                <span>${session.participants.length} لاعب</span>
                            </div>
                        </div>

                        <p style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 1rem; line-height: 1.5;">
                            ${session.description}
                        </p>

                        <div style="margin-bottom: 1rem;">
                            <strong style="color: var(--text-primary);">الأهداف:</strong>
                            <ul style="margin: 0.5rem 0; padding-right: 1.5rem; color: var(--text-secondary);">
                                ${session.objectives.map(obj => `<li>${obj}</li>`).join('')}
                            </ul>
                        </div>

                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                            ${session.status === 'مجدولة' ? `
                                <button onclick="startSession('${session.id}')" style="
                                    background: var(--success);
                                    color: white;
                                    border: none;
                                    padding: 0.5rem 1rem;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 0.9rem;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.3rem;
                                ">
                                    <i class="fas fa-play"></i>
                                    بدء الحصة
                                </button>
                                <button onclick="editSession('${session.id}')" style="
                                    background: var(--info);
                                    color: white;
                                    border: none;
                                    padding: 0.5rem 1rem;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 0.9rem;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.3rem;
                                ">
                                    <i class="fas fa-edit"></i>
                                    تعديل
                                </button>
                                <button onclick="cancelSession('${session.id}')" style="
                                    background: var(--danger);
                                    color: white;
                                    border: none;
                                    padding: 0.5rem 1rem;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 0.9rem;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.3rem;
                                ">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </button>
                            ` : session.status === 'مكتملة' ? `
                                <button onclick="viewSessionReport('${session.id}')" style="
                                    background: var(--brand-primary);
                                    color: white;
                                    border: none;
                                    padding: 0.5rem 1rem;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 0.9rem;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.3rem;
                                ">
                                    <i class="fas fa-file-alt"></i>
                                    عرض التقرير
                                </button>
                            ` : ''}

                            <button onclick="duplicateSession('${session.id}')" style="
                                background: transparent;
                                color: var(--text-secondary);
                                border: 1px solid var(--border);
                                padding: 0.5rem 1rem;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 0.9rem;
                                display: flex;
                                align-items: center;
                                gap: 0.3rem;
                            ">
                                <i class="fas fa-copy"></i>
                                نسخ
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // ==================== وظائف التفاعل مع الحصص ====================
        function addNewSession() {
            if (!currentCoach) return;

            const assignedPlayers = currentCoach.assignedPlayers || [];
            const playersOptions = assignedPlayers.map(id => {
                const player = coachData.players[id];
                return player ? `<option value="${id}">${player.name}</option>` : '';
            }).join('');

            Swal.fire({
                title: 'إضافة حصة تدريبية جديدة',
                html: `
                    <div style="text-align: right;">
                        <label style="display: block; margin-bottom: 0.5rem;">عنوان الحصة:</label>
                        <input id="sessionTitle" placeholder="مثال: تدريب كرة القدم - تقنيات الهجوم" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem;">التاريخ:</label>
                                <input id="sessionDate" type="date" style="width: 100%; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem;">الوقت:</label>
                                <input id="sessionTime" type="time" style="width: 100%; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem;">المدة (دقيقة):</label>
                                <input id="sessionDuration" type="number" value="90" style="width: 100%; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem;">نوع التدريب:</label>
                                <select id="sessionType" style="width: 100%; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                                    <option value="جماعي">جماعي</option>
                                    <option value="فردي">فردي</option>
                                    <option value="مباراة">مباراة</option>
                                </select>
                            </div>
                        </div>

                        <label style="display: block; margin-bottom: 0.5rem;">المكان:</label>
                        <input id="sessionLocation" placeholder="مثال: الملعب الرئيسي" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">

                        <label style="display: block; margin-bottom: 0.5rem;">اللاعبون المشاركون:</label>
                        <select id="sessionParticipants" multiple style="width: 100%; height: 100px; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                            ${playersOptions}
                        </select>

                        <label style="display: block; margin-bottom: 0.5rem;">وصف التدريب:</label>
                        <textarea id="sessionDescription" placeholder="وصف مفصل للحصة التدريبية..." style="width: 100%; height: 80px; margin-bottom: 1rem; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white; resize: vertical;"></textarea>

                        <label style="display: block; margin-bottom: 0.5rem;">أهداف التدريب (كل هدف في سطر منفصل):</label>
                        <textarea id="sessionObjectives" placeholder="تحسين دقة التسديد\nتطوير اللعب الجماعي" style="width: 100%; height: 60px; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white; resize: vertical;"></textarea>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'إضافة الحصة',
                cancelButtonText: 'إلغاء',
                width: '600px',
                preConfirm: () => {
                    const title = document.getElementById('sessionTitle').value;
                    const date = document.getElementById('sessionDate').value;
                    const time = document.getElementById('sessionTime').value;
                    const duration = parseInt(document.getElementById('sessionDuration').value);
                    const type = document.getElementById('sessionType').value;
                    const location = document.getElementById('sessionLocation').value;
                    const description = document.getElementById('sessionDescription').value;
                    const objectives = document.getElementById('sessionObjectives').value;

                    const participantsSelect = document.getElementById('sessionParticipants');
                    const participants = Array.from(participantsSelect.selectedOptions).map(option => option.value);

                    if (!title || !date || !time || !location || !description || participants.length === 0) {
                        Swal.showValidationMessage('يرجى ملء جميع الحقول المطلوبة واختيار لاعب واحد على الأقل');
                        return false;
                    }

                    return {
                        title, date, time, duration, type, location, description,
                        participants, objectives: objectives.split('\n').filter(o => o.trim())
                    };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    const newSession = {
                        id: 'S' + Date.now(),
                        coachId: currentCoach.id,
                        title: result.value.title,
                        date: result.value.date,
                        time: result.value.time,
                        duration: result.value.duration,
                        location: result.value.location,
                        type: result.value.type,
                        status: 'مجدولة',
                        participants: result.value.participants,
                        description: result.value.description,
                        objectives: result.value.objectives,
                        equipment: []
                    };

                    coachData.sessions.push(newSession);
                    saveCoachData();
                    updateDashboard();
                    updateNavigationBadges();

                    Swal.fire({
                        title: 'تم إضافة الحصة!',
                        text: 'تم إضافة الحصة التدريبية بنجاح',
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        function evaluatePlayer(playerId) {
            const player = coachData.players[playerId];
            if (!player) return;

            Swal.fire({
                title: `تقييم أداء ${player.name}`,
                html: `
                    <div style="text-align: right;">
                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem;">المهارات الفنية (1-10):</label>
                            <input id="technicalScore" type="number" min="1" max="10" value="8" style="width: 100%; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                        </div>

                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem;">اللياقة البدنية (1-10):</label>
                            <input id="physicalScore" type="number" min="1" max="10" value="7" style="width: 100%; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                        </div>

                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem;">الفهم التكتيكي (1-10):</label>
                            <input id="tacticalScore" type="number" min="1" max="10" value="8" style="width: 100%; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                        </div>

                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem;">الجانب النفسي (1-10):</label>
                            <input id="mentalScore" type="number" min="1" max="10" value="9" style="width: 100%; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white;">
                        </div>

                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem;">نقاط القوة (كل نقطة في سطر منفصل):</label>
                            <textarea id="playerStrengths" style="width: 100%; height: 80px; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white; resize: vertical;">${player.strengths.join('\n')}</textarea>
                        </div>

                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem;">نقاط التحسين (كل نقطة في سطر منفصل):</label>
                            <textarea id="playerWeaknesses" style="width: 100%; height: 80px; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white; resize: vertical;">${player.weaknesses.join('\n')}</textarea>
                        </div>

                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem;">التوصيات:</label>
                            <textarea id="playerRecommendations" placeholder="توصيات لتحسين الأداء..." style="width: 100%; height: 80px; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white; resize: vertical;"></textarea>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 0.5rem;">ملاحظات إضافية:</label>
                            <textarea id="playerNotes" placeholder="ملاحظات عامة حول الأداء..." style="width: 100%; height: 60px; padding: 0.5rem; border-radius: 5px; border: 1px solid #333; background: #2a2a2a; color: white; resize: vertical;"></textarea>
                        </div>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'حفظ التقييم',
                cancelButtonText: 'إلغاء',
                width: '600px',
                preConfirm: () => {
                    const technical = parseInt(document.getElementById('technicalScore').value);
                    const physical = parseInt(document.getElementById('physicalScore').value);
                    const tactical = parseInt(document.getElementById('tacticalScore').value);
                    const mental = parseInt(document.getElementById('mentalScore').value);
                    const strengths = document.getElementById('playerStrengths').value;
                    const weaknesses = document.getElementById('playerWeaknesses').value;
                    const recommendations = document.getElementById('playerRecommendations').value;
                    const notes = document.getElementById('playerNotes').value;

                    if (!technical || !physical || !tactical || !mental) {
                        Swal.showValidationMessage('يرجى ملء جميع درجات التقييم');
                        return false;
                    }

                    return {
                        technical, physical, tactical, mental,
                        strengths: strengths.split('\n').filter(s => s.trim()),
                        weaknesses: weaknesses.split('\n').filter(w => w.trim()),
                        recommendations: recommendations.split('\n').filter(r => r.trim()),
                        notes
                    };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    const overall = (result.value.technical + result.value.physical + result.value.tactical + result.value.mental) / 4;

                    const newEvaluation = {
                        id: 'E' + Date.now(),
                        playerId: playerId,
                        coachId: currentCoach.id,
                        date: new Date().toISOString().split('T')[0],
                        type: 'تقييم دوري',
                        scores: {
                            technical: result.value.technical,
                            physical: result.value.physical,
                            tactical: result.value.tactical,
                            mental: result.value.mental,
                            overall: parseFloat(overall.toFixed(2))
                        },
                        strengths: result.value.strengths,
                        weaknesses: result.value.weaknesses,
                        recommendations: result.value.recommendations,
                        notes: result.value.notes
                    };

                    // تحديث بيانات اللاعب
                    player.performanceScore = Math.round(overall * 10);
                    player.lastEvaluation = newEvaluation.date;
                    player.strengths = result.value.strengths;
                    player.weaknesses = result.value.weaknesses;

                    // تحديث تاريخ التقييم القادم (شهر من الآن)
                    const nextEvalDate = new Date();
                    nextEvalDate.setMonth(nextEvalDate.getMonth() + 1);
                    player.nextEvaluation = nextEvalDate.toISOString().split('T')[0];

                    coachData.evaluations.push(newEvaluation);
                    saveCoachData();
                    updateDashboard();

                    Swal.fire({
                        title: 'تم حفظ التقييم!',
                        text: `تم تقييم ${player.name} بنجاح. الدرجة الإجمالية: ${overall.toFixed(1)}/10`,
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        // ==================== وظائف مساعدة ====================
        function formatDate(dateString) {
            const date = new Date(dateString);
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            };
            return date.toLocaleDateString('ar-SA', options);
        }

        function showSection(sectionName) {
            // إخفاء جميع الأقسام
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
                section.classList.remove('active');
            });

            // إزالة الحالة النشطة من جميع الروابط
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // إظهار القسم المحدد
            const targetSection = document.getElementById(sectionName + 'Section');
            if (targetSection) {
                targetSection.style.display = 'block';
                targetSection.classList.add('active');
            }

            // تفعيل الرابط المحدد
            event.target.closest('.nav-link').classList.add('active');

            // تحديث عنوان الصفحة
            updatePageTitle(sectionName);
        }

        function updatePageTitle(sectionName) {
            const titles = {
                'overview': 'نظرة عامة',
                'players': 'إدارة اللاعبين',
                'schedule': 'جدولة التدريبات',
                'performance': 'تقييم الأداء',
                'reports': 'التقارير',
                'messages': 'الرسائل',
                'settings': 'الإعدادات'
            };

            const title = titles[sectionName] || 'لوحة التحكم';
            document.getElementById('pageTitle').textContent = title;
            document.getElementById('breadcrumb').textContent = `الرئيسية / ${title}`;
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }

        function createPerformanceChart() {
            const ctx = document.getElementById('performanceChart');
            if (!ctx) return;

            // بيانات تجريبية لأداء اللاعبين
            const performanceData = [75, 78, 82, 85, 88, 92];
            const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'];

            if (performanceChart) {
                performanceChart.destroy();
            }

            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: months,
                    datasets: [{
                        label: 'متوسط أداء اللاعبين',
                        data: performanceData,
                        borderColor: '#8B4513',
                        backgroundColor: 'rgba(139, 69, 19, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#D2691E',
                        pointBorderColor: '#8B4513',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff',
                                font: {
                                    family: 'Cairo',
                                    size: 12
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 60,
                            max: 100,
                            ticks: {
                                color: '#cccccc',
                                font: {
                                    family: 'Cairo'
                                }
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: '#cccccc',
                                font: {
                                    family: 'Cairo'
                                }
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    }
                }
            });
        }

        // ==================== وظائف أساسية أخرى ====================
        function updatePerformanceContent() {
            const container = document.getElementById('performanceContent');
            container.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">قسم تقييم الأداء قيد التطوير...</p>';
        }

        function updateReportsContent() {
            const container = document.getElementById('reportsContent');
            container.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">قسم التقارير قيد التطوير...</p>';
        }

        function updateMessagesContent() {
            const container = document.getElementById('messagesContent');
            container.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">قسم الرسائل قيد التطوير...</p>';
        }

        function updateSettingsContent() {
            const container = document.getElementById('settingsContent');
            container.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">قسم الإعدادات قيد التطوير...</p>';
        }

        function setupEventListeners() {
            // إضافة مستمعي الأحداث للتفاعل
            document.addEventListener('keydown', function(e) {
                if (e.altKey) {
                    switch(e.key) {
                        case '1': showSection('overview'); break;
                        case '2': showSection('players'); break;
                        case '3': showSection('schedule'); break;
                        case '4': showSection('performance'); break;
                        case '5': showSection('reports'); break;
                        case '6': showSection('messages'); break;
                        case '7': showSection('settings'); break;
                    }
                }
            });
        }

        function showNotifications() {
            Swal.fire({
                title: 'الإشعارات',
                html: `
                    <div style="text-align: right;">
                        <div style="padding: 1rem; border-bottom: 1px solid #333;">
                            <strong>تقييم معلق</strong><br>
                            <small style="color: #888;">يجب تقييم أداء محمد اللاعب</small>
                        </div>
                        <div style="padding: 1rem; border-bottom: 1px solid #333;">
                            <strong>حصة قادمة</strong><br>
                            <small style="color: #888;">تدريب كرة القدم غداً الساعة 4 مساءً</small>
                        </div>
                        <div style="padding: 1rem;">
                            <strong>رسالة جديدة</strong><br>
                            <small style="color: #888;">رسالة من ولي أمر فاطمة سعد الدين</small>
                        </div>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                confirmButtonText: 'إغلاق'
            });
        }

        function showQuickActions() {
            Swal.fire({
                title: 'إجراءات سريعة',
                html: `
                    <div style="display: grid; gap: 1rem; text-align: center;">
                        <button onclick="addNewSession()" class="swal2-confirm swal2-styled">إضافة حصة جديدة</button>
                        <button onclick="evaluatePlayer('P001')" class="swal2-confirm swal2-styled">تقييم لاعب</button>
                        <button onclick="sendMessageToParent('PAR001')" class="swal2-confirm swal2-styled">رسالة لولي أمر</button>
                        <button onclick="exportCoachReport()" class="swal2-confirm swal2-styled">تصدير تقرير</button>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                showConfirmButton: false,
                showCancelButton: true,
                cancelButtonText: 'إغلاق'
            });
        }

        function exportCoachReport() {
            Swal.fire({
                title: 'تصدير تقرير المدرب',
                text: 'جاري إنشاء التقرير...',
                background: '#1a1a1a',
                color: '#ffffff',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                    setTimeout(() => {
                        Swal.close();
                        Swal.fire({
                            title: 'تم إنشاء التقرير!',
                            text: 'تم تحميل تقرير المدرب بنجاح',
                            icon: 'success',
                            background: '#1a1a1a',
                            color: '#ffffff'
                        });
                    }, 2000);
                }
            });
        }

        function logout() {
            Swal.fire({
                title: 'تسجيل الخروج',
                text: 'هل أنت متأكد من تسجيل الخروج؟',
                icon: 'question',
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'نعم، سجل خروجي',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    sessionStorage.removeItem('userSession');
                    window.location.href = 'login.html';
                }
            });
        }

        console.log('🏆 لوحة تحكم المدرب مع الذكاء الاصطناعي جاهزة!');
    </script>
</body>
</html>
