<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إحصائيات صفحتي - أكاديمية 7C</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D2691E;
            --background-dark: #1a1a1a;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--background-dark);
            color: #ffffff;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .header {
            background: linear-gradient(135deg, var(--background-dark) 0%, #2d2d2d 100%);
            border-bottom: 2px solid var(--primary-color);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .card:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .stat-card {
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 69, 19, 0.1));
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.875rem;
        }

        .grid-2 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .grid-4 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }

        .device-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .country-flag {
            width: 24px;
            height: 16px;
            margin-left: 0.5rem;
            border-radius: 2px;
        }

        .time-slot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            margin-bottom: 0.5rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transition: width 0.3s ease;
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold">إحصائيات صفحتي</h1>
                <p class="text-gray-400">تحليل مفصل لمشاهدات وزيارات ملفك الشخصي</p>
            </div>
            <div class="flex gap-2">
                <a href="player-profile.html" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للملف
                </a>
                <button onclick="exportStats()" class="btn btn-primary">
                    <i class="fas fa-download"></i>
                    تصدير التقرير
                </button>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container">
        <!-- Overview Stats -->
        <div class="grid-4 fade-in mt-6">
            <div class="stat-card">
                <div class="stat-value" id="totalViews">0</div>
                <div class="stat-label">إجمالي المشاهدات</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="todayViews">0</div>
                <div class="stat-label">مشاهدات اليوم</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="uniqueVisitors">0</div>
                <div class="stat-label">زوار فريدون</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgViewTime">0</div>
                <div class="stat-label">متوسط وقت المشاهدة</div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid-2 fade-in">
            <!-- Views Chart -->
            <div class="card">
                <h3 class="text-xl font-bold mb-4 flex items-center">
                    <i class="fas fa-chart-line ml-2 text-blue-400"></i>
                    مشاهدات آخر 7 أيام
                </h3>
                <div class="chart-container">
                    <canvas id="viewsChart"></canvas>
                </div>
            </div>

            <!-- Device Types -->
            <div class="card">
                <h3 class="text-xl font-bold mb-4 flex items-center">
                    <i class="fas fa-mobile-alt ml-2 text-green-400"></i>
                    الأجهزة المستخدمة
                </h3>
                <div class="chart-container">
                    <canvas id="devicesChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Detailed Analytics -->
        <div class="grid-2 fade-in">
            <!-- Countries -->
            <div class="card">
                <h3 class="text-xl font-bold mb-4 flex items-center">
                    <i class="fas fa-globe ml-2 text-purple-400"></i>
                    البلدان الأكثر مشاهدة
                </h3>
                <div id="countriesStats">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>

            <!-- Peak Hours -->
            <div class="card">
                <h3 class="text-xl font-bold mb-4 flex items-center">
                    <i class="fas fa-clock ml-2 text-orange-400"></i>
                    أوقات الذروة
                </h3>
                <div id="peakHoursStats">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card fade-in">
            <h3 class="text-xl font-bold mb-4 flex items-center">
                <i class="fas fa-history ml-2 text-red-400"></i>
                النشاط الأخير
            </h3>
            <div id="recentActivity">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let viewsChart, devicesChart;
        let statsData = {
            totalViews: 0,
            todayViews: 0,
            uniqueVisitors: 0,
            avgViewTime: '2:30',
            dailyViews: [12, 19, 8, 15, 22, 18, 25],
            devices: { mobile: 65, desktop: 25, tablet: 10 },
            countries: [
                { name: 'السعودية', flag: '🇸🇦', views: 45, percentage: 65 },
                { name: 'الإمارات', flag: '🇦🇪', views: 12, percentage: 17 },
                { name: 'الكويت', flag: '🇰🇼', views: 8, percentage: 12 },
                { name: 'قطر', flag: '🇶🇦', views: 4, percentage: 6 }
            ],
            peakHours: [
                { hour: '16:00-17:00', views: 15, percentage: 25 },
                { hour: '20:00-21:00', views: 12, percentage: 20 },
                { hour: '14:00-15:00', views: 10, percentage: 17 },
                { hour: '19:00-20:00', views: 8, percentage: 13 }
            ]
        };

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadStatsData();
            updateOverviewStats();
            initializeCharts();
            populateCountriesStats();
            populatePeakHoursStats();
            populateRecentActivity();
        });

        // Load stats data from localStorage
        function loadStatsData() {
            const currentPlayer = JSON.parse(localStorage.getItem('current_player_data') || '{}');
            const playerId = currentPlayer.id || 'default';
            
            // Load views
            const totalViews = parseInt(localStorage.getItem(`player_views_${playerId}`) || '0');
            const visits = JSON.parse(localStorage.getItem(`player_visits_${playerId}`) || '[]');
            
            // Calculate today's views
            const today = new Date().toDateString();
            const todayVisits = visits.filter(visit => new Date(visit.timestamp).toDateString() === today);
            
            // Update stats
            statsData.totalViews = totalViews;
            statsData.todayViews = todayVisits.length;
            statsData.uniqueVisitors = new Set(visits.map(v => v.userAgent)).size;
            
            // Generate realistic daily views for the past week
            statsData.dailyViews = generateDailyViews(totalViews);
        }

        function generateDailyViews(total) {
            const days = 7;
            const views = [];
            let remaining = total;
            
            for (let i = 0; i < days - 1; i++) {
                const dayViews = Math.floor(Math.random() * (remaining / (days - i))) + 1;
                views.push(dayViews);
                remaining -= dayViews;
            }
            views.push(Math.max(0, remaining));
            
            return views;
        }

        // Update overview statistics
        function updateOverviewStats() {
            document.getElementById('totalViews').textContent = statsData.totalViews;
            document.getElementById('todayViews').textContent = statsData.todayViews;
            document.getElementById('uniqueVisitors').textContent = statsData.uniqueVisitors;
            document.getElementById('avgViewTime').textContent = statsData.avgViewTime;
        }

        // Initialize charts
        function initializeCharts() {
            initViewsChart();
            initDevicesChart();
        }

        function initViewsChart() {
            const ctx = document.getElementById('viewsChart').getContext('2d');
            viewsChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['6 أيام', '5 أيام', '4 أيام', '3 أيام', 'أمس', 'اليوم', 'غداً'],
                    datasets: [{
                        label: 'المشاهدات',
                        data: statsData.dailyViews,
                        borderColor: '#8B4513',
                        backgroundColor: 'rgba(139, 69, 19, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: '#ffffff' }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: { color: '#ffffff' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        x: {
                            ticks: { color: '#ffffff' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        }
                    }
                }
            });
        }

        function initDevicesChart() {
            const ctx = document.getElementById('devicesChart').getContext('2d');
            devicesChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['جوال', 'كمبيوتر', 'تابلت'],
                    datasets: [{
                        data: [statsData.devices.mobile, statsData.devices.desktop, statsData.devices.tablet],
                        backgroundColor: ['#8B4513', '#D2691E', '#CD853F']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: { color: '#ffffff' }
                        }
                    }
                }
            });
        }

        // Populate countries statistics
        function populateCountriesStats() {
            const container = document.getElementById('countriesStats');
            container.innerHTML = statsData.countries.map(country => `
                <div class="flex justify-between items-center mb-3">
                    <div class="flex items-center">
                        <span class="text-2xl ml-2">${country.flag}</span>
                        <span>${country.name}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <span class="text-sm text-gray-400">${country.views}</span>
                        <div class="w-20 progress-bar">
                            <div class="progress-fill" style="width: ${country.percentage}%"></div>
                        </div>
                        <span class="text-sm font-bold">${country.percentage}%</span>
                    </div>
                </div>
            `).join('');
        }

        // Populate peak hours statistics
        function populatePeakHoursStats() {
            const container = document.getElementById('peakHoursStats');
            container.innerHTML = statsData.peakHours.map(hour => `
                <div class="time-slot">
                    <span>${hour.hour}</span>
                    <div class="flex items-center gap-2">
                        <span class="text-sm text-gray-400">${hour.views}</span>
                        <div class="w-16 progress-bar">
                            <div class="progress-fill" style="width: ${hour.percentage * 4}%"></div>
                        </div>
                        <span class="text-sm font-bold">${hour.percentage}%</span>
                    </div>
                </div>
            `).join('');
        }

        // Populate recent activity
        function populateRecentActivity() {
            const container = document.getElementById('recentActivity');
            const activities = [
                { time: 'منذ 5 دقائق', action: 'مشاهدة من السعودية', device: 'جوال', icon: 'fa-eye' },
                { time: 'منذ 15 دقيقة', action: 'مشاهدة من الإمارات', device: 'كمبيوتر', icon: 'fa-eye' },
                { time: 'منذ ساعة', action: 'مشاركة على واتساب', device: 'جوال', icon: 'fa-share' },
                { time: 'منذ ساعتين', action: 'مشاهدة من الكويت', device: 'تابلت', icon: 'fa-eye' },
                { time: 'منذ 3 ساعات', action: 'تحميل البطاقة', device: 'كمبيوتر', icon: 'fa-download' }
            ];

            container.innerHTML = activities.map(activity => `
                <div class="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg mb-2">
                    <div class="flex items-center gap-3">
                        <i class="fas ${activity.icon} text-blue-400"></i>
                        <div>
                            <div class="font-medium">${activity.action}</div>
                            <div class="text-sm text-gray-400">${activity.device}</div>
                        </div>
                    </div>
                    <span class="text-sm text-gray-400">${activity.time}</span>
                </div>
            `).join('');
        }

        // Export statistics
        function exportStats() {
            const reportData = {
                generatedAt: new Date().toISOString(),
                totalViews: statsData.totalViews,
                todayViews: statsData.todayViews,
                uniqueVisitors: statsData.uniqueVisitors,
                avgViewTime: statsData.avgViewTime,
                dailyViews: statsData.dailyViews,
                devices: statsData.devices,
                countries: statsData.countries,
                peakHours: statsData.peakHours
            };

            const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `player_stats_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            // Show success message
            showNotification('تم تصدير التقرير بنجاح!', 'success');
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
            
            const colors = {
                'success': 'bg-green-600 text-white',
                'error': 'bg-red-600 text-white',
                'warning': 'bg-yellow-600 text-white',
                'info': 'bg-blue-600 text-white'
            };
            
            notification.className += ` ${colors[type] || colors.info}`;
            notification.innerHTML = `
                <div class="flex items-center gap-2">
                    <i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle"></i>
                    <span>${message}</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.remove('translate-x-full'), 100);
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        console.log('📊 تم تحميل صفحة إحصائيات اللاعب بنجاح');
    </script>
</body>
</html>
