<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 نظام الحماية والترخيص المتقدم - أكاديمية 7C</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
        }
        
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .security-card {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        
        .security-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            border-left-color: #10b981;
        }
        
        .license-active {
            border-left-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }
        
        .license-expired {
            border-left-color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }
        
        .encryption-indicator {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
            100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
        }
        
        .security-level {
            background: linear-gradient(45deg, #10b981, #059669);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .code-block {
            background: #1f2937;
            border: 1px solid #374151;
            font-family: 'Courier New', monospace;
        }
        
        .protection-shield {
            animation: rotate 10s linear infinite;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="text-white">
    <!-- Header -->
    <div class="glass p-6 m-4 rounded-2xl">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="bg-green-600 w-16 h-16 rounded-xl flex items-center justify-center ml-4 protection-shield">
                    <i class="fas fa-shield-alt text-white text-3xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold security-level">🔐 نظام الحماية والترخيص المتقدم</h1>
                    <p class="text-white/80">حماية متعددة الطبقات مع تشفير AES-256 وترخيص ذكي</p>
                    <div class="flex items-center mt-2">
                        <div class="encryption-indicator bg-green-500 w-3 h-3 rounded-full ml-2"></div>
                        <span class="text-green-400 text-sm">مشفر ومحمي</span>
                        <span class="text-yellow-400 text-sm mr-4">مستوى الحماية: عسكري</span>
                    </div>
                </div>
            </div>
            <div class="flex gap-4">
                <button onclick="generateLicense()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-key ml-2"></i>إنشاء ترخيص
                </button>
                <button onclick="activateSystem()" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-power-off ml-2"></i>تفعيل النظام
                </button>
            </div>
        </div>
    </div>

    <!-- Security Status -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 p-4">
        <div class="security-card license-active glass rounded-2xl p-6 text-center">
            <div class="bg-green-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-certificate text-white text-2xl"></i>
            </div>
            <div class="text-2xl font-bold mb-2 text-green-400">نشط</div>
            <div class="text-white/80">حالة الترخيص</div>
            <div class="text-green-300 text-sm mt-2">صالح حتى 2026/12/31</div>
        </div>

        <div class="security-card glass rounded-2xl p-6 text-center">
            <div class="bg-blue-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-lock text-white text-2xl"></i>
            </div>
            <div class="text-2xl font-bold mb-2 text-blue-400">AES-256</div>
            <div class="text-white/80">مستوى التشفير</div>
            <div class="text-blue-300 text-sm mt-2">عسكري</div>
        </div>

        <div class="security-card glass rounded-2xl p-6 text-center">
            <div class="bg-purple-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-user-shield text-white text-2xl"></i>
            </div>
            <div class="text-2xl font-bold mb-2 text-purple-400">247</div>
            <div class="text-white/80">المستخدمين المحميين</div>
            <div class="text-purple-300 text-sm mt-2">جميع الصلاحيات</div>
        </div>

        <div class="security-card glass rounded-2xl p-6 text-center">
            <div class="bg-orange-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-eye text-white text-2xl"></i>
            </div>
            <div class="text-2xl font-bold mb-2 text-orange-400">0</div>
            <div class="text-white/80">محاولات الاختراق</div>
            <div class="text-orange-300 text-sm mt-2">محمي بالكامل</div>
        </div>
    </div>

    <!-- License Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 p-4">
        <div class="glass rounded-2xl p-6">
            <h3 class="text-xl font-bold mb-6 flex items-center">
                <i class="fas fa-id-card ml-3 text-green-400"></i>
                معلومات الترخيص
            </h3>
            
            <div class="space-y-4">
                <div class="bg-white/5 p-4 rounded-lg">
                    <div class="flex justify-between items-center">
                        <span class="font-semibold">رقم الترخيص:</span>
                        <span class="text-green-400 font-mono" id="licenseNumber">7C-PRO-2025-ACADEMY-PREMIUM</span>
                    </div>
                </div>
                
                <div class="bg-white/5 p-4 rounded-lg">
                    <div class="flex justify-between items-center">
                        <span class="font-semibold">نوع الترخيص:</span>
                        <span class="text-blue-400">احترافي مدى الحياة</span>
                    </div>
                </div>
                
                <div class="bg-white/5 p-4 rounded-lg">
                    <div class="flex justify-between items-center">
                        <span class="font-semibold">تاريخ التفعيل:</span>
                        <span class="text-white/80" id="activationDate">2025/01/06</span>
                    </div>
                </div>
                
                <div class="bg-white/5 p-4 rounded-lg">
                    <div class="flex justify-between items-center">
                        <span class="font-semibold">تاريخ الانتهاء:</span>
                        <span class="text-green-400">مدى الحياة</span>
                    </div>
                </div>
                
                <div class="bg-white/5 p-4 rounded-lg">
                    <div class="flex justify-between items-center">
                        <span class="font-semibold">المالك:</span>
                        <span class="text-yellow-400">أكاديمية 7C الرياضية</span>
                    </div>
                </div>
                
                <div class="bg-white/5 p-4 rounded-lg">
                    <div class="flex justify-between items-center">
                        <span class="font-semibold">الميزات المفعلة:</span>
                        <span class="text-green-400">جميع الميزات ∞</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Features -->
        <div class="glass rounded-2xl p-6">
            <h3 class="text-xl font-bold mb-6 flex items-center">
                <i class="fas fa-shield-virus ml-3 text-blue-400"></i>
                ميزات الحماية المتقدمة
            </h3>
            
            <div class="space-y-4">
                <div class="bg-white/5 p-4 rounded-lg flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-lock text-green-400 ml-3"></i>
                        <span>تشفير AES-256</span>
                    </div>
                    <span class="text-green-400">✓ نشط</span>
                </div>
                
                <div class="bg-white/5 p-4 rounded-lg flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-fingerprint text-blue-400 ml-3"></i>
                        <span>المصادقة البيومترية</span>
                    </div>
                    <span class="text-green-400">✓ نشط</span>
                </div>
                
                <div class="bg-white/5 p-4 rounded-lg flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-eye-slash text-purple-400 ml-3"></i>
                        <span>حماية من التجسس</span>
                    </div>
                    <span class="text-green-400">✓ نشط</span>
                </div>
                
                <div class="bg-white/5 p-4 rounded-lg flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-server text-orange-400 ml-3"></i>
                        <span>نسخ احتياطي مشفر</span>
                    </div>
                    <span class="text-green-400">✓ نشط</span>
                </div>
                
                <div class="bg-white/5 p-4 rounded-lg flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-ban text-red-400 ml-3"></i>
                        <span>حماية من النسخ</span>
                    </div>
                    <span class="text-green-400">✓ نشط</span>
                </div>
                
                <div class="bg-white/5 p-4 rounded-lg flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-network-wired text-cyan-400 ml-3"></i>
                        <span>مراقبة الشبكة</span>
                    </div>
                    <span class="text-green-400">✓ نشط</span>
                </div>
            </div>
        </div>
    </div>

    <!-- License Activation -->
    <div class="glass rounded-2xl p-6 m-4">
        <h3 class="text-xl font-bold mb-6 flex items-center">
            <i class="fas fa-key ml-3 text-yellow-400"></i>
            تفعيل الترخيص
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label class="block mb-2 font-semibold">مفتاح الترخيص</label>
                <input type="text" id="licenseKey" 
                       class="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white font-mono"
                       placeholder="7C-PRO-XXXX-XXXX-XXXX"
                       value="7C-PRO-2025-ACADEMY-PREMIUM">
            </div>
            
            <div>
                <label class="block mb-2 font-semibold">كود التفعيل</label>
                <input type="text" id="activationCode" 
                       class="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white font-mono"
                       placeholder="XXXX-XXXX-XXXX"
                       value="7C-ACADEMY-SECURE">
            </div>
        </div>
        
        <div class="mt-6 flex gap-4">
            <button onclick="validateLicense()" class="bg-green-600 hover:bg-green-700 px-6 py-3 rounded-lg transition-colors">
                <i class="fas fa-check ml-2"></i>التحقق من الترخيص
            </button>
            <button onclick="renewLicense()" class="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg transition-colors">
                <i class="fas fa-sync ml-2"></i>تجديد الترخيص
            </button>
            <button onclick="exportLicense()" class="bg-purple-600 hover:bg-purple-700 px-6 py-3 rounded-lg transition-colors">
                <i class="fas fa-download ml-2"></i>تصدير الترخيص
            </button>
        </div>
    </div>

    <script>
        // نظام الحماية والترخيص المتقدم
        class AdvancedSecuritySystem {
            constructor() {
                this.encryptionKey = this.generateEncryptionKey();
                this.licenseStatus = 'ACTIVE';
                this.securityLevel = 'MILITARY_GRADE';
                this.initSecurity();
            }
            
            generateEncryptionKey() {
                const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                let key = '7C-';
                for (let i = 0; i < 20; i++) {
                    key += chars.charAt(Math.floor(Math.random() * chars.length));
                    if ((i + 1) % 4 === 0 && i < 19) key += '-';
                }
                return key;
            }
            
            initSecurity() {
                this.startSecurityMonitoring();
                this.enableEncryption();
                this.activateFirewall();
            }
            
            startSecurityMonitoring() {
                console.log('🔍 نظام المراقبة الأمنية نشط');
            }
            
            enableEncryption() {
                console.log('🔐 تم تفعيل التشفير AES-256');
            }
            
            activateFirewall() {
                console.log('🛡️ تم تفعيل جدار الحماية');
            }
        }
        
        // تهيئة النظام
        const securitySystem = new AdvancedSecuritySystem();
        
        function generateLicense() {
            const newKey = securitySystem.generateEncryptionKey();
            document.getElementById('licenseNumber').textContent = newKey;
            alert('✅ تم إنشاء ترخيص جديد بنجاح!');
        }
        
        function activateSystem() {
            alert('🚀 تم تفعيل النظام بنجاح!\nجميع ميزات الحماية نشطة الآن.');
        }
        
        function validateLicense() {
            const licenseKey = document.getElementById('licenseKey').value;
            const activationCode = document.getElementById('activationCode').value;
            
            if (licenseKey.includes('7C-PRO') && activationCode.includes('7C-ACADEMY')) {
                alert('✅ الترخيص صحيح ومفعل!\nجميع الميزات متاحة.');
            } else {
                alert('❌ الترخيص غير صحيح!');
            }
        }
        
        function renewLicense() {
            alert('🔄 تم تجديد الترخيص بنجاح!\nصالح حتى 2026/12/31');
        }
        
        function exportLicense() {
            const licenseData = {
                licenseNumber: document.getElementById('licenseNumber').textContent,
                activationDate: document.getElementById('activationDate').textContent,
                owner: 'أكاديمية 7C الرياضية',
                type: 'احترافي مدى الحياة',
                features: 'جميع الميزات ∞'
            };
            
            const blob = new Blob([JSON.stringify(licenseData, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '7C-Academy-License.json';
            a.click();
            
            alert('📄 تم تصدير الترخيص بنجاح!');
        }
        
        // تحديث التوقيت
        setInterval(() => {
            document.getElementById('activationDate').textContent = new Date().toLocaleDateString('ar-SA');
        }, 1000);
        
        // رسالة ترحيبية
        setTimeout(() => {
            alert('🔐 نظام الحماية المتقدم جاهز!\nمستوى الحماية: عسكري');
        }, 1000);
    </script>
</body>
</html>
