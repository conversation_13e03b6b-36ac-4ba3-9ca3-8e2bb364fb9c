<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدير لوحات التحكم - أكاديمية 7C</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        :root {
            --primary-color: #1a365d;
            --secondary-color: #3182ce;
            --accent-color: #fbbf24;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --bg-dark: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --text-light: #e2e8f0;
            --text-dark: #1a202c;
            --border-color: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            color: var(--text-light);
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            z-index: -1;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .logo-text h1 {
            font-size: 1.3rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.2rem;
        }

        .logo-text p {
            font-size: 0.8rem;
            color: var(--text-light);
            opacity: 0.8;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 2rem;
            height: calc(100vh - 120px);
        }

        .sidebar {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 1.5rem;
            height: fit-content;
        }

        .sidebar-section {
            margin-bottom: 2rem;
        }

        .sidebar-title {
            font-size: 1.1rem;
            font-weight: 700;
            color: white;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .sidebar-item {
            padding: 0.75rem 1rem;
            margin-bottom: 0.5rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            border: 1px solid transparent;
        }

        .sidebar-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary-color);
        }

        .sidebar-item.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }

        .main-content {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 2rem;
            overflow-y: auto;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .content-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: white;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            border: none;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 4px 15px rgba(26, 54, 93, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(26, 54, 93, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #059669);
            color: white;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), #d97706);
            color: white;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--error-color), #dc2626);
            color: white;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        }

        .btn-secondary {
            background: var(--glass-bg);
            color: var(--text-light);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary-color);
        }

        .card {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            border-color: var(--primary-color);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .dashboard-card {
            position: relative;
            overflow: hidden;
        }

        .dashboard-preview {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
        }

        .dashboard-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect width="30" height="15" x="5" y="10" fill="rgba(255,255,255,0.3)" rx="2"/><rect width="25" height="10" x="40" y="12" fill="rgba(255,255,255,0.2)" rx="2"/><rect width="20" height="8" x="70" y="14" fill="rgba(255,255,255,0.2)" rx="2"/><rect width="40" height="30" x="5" y="35" fill="rgba(255,255,255,0.2)" rx="3"/><rect width="40" height="20" x="50" y="40" fill="rgba(255,255,255,0.15)" rx="3"/><rect width="85" height="15" x="5" y="75" fill="rgba(255,255,255,0.1)" rx="2"/></svg>') no-repeat center;
            background-size: 80%;
        }

        .dashboard-info h3 {
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.5rem;
        }

        .dashboard-info p {
            color: var(--text-light);
            opacity: 0.8;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .dashboard-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: var(--text-light);
            opacity: 0.7;
            margin-bottom: 1rem;
        }

        .dashboard-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            padding: 0.5rem;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            font-size: 0.9rem;
        }

        .action-btn.edit {
            background: var(--warning-color);
        }

        .action-btn.delete {
            background: var(--error-color);
        }

        .action-btn.duplicate {
            background: var(--success-color);
        }

        .action-btn.settings {
            background: var(--secondary-color);
        }

        .action-btn:hover {
            transform: scale(1.1);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            z-index: 2000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 2rem;
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
        }

        .close-btn {
            background: none;
            border: none;
            color: var(--text-light);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: white;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            background: var(--glass-bg);
            color: white;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(26, 54, 93, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: left 0.75rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-left: 2.5rem;
        }

        .color-picker-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .color-picker {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .color-input {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .tab {
            padding: 1rem 1.5rem;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
            color: var(--text-light);
        }

        .tab.active {
            color: white;
            border-bottom-color: var(--primary-color);
            background: rgba(26, 54, 93, 0.1);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .widget-library {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .widget-item {
            background: var(--glass-bg);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .widget-item:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .widget-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: var(--accent-color);
        }

        .widget-name {
            font-weight: 600;
            color: white;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
                height: auto;
            }
            
            .sidebar {
                order: 2;
            }
            
            .main-content {
                order: 1;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">7C</div>
                <div class="logo-text">
                    <h1>مدير لوحات التحكم</h1>
                    <p>أكاديمية 7C الرياضية</p>
                </div>
            </div>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="createNewDashboard()">
                    <i class="fas fa-plus"></i>
                    لوحة تحكم جديدة
                </button>
                <button class="btn btn-secondary" onclick="importDashboard()">
                    <i class="fas fa-upload"></i>
                    استيراد
                </button>
                <button class="btn btn-secondary" onclick="exportAllDashboards()">
                    <i class="fas fa-download"></i>
                    تصدير الكل
                </button>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container">
        <div class="main-grid">
            <!-- Sidebar -->
            <div class="sidebar">
                <div class="sidebar-section">
                    <div class="sidebar-title">
                        <i class="fas fa-tachometer-alt"></i>
                        إدارة اللوحات
                    </div>
                    <div class="sidebar-item active" onclick="showSection('dashboards')">
                        <i class="fas fa-th-large"></i>
                        جميع اللوحات
                    </div>
                    <div class="sidebar-item" onclick="showSection('templates')">
                        <i class="fas fa-layer-group"></i>
                        القوالب الجاهزة
                    </div>
                    <div class="sidebar-item" onclick="showSection('widgets')">
                        <i class="fas fa-puzzle-piece"></i>
                        مكتبة الأدوات
                    </div>
                </div>

                <div class="sidebar-section">
                    <div class="sidebar-title">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </div>
                    <div class="sidebar-item" onclick="showSection('themes')">
                        <i class="fas fa-palette"></i>
                        السمات والألوان
                    </div>
                    <div class="sidebar-item" onclick="showSection('layouts')">
                        <i class="fas fa-columns"></i>
                        تخطيطات الشاشة
                    </div>
                    <div class="sidebar-item" onclick="showSection('permissions')">
                        <i class="fas fa-shield-alt"></i>
                        الصلاحيات
                    </div>
                </div>

                <div class="sidebar-section">
                    <div class="sidebar-title">
                        <i class="fas fa-chart-bar"></i>
                        التحليلات
                    </div>
                    <div class="sidebar-item" onclick="showSection('analytics')">
                        <i class="fas fa-chart-line"></i>
                        إحصائيات الاستخدام
                    </div>
                    <div class="sidebar-item" onclick="showSection('performance')">
                        <i class="fas fa-tachometer-alt"></i>
                        الأداء
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <div id="dashboards-section" class="content-section active">
                    <div class="content-header">
                        <div class="content-title">
                            <i class="fas fa-th-large"></i>
                            لوحات التحكم
                        </div>
                        <div class="header-actions">
                            <select class="form-input" style="width: auto; margin-left: 1rem;" onchange="filterDashboards(this.value)">
                                <option value="">جميع اللوحات</option>
                                <option value="active">نشطة</option>
                                <option value="draft">مسودة</option>
                                <option value="archived">مؤرشفة</option>
                            </select>
                            <input type="text" class="form-input" placeholder="البحث في اللوحات..." style="width: 250px;" onkeyup="searchDashboards(this.value)">
                        </div>
                    </div>

                    <div class="dashboard-grid" id="dashboardsGrid">
                        <!-- Dashboards will be loaded here -->
                    </div>
                </div>

                <!-- Other sections will be added via JavaScript -->
            </div>
        </div>
    </div>
</body>
</html>
