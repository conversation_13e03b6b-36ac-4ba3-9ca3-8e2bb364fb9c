<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بروشور التسجيل | أكاديمية 7C الرياضية النموذجية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #fff;
            margin: 0;
            padding: 0;
        }
        .brochure-container {
            max-width: 500px;
            margin: 40px auto;
            background: rgba(255,255,255,0.07);
            border-radius: 24px;
            box-shadow: 0 8px 32px #0003;
            padding: 32px 24px 24px 24px;
            text-align: center;
        }
        .logo {
            width: 90px;
            height: 90px;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            font-weight: bold;
            color: #fff;
            margin: 0 auto 18px auto;
            box-shadow: 0 4px 16px #3b82f655;
        }
        h1 {
            font-size: 2.1rem;
            margin-bottom: 10px;
            color: #3b82f6;
        }
        .subtitle {
            font-size: 1.1rem;
            color: #8b5cf6;
            margin-bottom: 18px;
        }
        .features {
            text-align: right;
            margin: 24px 0 18px 0;
        }
        .features li {
            margin-bottom: 10px;
            font-size: 1rem;
            color: #fff;
            background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%);
            padding: 7px 16px;
            border-radius: 8px;
            box-shadow: 0 2px 8px #0001;
        }
        .cta {
            margin: 28px 0 0 0;
        }
        .cta-btn {
            background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%);
            color: #fff;
            font-size: 1.2rem;
            font-weight: bold;
            padding: 14px 38px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            box-shadow: 0 4px 16px #3b82f655;
            transition: background 0.3s;
        }
        .cta-btn:hover {
            background: linear-gradient(90deg, #8b5cf6 0%, #3b82f6 100%);
        }
        .contact {
            margin-top: 24px;
            color: #cbd5e1;
            font-size: 1rem;
        }
    </style>
</head>
<body>
    <div class="brochure-container">
        <div class="logo">7C</div>
        <h1>سجّل الآن في أكاديمية 7C الرياضية النموذجية</h1>
        <div class="subtitle">انضم إلى بيئة رياضية متطورة وابدأ رحلتك نحو التميز!</div>
        <ul class="features">
            <li>برامج تدريبية احترافية لجميع الأعمار</li>
            <li>مدربون معتمدون بخبرة عالية</li>
            <li>ملاعب ومرافق حديثة</li>
            <li>متابعة أداء اللاعبين بتقنيات ذكية</li>
            <li>أنشطة ومسابقات رياضية دورية</li>
            <li>دعم كامل للطلاب الموهوبين</li>
        </ul>
        <div class="cta">
            <a href="#registration" class="cta-btn">سجّل الآن</a>
        </div>
        <div class="contact">
            للاستفسار: 0500000000<br>
            أو عبر واتساب: <a href="https://wa.me/966500000000" style="color:#3b82f6;text-decoration:underline;">اضغط هنا</a>
        </div>
    </div>
</body>
</html>
