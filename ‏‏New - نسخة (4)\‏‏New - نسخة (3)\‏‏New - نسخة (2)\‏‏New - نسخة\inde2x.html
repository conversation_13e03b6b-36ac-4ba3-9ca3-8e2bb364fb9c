<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أكاديمية 7C الرياضية | نظام إدارة رياضي ذكي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #1e40af;
            --secondary-color: #8b5cf6;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
            overflow-x: hidden;
        }

        .hero-bg {
            background: linear-gradient(135deg, rgba(30, 64, 175, 0.9) 0%, rgba(139, 92, 246, 0.9) 100%),
                        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
            background-size: cover;
            background-attachment: fixed;
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .animate-float {
            animation: float 6s ease-in-out infinite;
        }

        .animate-pulse-slow {
            animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        .animate-bounce-slow {
            animation: bounce 3s infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-20px) rotate(1deg); }
            50% { transform: translateY(-10px) rotate(0deg); }
            75% { transform: translateY(-15px) rotate(-1deg); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .slide-in-up { animation: slideInUp 0.8s ease-out; }
        .slide-in-right { animation: slideInRight 0.8s ease-out; }
        .slide-in-left { animation: slideInLeft 0.8s ease-out; }

        .gradient-text {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .card-hover {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .card-hover:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .ai-glow {
            box-shadow: 0 0 30px rgba(139, 92, 246, 0.5);
            animation: aiPulse 2s ease-in-out infinite alternate;
        }

        @keyframes aiPulse {
            from { box-shadow: 0 0 30px rgba(139, 92, 246, 0.5); }
            to { box-shadow: 0 0 50px rgba(139, 92, 246, 0.8); }
        }

        .typing-animation {
            border-right: 2px solid #3b82f6;
            animation: typing 3s steps(40) infinite, blink 1s infinite;
        }

        @keyframes typing {
            0%, 50% { width: 0; }
            100% { width: 100%; }
        }

        @keyframes blink {
            0%, 50% { border-color: transparent; }
            51%, 100% { border-color: #3b82f6; }
        }

        .parallax {
            transform: translateZ(0);
            will-change: transform;
        }

        .stats-counter {
            font-variant-numeric: tabular-nums;
        }
    </style>
</head>
<body class="min-h-screen text-white">
    <!-- AI Assistant Floating Button -->
    <div id="ai-assistant" class="fixed bottom-6 left-6 z-50">
        <button onclick="toggleAI()" class="ai-glow bg-gradient-to-r from-purple-600 to-blue-600 text-white p-4 rounded-full shadow-2xl hover:scale-110 transition-all duration-300">
            <i class="fas fa-robot text-2xl"></i>
        </button>
    </div>

    <!-- AI Chat Panel -->
    <div id="ai-panel" class="fixed bottom-24 left-6 w-80 h-96 glass-effect rounded-2xl z-40 transform translate-y-full opacity-0 transition-all duration-500">
        <div class="p-4 border-b border-white/20">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center ml-2">
                        <i class="fas fa-robot text-sm"></i>
                    </div>
                    <span class="font-bold">مساعد 7C الذكي</span>
                </div>
                <button onclick="toggleAI()" class="text-white/60 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div id="ai-messages" class="p-4 h-64 overflow-y-auto">
            <div class="ai-message mb-3">
                <div class="bg-gradient-to-r from-purple-600/20 to-blue-600/20 p-3 rounded-lg">
                    <p class="text-sm">مرحباً! أنا مساعدك الذكي في أكاديمية 7C. كيف يمكنني مساعدتك اليوم؟</p>
                </div>
            </div>
        </div>
        <div class="p-4 border-t border-white/20">
            <div class="flex">
                <input type="text" id="ai-input" placeholder="اكتب رسالتك..." class="flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-sm text-white placeholder-white/60 focus:outline-none focus:border-blue-400">
                <button onclick="sendAIMessage()" class="mr-2 bg-gradient-to-r from-purple-600 to-blue-600 px-4 py-2 rounded-lg hover:scale-105 transition-transform">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Header -->
    <header class="glass-effect sticky top-0 z-40 border-b border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-lg animate-float ai-glow">
                        7C
                    </div>
                    <div class="mr-4">
                        <span class="text-2xl font-bold gradient-text">أكاديمية 7C الرياضية</span>
                        <div class="text-xs text-blue-200">نظام إدارة رياضي ذكي</div>
                    </div>
                </div>

                <nav class="hidden lg:flex space-x-8 space-x-reverse">
                    <a href="#home" class="nav-link text-white hover:text-blue-300 transition-colors"><i class="fas fa-home ml-2"></i>الرئيسية</a>
                    <a href="#features" class="nav-link text-white hover:text-blue-300 transition-colors"><i class="fas fa-star ml-2"></i>الميزات</a>
                    <a href="#ai-section" class="nav-link text-white hover:text-purple-300 transition-colors"><i class="fas fa-robot ml-2"></i>الذكاء الاصطناعي</a>
                    <a href="#stats" class="nav-link text-white hover:text-green-300 transition-colors"><i class="fas fa-chart-bar ml-2"></i>الإحصائيات</a>
                    <a href="#contact" class="nav-link text-white hover:text-orange-300 transition-colors"><i class="fas fa-envelope ml-2"></i>اتصل بنا</a>
                </nav>

                <div class="flex items-center space-x-4 space-x-reverse">
                    <button onclick="showNotifications()" class="relative p-2 text-white hover:text-blue-300 transition-colors">
                        <i class="fas fa-bell text-lg"></i>
                        <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center animate-pulse">3</span>
                    </button>
                    <button onclick="showDashboard()" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 font-bold">
                        <i class="fas fa-tachometer-alt ml-2"></i>لوحة التحكم
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero-bg min-h-screen flex items-center relative overflow-hidden">
        <!-- Floating Elements -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <div class="absolute top-20 right-20 w-32 h-32 bg-blue-500/10 rounded-full animate-float"></div>
            <div class="absolute top-40 left-40 w-24 h-24 bg-purple-500/10 rounded-full animate-bounce-slow"></div>
            <div class="absolute bottom-40 right-60 w-20 h-20 bg-cyan-500/10 rounded-full animate-pulse-slow"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 relative z-10">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <!-- Left Content -->
                <div class="text-center lg:text-right slide-in-right">
                    <div class="inline-flex items-center bg-white/10 backdrop-blur-md rounded-full px-6 py-3 mb-8 border border-white/20">
                        <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse ml-3"></div>
                        <span class="text-sm font-medium">النظام متصل ويعمل بكفاءة عالية</span>
                    </div>

                    <h1 class="text-5xl lg:text-7xl font-black mb-6 leading-tight">
                        <span class="gradient-text">أكاديمية</span><br>
                        <span class="text-white">7C الرياضية</span>
                    </h1>

                    <div class="text-xl lg:text-2xl text-blue-100 mb-8 leading-relaxed">
                        <div class="typing-animation overflow-hidden whitespace-nowrap">
                            نظام إدارة رياضي ذكي مدعوم بالذكاء الاصطناعي
                        </div>
                    </div>

                    <p class="text-lg text-white/80 mb-10 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                        منصة متكاملة لإدارة الأكاديميات الرياضية مع تقنيات الذكاء الاصطناعي المتقدمة لتحليل الأداء وتطوير المواهب الرياضية
                    </p>

                    <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-12">
                        <button onclick="showDashboard()" class="group bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-2xl hover:shadow-2xl transition-all duration-300 text-lg font-bold transform hover:scale-105 relative overflow-hidden">
                            <span class="relative z-10 flex items-center justify-center">
                                <i class="fas fa-rocket ml-3 group-hover:animate-bounce"></i>
                                دخول لوحة التحكم
                            </span>
                            <div class="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </button>
                        <button onclick="startAIDemo()" class="group glass-effect text-white border-2 border-white/30 px-8 py-4 rounded-2xl hover:bg-white/10 transition-all duration-300 text-lg font-bold transform hover:scale-105">
                            <i class="fas fa-robot ml-3 group-hover:animate-spin"></i>
                            تجربة الذكاء الاصطناعي
                        </button>
                    </div>

                    <!-- Quick Stats -->
                    <div class="grid grid-cols-3 gap-6 max-w-md mx-auto lg:mx-0">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-white stats-counter" data-target="1250">0</div>
                            <div class="text-sm text-blue-200">لاعب نشط</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-white stats-counter" data-target="89">0</div>
                            <div class="text-sm text-blue-200">مدرب محترف</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-white stats-counter" data-target="156">0</div>
                            <div class="text-sm text-blue-200">برنامج تدريبي</div>
                        </div>
                    </div>
                </div>

                <!-- Right Content - 3D Dashboard Preview -->
                <div class="slide-in-left">
                    <div class="relative">
                        <!-- Main Dashboard Card -->
                        <div class="glass-effect rounded-3xl p-8 transform rotate-3 hover:rotate-0 transition-transform duration-500 card-hover">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-bold text-white">لوحة التحكم الذكية</h3>
                                <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                            </div>

                            <!-- Mini Chart -->
                            <div class="mb-6">
                                <canvas id="heroChart" width="300" height="150"></canvas>
                            </div>

                            <!-- Stats Grid -->
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div class="bg-gradient-to-r from-blue-500/20 to-purple-500/20 p-4 rounded-xl">
                                    <div class="text-2xl font-bold text-white">94%</div>
                                    <div class="text-sm text-blue-200">معدل الحضور</div>
                                </div>
                                <div class="bg-gradient-to-r from-green-500/20 to-blue-500/20 p-4 rounded-xl">
                                    <div class="text-2xl font-bold text-white">4.8</div>
                                    <div class="text-sm text-green-200">تقييم الأداء</div>
                                </div>
                            </div>

                            <!-- AI Insights -->
                            <div class="bg-gradient-to-r from-purple-500/20 to-pink-500/20 p-4 rounded-xl">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-robot text-purple-300 ml-2"></i>
                                    <span class="text-sm font-medium text-white">رؤى الذكاء الاصطناعي</span>
                                </div>
                                <p class="text-xs text-white/80">تحسن ملحوظ في أداء الفريق بنسبة 23% هذا الشهر</p>
                            </div>
                        </div>

                        <!-- Floating Cards -->
                        <div class="absolute -top-4 -right-4 glass-effect rounded-2xl p-4 animate-float">
                            <div class="flex items-center">
                                <i class="fas fa-trophy text-yellow-400 text-xl ml-2"></i>
                                <div>
                                    <div class="text-sm font-bold text-white">12</div>
                                    <div class="text-xs text-yellow-200">بطولة</div>
                                </div>
                            </div>
                        </div>

                        <div class="absolute -bottom-4 -left-4 glass-effect rounded-2xl p-4 animate-bounce-slow">
                            <div class="flex items-center">
                                <i class="fas fa-users text-green-400 text-xl ml-2"></i>
                                <div>
                                    <div class="text-sm font-bold text-white">+47</div>
                                    <div class="text-xs text-green-200">لاعب جديد</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div class="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
                <div class="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl lg:text-5xl font-bold mb-6">
                    <span class="gradient-text">ميزات النظام المتقدمة</span>
                </h2>
                <p class="text-xl text-white/80 max-w-3xl mx-auto">
                    اكتشف مجموعة شاملة من الأدوات والتقنيات المتطورة لإدارة أكاديميتك الرياضية بكفاءة عالية
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Player Management -->
                <div class="group glass-effect rounded-3xl p-8 card-hover border border-white/10">
                    <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-users text-3xl text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4">إدارة اللاعبين</h3>
                    <p class="text-white/70 mb-6 leading-relaxed">
                        نظام شامل لإدارة ملفات اللاعبين مع تتبع الأداء والإحصائيات التفصيلية والتقييمات الدورية
                    </p>
                    <ul class="space-y-2 text-sm text-white/60">
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>ملفات شخصية مفصلة</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>تتبع الأداء المباشر</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>تقارير تقييم شاملة</li>
                    </ul>
                </div>

                <!-- Training Management -->
                <div class="group glass-effect rounded-3xl p-8 card-hover border border-white/10">
                    <div class="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-dumbbell text-3xl text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4">إدارة التدريب</h3>
                    <p class="text-white/70 mb-6 leading-relaxed">
                        جدولة ذكية للتدريبات مع تتبع الحضور وتخطيط البرامج التدريبية المخصصة لكل لاعب
                    </p>
                    <ul class="space-y-2 text-sm text-white/60">
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>جدولة تلقائية ذكية</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>برامج تدريبية مخصصة</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>تتبع التقدم المستمر</li>
                    </ul>
                </div>

                <!-- AI Analytics -->
                <div class="group glass-effect rounded-3xl p-8 card-hover border border-white/10 ai-glow">
                    <div class="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-robot text-3xl text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4">الذكاء الاصطناعي</h3>
                    <p class="text-white/70 mb-6 leading-relaxed">
                        تحليلات متقدمة مدعومة بالذكاء الاصطناعي لتقييم الأداء وتقديم توصيات مخصصة لكل لاعب
                    </p>
                    <ul class="space-y-2 text-sm text-white/60">
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>تحليل الأداء الذكي</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>توصيات مخصصة</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>تنبؤات مستقبلية</li>
                    </ul>
                </div>

                <!-- Performance Analytics -->
                <div class="group glass-effect rounded-3xl p-8 card-hover border border-white/10">
                    <div class="w-20 h-20 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-chart-line text-3xl text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4">تحليل الأداء</h3>
                    <p class="text-white/70 mb-6 leading-relaxed">
                        رؤى عميقة حول أداء الفرق واللاعبين مع تقارير تفاعلية ومؤشرات أداء رئيسية
                    </p>
                    <ul class="space-y-2 text-sm text-white/60">
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>تقارير تفاعلية</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>مؤشرات أداء رئيسية</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>مقارنات تنافسية</li>
                    </ul>
                </div>

                <!-- Communication -->
                <div class="group glass-effect rounded-3xl p-8 card-hover border border-white/10">
                    <div class="w-20 h-20 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-comments text-3xl text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4">التواصل الذكي</h3>
                    <p class="text-white/70 mb-6 leading-relaxed">
                        منصة تواصل متكاملة تربط المدربين واللاعبين وأولياء الأمور في بيئة آمنة ومنظمة
                    </p>
                    <ul class="space-y-2 text-sm text-white/60">
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>رسائل فورية آمنة</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>إشعارات ذكية</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>مجموعات منظمة</li>
                    </ul>
                </div>

                <!-- Financial Management -->
                <div class="group glass-effect rounded-3xl p-8 card-hover border border-white/10">
                    <div class="w-20 h-20 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-credit-card text-3xl text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4">الإدارة المالية</h3>
                    <p class="text-white/70 mb-6 leading-relaxed">
                        نظام مالي متكامل لإدارة الاشتراكات والمدفوعات مع تقارير مالية تفصيلية
                    </p>
                    <ul class="space-y-2 text-sm text-white/60">
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>إدارة الاشتراكات</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>معالجة المدفوعات</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-400 ml-2"></i>تقارير مالية شاملة</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- AI Section -->
    <section id="ai-section" class="py-20 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <div class="inline-flex items-center bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-md rounded-full px-6 py-3 mb-8 border border-purple-500/30">
                    <i class="fas fa-robot text-purple-400 ml-3"></i>
                    <span class="text-purple-300 font-medium">مدعوم بالذكاء الاصطناعي</span>
                </div>
                <h2 class="text-4xl lg:text-5xl font-bold mb-6">
                    <span class="gradient-text">الذكاء الاصطناعي المتقدم</span>
                </h2>
                <p class="text-xl text-white/80 max-w-3xl mx-auto">
                    تقنيات ذكية متطورة لتحليل الأداء الرياضي وتقديم رؤى عميقة لتطوير المواهب
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
                <!-- AI Features -->
                <div class="space-y-8">
                    <div class="glass-effect rounded-2xl p-6 border border-purple-500/20 ai-glow">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center ml-4">
                                <i class="fas fa-brain text-white text-xl"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white">تحليل الأداء الذكي</h3>
                        </div>
                        <p class="text-white/70 mb-4">
                            خوارزميات متقدمة لتحليل أداء اللاعبين وتحديد نقاط القوة والضعف
                        </p>
                        <div class="bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg p-4">
                            <div class="text-sm text-white/60">مثال: تحسن في السرعة بنسبة 15% خلال الشهر الماضي</div>
                        </div>
                    </div>

                    <div class="glass-effect rounded-2xl p-6 border border-blue-500/20">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center ml-4">
                                <i class="fas fa-chart-line text-white text-xl"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white">التنبؤ بالأداء</h3>
                        </div>
                        <p class="text-white/70 mb-4">
                            نماذج تنبؤية لتوقع الأداء المستقبلي وتحديد أفضل استراتيجيات التدريب
                        </p>
                        <div class="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-lg p-4">
                            <div class="text-sm text-white/60">توقع: احتمالية تحسن الأداء 87% مع البرنامج الحالي</div>
                        </div>
                    </div>

                    <div class="glass-effect rounded-2xl p-6 border border-green-500/20">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center ml-4">
                                <i class="fas fa-lightbulb text-white text-xl"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white">توصيات مخصصة</h3>
                        </div>
                        <p class="text-white/70 mb-4">
                            اقتراحات ذكية مخصصة لكل لاعب بناءً على تحليل شامل لأدائه وإمكانياته
                        </p>
                        <div class="bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg p-4">
                            <div class="text-sm text-white/60">توصية: زيادة تمارين القوة بنسبة 20% لتحسين الأداء</div>
                        </div>
                    </div>
                </div>

                <!-- AI Demo Interface -->
                <div class="glass-effect rounded-3xl p-8 border border-white/10">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-2xl font-bold text-white">مساعد 7C الذكي</h3>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse ml-2"></div>
                            <span class="text-sm text-green-300">متصل</span>
                        </div>
                    </div>

                    <div class="space-y-4 mb-6 h-64 overflow-y-auto">
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center ml-3 flex-shrink-0">
                                <i class="fas fa-robot text-white text-sm"></i>
                            </div>
                            <div class="bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-2xl p-4 max-w-xs">
                                <p class="text-white text-sm">مرحباً! كيف يمكنني مساعدتك في تحليل أداء الفريق اليوم؟</p>
                            </div>
                        </div>

                        <div class="flex items-start justify-end">
                            <div class="bg-white/10 rounded-2xl p-4 max-w-xs ml-3">
                                <p class="text-white text-sm">أريد تحليل أداء اللاعب أحمد محمد</p>
                            </div>
                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center ml-3 flex-shrink-0">
                                <i class="fas fa-robot text-white text-sm"></i>
                            </div>
                            <div class="bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-2xl p-4 max-w-xs">
                                <p class="text-white text-sm">بناءً على التحليل، أحمد يظهر تحسناً ملحوظاً في السرعة (+15%) والدقة (+12%). أنصح بزيادة تمارين القوة.</p>
                            </div>
                        </div>
                    </div>

                    <div class="flex">
                        <input type="text" placeholder="اسأل الذكاء الاصطناعي..." class="flex-1 bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/60 focus:outline-none focus:border-purple-400">
                        <button class="mr-3 bg-gradient-to-r from-purple-500 to-blue-500 px-6 py-3 rounded-xl hover:scale-105 transition-transform">
                            <i class="fas fa-paper-plane text-white"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section id="stats" class="py-20 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl lg:text-5xl font-bold mb-6">
                    <span class="gradient-text">إحصائيات مذهلة</span>
                </h2>
                <p class="text-xl text-white/80 max-w-3xl mx-auto">
                    أرقام حقيقية تعكس نجاح نظام أكاديمية 7C الرياضية في تطوير المواهب الرياضية
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                <div class="glass-effect rounded-3xl p-8 text-center card-hover border border-white/10">
                    <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-users text-3xl text-white"></i>
                    </div>
                    <div class="text-4xl font-bold text-white mb-2 stats-counter" data-target="1250">0</div>
                    <div class="text-blue-200 font-medium">لاعب نشط</div>
                    <div class="text-sm text-white/60 mt-2">+15% هذا الشهر</div>
                </div>

                <div class="glass-effect rounded-3xl p-8 text-center card-hover border border-white/10">
                    <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-trophy text-3xl text-white"></i>
                    </div>
                    <div class="text-4xl font-bold text-white mb-2 stats-counter" data-target="89">0</div>
                    <div class="text-green-200 font-medium">بطولة محققة</div>
                    <div class="text-sm text-white/60 mt-2">+8 هذا العام</div>
                </div>

                <div class="glass-effect rounded-3xl p-8 text-center card-hover border border-white/10">
                    <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-graduation-cap text-3xl text-white"></i>
                    </div>
                    <div class="text-4xl font-bold text-white mb-2 stats-counter" data-target="156">0</div>
                    <div class="text-purple-200 font-medium">مدرب محترف</div>
                    <div class="text-sm text-white/60 mt-2">معتمدين دولياً</div>
                </div>

                <div class="glass-effect rounded-3xl p-8 text-center card-hover border border-white/10">
                    <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-chart-line text-3xl text-white"></i>
                    </div>
                    <div class="text-4xl font-bold text-white mb-2">
                        <span class="stats-counter" data-target="94">0</span>%
                    </div>
                    <div class="text-orange-200 font-medium">معدل النجاح</div>
                    <div class="text-sm text-white/60 mt-2">في تطوير المواهب</div>
                </div>
            </div>

            <!-- Performance Chart -->
            <div class="glass-effect rounded-3xl p-8 border border-white/10">
                <h3 class="text-2xl font-bold text-white mb-6 text-center">تطور الأداء خلال العام</h3>
                <div class="h-64">
                    <canvas id="performanceChart"></canvas>
                </div>
            </div>
        </div>
    </section>

    <!-- Dashboard Preview -->
    <section id="dashboard" class="py-20 relative hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="glass-effect rounded-3xl p-8 border border-white/10">
                <h2 class="text-3xl font-bold text-white mb-8 text-center">
                    <span class="gradient-text">لوحة التحكم الذكية</span>
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-gradient-to-r from-blue-500/20 to-purple-500/20 p-6 rounded-2xl border border-blue-500/30">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-bold text-white">إجمالي اللاعبين</h3>
                            <i class="fas fa-users text-blue-400 text-xl"></i>
                        </div>
                        <div class="text-3xl font-bold text-white mb-2">1,250</div>
                        <div class="text-blue-200 text-sm">+12% من الشهر الماضي</div>
                    </div>

                    <div class="bg-gradient-to-r from-green-500/20 to-emerald-500/20 p-6 rounded-2xl border border-green-500/30">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-bold text-white">الجلسات التدريبية</h3>
                            <i class="fas fa-dumbbell text-green-400 text-xl"></i>
                        </div>
                        <div class="text-3xl font-bold text-white mb-2">847</div>
                        <div class="text-green-200 text-sm">هذا الشهر</div>
                    </div>

                    <div class="bg-gradient-to-r from-purple-500/20 to-pink-500/20 p-6 rounded-2xl border border-purple-500/30">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-bold text-white">معدل الحضور</h3>
                            <i class="fas fa-chart-pie text-purple-400 text-xl"></i>
                        </div>
                        <div class="text-3xl font-bold text-white mb-2">94%</div>
                        <div class="text-purple-200 text-sm">أعلى من المتوسط</div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="bg-white/5 rounded-2xl p-6 border border-white/10">
                        <h4 class="font-bold text-lg mb-4 text-white flex items-center">
                            <i class="fas fa-bolt text-yellow-400 ml-2"></i>
                            الإجراءات السريعة
                        </h4>
                        <div class="space-y-3">
                            <button class="w-full text-right p-3 hover:bg-white/10 rounded-lg transition-colors duration-200 border border-white/20 text-white flex items-center">
                                <i class="fas fa-user-plus text-blue-400 ml-3"></i>
                                إضافة لاعب جديد
                            </button>
                            <button class="w-full text-right p-3 hover:bg-white/10 rounded-lg transition-colors duration-200 border border-white/20 text-white flex items-center">
                                <i class="fas fa-calendar-plus text-green-400 ml-3"></i>
                                جدولة تدريب
                            </button>
                            <button class="w-full text-right p-3 hover:bg-white/10 rounded-lg transition-colors duration-200 border border-white/20 text-white flex items-center">
                                <i class="fas fa-envelope text-purple-400 ml-3"></i>
                                إرسال رسالة جماعية
                            </button>
                            <button class="w-full text-right p-3 hover:bg-white/10 rounded-lg transition-colors duration-200 border border-white/20 text-white flex items-center">
                                <i class="fas fa-chart-bar text-orange-400 ml-3"></i>
                                عرض التقارير
                            </button>
                        </div>
                    </div>

                    <div class="bg-white/5 rounded-2xl p-6 border border-white/10">
                        <h4 class="font-bold text-lg mb-4 text-white flex items-center">
                            <i class="fas fa-clock text-blue-400 ml-2"></i>
                            النشاطات الأخيرة
                        </h4>
                        <div class="space-y-3">
                            <div class="text-sm text-white/80 p-3 bg-white/5 rounded-lg border border-white/10 flex items-center">
                                <i class="fas fa-user-check text-green-400 ml-3"></i>
                                انضم أحمد محمد إلى فريق تحت 16
                            </div>
                            <div class="text-sm text-white/80 p-3 bg-white/5 rounded-lg border border-white/10 flex items-center">
                                <i class="fas fa-flag-checkered text-blue-400 ml-3"></i>
                                تم إنهاء جلسة التدريب التقني
                            </div>
                            <div class="text-sm text-white/80 p-3 bg-white/5 rounded-lg border border-white/10 flex items-center">
                                <i class="fas fa-comment text-purple-400 ml-3"></i>
                                رسالة جديدة من المدرب سالم
                            </div>
                            <div class="text-sm text-white/80 p-3 bg-white/5 rounded-lg border border-white/10 flex items-center">
                                <i class="fas fa-file-alt text-orange-400 ml-3"></i>
                                تم إنشاء تقرير الأداء الشهري
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact" class="relative py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="glass-effect rounded-3xl p-12 border border-white/10">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                    <!-- Company Info -->
                    <div class="lg:col-span-2">
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center text-white font-bold text-2xl ml-4 ai-glow">
                                7C
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-white">أكاديمية 7C الرياضية</h3>
                                <p class="text-blue-200">نظام إدارة رياضي ذكي</p>
                            </div>
                        </div>
                        <p class="text-white/70 mb-6 leading-relaxed">
                            منصة متكاملة لإدارة الأكاديميات الرياضية مع تقنيات الذكاء الاصطناعي المتقدمة لتحليل الأداء وتطوير المواهب الرياضية بكفاءة عالية.
                        </p>
                        <div class="flex space-x-4 space-x-reverse">
                            <a href="#" class="w-12 h-12 bg-blue-600/20 rounded-xl flex items-center justify-center text-blue-400 hover:bg-blue-600/30 transition-colors">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="w-12 h-12 bg-blue-400/20 rounded-xl flex items-center justify-center text-blue-300 hover:bg-blue-400/30 transition-colors">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="w-12 h-12 bg-pink-600/20 rounded-xl flex items-center justify-center text-pink-400 hover:bg-pink-600/30 transition-colors">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="w-12 h-12 bg-blue-700/20 rounded-xl flex items-center justify-center text-blue-400 hover:bg-blue-700/30 transition-colors">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div>
                        <h4 class="text-lg font-bold text-white mb-6">روابط سريعة</h4>
                        <ul class="space-y-3">
                            <li><a href="#home" class="text-white/70 hover:text-white transition-colors flex items-center"><i class="fas fa-chevron-left ml-2 text-xs"></i>الرئيسية</a></li>
                            <li><a href="#features" class="text-white/70 hover:text-white transition-colors flex items-center"><i class="fas fa-chevron-left ml-2 text-xs"></i>الميزات</a></li>
                            <li><a href="#ai-section" class="text-white/70 hover:text-white transition-colors flex items-center"><i class="fas fa-chevron-left ml-2 text-xs"></i>الذكاء الاصطناعي</a></li>
                            <li><a href="#stats" class="text-white/70 hover:text-white transition-colors flex items-center"><i class="fas fa-chevron-left ml-2 text-xs"></i>الإحصائيات</a></li>
                        </ul>
                    </div>

                    <!-- Contact Info -->
                    <div>
                        <h4 class="text-lg font-bold text-white mb-6">تواصل معنا</h4>
                        <ul class="space-y-4">
                            <li class="flex items-center text-white/70">
                                <i class="fas fa-envelope text-blue-400 ml-3"></i>
                                <EMAIL>
                            </li>
                            <li class="flex items-center text-white/70">
                                <i class="fas fa-phone text-green-400 ml-3"></i>
                                +966-XX-XXX-XXXX
                            </li>
                            <li class="flex items-center text-white/70">
                                <i class="fas fa-map-marker-alt text-red-400 ml-3"></i>
                                الرياض، المملكة العربية السعودية
                            </li>
                            <li class="flex items-center text-white/70">
                                <i class="fas fa-clock text-yellow-400 ml-3"></i>
                                24/7 دعم فني
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="border-t border-white/10 mt-12 pt-8 text-center">
                    <p class="text-white/60 mb-4">
                        © 2024 أكاديمية 7C الرياضية. جميع الحقوق محفوظة.
                    </p>
                    <p class="text-white/40 text-sm">
                        مطور بـ ❤️ باستخدام أحدث تقنيات الذكاء الاصطناعي
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- نظام تحرير الصفحة المتقدم -->
    <div id="edit-overlay" style="display:none;position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.8);z-index:10000;backdrop-filter:blur(10px);">
        <div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:#fff;padding:30px;border-radius:20px;box-shadow:0 20px 60px rgba(0,0,0,0.3);min-width:400px;text-align:center;direction:rtl;">
            <h3 style="color:#1e40af;margin-bottom:20px;font-size:24px;">🔐 تحرير الصفحة الرئيسية</h3>
            <p style="color:#666;margin-bottom:20px;">أدخل كلمة المرور للمتابعة:</p>
            <input type="password" id="edit-password" placeholder="كلمة المرور..." style="width:100%;padding:15px;border:2px solid #e5e7eb;border-radius:10px;font-size:16px;margin-bottom:20px;text-align:center;">
            <div style="display:flex;gap:10px;justify-content:center;">
                <button onclick="verifyPassword()" style="background:#1e40af;color:#fff;padding:12px 24px;border:none;border-radius:10px;cursor:pointer;font-size:16px;">تأكيد</button>
                <button onclick="closeEditMode()" style="background:#ef4444;color:#fff;padding:12px 24px;border:none;border-radius:10px;cursor:pointer;font-size:16px;">إلغاء</button>
            </div>
        </div>
    </div>

    <!-- شريط أدوات التحرير -->
    <div id="edit-toolbar" style="display:none;position:fixed;top:20px;left:50%;transform:translateX(-50%);background:rgba(30,64,175,0.95);color:#fff;padding:15px 25px;border-radius:15px;z-index:9999;backdrop-filter:blur(10px);box-shadow:0 10px 30px rgba(0,0,0,0.3);">
        <div style="display:flex;align-items:center;gap:15px;font-size:14px;flex-wrap:wrap;">
            <span>🎨 وضع التحرير نشط</span>
            <button onclick="showLibraryPanel()" style="background:#8b5cf6;color:#fff;padding:8px 16px;border:none;border-radius:8px;cursor:pointer;">📚 مكتبات</button>
            <button onclick="saveChanges()" style="background:#10b981;color:#fff;padding:8px 16px;border:none;border-radius:8px;cursor:pointer;">💾 حفظ</button>
            <button onclick="exitEditMode()" style="background:#ef4444;color:#fff;padding:8px 16px;border:none;border-radius:8px;cursor:pointer;">❌ خروج</button>
        </div>
    </div>

    <!-- نافذة المكتبات -->
    <div id="library-panel" style="display:none;position:fixed;top:100px;right:20px;width:400px;max-height:500px;background:#fff;color:#222;padding:20px;border-radius:16px;box-shadow:0 20px 60px rgba(0,0,0,0.3);z-index:10001;direction:rtl;overflow-y:auto;">
        <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:15px;">
            <h3 style="margin:0;color:#1e40af;font-size:20px;">📚 مكتبة المكونات</h3>
            <button onclick="hideLibraryPanel()" style="background:#ef4444;color:white;border:none;font-size:16px;cursor:pointer;padding:5px 10px;border-radius:6px;font-weight:bold;">✕ إغلاق</button>
        </div>

        <div style="margin-bottom:15px;">
            <input id="library-search" type="text" placeholder="ابحث عن مكتبة... (مثال: charts, icons, fonts)" style="width:100%;padding:12px;border:2px solid #e5e7eb;border-radius:8px;font-size:14px;">
        </div>

        <div id="library-results" style="max-height:200px;overflow-y:auto;margin-bottom:15px;border:1px solid #e5e7eb;border-radius:8px;padding:10px;">
            <div style="text-align:center;color:#666;padding:20px;">اكتب في البحث لعرض المكتبات المتاحة</div>
        </div>

        <div style="display:flex;gap:10px;margin-bottom:15px;">
            <button id="add-library-btn" onclick="addSelectedLibrary()" style="flex:1;background:#1e40af;color:#fff;padding:12px;border:none;border-radius:8px;cursor:pointer;font-size:14px;" disabled>إضافة المكتبة</button>
            <button onclick="showCustomLibrary()" style="background:#8b5cf6;color:#fff;padding:12px 16px;border:none;border-radius:8px;cursor:pointer;font-size:14px;">مخصص</button>
        </div>

        <div style="display:flex;gap:10px;margin-bottom:15px;">
            <button onclick="testLibraryEffects()" style="flex:1;background:#10b981;color:#fff;padding:10px;border:none;border-radius:8px;cursor:pointer;font-size:13px;">🧪 اختبار المكتبات</button>
            <button onclick="showLibraryExamples()" style="background:#f59e0b;color:#fff;padding:10px 16px;border:none;border-radius:8px;cursor:pointer;font-size:13px;">💡 أمثلة</button>
        </div>

        <div style="display:flex;gap:5px;margin-bottom:15px;flex-wrap:wrap;">
            <button onclick="addQuickLibrary('jquery')" style="background:#0066cc;color:#fff;padding:6px 10px;border:none;border-radius:6px;cursor:pointer;font-size:11px;">⚡ jQuery</button>
            <button onclick="addQuickLibrary('sweetalert2')" style="background:#ff6b6b;color:#fff;padding:6px 10px;border:none;border-radius:6px;cursor:pointer;font-size:11px;">🍭 Sweet</button>
            <button onclick="addQuickLibrary('chart')" style="background:#4ecdc4;color:#fff;padding:6px 10px;border:none;border-radius:6px;cursor:pointer;font-size:11px;">📊 Chart</button>
            <button onclick="addQuickLibrary('animate')" style="background:#a8e6cf;color:#333;padding:6px 10px;border:none;border-radius:6px;cursor:pointer;font-size:11px;">🎭 Animate</button>
        </div>

        <!-- قائمة المكتبات المضافة -->
        <div style="border-top:2px solid #e5e7eb;padding-top:15px;">
            <h4 style="margin:0 0 10px 0;color:#1e40af;font-size:16px;">📦 المكتبات المضافة:</h4>
            <div id="added-libraries" style="max-height:150px;overflow-y:auto;">
                <div style="text-align:center;color:#666;padding:10px;font-size:13px;">لم يتم إضافة أي مكتبات بعد</div>
            </div>
        </div>

        <div id="custom-library" style="display:none;margin-top:15px;padding:15px;background:#f8fafc;border-radius:8px;">
            <label style="display:block;margin-bottom:8px;font-weight:bold;">رابط مخصص:</label>
            <input id="custom-url" type="text" placeholder="https://cdn.jsdelivr.net/npm/..." style="width:100%;padding:10px;border:1px solid #d1d5db;border-radius:6px;margin-bottom:10px;">
            <button onclick="addCustomLibrary()" style="background:#10b981;color:#fff;padding:8px 16px;border:none;border-radius:6px;cursor:pointer;">إضافة</button>
        </div>

        <div id="library-message" style="margin-top:10px;padding:10px;border-radius:6px;font-size:13px;display:none;"></div>
    </div>

    <script>
        // نظام تحرير الصفحة المتقدم
        let isEditMode = false;
        let originalContent = '';
        let selectedLibrary = null;
        let addedLibraries = [];

        // إعدادات النظام
        const EDIT_CONFIG = {
            activationCode: '54139',
            password: 'jfi4622@1',
            saveCode: 'save2024'
        };

        // قائمة المكتبات المتاحة
        const LIBRARIES = [
            {name: 'Bootstrap RTL', url: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css', type: 'css', desc: 'إطار تصميم متوافق مع العربية'},
            {name: 'Vazirmatn Font', url: 'https://cdn.jsdelivr.net/npm/@fontsource/vazirmatn@5.0.12/variable.css', type: 'css', desc: 'خط عربي عصري وجميل'},
            {name: 'Chart.js', url: 'https://cdn.jsdelivr.net/npm/chart.js', type: 'js', desc: 'رسوم بيانية تفاعلية متقدمة'},
            {name: 'ApexCharts', url: 'https://cdn.jsdelivr.net/npm/apexcharts', type: 'js', desc: 'مكتبة رسوم بيانية احترافية'},
            {name: 'SweetAlert2', url: 'https://cdn.jsdelivr.net/npm/sweetalert2@11', type: 'js', desc: 'نوافذ تنبيه وحوار عصرية'},
            {name: 'Alpine.js', url: 'https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js', type: 'js', desc: 'تفاعلات واجهة بسيطة وقوية'},
            {name: 'Vue.js', url: 'https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.prod.js', type: 'js', desc: 'إطار عمل واجهات حديث'},
            {name: 'jQuery', url: 'https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js', type: 'js', desc: 'مكتبة جافاسكريبت شهيرة'},
            {name: 'Animate.css', url: 'https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css', type: 'css', desc: 'حركات وتأثيرات CSS جاهزة'},
            {name: 'AOS Animation', url: 'https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css', type: 'css', desc: 'حركات عند التمرير'},
            {name: 'AOS JS', url: 'https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js', type: 'js', desc: 'مكتبة حركات التمرير'},
            {name: 'Swiper CSS', url: 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css', type: 'css', desc: 'سلايدر متطور'},
            {name: 'Swiper JS', url: 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js', type: 'js', desc: 'مكتبة سلايدر احترافية'},
            {name: 'Tippy.js', url: 'https://cdn.jsdelivr.net/npm/tippy.js@6/dist/tippy-bundle.umd.min.js', type: 'js', desc: 'تولتيب وبوب أوفر متطور'},
            {name: 'Tabler Icons', url: 'https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@2.47.0/tabler-icons.min.css', type: 'css', desc: 'أيقونات SVG حديثة ومتنوعة'},
            {name: 'Moment.js', url: 'https://cdn.jsdelivr.net/npm/moment@2.29.4/min/moment-with-locales.min.js', type: 'js', desc: 'تاريخ ووقت مع دعم العربية'},
            {name: 'Select2 CSS', url: 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css', type: 'css', desc: 'ستايل قوائم اختيار متقدمة'},
            {name: 'Select2 JS', url: 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js', type: 'js', desc: 'قوائم اختيار متقدمة'},
            {name: 'SortableJS', url: 'https://cdn.jsdelivr.net/npm/sortablejs@1.15.2/Sortable.min.js', type: 'js', desc: 'سحب وإفلات متطور'},
            {name: 'Lottie Player', url: 'https://cdn.jsdelivr.net/npm/@lottiefiles/lottie-player@2.0.2/dist/lottie-player.js', type: 'js', desc: 'حركات Lottie تفاعلية'},
            {name: 'Three.js', url: 'https://cdn.jsdelivr.net/npm/three@0.158.0/build/three.min.js', type: 'js', desc: 'رسوم ثلاثية الأبعاد'},
            {name: 'Particles.js', url: 'https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js', type: 'js', desc: 'تأثيرات جسيمات متحركة'},
            {name: 'Typed.js', url: 'https://cdn.jsdelivr.net/npm/typed.js@2.1.0/dist/typed.umd.js', type: 'js', desc: 'تأثير الكتابة التدريجية'},
            {name: 'CountUp.js', url: 'https://cdn.jsdelivr.net/npm/countup.js@2.8.0/dist/countUp.umd.js', type: 'js', desc: 'عداد أرقام متحرك'},
            {name: 'Prism.js', url: 'https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js', type: 'js', desc: 'تمييز أكواد البرمجة'},
            {name: 'Prism CSS', url: 'https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css', type: 'css', desc: 'ستايل تمييز الأكواد'},
            {name: 'Leaflet CSS', url: 'https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.css', type: 'css', desc: 'خرائط تفاعلية'},
            {name: 'Leaflet JS', url: 'https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.js', type: 'js', desc: 'مكتبة خرائط متقدمة'},
            {name: 'Masonry', url: 'https://cdn.jsdelivr.net/npm/masonry-layout@4.2.2/dist/masonry.pkgd.min.js', type: 'js', desc: 'تخطيط شبكي متجاوب'},
            {name: 'Isotope', url: 'https://cdn.jsdelivr.net/npm/isotope-layout@3.0.6/dist/isotope.pkgd.min.js', type: 'js', desc: 'فلترة وترتيب العناصر'},
            {name: 'Glide.js CSS', url: 'https://cdn.jsdelivr.net/npm/@glidejs/glide@3.6.0/dist/css/glide.core.min.css', type: 'css', desc: 'سلايدر خفيف'},
            {name: 'Glide.js', url: 'https://cdn.jsdelivr.net/npm/@glidejs/glide@3.6.0/dist/glide.min.js', type: 'js', desc: 'مكتبة سلايدر خفيفة'},
            {name: 'Fancybox CSS', url: 'https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0.33/dist/fancybox/fancybox.css', type: 'css', desc: 'عارض صور متطور'},
            {name: 'Fancybox JS', url: 'https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0.33/dist/fancybox/fancybox.umd.js', type: 'js', desc: 'مكتبة عرض الصور'},
            {name: 'Quill CSS', url: 'https://cdn.jsdelivr.net/npm/quill@1.3.7/dist/quill.snow.css', type: 'css', desc: 'محرر نصوص غني'},
            {name: 'Quill JS', url: 'https://cdn.jsdelivr.net/npm/quill@1.3.7/dist/quill.min.js', type: 'js', desc: 'محرر نصوص متقدم'},
            {name: 'Flatpickr CSS', url: 'https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.css', type: 'css', desc: 'منتقي تاريخ عصري'},
            {name: 'Flatpickr JS', url: 'https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.js', type: 'js', desc: 'مكتبة اختيار التاريخ'},
            {name: 'Notyf CSS', url: 'https://cdn.jsdelivr.net/npm/notyf@3.10.0/notyf.min.css', type: 'css', desc: 'إشعارات عصرية'},
            {name: 'Notyf JS', url: 'https://cdn.jsdelivr.net/npm/notyf@3.10.0/notyf.min.js', type: 'js', desc: 'مكتبة إشعارات جميلة'}
        ];

        // متغيرات للتحكم في الكود المدخل
        let enteredCode = '';
        let codeTimeout = null;

        // استماع لضغطات المفاتيح لتفعيل النظام
        document.addEventListener('keydown', function(e) {
            // إضافة الرقم المضغوط للكود
            if (e.key >= '0' && e.key <= '9') {
                enteredCode += e.key;

                // إعادة تعيين المؤقت
                clearTimeout(codeTimeout);
                codeTimeout = setTimeout(() => {
                    enteredCode = '';
                }, 3000); // مسح الكود بعد 3 ثوان

                // فحص الكود
                if (enteredCode === EDIT_CONFIG.activationCode) {
                    showPasswordDialog();
                    enteredCode = '';
                }
            }

            // إذا كان في وضع التحرير وضغط كود الحفظ
            if (isEditMode && enteredCode.includes(EDIT_CONFIG.saveCode)) {
                saveChanges();
                enteredCode = '';
            }

            // الخروج من وضع التحرير بـ ESC
            if (e.key === 'Escape' && isEditMode) {
                exitEditMode();
            }
        });

        // عرض نافذة كلمة المرور
        function showPasswordDialog() {
            document.getElementById('edit-overlay').style.display = 'block';
            document.getElementById('edit-password').focus();
        }

        // إغلاق نافذة التحرير
        function closeEditMode() {
            document.getElementById('edit-overlay').style.display = 'none';
            document.getElementById('edit-password').value = '';
        }

        // التحقق من كلمة المرور
        function verifyPassword() {
            const password = document.getElementById('edit-password').value;

            if (password === EDIT_CONFIG.password) {
                closeEditMode();
                activateEditMode();
            } else {
                alert('❌ كلمة المرور غير صحيحة!');
                document.getElementById('edit-password').value = '';
                document.getElementById('edit-password').focus();
            }
        }

        // تفعيل وضع التحرير
        function activateEditMode() {
            isEditMode = true;
            originalContent = document.body.innerHTML;

            // إظهار شريط الأدوات
            document.getElementById('edit-toolbar').style.display = 'block';

            // تفعيل التحرير المباشر
            document.body.contentEditable = true;
            document.body.style.outline = '2px dashed #1e40af';
            document.body.style.outlineOffset = '5px';

            // إضافة أنماط التحرير
            addEditStyles();

            // تفعيل السحب والإفلات
            enableDragAndDrop();

            // إظهار رسالة النجاح
            showNotification('✅ تم تفعيل وضع التحرير بنجاح!', 'success');
        }

        // إضافة أنماط التحرير
        function addEditStyles() {
            const style = document.createElement('style');
            style.id = 'edit-mode-styles';
            style.textContent = `
                [contenteditable="true"] {
                    transition: all 0.3s ease;
                }
                [contenteditable="true"]:hover {
                    background: rgba(30, 64, 175, 0.1) !important;
                    border-radius: 8px;
                }
                [contenteditable="true"]:focus {
                    background: rgba(30, 64, 175, 0.2) !important;
                    border-radius: 8px;
                    box-shadow: 0 0 0 2px #1e40af;
                }
                .draggable {
                    cursor: move;
                    position: relative;
                }
                .draggable:hover::before {
                    content: "🔄 اسحب لتحريك";
                    position: absolute;
                    top: -30px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: #1e40af;
                    color: white;
                    padding: 5px 10px;
                    border-radius: 5px;
                    font-size: 12px;
                    z-index: 1000;
                }
            `;
            document.head.appendChild(style);
        }

        // تفعيل السحب والإفلات
        function enableDragAndDrop() {
            const elements = document.querySelectorAll('section, div, header, footer');
            elements.forEach(el => {
                el.draggable = true;
                el.classList.add('draggable');

                el.addEventListener('dragstart', function(e) {
                    e.dataTransfer.setData('text/html', this.outerHTML);
                    e.dataTransfer.effectAllowed = 'move';
                    this.style.opacity = '0.5';
                });

                el.addEventListener('dragend', function(e) {
                    this.style.opacity = '1';
                });

                el.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';
                    this.style.background = 'rgba(30, 64, 175, 0.1)';
                });

                el.addEventListener('dragleave', function(e) {
                    this.style.background = '';
                });

                el.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.style.background = '';

                    const draggedHTML = e.dataTransfer.getData('text/html');
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = draggedHTML;
                    const draggedElement = tempDiv.firstChild;

                    this.parentNode.insertBefore(draggedElement, this.nextSibling);
                    enableDragAndDrop(); // إعادة تفعيل السحب للعنصر الجديد
                });
            });
        }

        // حفظ التغييرات
        function saveChanges() {
            try {
                // حفظ المحتوى
                localStorage.setItem('7c_academy_content', document.body.innerHTML);
                localStorage.setItem('7c_academy_timestamp', new Date().toISOString());

                // حفظ المكتبات
                saveLibrariesToStorage();

                showNotification('💾 تم حفظ التغييرات والمكتبات بنجاح!', 'success');
            } catch (error) {
                showNotification('❌ خطأ في حفظ التغييرات!', 'error');
            }
        }

        // الخروج من وضع التحرير
        function exitEditMode() {
            isEditMode = false;

            // إخفاء شريط الأدوات
            document.getElementById('edit-toolbar').style.display = 'none';

            // إلغاء التحرير
            document.body.contentEditable = false;
            document.body.style.outline = 'none';

            // إزالة أنماط التحرير
            const editStyles = document.getElementById('edit-mode-styles');
            if (editStyles) editStyles.remove();

            // إزالة السحب والإفلات
            const draggables = document.querySelectorAll('.draggable');
            draggables.forEach(el => {
                el.draggable = false;
                el.classList.remove('draggable');
            });

            showNotification('🚪 تم الخروج من وضع التحرير', 'info');
        }

        // وظائف نظام المكتبات
        function showLibraryPanel() {
            document.getElementById('library-panel').style.display = 'block';
            setupLibrarySearch();
            loadSavedLibraries();
        }

        function hideLibraryPanel() {
            document.getElementById('library-panel').style.display = 'none';
            document.getElementById('custom-library').style.display = 'none';

            // إعادة تعيين الحقول
            document.getElementById('library-search').value = '';
            document.getElementById('custom-url').value = '';
            document.getElementById('library-results').innerHTML = '<div style="text-align:center;color:#666;padding:20px;">اكتب في البحث لعرض المكتبات المتاحة</div>';
            document.getElementById('add-library-btn').disabled = true;
            selectedLibrary = null;

            // إخفاء رسالة المكتبة
            document.getElementById('library-message').style.display = 'none';
        }

        function setupLibrarySearch() {
            const searchInput = document.getElementById('library-search');
            const resultsDiv = document.getElementById('library-results');

            searchInput.oninput = function() {
                const query = searchInput.value.trim().toLowerCase();
                resultsDiv.innerHTML = '';
                selectedLibrary = null;
                document.getElementById('add-library-btn').disabled = true;

                if (!query) {
                    resultsDiv.innerHTML = '<div style="text-align:center;color:#666;padding:20px;">اكتب في البحث لعرض المكتبات المتاحة</div>';
                    return;
                }

                const filtered = LIBRARIES.filter(lib =>
                    lib.name.toLowerCase().includes(query) ||
                    lib.desc.toLowerCase().includes(query) ||
                    lib.type.toLowerCase().includes(query)
                );

                if (filtered.length === 0) {
                    resultsDiv.innerHTML = '<div style="text-align:center;color:#666;padding:20px;">لا توجد مكتبات مطابقة للبحث</div>';
                    return;
                }

                filtered.forEach(lib => {
                    const libDiv = document.createElement('div');
                    libDiv.style.cssText = `
                        padding: 12px;
                        margin-bottom: 8px;
                        border: 2px solid #e5e7eb;
                        border-radius: 8px;
                        cursor: pointer;
                        transition: all 0.2s;
                        background: #f9fafb;
                    `;

                    libDiv.innerHTML = `
                        <div style="font-weight:bold;color:#1e40af;margin-bottom:4px;">
                            ${lib.name}
                            <span style="background:${lib.type === 'js' ? '#10b981' : '#8b5cf6'};color:white;padding:2px 6px;border-radius:4px;font-size:11px;margin-right:8px;">${lib.type.toUpperCase()}</span>
                        </div>
                        <div style="font-size:13px;color:#666;">${lib.desc}</div>
                        <div style="font-size:11px;color:#999;margin-top:4px;word-break:break-all;">${lib.url}</div>
                    `;

                    libDiv.onmouseover = () => {
                        libDiv.style.borderColor = '#1e40af';
                        libDiv.style.background = '#eff6ff';
                    };

                    libDiv.onmouseout = () => {
                        if (selectedLibrary !== lib) {
                            libDiv.style.borderColor = '#e5e7eb';
                            libDiv.style.background = '#f9fafb';
                        }
                    };

                    libDiv.onclick = () => {
                        // إزالة التحديد من العناصر الأخرى
                        resultsDiv.querySelectorAll('div').forEach(d => {
                            d.style.borderColor = '#e5e7eb';
                            d.style.background = '#f9fafb';
                        });

                        // تحديد العنصر الحالي
                        libDiv.style.borderColor = '#1e40af';
                        libDiv.style.background = '#eff6ff';

                        selectedLibrary = lib;
                        document.getElementById('add-library-btn').disabled = false;
                        showLibraryMessage(`تم اختيار: ${lib.name}`, 'info');
                    };

                    resultsDiv.appendChild(libDiv);
                });
            };
        }

        function addSelectedLibrary() {
            if (!selectedLibrary) {
                showLibraryMessage('يرجى اختيار مكتبة أولاً', 'error');
                return;
            }

            // إضافة المكتبة مع رسالة واضحة
            showLibraryMessage(`🔄 جاري إضافة ${selectedLibrary.name}...`, 'info');
            addLibraryToPage(selectedLibrary.url, selectedLibrary.type, selectedLibrary.name);
        }

        function showCustomLibrary() {
            const customDiv = document.getElementById('custom-library');
            customDiv.style.display = customDiv.style.display === 'none' ? 'block' : 'none';
        }

        function addCustomLibrary() {
            const url = document.getElementById('custom-url').value.trim();
            if (!url) {
                showLibraryMessage('يرجى إدخال رابط صحيح', 'error');
                return;
            }

            const type = url.endsWith('.css') ? 'css' : url.endsWith('.js') ? 'js' : null;
            if (!type) {
                showLibraryMessage('الرابط يجب أن ينتهي بـ .css أو .js', 'error');
                return;
            }

            const name = url.split('/').pop().split('.')[0];
            addLibraryToPage(url, type, name);
            document.getElementById('custom-url').value = '';
        }

        function addLibraryToPage(url, type, name) {
            // فحص إذا كانت المكتبة موجودة مسبقاً
            const existing = addedLibraries.find(lib => lib.url === url);
            if (existing) {
                showLibraryMessage(`المكتبة ${name} موجودة مسبقاً`, 'warning');
                return;
            }

            // فحص إذا كان العنصر موجود في DOM
            const existingElement = document.querySelector(`[src="${url}"], [href="${url}"]`);
            if (existingElement) {
                showLibraryMessage(`المكتبة ${name} محملة مسبقاً في الصفحة`, 'warning');
                return;
            }

            let element;
            if (type === 'js') {
                element = document.createElement('script');
                element.src = url;
                element.async = false;
                element.defer = false;
                // إضافة للـ head بدلاً من body للتأكد من التحميل
                document.head.appendChild(element);
            } else if (type === 'css') {
                element = document.createElement('link');
                element.rel = 'stylesheet';
                element.href = url;
                element.type = 'text/css';
                document.head.appendChild(element);
            }

            // إضافة المكتبة للقائمة
            const libraryInfo = {
                name: name,
                url: url,
                type: type,
                element: element,
                id: Date.now() + Math.random(),
                loaded: false
            };

            addedLibraries.push(libraryInfo);

            // تحديث العرض فوراً
            updateAddedLibrariesDisplay();
            saveLibrariesToStorage();

            element.onload = () => {
                libraryInfo.loaded = true;
                showLibraryMessage(`✅ تم تحميل ${name} بنجاح! يمكنك الآن اختبارها.`, 'success');
                updateLibraryStatus(libraryInfo.id, 'loaded');

                // إضافة تنبيه للمستخدم
                setTimeout(() => {
                    showLibraryMessage(`💡 اضغط "🧪 اختبار المكتبات" لرؤية ${name} في العمل!`, 'info');
                }, 2000);
            };

            element.onerror = () => {
                showLibraryMessage(`❌ فشل في تحميل ${name}`, 'error');
                updateLibraryStatus(libraryInfo.id, 'error');
            };

            showLibraryMessage(`🔄 جاري تحميل ${name}...`, 'info');
        }

        function updateAddedLibrariesDisplay() {
            const container = document.getElementById('added-libraries');

            if (addedLibraries.length === 0) {
                container.innerHTML = '<div style="text-align:center;color:#666;padding:10px;font-size:13px;">لم يتم إضافة أي مكتبات بعد</div>';
                return;
            }

            container.innerHTML = '';

            addedLibraries.forEach(lib => {
                const libDiv = document.createElement('div');
                libDiv.id = `lib-${lib.id}`;
                libDiv.style.cssText = `
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 12px;
                    margin-bottom: 6px;
                    background: #f8fafc;
                    border: 1px solid #e5e7eb;
                    border-radius: 6px;
                    font-size: 13px;
                `;

                libDiv.innerHTML = `
                    <div style="flex: 1;">
                        <div style="font-weight: bold; color: #1e40af;">
                            ${lib.name}
                            <span style="background:${lib.type === 'js' ? '#10b981' : '#8b5cf6'};color:white;padding:1px 4px;border-radius:3px;font-size:10px;margin-right:6px;">${lib.type.toUpperCase()}</span>
                        </div>
                        <div style="color: #666; font-size: 11px; margin-top: 2px;" class="lib-status">🔄 جاري التحميل...</div>
                    </div>
                    <button onclick="removeLibrary('${lib.id}')" style="background:#ef4444;color:white;border:none;border-radius:4px;padding:4px 8px;cursor:pointer;font-size:11px;">حذف</button>
                `;

                container.appendChild(libDiv);
            });
        }

        function updateLibraryStatus(libId, status) {
            const libElement = document.getElementById(`lib-${libId}`);
            if (!libElement) return;

            const statusElement = libElement.querySelector('.lib-status');
            if (!statusElement) return;

            switch(status) {
                case 'loaded':
                    statusElement.innerHTML = '✅ تم التحميل بنجاح';
                    statusElement.style.color = '#10b981';
                    break;
                case 'error':
                    statusElement.innerHTML = '❌ فشل في التحميل';
                    statusElement.style.color = '#ef4444';
                    break;
            }
        }

        function removeLibrary(libId) {
            const libIndex = addedLibraries.findIndex(lib => lib.id == libId);
            if (libIndex === -1) return;

            const lib = addedLibraries[libIndex];

            // إزالة العنصر من DOM
            if (lib.element && lib.element.parentNode) {
                lib.element.parentNode.removeChild(lib.element);
            }

            // إزالة من القائمة
            addedLibraries.splice(libIndex, 1);

            // تحديث العرض
            updateAddedLibrariesDisplay();

            showLibraryMessage(`تم حذف ${lib.name}`, 'info');
            saveLibrariesToStorage();
        }

        // اختبار تأثيرات المكتبات
        function testLibraryEffects() {
            let results = [];
            let hasEffects = false;

            // اختبار بسيط للمكتبات المحملة
            console.log('🔍 فحص المكتبات المتاحة...');

            // اختبار jQuery
            if (window.jQuery || window.$) {
                results.push('✅ jQuery متاح');
                try {
                    // تأثير بسيط وواضح
                    const title = document.querySelector('h1');
                    if (title) {
                        $(title).css({
                            'transition': 'all 1s ease',
                            'transform': 'scale(1.1)',
                            'color': '#10b981'
                        });
                        setTimeout(() => {
                            $(title).css({
                                'transform': 'scale(1)',
                                'color': ''
                            });
                        }, 2000);
                        hasEffects = true;
                        results.push('🎨 تأثير jQuery مطبق على العنوان');
                    }
                } catch(e) {
                    results.push('❌ خطأ في jQuery: ' + e.message);
                }
            } else {
                results.push('⚠️ jQuery غير متاح - جرب إضافة مكتبة jquery');
            }

            // اختبار SweetAlert2
            if (window.Swal) {
                results.push('✅ SweetAlert2 متاح');
                Swal.fire({
                    title: '🎉 ممتاز!',
                    text: 'SweetAlert2 يعمل بشكل رائع!',
                    icon: 'success',
                    timer: 3000,
                    timerProgressBar: true,
                    position: 'center'
                });
                hasEffects = true;
            } else {
                results.push('⚠️ SweetAlert2 غير متاح - جرب إضافة مكتبة sweetalert2');
            }

            // اختبار Chart.js
            if (window.Chart) {
                results.push('✅ Chart.js متاح');
                createSimpleChart();
                hasEffects = true;
                results.push('📊 رسم بياني تم إنشاؤه');
            } else {
                results.push('⚠️ Chart.js غير متاح - جرب إضافة مكتبة chart');
            }

            // تأثير بصري بسيط بدون مكتبات
            if (!hasEffects) {
                createSimpleEffect();
                results.push('🎭 تأثير بصري بسيط تم تطبيقه');
            }

            // عرض النتائج
            const resultText = results.join('\n');
            console.log('نتائج الاختبار:', results);

            // عرض النتائج في نافذة
            setTimeout(() => {
                alert('🧪 نتائج اختبار المكتبات:\n\n' + resultText);
            }, 500);
        }

        // إنشاء رسم بياني بسيط
        function createSimpleChart() {
            if (!window.Chart) return;

            // إزالة أي رسم سابق
            const existingChart = document.getElementById('demo-chart');
            if (existingChart) existingChart.remove();

            // إنشاء عنصر للرسم
            const chartContainer = document.createElement('div');
            chartContainer.id = 'demo-chart';
            chartContainer.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 20px;
                border-radius: 15px;
                box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                z-index: 10002;
                width: 400px;
                height: 300px;
            `;

            const canvas = document.createElement('canvas');
            canvas.width = 350;
            canvas.height = 250;
            chartContainer.appendChild(canvas);

            const closeBtn = document.createElement('button');
            closeBtn.textContent = '✕ إغلاق';
            closeBtn.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                background: #ef4444;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 5px;
                cursor: pointer;
            `;
            closeBtn.onclick = () => chartContainer.remove();
            chartContainer.appendChild(closeBtn);

            document.body.appendChild(chartContainer);

            // إنشاء الرسم البياني
            new Chart(canvas, {
                type: 'bar',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل'],
                    datasets: [{
                        label: 'المبيعات',
                        data: [12, 19, 3, 17],
                        backgroundColor: ['#1e40af', '#8b5cf6', '#10b981', '#f59e0b']
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: '📊 اختبار Chart.js - يعمل بشكل رائع!'
                        }
                    }
                }
            });

            // إزالة تلقائية بعد 10 ثوان
            setTimeout(() => {
                if (chartContainer.parentNode) {
                    chartContainer.remove();
                }
            }, 10000);
        }

        // تأثير بصري بسيط بدون مكتبات
        function createSimpleEffect() {
            const effect = document.createElement('div');
            effect.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: linear-gradient(45deg, #1e40af, #8b5cf6);
                color: white;
                padding: 30px;
                border-radius: 20px;
                font-size: 20px;
                font-weight: bold;
                z-index: 10002;
                box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                animation: simpleFloat 2s ease-in-out;
                text-align: center;
                direction: rtl;
            `;

            effect.innerHTML = `
                <div>🎉 النظام يعمل!</div>
                <div style="font-size: 14px; margin-top: 10px; opacity: 0.8;">
                    جرب إضافة مكتبات للمزيد من التأثيرات
                </div>
            `;

            document.body.appendChild(effect);

            // إضافة الحركة
            const style = document.createElement('style');
            style.textContent = `
                @keyframes simpleFloat {
                    0% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
                    50% { transform: translate(-50%, -50%) scale(1.1); opacity: 1; }
                    100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
                }
            `;
            document.head.appendChild(style);

            setTimeout(() => {
                effect.remove();
                style.remove();
            }, 3000);
        }

        // إنشاء رسم بياني تجريبي
        function createTestChart() {
            if (!window.Chart) return;

            // إنشاء canvas للرسم البياني
            const canvas = document.createElement('canvas');
            canvas.id = 'test-chart';
            canvas.width = 300;
            canvas.height = 200;
            canvas.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                z-index: 10002;
                padding: 20px;
            `;

            document.body.appendChild(canvas);

            // إنشاء الرسم البياني
            new Chart(canvas, {
                type: 'doughnut',
                data: {
                    labels: ['JavaScript', 'CSS', 'HTML'],
                    datasets: [{
                        data: [60, 25, 15],
                        backgroundColor: ['#1e40af', '#8b5cf6', '#10b981']
                    }]
                },
                options: {
                    responsive: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'اختبار Chart.js'
                        }
                    }
                }
            });

            // إزالة الرسم البياني بعد 5 ثوان
            setTimeout(() => {
                canvas.remove();
            }, 5000);
        }

        // إنشاء تأثير الكتابة
        function createTypedEffect() {
            if (!window.Typed) return;

            const element = document.createElement('div');
            element.style.cssText = `
                position: fixed;
                top: 30%;
                left: 50%;
                transform: translateX(-50%);
                background: #1e40af;
                color: white;
                padding: 20px 30px;
                border-radius: 15px;
                font-size: 18px;
                z-index: 10002;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                direction: rtl;
            `;

            document.body.appendChild(element);

            new Typed(element, {
                strings: ['مرحباً بك في أكاديمية 7C!', 'Typed.js يعمل بشكل رائع!', 'جرب المزيد من المكتبات!'],
                typeSpeed: 50,
                backSpeed: 30,
                loop: false,
                onComplete: () => {
                    setTimeout(() => element.remove(), 2000);
                }
            });
        }

        // عرض أمثلة للمكتبات
        function showLibraryExamples() {
            const examples = `
🎨 أمثلة للمكتبات المتاحة:

📊 للرسوم البيانية:
• Chart.js - رسوم بيانية تفاعلية
• ApexCharts - رسوم احترافية

🎭 للحركات والتأثيرات:
• Animate.css - حركات CSS جاهزة
• AOS - حركات عند التمرير
• Typed.js - تأثير الكتابة

🎨 للتصميم:
• Bootstrap RTL - تصميم متجاوب
• SweetAlert2 - نوافذ جميلة

⚡ للتفاعل:
• jQuery - تفاعلات سهلة
• Alpine.js - تفاعلات حديثة

🔧 نصائح:
1. أضف المكتبة أولاً
2. اضغط "🧪 اختبار المكتبات"
3. شاهد التأثيرات تعمل!
            `;

            alert(examples);
        }

        // إضافة سريعة للمكتبات الشائعة
        function addQuickLibrary(libName) {
            const quickLibs = {
                'jquery': {
                    name: 'jQuery',
                    url: 'https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js',
                    type: 'js'
                },
                'sweetalert2': {
                    name: 'SweetAlert2',
                    url: 'https://cdn.jsdelivr.net/npm/sweetalert2@11',
                    type: 'js'
                },
                'chart': {
                    name: 'Chart.js',
                    url: 'https://cdn.jsdelivr.net/npm/chart.js',
                    type: 'js'
                },
                'animate': {
                    name: 'Animate.css',
                    url: 'https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css',
                    type: 'css'
                }
            };

            const lib = quickLibs[libName];
            if (lib) {
                showLibraryMessage(`⚡ إضافة سريعة: ${lib.name}`, 'info');
                addLibraryToPage(lib.url, lib.type, lib.name);
            }
        }

        function saveLibrariesToStorage() {
            const librariesData = addedLibraries.map(lib => ({
                name: lib.name,
                url: lib.url,
                type: lib.type
            }));
            localStorage.setItem('7c_added_libraries', JSON.stringify(librariesData));
        }

        function loadSavedLibraries() {
            const saved = localStorage.getItem('7c_added_libraries');
            if (!saved) return;

            try {
                const librariesData = JSON.parse(saved);
                librariesData.forEach(lib => {
                    // فحص إذا لم تكن المكتبة مضافة مسبقاً
                    const existing = addedLibraries.find(existing => existing.url === lib.url);
                    if (!existing) {
                        addLibraryToPage(lib.url, lib.type, lib.name);
                    }
                });
            } catch (error) {
                console.error('خطأ في تحميل المكتبات المحفوظة:', error);
            }
        }

        function showLibraryMessage(message, type) {
            const messageDiv = document.getElementById('library-message');
            messageDiv.style.display = 'block';
            messageDiv.textContent = message;

            const colors = {
                success: '#10b981',
                error: '#ef4444',
                warning: '#f59e0b',
                info: '#1e40af'
            };

            messageDiv.style.background = colors[type] || colors.info;
            messageDiv.style.color = 'white';

            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 3000);
        }

        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#1e40af'};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 10001;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                animation: slideIn 0.3s ease;
                direction: rtl;
                font-weight: bold;
            `;

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // استرجاع المحتوى المحفوظ عند تحميل الصفحة
        window.addEventListener('DOMContentLoaded', function() {
            // استرجاع المحتوى
            const savedContent = localStorage.getItem('7c_academy_content');
            const savedTimestamp = localStorage.getItem('7c_academy_timestamp');

            if (savedContent && savedTimestamp) {
                const saveDate = new Date(savedTimestamp);
                const now = new Date();
                const daysDiff = (now - saveDate) / (1000 * 60 * 60 * 24);

                // إذا كان الحفظ خلال آخر 30 يوم
                if (daysDiff <= 30) {
                    document.body.innerHTML = savedContent;
                    console.log('تم استرجاع المحتوى المحفوظ من:', saveDate.toLocaleString('ar'));
                }
            }

            // استرجاع المكتبات المحفوظة
            setTimeout(() => {
                loadSavedLibraries();
            }, 1000); // تأخير لضمان تحميل الصفحة أولاً
        });

        // إضافة أنماط الحركة
        const animationStyles = document.createElement('style');
        animationStyles.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(animationStyles);

        // الكود الأصلي للموقع
        // AI Assistant Functions
        function toggleAI() {
            const panel = document.getElementById('ai-panel');
            const isVisible = panel.style.transform === 'translateY(0px)' && panel.style.opacity === '1';

            if (isVisible) {
                panel.style.transform = 'translateY(100%)';
                panel.style.opacity = '0';
            } else {
                panel.style.transform = 'translateY(0px)';
                panel.style.opacity = '1';
            }
        }

        function sendAIMessage() {
            const input = document.getElementById('ai-input');
            const messages = document.getElementById('ai-messages');

            if (input.value.trim()) {
                // Add user message
                const userMessage = document.createElement('div');
                userMessage.className = 'user-message mb-3 text-left';
                userMessage.innerHTML = `
                    <div class="bg-blue-600/20 p-3 rounded-lg inline-block max-w-xs">
                        <p class="text-sm text-white">${input.value}</p>
                    </div>
                `;
                messages.appendChild(userMessage);

                // Simulate AI response
                setTimeout(() => {
                    const aiMessage = document.createElement('div');
                    aiMessage.className = 'ai-message mb-3';
                    aiMessage.innerHTML = `
                        <div class="bg-gradient-to-r from-purple-600/20 to-blue-600/20 p-3 rounded-lg">
                            <p class="text-sm">شكراً لك! سأساعدك في تحليل هذا الموضوع وتقديم أفضل الحلول.</p>
                        </div>
                    `;
                    messages.appendChild(aiMessage);
                    messages.scrollTop = messages.scrollHeight;
                }, 1000);

                input.value = '';
                messages.scrollTop = messages.scrollHeight;
            }
        }

        // Dashboard Functions
        function showDashboard() {
            const dashboard = document.getElementById('dashboard');
            dashboard.classList.remove('hidden');
            dashboard.scrollIntoView({ behavior: 'smooth' });
        }

        function showNotifications() {
            alert('🔔 لديك 3 إشعارات جديدة:\n\n1. تم تسجيل لاعب جديد\n2. جلسة تدريب غداً الساعة 4 مساءً\n3. تقرير الأداء الشهري جاهز');
        }

        function startAIDemo() {
            toggleAI();
            setTimeout(() => {
                const input = document.getElementById('ai-input');
                input.value = 'أريد تحليل أداء الفريق';
                sendAIMessage();
            }, 500);
        }

        // Statistics Counter Animation
        function animateCounters() {
            const counters = document.querySelectorAll('.stats-counter');

            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const duration = 2000;
                const step = target / (duration / 16);
                let current = 0;

                const timer = setInterval(() => {
                    current += step;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    counter.textContent = Math.floor(current);
                }, 16);
            });
        }

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    if (entry.target.classList.contains('stats-counter')) {
                        animateCounters();
                    }
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Charts initialization
        function initCharts() {
            // Hero Chart
            const heroCtx = document.getElementById('heroChart');
            if (heroCtx) {
                new Chart(heroCtx, {
                    type: 'line',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                        datasets: [{
                            label: 'الأداء',
                            data: [65, 78, 85, 92, 88, 94],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false }
                        },
                        scales: {
                            y: { display: false },
                            x: { display: false }
                        }
                    }
                });
            }

            // Performance Chart
            const perfCtx = document.getElementById('performanceChart');
            if (perfCtx) {
                new Chart(perfCtx, {
                    type: 'bar',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                        datasets: [{
                            label: 'عدد اللاعبين',
                            data: [850, 920, 1050, 1150, 1200, 1250, 1300, 1280, 1350, 1400, 1380, 1450],
                            backgroundColor: 'rgba(59, 130, 246, 0.8)',
                            borderColor: '#3b82f6',
                            borderWidth: 2,
                            borderRadius: 8
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                labels: { color: '#fff' }
                            }
                        },
                        scales: {
                            y: {
                                ticks: { color: '#fff' },
                                grid: { color: 'rgba(255,255,255,0.1)' }
                            },
                            x: {
                                ticks: { color: '#fff' },
                                grid: { color: 'rgba(255,255,255,0.1)' }
                            }
                        }
                    }
                });
            }
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize charts
            initCharts();

            // Setup intersection observer
            document.querySelectorAll('.slide-in-up, .slide-in-right, .slide-in-left, .stats-counter').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(50px)';
                observer.observe(el);
            });

            // AI input enter key
            document.getElementById('ai-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendAIMessage();
                }
            });

            // Password input enter key
            document.getElementById('edit-password').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    verifyPassword();
                }
            });

            // Smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
