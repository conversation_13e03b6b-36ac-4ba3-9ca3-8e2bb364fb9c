<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الإدارة - أكاديمية 7C الرياضية</title>
    
    <!-- External Libraries -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.0/dist/sweetalert2.all.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    
    <style>
        /* ==================== متغيرات الألوان الأساسية ==================== */
        :root {
            --primary-bg: #1a1a1a;
            --brand-primary: #8B4513;
            --brand-secondary: #D2691E;
            --accent-dark: #2F4F4F;
            --success: #28a745;
            --danger: #dc3545;
            --warning: #ffc107;
            --info: #17a2b8;
            --glass: rgba(255,255,255,0.1);
            --border: rgba(255,255,255,0.2);
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --shadow-dark: 0 8px 32px rgba(0, 0, 0, 0.3);
            --shadow-glow: 0 0 20px rgba(139, 69, 19, 0.3);
        }

        /* ==================== إعدادات أساسية ==================== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--primary-bg);
            color: var(--text-primary);
            overflow-x: hidden;
        }

        /* ==================== التخطيط الرئيسي ==================== */
        .main-layout {
            display: grid;
            grid-template-columns: 280px 1fr;
            grid-template-rows: 70px 1fr;
            grid-template-areas: 
                "sidebar header"
                "sidebar content";
            min-height: 100vh;
        }

        /* ==================== الشريط الجانبي ==================== */
        .sidebar {
            grid-area: sidebar;
            background: linear-gradient(180deg, var(--brand-primary) 0%, var(--accent-dark) 100%);
            border-left: 1px solid var(--border);
            overflow-y: auto;
            transition: all 0.3s ease;
            position: relative;
        }

        .sidebar-header {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid var(--border);
            background: rgba(0, 0, 0, 0.2);
        }

        .academy-logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--brand-secondary), #ff8c42);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            font-size: 1.5rem;
            color: white;
            box-shadow: var(--shadow-glow);
            animation: pulse 3s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .academy-name {
            font-size: 1.1rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.25rem;
        }

        .academy-subtitle {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.7);
        }

        /* ==================== قائمة التنقل ==================== */
        .nav-menu {
            padding: 1rem 0;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
            position: relative;
            cursor: pointer;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-right-color: var(--brand-secondary);
            transform: translateX(-5px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border-right-color: var(--brand-secondary);
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
        }

        .nav-icon {
            width: 20px;
            margin-left: 1rem;
            font-size: 1.1rem;
        }

        .nav-text {
            font-weight: 600;
            font-size: 0.95rem;
        }

        .nav-badge {
            margin-right: auto;
            background: var(--danger);
            color: white;
            font-size: 0.7rem;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            font-weight: 600;
        }

        /* ==================== رأس الصفحة ==================== */
        .header {
            grid-area: header;
            background: var(--glass);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 2rem;
            box-shadow: var(--shadow-dark);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--brand-primary);
        }

        .breadcrumb {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-btn {
            background: var(--glass);
            border: 1px solid var(--border);
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .header-btn:hover {
            background: var(--brand-primary);
            transform: translateY(-2px);
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem 1rem;
            background: var(--glass);
            border: 1px solid var(--border);
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-profile:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        .user-avatar {
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .user-info {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .user-role {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        /* ==================== المحتوى الرئيسي ==================== */
        .content {
            grid-area: content;
            padding: 2rem;
            overflow-y: auto;
            background: linear-gradient(135deg, var(--primary-bg) 0%, #1e1e2e 100%);
        }

        /* ==================== بطاقات KPI ==================== */
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .kpi-card {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .kpi-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-dark);
        }

        .kpi-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--brand-primary), var(--brand-secondary));
        }

        .kpi-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .kpi-title {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 600;
        }

        .kpi-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .kpi-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .kpi-change {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .kpi-change.positive {
            color: var(--success);
        }

        .kpi-change.negative {
            color: var(--danger);
        }

        .kpi-change.neutral {
            color: var(--text-secondary);
        }

        /* ==================== تصميم متجاوب ==================== */
        @media (max-width: 1024px) {
            .main-layout {
                grid-template-columns: 1fr;
                grid-template-areas: 
                    "header"
                    "content";
            }
            
            .sidebar {
                position: fixed;
                left: -280px;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            
            .sidebar.open {
                left: 0;
            }
        }

        @media (max-width: 768px) {
            .content {
                padding: 1rem;
            }
            
            .kpi-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .header {
                padding: 0 1rem;
            }
            
            .page-title {
                font-size: 1.2rem;
            }
            
            .user-info {
                display: none;
            }
        }

        /* ==================== تصميم النوافذ المنبثقة ==================== */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 1000;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .modal-content {
            background: var(--primary-bg);
            border: 1px solid var(--border);
            border-radius: 16px;
            max-width: 600px;
            width: 100%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid var(--border);
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            color: white;
            border-radius: 16px 16px 0 0;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 700;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: rotate(90deg);
        }

        .modal-body {
            padding: 1.5rem;
            color: var(--text-primary);
        }

        .modal-body input,
        .modal-body select,
        .modal-body textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border);
            border-radius: 8px;
            background: var(--glass);
            color: var(--text-primary);
            font-family: inherit;
            transition: all 0.3s ease;
        }

        .modal-body input:focus,
        .modal-body select:focus,
        .modal-body textarea:focus {
            outline: none;
            border-color: var(--brand-primary);
            box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
        }

        .modal-body label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* ==================== تصميم التبويبات ==================== */
        .match-tab {
            background: transparent;
            border: none;
            border-bottom: 3px solid transparent;
            color: var(--text-secondary);
            padding: 1rem 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            font-size: 0.95rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .match-tab:hover {
            color: var(--text-primary);
            background: rgba(255, 255, 255, 0.05);
        }

        .match-tab.active {
            color: var(--brand-primary);
            border-bottom-color: var(--brand-primary);
            background: rgba(139, 69, 19, 0.1);
        }

        .match-tab-content {
            display: none;
        }

        .match-tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease-in;
        }

        /* ==================== تصميم بطاقات المباريات ==================== */
        .match-card {
            background: rgba(255,255,255,0.05);
            border: 1px solid var(--border);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .match-card:hover {
            background: rgba(255,255,255,0.1);
            transform: translateY(-2px);
            box-shadow: var(--shadow-dark);
        }

        .match-card.live {
            border-right: 4px solid #ef4444;
            animation: livePulse 2s infinite;
        }

        .match-card.finished {
            border-right: 4px solid #10b981;
        }

        .match-card.scheduled {
            border-right: 4px solid #3b82f6;
        }

        @keyframes livePulse {
            0%, 100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4); }
            50% { box-shadow: 0 0 0 10px rgba(239, 68, 68, 0); }
        }

        .team-logo {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .match-score {
            font-size: 2rem;
            font-weight: 800;
            color: var(--text-primary);
            text-align: center;
            min-width: 60px;
        }

        .live-indicator {
            background: #ef4444;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            animation: pulse 2s infinite;
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .live-indicator::before {
            content: '';
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="main-layout">
        <!-- الشريط الجانبي -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="academy-logo">
                    <i class="fas fa-dumbbell"></i>
                </div>
                <div class="academy-name">أكاديمية 7C</div>
                <div class="academy-subtitle">نظام الإدارة المتكامل</div>
            </div>

            <nav class="nav-menu">
                <div class="nav-item">
                    <div class="nav-link active" onclick="showSection('dashboard')">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">لوحة المعلومات</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('players')">
                        <i class="fas fa-users nav-icon"></i>
                        <span class="nav-text">إدارة اللاعبين</span>
                        <span class="nav-badge" id="playersCount">0</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('coaches')">
                        <i class="fas fa-user-tie nav-icon"></i>
                        <span class="nav-text">إدارة المدربين</span>
                        <span class="nav-badge" id="coachesCount">0</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('attendance')">
                        <i class="fas fa-calendar-check nav-icon"></i>
                        <span class="nav-text">الحضور والغياب</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('financial')">
                        <i class="fas fa-money-bill-wave nav-icon"></i>
                        <span class="nav-text">النظام المالي</span>
                        <span class="nav-badge" id="pendingPayments">0</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('applications')">
                        <i class="fas fa-file-alt nav-icon"></i>
                        <span class="nav-text">إدارة الطلبات</span>
                        <span class="nav-badge" id="pendingApplicationsCount">0</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('matches')">
                        <i class="fas fa-futbol nav-icon"></i>
                        <span class="nav-text">إدارة المباريات</span>
                        <span class="nav-badge" id="upcomingMatchesCount">0</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('reports')">
                        <i class="fas fa-chart-line nav-icon"></i>
                        <span class="nav-text">التقارير والإحصائيات</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('messages')">
                        <i class="fas fa-envelope nav-icon"></i>
                        <span class="nav-text">نظام الرسائل</span>
                        <span class="nav-badge" id="unreadMessages">0</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="showSection('settings')">
                        <i class="fas fa-cog nav-icon"></i>
                        <span class="nav-text">إعدادات النظام</span>
                    </div>
                </div>

                <div class="nav-item">
                    <div class="nav-link" onclick="logout()">
                        <i class="fas fa-sign-out-alt nav-icon"></i>
                        <span class="nav-text">تسجيل الخروج</span>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- رأس الصفحة -->
        <header class="header">
            <div class="header-left">
                <button class="header-btn" onclick="toggleSidebar()" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div>
                    <h1 class="page-title" id="pageTitle">لوحة المعلومات</h1>
                    <div class="breadcrumb" id="breadcrumb">الرئيسية / لوحة المعلومات</div>
                </div>
            </div>

            <div class="header-right">
                <button class="header-btn" onclick="showNotifications()">
                    <i class="fas fa-bell"></i>
                    <span id="notificationCount">3</span>
                </button>

                <button class="header-btn" onclick="showQuickActions()">
                    <i class="fas fa-plus"></i>
                    إضافة سريعة
                </button>

                <div class="user-profile" onclick="showUserMenu()">
                    <div class="user-avatar">م</div>
                    <div class="user-info">
                        <div class="user-name" id="userName">مدير النظام</div>
                        <div class="user-role">مدير عام</div>
                    </div>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </header>

        <!-- المحتوى الرئيسي -->
        <main class="content">
            <!-- قسم لوحة المعلومات -->
            <div id="dashboardSection" class="content-section active">
                <!-- بطاقات KPI -->
                <div class="kpi-grid">
                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">إجمالي اللاعبين</div>
                            <div class="kpi-icon">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="kpi-value" id="totalPlayersKPI">0</div>
                        <div class="kpi-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12% من الشهر الماضي</span>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">المدربين النشطين</div>
                            <div class="kpi-icon">
                                <i class="fas fa-user-tie"></i>
                            </div>
                        </div>
                        <div class="kpi-value" id="activeCoachesKPI">0</div>
                        <div class="kpi-change neutral">
                            <i class="fas fa-minus"></i>
                            <span>لا تغيير</span>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">معدل الحضور اليومي</div>
                            <div class="kpi-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                        </div>
                        <div class="kpi-value" id="attendanceRateKPI">0%</div>
                        <div class="kpi-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+5% من أمس</span>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">الإيرادات الشهرية</div>
                            <div class="kpi-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                        </div>
                        <div class="kpi-value" id="monthlyRevenueKPI">0 ر.س</div>
                        <div class="kpi-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+8% من الشهر الماضي</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قسم إدارة اللاعبين -->
            <div id="playersSection" class="content-section" style="display: none;">
                <h2>إدارة اللاعبين</h2>
                <p>قسم إدارة اللاعبين قيد التطوير...</p>
            </div>

            <!-- قسم إدارة المدربين -->
            <div id="coachesSection" class="content-section" style="display: none;">
                <h2>إدارة المدربين</h2>
                <p>قسم إدارة المدربين قيد التطوير...</p>
            </div>

            <!-- قسم الحضور والغياب -->
            <div id="attendanceSection" class="content-section" style="display: none;">
                <h2>الحضور والغياب</h2>
                <p>قسم الحضور والغياب قيد التطوير...</p>
            </div>

            <!-- قسم النظام المالي -->
            <div id="financialSection" class="content-section" style="display: none;">
                <h2>النظام المالي</h2>
                <p>النظام المالي قيد التطوير...</p>
            </div>

            <!-- قسم إدارة الطلبات -->
            <div id="applicationsSection" class="content-section" style="display: none;">
                <!-- إحصائيات الطلبات -->
                <div class="stats-grid" style="margin-bottom: 2rem;">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">طلبات جديدة</div>
                            <div class="stat-icon" style="background: linear-gradient(135deg, #fbbf24, #f59e0b);">
                                <i class="fas fa-file-plus"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="newApplicationsCount">0</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12% من الأسبوع الماضي</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">قيد المراجعة</div>
                            <div class="stat-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="reviewingApplicationsCount">0</div>
                        <div class="stat-change neutral">
                            <i class="fas fa-clock"></i>
                            <span>متوسط المراجعة: 2 أيام</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">مقبولة</div>
                            <div class="stat-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="approvedApplicationsCount">0</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>معدل القبول: 85%</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">تقييم الذكاء الاصطناعي</div>
                            <div class="stat-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                                <i class="fas fa-brain"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="avgAiScore">0%</div>
                        <div class="stat-change positive">
                            <i class="fas fa-robot"></i>
                            <span>دقة التوقع: 92%</span>
                        </div>
                    </div>
                </div>

                <!-- أدوات التحكم والفلترة -->
                <div class="card" style="margin-bottom: 2rem;">
                    <div class="card-header">
                        <h3 class="card-title">أدوات إدارة الطلبات</h3>
                        <div class="card-actions">
                            <button class="btn primary" onclick="addNewApplication()">
                                <i class="fas fa-plus"></i>
                                إضافة طلب جديد
                            </button>
                            <button class="btn" onclick="exportApplicationsReport()">
                                <i class="fas fa-download"></i>
                                تصدير التقرير
                            </button>
                            <button class="btn" onclick="showAiInsights()">
                                <i class="fas fa-brain"></i>
                                رؤى الذكاء الاصطناعي
                            </button>
                        </div>
                    </div>

                    <div style="padding: 1.5rem;">
                        <div style="display: grid; grid-template-columns: 1fr auto auto; gap: 1rem; align-items: center; margin-bottom: 1rem;">
                            <div style="position: relative;">
                                <input type="text" id="applicationsSearchInput" placeholder="البحث في الطلبات..."
                                       onkeyup="searchApplications()" style="
                                    width: 100%;
                                    padding: 0.75rem 1rem 0.75rem 3rem;
                                    border: 1px solid var(--border);
                                    border-radius: 8px;
                                    background: var(--glass);
                                    color: var(--text-primary);
                                    font-family: inherit;
                                ">
                                <i class="fas fa-search" style="
                                    position: absolute;
                                    left: 1rem;
                                    top: 50%;
                                    transform: translateY(-50%);
                                    color: var(--text-secondary);
                                "></i>
                            </div>

                            <select id="applicationsStatusFilter" onchange="filterApplicationsByStatus()" style="
                                padding: 0.75rem;
                                border: 1px solid var(--border);
                                border-radius: 8px;
                                background: var(--glass);
                                color: var(--text-primary);
                                font-family: inherit;
                            ">
                                <option value="all">جميع الحالات</option>
                                <option value="pending">قيد الانتظار</option>
                                <option value="reviewing">قيد المراجعة</option>
                                <option value="approved">مقبولة</option>
                                <option value="rejected">مرفوضة</option>
                            </select>

                            <select id="applicationsCategoryFilter" onchange="filterApplicationsByCategory()" style="
                                padding: 0.75rem;
                                border: 1px solid var(--border);
                                border-radius: 8px;
                                background: var(--glass);
                                color: var(--text-primary);
                                font-family: inherit;
                            ">
                                <option value="all">جميع الفئات</option>
                                <option value="براعم">براعم</option>
                                <option value="ناشئين">ناشئين</option>
                                <option value="شباب">شباب</option>
                            </select>
                        </div>

                        <!-- أزرار الإجراءات المجمعة -->
                        <div style="display: flex; gap: 0.5rem; margin-bottom: 1rem;">
                            <button class="btn" onclick="selectAllApplications()" style="font-size: 0.9rem;">
                                <i class="fas fa-check-square"></i>
                                تحديد الكل
                            </button>
                            <button class="btn" onclick="bulkApproveApplications()" style="font-size: 0.9rem; background: var(--success);">
                                <i class="fas fa-check"></i>
                                قبول المحدد
                            </button>
                            <button class="btn" onclick="bulkRejectApplications()" style="font-size: 0.9rem; background: var(--danger);">
                                <i class="fas fa-times"></i>
                                رفض المحدد
                            </button>
                            <button class="btn" onclick="bulkDeleteApplications()" style="font-size: 0.9rem; background: var(--warning);">
                                <i class="fas fa-trash"></i>
                                حذف المحدد
                            </button>
                        </div>
                    </div>
                </div>

                <!-- قائمة الطلبات -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">قائمة طلبات الانضمام</h3>
                        <div class="card-actions">
                            <span id="applicationsCount" style="color: var(--text-secondary); font-size: 0.9rem;">
                                عرض 0 من 0 طلب
                            </span>
                        </div>
                    </div>

                    <div id="applicationsList" style="padding: 1.5rem;">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>

                    <!-- صفحات التنقل -->
                    <div id="applicationsPagination" style="padding: 1rem 1.5rem; border-top: 1px solid var(--border); display: flex; justify-content: center;">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <!-- قسم إدارة المباريات -->
            <div id="matchesSection" class="content-section" style="display: none;">
                <!-- إحصائيات المباريات -->
                <div class="stats-grid" style="margin-bottom: 2rem;">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">مباريات قادمة</div>
                            <div class="stat-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                                <i class="fas fa-calendar-plus"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="scheduledMatchesCount">0</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>هذا الأسبوع</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">مباريات مباشرة</div>
                            <div class="stat-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                                <i class="fas fa-broadcast-tower"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="liveMatchesCount">0</div>
                        <div class="stat-change neutral">
                            <i class="fas fa-circle" style="color: #ef4444; animation: pulse 2s infinite;"></i>
                            <span>جارية الآن</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">إجمالي الفرق</div>
                            <div class="stat-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="totalTeamsCount">0</div>
                        <div class="stat-change positive">
                            <i class="fas fa-trophy"></i>
                            <span>فرق نشطة</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">معدل الأهداف</div>
                            <div class="stat-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                                <i class="fas fa-futbol"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="avgGoalsPerMatch">0</div>
                        <div class="stat-change positive">
                            <i class="fas fa-chart-line"></i>
                            <span>هدف/مباراة</span>
                        </div>
                    </div>
                </div>

                <!-- أدوات التحكم السريع -->
                <div class="card" style="margin-bottom: 2rem;">
                    <div class="card-header">
                        <h3 class="card-title">أدوات إدارة المباريات</h3>
                        <div class="card-actions">
                            <button class="btn primary" onclick="addNewMatch()">
                                <i class="fas fa-plus"></i>
                                مباراة جديدة
                            </button>
                            <button class="btn" onclick="showMatchCalendar()">
                                <i class="fas fa-calendar"></i>
                                التقويم
                            </button>
                            <button class="btn" onclick="showLiveMatches()">
                                <i class="fas fa-broadcast-tower"></i>
                                المباريات المباشرة
                            </button>
                            <button class="btn" onclick="generateMatchReport()">
                                <i class="fas fa-chart-bar"></i>
                                تقرير المباريات
                            </button>
                        </div>
                    </div>
                </div>

                <!-- تبويبات إدارة المباريات -->
                <div class="card">
                    <div class="card-header" style="border-bottom: none; padding-bottom: 0;">
                        <div style="display: flex; gap: 1rem; width: 100%;">
                            <button class="match-tab active" onclick="showMatchTab('matches')" id="matchesTab">
                                <i class="fas fa-futbol"></i>
                                المباريات
                            </button>
                            <button class="match-tab" onclick="showMatchTab('teams')" id="teamsTab">
                                <i class="fas fa-users"></i>
                                الفرق
                            </button>
                            <button class="match-tab" onclick="showMatchTab('referees')" id="refereesTab">
                                <i class="fas fa-whistle"></i>
                                الحكام
                            </button>
                            <button class="match-tab" onclick="showMatchTab('venues')" id="venuesTab">
                                <i class="fas fa-map-marker-alt"></i>
                                الملاعب
                            </button>
                        </div>
                    </div>

                    <!-- محتوى المباريات -->
                    <div id="matchesTabContent" class="match-tab-content active">
                        <div style="padding: 1.5rem;">
                            <!-- فلاتر المباريات -->
                            <div style="display: grid; grid-template-columns: 1fr auto auto auto; gap: 1rem; align-items: center; margin-bottom: 1.5rem;">
                                <div style="position: relative;">
                                    <input type="text" id="matchesSearchInput" placeholder="البحث في المباريات..."
                                           onkeyup="searchMatches()" style="
                                        width: 100%;
                                        padding: 0.75rem 1rem 0.75rem 3rem;
                                        border: 1px solid var(--border);
                                        border-radius: 8px;
                                        background: var(--glass);
                                        color: var(--text-primary);
                                        font-family: inherit;
                                    ">
                                    <i class="fas fa-search" style="
                                        position: absolute;
                                        left: 1rem;
                                        top: 50%;
                                        transform: translateY(-50%);
                                        color: var(--text-secondary);
                                    "></i>
                                </div>

                                <select id="matchStatusFilter" onchange="filterMatchesByStatus()" style="
                                    padding: 0.75rem;
                                    border: 1px solid var(--border);
                                    border-radius: 8px;
                                    background: var(--glass);
                                    color: var(--text-primary);
                                    font-family: inherit;
                                ">
                                    <option value="all">جميع الحالات</option>
                                    <option value="scheduled">مجدولة</option>
                                    <option value="live">مباشرة</option>
                                    <option value="finished">منتهية</option>
                                    <option value="postponed">مؤجلة</option>
                                    <option value="cancelled">ملغية</option>
                                </select>

                                <select id="matchCompetitionFilter" onchange="filterMatchesByCompetition()" style="
                                    padding: 0.75rem;
                                    border: 1px solid var(--border);
                                    border-radius: 8px;
                                    background: var(--glass);
                                    color: var(--text-primary);
                                    font-family: inherit;
                                ">
                                    <option value="all">جميع البطولات</option>
                                    <option value="دوري الأكاديمية">دوري الأكاديمية</option>
                                    <option value="كأس الأكاديمية">كأس الأكاديمية</option>
                                    <option value="مباريات ودية">مباريات ودية</option>
                                </select>

                                <input type="date" id="matchDateFilter" onchange="filterMatchesByDate()" style="
                                    padding: 0.75rem;
                                    border: 1px solid var(--border);
                                    border-radius: 8px;
                                    background: var(--glass);
                                    color: var(--text-primary);
                                    font-family: inherit;
                                ">
                            </div>

                            <!-- قائمة المباريات -->
                            <div id="matchesList">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- محتوى الفرق -->
                    <div id="teamsTabContent" class="match-tab-content">
                        <div style="padding: 1.5rem;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                                <h4>إدارة الفرق</h4>
                                <button class="btn primary" onclick="addNewTeam()">
                                    <i class="fas fa-plus"></i>
                                    فريق جديد
                                </button>
                            </div>
                            <div id="teamsList">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- محتوى الحكام -->
                    <div id="refereesTabContent" class="match-tab-content">
                        <div style="padding: 1.5rem;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                                <h4>إدارة الحكام</h4>
                                <button class="btn primary" onclick="addNewReferee()">
                                    <i class="fas fa-plus"></i>
                                    حكم جديد
                                </button>
                            </div>
                            <div id="refereesList">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- محتوى الملاعب -->
                    <div id="venuesTabContent" class="match-tab-content">
                        <div style="padding: 1.5rem;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                                <h4>إدارة الملاعب</h4>
                                <button class="btn primary" onclick="addNewVenue()">
                                    <i class="fas fa-plus"></i>
                                    ملعب جديد
                                </button>
                            </div>
                            <div id="venuesList">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قسم التقارير -->
            <div id="reportsSection" class="content-section" style="display: none;">
                <h2>التقارير والإحصائيات</h2>
                <p>قسم التقارير قيد التطوير...</p>
            </div>

            <!-- قسم الرسائل -->
            <div id="messagesSection" class="content-section" style="display: none;">
                <h2>نظام الرسائل</h2>
                <p>نظام الرسائل قيد التطوير...</p>
            </div>

            <!-- قسم الإعدادات -->
            <div id="settingsSection" class="content-section" style="display: none;">
                <h2>إعدادات النظام</h2>
                <p>إعدادات النظام قيد التطوير...</p>
            </div>
        </main>
    </div>

    <!-- نافذة تفاصيل الطلب -->
    <div id="applicationModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 800px; max-height: 90vh; overflow-y: auto;">
            <div class="modal-header">
                <h3>تفاصيل طلب الانضمام</h3>
                <button onclick="closeApplicationModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="applicationDetails" class="modal-body">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </div>
    </div>

    <!-- نافذة إضافة طلب جديد -->
    <div id="addApplicationModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3>إضافة طلب انضمام جديد</h3>
                <button onclick="closeAddApplicationModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="addApplicationForm" onsubmit="submitNewApplication(event)">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">الاسم الكامل</label>
                            <input type="text" name="name" required style="
                                width: 100%; padding: 0.75rem; border: 1px solid var(--border);
                                border-radius: 8px; background: var(--glass); color: var(--text-primary);
                            ">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">العمر</label>
                            <input type="number" name="age" min="5" max="25" required style="
                                width: 100%; padding: 0.75rem; border: 1px solid var(--border);
                                border-radius: 8px; background: var(--glass); color: var(--text-primary);
                            ">
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">رقم الجوال</label>
                            <input type="tel" name="phone" required style="
                                width: 100%; padding: 0.75rem; border: 1px solid var(--border);
                                border-radius: 8px; background: var(--glass); color: var(--text-primary);
                            ">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">البريد الإلكتروني</label>
                            <input type="email" name="email" required style="
                                width: 100%; padding: 0.75rem; border: 1px solid var(--border);
                                border-radius: 8px; background: var(--glass); color: var(--text-primary);
                            ">
                        </div>
                    </div>

                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">الفئة العمرية</label>
                        <select name="category" required style="
                            width: 100%; padding: 0.75rem; border: 1px solid var(--border);
                            border-radius: 8px; background: var(--glass); color: var(--text-primary);
                        ">
                            <option value="">اختر الفئة</option>
                            <option value="براعم">براعم (5-10 سنوات)</option>
                            <option value="ناشئين">ناشئين (11-16 سنة)</option>
                            <option value="شباب">شباب (17-25 سنة)</option>
                        </select>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                        <div>
                            <label style="display: flex; align-items: center; gap: 0.5rem;">
                                <input type="checkbox" name="medicalCertificate" style="accent-color: var(--brand-primary);">
                                <span>الشهادة الطبية</span>
                            </label>
                        </div>
                        <div>
                            <label style="display: flex; align-items: center; gap: 0.5rem;">
                                <input type="checkbox" name="parentConsent" style="accent-color: var(--brand-primary);">
                                <span>موافقة ولي الأمر</span>
                            </label>
                        </div>
                    </div>

                    <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                        <button type="button" onclick="closeAddApplicationModal()" class="btn">
                            إلغاء
                        </button>
                        <button type="submit" class="btn primary">
                            <i class="fas fa-plus"></i>
                            إضافة الطلب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة رؤى الذكاء الاصطناعي -->
    <div id="aiInsightsModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3>رؤى الذكاء الاصطناعي للطلبات</h3>
                <button onclick="closeAiInsightsModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="aiInsightsContent" class="modal-body">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </div>
    </div>

    <!-- نافذة إصدار الشهادة -->
    <div id="certificateModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 700px;">
            <div class="modal-header">
                <h3>سند الاشتراك</h3>
                <button onclick="closeCertificateModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="certificateContent">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
                <div style="display: flex; gap: 1rem; justify-content: center; margin-top: 2rem;">
                    <button onclick="printCertificate()" class="btn primary">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                    <button onclick="downloadCertificate()" class="btn">
                        <i class="fas fa-download"></i>
                        تحميل PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // ==================== متغيرات عامة ====================
        let currentUser = null;
        let academyData = {
            players: [],
            coaches: [],
            attendance: [],
            payments: [],
            applications: [],
            matches: [],
            tournaments: [],
            teams: [],
            referees: [],
            venues: [],
            matchStats: [],
            formations: [],
            liveMatches: [],
            settings: {}
        };

        // متغيرات إدارة الطلبات
        let applicationsData = [];
        let filteredApplications = [];
        let currentPage = 1;
        let itemsPerPage = 10;

        // ==================== بيانات تجريبية ====================
        const sampleData = {
            players: [
                {
                    id: 'P001',
                    name: 'أحمد محمد علي',
                    age: 16,
                    gender: 'ذكر',
                    phone: '**********',
                    email: '<EMAIL>',
                    coach: 'C001',
                    level: 'متقدم',
                    joinDate: '2024-01-15',
                    lastAttendance: '2024-06-20',
                    attendanceRate: 85,
                    performanceScore: 92,
                    status: 'نشط'
                },
                {
                    id: 'P002',
                    name: 'فاطمة سعد الدين',
                    age: 14,
                    gender: 'أنثى',
                    phone: '**********',
                    email: '<EMAIL>',
                    coach: 'C002',
                    level: 'متوسط',
                    joinDate: '2024-02-10',
                    lastAttendance: '2024-06-19',
                    attendanceRate: 78,
                    performanceScore: 88,
                    status: 'نشط'
                },
                {
                    id: 'P003',
                    name: 'محمد عبدالله',
                    age: 18,
                    gender: 'ذكر',
                    phone: '**********',
                    email: '<EMAIL>',
                    coach: 'C001',
                    level: 'مبتدئ',
                    joinDate: '2024-03-05',
                    lastAttendance: '2024-06-15',
                    attendanceRate: 65,
                    performanceScore: 75,
                    status: 'تحذير'
                }
            ],
            coaches: [
                {
                    id: 'C001',
                    name: 'كابتن أحمد المدرب',
                    specialization: 'كرة القدم',
                    experience: 8,
                    phone: '0501111111',
                    email: '<EMAIL>',
                    assignedPlayers: ['P001', 'P003'],
                    rating: 4.8,
                    status: 'نشط'
                },
                {
                    id: 'C002',
                    name: 'كابتن سارة المدربة',
                    specialization: 'السباحة',
                    experience: 5,
                    phone: '0502222222',
                    email: '<EMAIL>',
                    assignedPlayers: ['P002'],
                    rating: 4.9,
                    status: 'نشط'
                }
            ],
            applications: [
                {
                    id: 'APP001',
                    name: 'سارة أحمد محمد',
                    age: 12,
                    gender: 'أنثى',
                    phone: '**********',
                    email: '<EMAIL>',
                    parentName: 'أحمد محمد علي',
                    parentPhone: '**********',
                    category: 'ناشئين',
                    sport: 'كرة القدم',
                    medicalCertificate: true,
                    parentConsent: true,
                    status: 'pending',
                    submissionDate: '2024-06-20',
                    reviewDate: null,
                    reviewedBy: null,
                    notes: '',
                    aiScore: 85,
                    aiRecommendation: 'قبول',
                    aiReason: 'العمر مناسب، الوثائق مكتملة، تقييم إيجابي'
                },
                {
                    id: 'APP002',
                    name: 'محمد عبدالله سعد',
                    age: 8,
                    gender: 'ذكر',
                    phone: '**********',
                    email: '<EMAIL>',
                    parentName: 'عبدالله سعد الدين',
                    parentPhone: '**********',
                    category: 'براعم',
                    sport: 'السباحة',
                    medicalCertificate: true,
                    parentConsent: true,
                    status: 'reviewing',
                    submissionDate: '2024-06-18',
                    reviewDate: '2024-06-19',
                    reviewedBy: 'مدير النظام',
                    notes: 'يحتاج لتقييم طبي إضافي',
                    aiScore: 78,
                    aiRecommendation: 'قبول مشروط',
                    aiReason: 'العمر مناسب، يحتاج متابعة طبية'
                },
                {
                    id: 'APP003',
                    name: 'فاطمة خالد أحمد',
                    age: 16,
                    gender: 'أنثى',
                    phone: '**********',
                    email: '<EMAIL>',
                    parentName: 'خالد أحمد محمد',
                    parentPhone: '**********',
                    category: 'شباب',
                    sport: 'كرة السلة',
                    medicalCertificate: true,
                    parentConsent: true,
                    status: 'approved',
                    submissionDate: '2024-06-15',
                    reviewDate: '2024-06-16',
                    reviewedBy: 'مدير النظام',
                    notes: 'لاعبة موهوبة، تم القبول فوراً',
                    aiScore: 95,
                    aiRecommendation: 'قبول فوري',
                    aiReason: 'مؤهلات ممتازة، خبرة سابقة، تقييم عالي'
                },
                {
                    id: 'APP004',
                    name: 'عمر محمد علي',
                    age: 14,
                    gender: 'ذكر',
                    phone: '**********',
                    email: '<EMAIL>',
                    parentName: 'محمد علي حسن',
                    parentPhone: '**********',
                    category: 'ناشئين',
                    sport: 'كرة القدم',
                    medicalCertificate: false,
                    parentConsent: true,
                    status: 'rejected',
                    submissionDate: '2024-06-10',
                    reviewDate: '2024-06-12',
                    reviewedBy: 'مدير النظام',
                    notes: 'الشهادة الطبية غير مكتملة',
                    aiScore: 45,
                    aiRecommendation: 'رفض',
                    aiReason: 'وثائق ناقصة، عدم استيفاء المتطلبات'
                },
                {
                    id: 'APP005',
                    name: 'نورا عبدالرحمن',
                    age: 10,
                    gender: 'أنثى',
                    phone: '**********',
                    email: '<EMAIL>',
                    parentName: 'عبدالرحمن محمد',
                    parentPhone: '**********',
                    category: 'براعم',
                    sport: 'الجمباز',
                    medicalCertificate: true,
                    parentConsent: true,
                    status: 'pending',
                    submissionDate: '2024-06-22',
                    reviewDate: null,
                    reviewedBy: null,
                    notes: '',
                    aiScore: 88,
                    aiRecommendation: 'قبول',
                    aiReason: 'مؤهلات جيدة، عمر مناسب، وثائق مكتملة'
                },
                {
                    id: 'APP006',
                    name: 'يوسف أحمد سالم',
                    age: 17,
                    gender: 'ذكر',
                    phone: '**********',
                    email: '<EMAIL>',
                    parentName: 'أحمد سالم محمد',
                    parentPhone: '**********',
                    category: 'شباب',
                    sport: 'التنس',
                    medicalCertificate: true,
                    parentConsent: true,
                    status: 'reviewing',
                    submissionDate: '2024-06-21',
                    reviewDate: '2024-06-22',
                    reviewedBy: 'مدير النظام',
                    notes: 'مراجعة الخبرة السابقة',
                    aiScore: 82,
                    aiRecommendation: 'قبول',
                    aiReason: 'خبرة سابقة جيدة، مؤهلات مناسبة'
                }
            ],
            // ==================== بيانات الفرق ====================
            teams: [
                {
                    id: 'T001',
                    name: 'نسور 7C',
                    category: 'ناشئين',
                    sport: 'كرة القدم',
                    coach: 'C001',
                    captain: 'P001',
                    players: ['P001', 'P002', 'P003'],
                    formation: '4-4-2',
                    homeVenue: 'V001',
                    founded: '2024-01-15',
                    wins: 8,
                    draws: 2,
                    losses: 1,
                    goalsFor: 24,
                    goalsAgainst: 8,
                    points: 26,
                    position: 1,
                    logo: null,
                    colors: {
                        primary: '#8B4513',
                        secondary: '#D2691E',
                        third: '#FFFFFF'
                    }
                },
                {
                    id: 'T002',
                    name: 'صقور الأكاديمية',
                    category: 'براعم',
                    sport: 'كرة القدم',
                    coach: 'C002',
                    captain: 'P004',
                    players: ['P004', 'P005', 'P006'],
                    formation: '3-3-2',
                    homeVenue: 'V002',
                    founded: '2024-02-01',
                    wins: 6,
                    draws: 3,
                    losses: 2,
                    goalsFor: 18,
                    goalsAgainst: 12,
                    points: 21,
                    position: 2,
                    logo: null,
                    colors: {
                        primary: '#1e3a8a',
                        secondary: '#3b82f6',
                        third: '#FFFFFF'
                    }
                },
                {
                    id: 'T003',
                    name: 'أسود الملعب',
                    category: 'شباب',
                    sport: 'كرة القدم',
                    coach: 'C003',
                    captain: 'P007',
                    players: ['P007', 'P008', 'P009'],
                    formation: '4-3-3',
                    homeVenue: 'V001',
                    founded: '2024-01-20',
                    wins: 7,
                    draws: 1,
                    losses: 3,
                    goalsFor: 22,
                    goalsAgainst: 15,
                    points: 22,
                    position: 3,
                    logo: null,
                    colors: {
                        primary: '#000000',
                        secondary: '#FFD700',
                        third: '#FFFFFF'
                    }
                },
                {
                    id: 'T004',
                    name: 'نجوم المستقبل',
                    category: 'ناشئين',
                    sport: 'كرة السلة',
                    coach: 'C002',
                    captain: 'P010',
                    players: ['P010', 'P011', 'P012'],
                    formation: '2-1-2',
                    homeVenue: 'V003',
                    founded: '2024-03-01',
                    wins: 5,
                    draws: 0,
                    losses: 4,
                    goalsFor: 89,
                    goalsAgainst: 76,
                    points: 15,
                    position: 4,
                    logo: null,
                    colors: {
                        primary: '#dc2626',
                        secondary: '#fbbf24',
                        third: '#FFFFFF'
                    }
                }
            ],
            // ==================== بيانات الحكام ====================
            referees: [
                {
                    id: 'R001',
                    name: 'أحمد الحكم',
                    phone: '0501111111',
                    email: '<EMAIL>',
                    level: 'دولي',
                    sports: ['كرة القدم'],
                    experience: 10,
                    rating: 4.8,
                    matchesRefereed: 156,
                    availability: {
                        monday: true,
                        tuesday: true,
                        wednesday: true,
                        thursday: true,
                        friday: false,
                        saturday: true,
                        sunday: true
                    },
                    certifications: ['FIFA', 'AFC'],
                    joinDate: '2020-01-15'
                },
                {
                    id: 'R002',
                    name: 'سارة المساعدة',
                    phone: '0502222222',
                    email: '<EMAIL>',
                    level: 'محلي',
                    sports: ['كرة القدم', 'كرة السلة'],
                    experience: 5,
                    rating: 4.6,
                    matchesRefereed: 89,
                    availability: {
                        monday: true,
                        tuesday: false,
                        wednesday: true,
                        thursday: true,
                        friday: true,
                        saturday: true,
                        sunday: false
                    },
                    certifications: ['SAFF'],
                    joinDate: '2022-03-10'
                },
                {
                    id: 'R003',
                    name: 'محمد الخبير',
                    phone: '0503333333',
                    email: '<EMAIL>',
                    level: 'إقليمي',
                    sports: ['كرة القدم'],
                    experience: 8,
                    rating: 4.7,
                    matchesRefereed: 134,
                    availability: {
                        monday: false,
                        tuesday: true,
                        wednesday: true,
                        thursday: false,
                        friday: true,
                        saturday: true,
                        sunday: true
                    },
                    certifications: ['AFC', 'SAFF'],
                    joinDate: '2021-06-20'
                }
            ],
            // ==================== بيانات الملاعب ====================
            venues: [
                {
                    id: 'V001',
                    name: 'الملعب الرئيسي',
                    type: 'كرة القدم',
                    capacity: 500,
                    surface: 'عشب طبيعي',
                    dimensions: '105x68 متر',
                    facilities: ['إضاءة ليلية', 'نظام صوتي', 'غرف ملابس', 'مدرجات'],
                    location: 'المبنى الرئيسي',
                    status: 'متاح',
                    maintenanceSchedule: [],
                    bookingRate: 500,
                    weatherProtection: false,
                    parkingSpaces: 100,
                    accessibility: true
                },
                {
                    id: 'V002',
                    name: 'الملعب الفرعي',
                    type: 'كرة القدم',
                    capacity: 200,
                    surface: 'عشب صناعي',
                    dimensions: '90x45 متر',
                    facilities: ['إضاءة ليلية', 'غرف ملابس'],
                    location: 'المبنى الفرعي',
                    status: 'متاح',
                    maintenanceSchedule: [],
                    bookingRate: 300,
                    weatherProtection: false,
                    parkingSpaces: 50,
                    accessibility: true
                },
                {
                    id: 'V003',
                    name: 'صالة كرة السلة',
                    type: 'كرة السلة',
                    capacity: 300,
                    surface: 'باركيه',
                    dimensions: '28x15 متر',
                    facilities: ['تكييف', 'نظام صوتي', 'غرف ملابس', 'مدرجات'],
                    location: 'المبنى الداخلي',
                    status: 'متاح',
                    maintenanceSchedule: [],
                    bookingRate: 400,
                    weatherProtection: true,
                    parkingSpaces: 80,
                    accessibility: true
                },
                {
                    id: 'V004',
                    name: 'ملعب التدريب',
                    type: 'متعدد الاستخدامات',
                    capacity: 100,
                    surface: 'مطاط',
                    dimensions: '50x30 متر',
                    facilities: ['إضاءة ليلية', 'غرفة ملابس'],
                    location: 'منطقة التدريب',
                    status: 'متاح',
                    maintenanceSchedule: [],
                    bookingRate: 200,
                    weatherProtection: false,
                    parkingSpaces: 30,
                    accessibility: true
                }
            ],
            // ==================== بيانات المباريات ====================
            matches: [
                {
                    id: 'M001',
                    title: 'نسور 7C ضد صقور الأكاديمية',
                    homeTeam: 'T001',
                    awayTeam: 'T002',
                    date: '2024-06-28',
                    time: '16:00',
                    venue: 'V001',
                    referee: 'R001',
                    assistantReferees: ['R002'],
                    competition: 'دوري الأكاديمية',
                    round: 'الجولة 12',
                    status: 'scheduled', // scheduled, live, finished, postponed, cancelled
                    importance: 'high', // low, medium, high
                    ticketPrice: 20,
                    expectedAttendance: 400,
                    weather: {
                        condition: 'مشمس',
                        temperature: 28,
                        humidity: 45,
                        windSpeed: 12
                    },
                    broadcastInfo: {
                        isLive: true,
                        channel: 'قناة الأكاديمية',
                        commentator: 'أحمد المعلق'
                    },
                    aiPrediction: {
                        homeWinProbability: 65,
                        drawProbability: 20,
                        awayWinProbability: 15,
                        expectedGoals: {
                            home: 2.1,
                            away: 1.3
                        },
                        keyFactors: ['الأداء المنزلي القوي', 'غياب لاعب مهم في الفريق الضيف']
                    }
                },
                {
                    id: 'M002',
                    title: 'أسود الملعب ضد نجوم المستقبل',
                    homeTeam: 'T003',
                    awayTeam: 'T004',
                    date: '2024-06-25',
                    time: '18:30',
                    venue: 'V003',
                    referee: 'R003',
                    assistantReferees: ['R001'],
                    competition: 'كأس الأكاديمية',
                    round: 'ربع النهائي',
                    status: 'finished',
                    importance: 'high',
                    ticketPrice: 25,
                    actualAttendance: 280,
                    result: {
                        homeScore: 3,
                        awayScore: 1,
                        homeScorers: [
                            { player: 'P007', minute: 23, type: 'goal' },
                            { player: 'P008', minute: 45, type: 'goal' },
                            { player: 'P007', minute: 78, type: 'goal' }
                        ],
                        awayScorers: [
                            { player: 'P010', minute: 67, type: 'goal' }
                        ],
                        cards: [
                            { player: 'P009', minute: 34, type: 'yellow' },
                            { player: 'P011', minute: 56, type: 'yellow' },
                            { player: 'P012', minute: 89, type: 'red' }
                        ]
                    },
                    stats: {
                        possession: { home: 58, away: 42 },
                        shots: { home: 12, away: 8 },
                        shotsOnTarget: { home: 6, away: 3 },
                        corners: { home: 7, away: 4 },
                        fouls: { home: 11, away: 15 },
                        passes: { home: 342, away: 278 },
                        passAccuracy: { home: 84, away: 79 }
                    }
                },
                {
                    id: 'M003',
                    title: 'نسور 7C ضد أسود الملعب',
                    homeTeam: 'T001',
                    awayTeam: 'T003',
                    date: '2024-06-30',
                    time: '20:00',
                    venue: 'V001',
                    referee: 'R002',
                    assistantReferees: ['R003'],
                    competition: 'دوري الأكاديمية',
                    round: 'الجولة 13',
                    status: 'scheduled',
                    importance: 'high',
                    ticketPrice: 30,
                    expectedAttendance: 500,
                    weather: {
                        condition: 'غائم جزئياً',
                        temperature: 25,
                        humidity: 60,
                        windSpeed: 8
                    },
                    aiPrediction: {
                        homeWinProbability: 45,
                        drawProbability: 30,
                        awayWinProbability: 25,
                        expectedGoals: {
                            home: 1.8,
                            away: 1.6
                        },
                        keyFactors: ['مباراة قمة', 'كلا الفريقين في حالة جيدة']
                    }
                },
                {
                    id: 'M004',
                    title: 'صقور الأكاديمية ضد نجوم المستقبل',
                    homeTeam: 'T002',
                    awayTeam: 'T004',
                    date: '2024-06-26',
                    time: '15:00',
                    venue: 'V002',
                    referee: 'R001',
                    assistantReferees: ['R002'],
                    competition: 'دوري الأكاديمية',
                    round: 'الجولة 11',
                    status: 'live',
                    importance: 'medium',
                    ticketPrice: 15,
                    actualAttendance: 180,
                    liveScore: {
                        homeScore: 1,
                        awayScore: 0,
                        minute: 67,
                        period: 'الشوط الثاني',
                        events: [
                            { minute: 23, type: 'goal', team: 'home', player: 'P004', description: 'هدف رائع من خارج المنطقة' },
                            { minute: 34, type: 'yellow', team: 'away', player: 'P010', description: 'بطاقة صفراء للعب الخشن' },
                            { minute: 45, type: 'substitution', team: 'home', playerOut: 'P005', playerIn: 'P006', description: 'تبديل تكتيكي' }
                        ]
                    },
                    liveStats: {
                        possession: { home: 52, away: 48 },
                        shots: { home: 8, away: 6 },
                        shotsOnTarget: { home: 3, away: 2 },
                        corners: { home: 4, away: 3 },
                        fouls: { home: 7, away: 9 }
                    }
                }
            ]
        };

        // ==================== التهيئة عند تحميل الصفحة ====================
        document.addEventListener('DOMContentLoaded', function() {
            initializeSystem();
            loadUserSession();
            loadAcademyData();
            updateDashboard();
            setupEventListeners();

            console.log('🏢 لوحة تحكم الإدارة جاهزة!');
        });

        function initializeSystem() {
            // تحميل البيانات من localStorage أو إنشاء بيانات تجريبية
            const savedData = localStorage.getItem('academyData');
            if (savedData) {
                academyData = JSON.parse(savedData);
            } else {
                // إنشاء بيانات تجريبية
                academyData.players = sampleData.players;
                academyData.coaches = sampleData.coaches;
                academyData.applications = sampleData.applications;
                academyData.teams = sampleData.teams;
                academyData.referees = sampleData.referees;
                academyData.venues = sampleData.venues;
                academyData.matches = sampleData.matches;
                saveAcademyData();
            }
        }

        function loadUserSession() {
            // تحميل بيانات المستخدم من الجلسة
            const sessionData = sessionStorage.getItem('userSession');
            if (sessionData) {
                try {
                    const decryptedData = decryptData(sessionData);
                    currentUser = decryptedData.user;
                    document.getElementById('userName').textContent = currentUser.name;
                } catch (error) {
                    console.error('خطأ في تحميل بيانات الجلسة:', error);
                    // للاختبار - تحميل بيانات افتراضية
                    currentUser = { name: 'مدير النظام', role: 'admin' };
                    document.getElementById('userName').textContent = currentUser.name;
                }
            } else {
                // للاختبار - تحميل بيانات افتراضية
                currentUser = { name: 'مدير النظام', role: 'admin' };
                document.getElementById('userName').textContent = currentUser.name;
            }
        }

        function decryptData(encryptedData) {
            try {
                const bytes = CryptoJS.AES.decrypt(encryptedData, 'academy7c_secret');
                return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
            } catch (e) {
                throw new Error('فشل في فك التشفير');
            }
        }

        // ==================== إدارة البيانات ====================
        function loadAcademyData() {
            const savedData = localStorage.getItem('academyData');
            if (savedData) {
                academyData = JSON.parse(savedData);
            }
        }

        function saveAcademyData() {
            localStorage.setItem('academyData', JSON.stringify(academyData));
        }

        // ==================== تحديث لوحة المعلومات ====================
        function updateDashboard() {
            updateKPIs();
            updateNavigationBadges();
            updateApplicationsData();
            updateMatchesData();
            runAIAnalysis();
        }

        function updateKPIs() {
            // تحديث بطاقات KPI
            document.getElementById('totalPlayersKPI').textContent = academyData.players.length;
            document.getElementById('activeCoachesKPI').textContent = academyData.coaches.filter(c => c.status === 'نشط').length;

            // حساب معدل الحضور
            const attendanceRate = calculateAverageAttendance();
            document.getElementById('attendanceRateKPI').textContent = attendanceRate + '%';

            // حساب الإيرادات الشهرية (محاكاة)
            const monthlyRevenue = calculateMonthlyRevenue();
            document.getElementById('monthlyRevenueKPI').textContent = monthlyRevenue.toLocaleString('ar-SA') + ' ر.س';
        }

        function updateNavigationBadges() {
            document.getElementById('playersCount').textContent = academyData.players.length;
            document.getElementById('coachesCount').textContent = academyData.coaches.length;

            // حساب الطلبات المعلقة
            const pendingApplications = academyData.applications ?
                academyData.applications.filter(app => app.status === 'pending').length : 0;
            document.getElementById('pendingApplicationsCount').textContent = pendingApplications;

            // حساب المباريات القادمة
            const upcomingMatches = academyData.matches ?
                academyData.matches.filter(match => match.status === 'scheduled').length : 0;
            document.getElementById('upcomingMatchesCount').textContent = upcomingMatches;

            // حساب المدفوعات المعلقة (محاكاة)
            const pendingPayments = Math.floor(Math.random() * 10) + 1;
            document.getElementById('pendingPayments').textContent = pendingPayments;

            // حساب الرسائل غير المقروءة (محاكاة)
            const unreadMessages = Math.floor(Math.random() * 5) + 1;
            document.getElementById('unreadMessages').textContent = unreadMessages;
        }

        // ==================== خوارزميات الذكاء الاصطناعي ====================
        function calculateAverageAttendance() {
            if (academyData.players.length === 0) return 0;

            const totalAttendance = academyData.players.reduce((sum, player) => {
                return sum + (player.attendanceRate || 0);
            }, 0);

            return Math.round(totalAttendance / academyData.players.length);
        }

        function calculateMonthlyRevenue() {
            // محاكاة حساب الإيرادات الشهرية
            const baseRevenue = academyData.players.length * 500; // 500 ريال لكل لاعب
            const variationFactor = 0.8 + (Math.random() * 0.4); // تنويع ±20%
            return Math.round(baseRevenue * variationFactor);
        }

        function runAIAnalysis() {
            // تحليل مخاطر الانقطاع
            analyzeDropoutRisk();

            // تحليل الأداء العام
            analyzePerformanceTrends();

            // توصيات ذكية
            generateSmartRecommendations();
        }

        function analyzeDropoutRisk() {
            academyData.players.forEach(player => {
                const riskScore = calculateDropoutRisk(player);
                player.dropoutRisk = riskScore;

                if (riskScore > 70) {
                    console.warn(`تحذير: اللاعب ${player.name} معرض لخطر الانقطاع (${riskScore}%)`);
                }
            });
        }

        function calculateDropoutRisk(player) {
            let riskScore = 0;

            // تحليل معدل الحضور
            if (player.attendanceRate < 60) riskScore += 30;
            else if (player.attendanceRate < 75) riskScore += 15;

            // تحليل الأداء
            if (player.performanceScore < 70) riskScore += 25;
            else if (player.performanceScore < 80) riskScore += 10;

            // تحليل آخر حضور
            const daysSinceLastAttendance = calculateDaysSince(player.lastAttendance);
            if (daysSinceLastAttendance > 7) riskScore += 20;
            else if (daysSinceLastAttendance > 3) riskScore += 10;

            // عامل عشوائي للمحاكاة
            riskScore += Math.random() * 15;

            return Math.min(Math.round(riskScore), 100);
        }

        function calculateDaysSince(dateString) {
            const lastDate = new Date(dateString);
            const today = new Date();
            const diffTime = Math.abs(today - lastDate);
            return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        }

        function analyzePerformanceTrends() {
            const averagePerformance = academyData.players.reduce((sum, player) => {
                return sum + (player.performanceScore || 0);
            }, 0) / academyData.players.length;

            console.log(`متوسط الأداء العام: ${averagePerformance.toFixed(1)}%`);

            // تحديد اللاعبين المتميزين والمحتاجين للدعم
            const topPerformers = academyData.players.filter(p => p.performanceScore > 90);
            const needSupport = academyData.players.filter(p => p.performanceScore < 70);

            console.log(`اللاعبين المتميزين: ${topPerformers.length}`);
            console.log(`اللاعبين المحتاجين للدعم: ${needSupport.length}`);
        }

        function generateSmartRecommendations() {
            const recommendations = [];

            // توصيات بناءً على معدل الحضور
            const lowAttendancePlayers = academyData.players.filter(p => p.attendanceRate < 70);
            if (lowAttendancePlayers.length > 0) {
                recommendations.push({
                    type: 'attendance',
                    priority: 'عالية',
                    message: `${lowAttendancePlayers.length} لاعب لديهم معدل حضور منخفض. يُنصح بالتواصل معهم.`,
                    action: 'إرسال رسائل تحفيزية'
                });
            }

            // توصيات بناءً على الأداء
            const lowPerformancePlayers = academyData.players.filter(p => p.performanceScore < 75);
            if (lowPerformancePlayers.length > 0) {
                recommendations.push({
                    type: 'performance',
                    priority: 'متوسطة',
                    message: `${lowPerformancePlayers.length} لاعب يحتاجون لبرامج تدريب إضافية.`,
                    action: 'وضع خطط تدريب مخصصة'
                });
            }

            // توصيات مالية
            const monthlyRevenue = calculateMonthlyRevenue();
            const expectedRevenue = academyData.players.length * 500;
            if (monthlyRevenue < expectedRevenue * 0.8) {
                recommendations.push({
                    type: 'financial',
                    priority: 'عالية',
                    message: 'الإيرادات أقل من المتوقع. يُنصح بمراجعة المدفوعات المتأخرة.',
                    action: 'متابعة المدفوعات المعلقة'
                });
            }

            console.log('التوصيات الذكية:', recommendations);
            return recommendations;
        }

        // ==================== وظائف التنقل ====================
        function showSection(sectionName) {
            // إخفاء جميع الأقسام
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
            });

            // إزالة الحالة النشطة من جميع الروابط
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // إظهار القسم المحدد
            const targetSection = document.getElementById(sectionName + 'Section');
            if (targetSection) {
                targetSection.style.display = 'block';
            }

            // تفعيل الرابط المحدد
            event.target.closest('.nav-link').classList.add('active');

            // تحديث عنوان الصفحة
            updatePageTitle(sectionName);
        }

        function updatePageTitle(sectionName) {
            const titles = {
                'dashboard': 'لوحة المعلومات',
                'players': 'إدارة اللاعبين',
                'coaches': 'إدارة المدربين',
                'attendance': 'الحضور والغياب',
                'financial': 'النظام المالي',
                'applications': 'إدارة الطلبات',
                'matches': 'إدارة المباريات',
                'reports': 'التقارير والإحصائيات',
                'messages': 'نظام الرسائل',
                'settings': 'إعدادات النظام'
            };

            const title = titles[sectionName] || 'لوحة التحكم';
            document.getElementById('pageTitle').textContent = title;
            document.getElementById('breadcrumb').textContent = `الرئيسية / ${title}`;
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }

        // ==================== وظائف الرأس ====================
        function showNotifications() {
            Swal.fire({
                title: 'الإشعارات',
                html: `
                    <div style="text-align: right;">
                        <div style="padding: 1rem; border-bottom: 1px solid #333;">
                            <strong>تحذير:</strong> 3 لاعبين لم يحضروا منذ أسبوع
                        </div>
                        <div style="padding: 1rem; border-bottom: 1px solid #333;">
                            <strong>تذكير:</strong> 5 فواتير مستحقة الدفع
                        </div>
                        <div style="padding: 1rem;">
                            <strong>معلومة:</strong> تم إضافة لاعب جديد اليوم
                        </div>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                confirmButtonText: 'إغلاق'
            });
        }

        function showQuickActions() {
            Swal.fire({
                title: 'إضافة سريعة',
                html: `
                    <div style="display: grid; gap: 1rem; text-align: center;">
                        <button onclick="addNewPlayer()" class="swal2-confirm swal2-styled">إضافة لاعب جديد</button>
                        <button onclick="addNewCoach()" class="swal2-confirm swal2-styled">إضافة مدرب جديد</button>
                        <button onclick="recordAttendance()" class="swal2-confirm swal2-styled">تسجيل حضور</button>
                        <button onclick="createInvoice()" class="swal2-confirm swal2-styled">إنشاء فاتورة</button>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                showConfirmButton: false,
                showCancelButton: true,
                cancelButtonText: 'إغلاق'
            });
        }

        function showUserMenu() {
            Swal.fire({
                title: 'قائمة المستخدم',
                html: `
                    <div style="display: grid; gap: 1rem; text-align: center;">
                        <button onclick="editProfile()" class="swal2-confirm swal2-styled">تعديل الملف الشخصي</button>
                        <button onclick="changePassword()" class="swal2-confirm swal2-styled">تغيير كلمة المرور</button>
                        <button onclick="viewActivity()" class="swal2-confirm swal2-styled">عرض النشاط</button>
                        <button onclick="logout()" class="swal2-deny swal2-styled">تسجيل الخروج</button>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                showConfirmButton: false,
                showCancelButton: true,
                cancelButtonText: 'إغلاق'
            });
        }

        // ==================== وظائف الإضافة السريعة ====================
        function addNewPlayer() {
            Swal.fire({
                title: 'إضافة لاعب جديد',
                text: 'سيتم فتح نموذج إضافة لاعب جديد',
                icon: 'info',
                background: '#1a1a1a',
                color: '#ffffff',
                confirmButtonText: 'موافق'
            });
        }

        function addNewCoach() {
            Swal.fire({
                title: 'إضافة مدرب جديد',
                text: 'سيتم فتح نموذج إضافة مدرب جديد',
                icon: 'info',
                background: '#1a1a1a',
                color: '#ffffff',
                confirmButtonText: 'موافق'
            });
        }

        function recordAttendance() {
            Swal.fire({
                title: 'تسجيل حضور',
                text: 'سيتم فتح نظام تسجيل الحضور',
                icon: 'info',
                background: '#1a1a1a',
                color: '#ffffff',
                confirmButtonText: 'موافق'
            });
        }

        function createInvoice() {
            Swal.fire({
                title: 'إنشاء فاتورة',
                text: 'سيتم فتح نموذج إنشاء فاتورة جديدة',
                icon: 'info',
                background: '#1a1a1a',
                color: '#ffffff',
                confirmButtonText: 'موافق'
            });
        }

        // ==================== وظائف المستخدم ====================
        function editProfile() {
            Swal.fire({
                title: 'تعديل الملف الشخصي',
                text: 'سيتم فتح نموذج تعديل الملف الشخصي',
                icon: 'info',
                background: '#1a1a1a',
                color: '#ffffff',
                confirmButtonText: 'موافق'
            });
        }

        function changePassword() {
            Swal.fire({
                title: 'تغيير كلمة المرور',
                text: 'سيتم فتح نموذج تغيير كلمة المرور',
                icon: 'info',
                background: '#1a1a1a',
                color: '#ffffff',
                confirmButtonText: 'موافق'
            });
        }

        function viewActivity() {
            Swal.fire({
                title: 'نشاط المستخدم',
                html: `
                    <div style="text-align: right;">
                        <div style="padding: 0.5rem; border-bottom: 1px solid #333;">
                            <strong>اليوم:</strong> تسجيل دخول في 09:30 ص
                        </div>
                        <div style="padding: 0.5rem; border-bottom: 1px solid #333;">
                            <strong>أمس:</strong> إضافة 2 لاعب جديد
                        </div>
                        <div style="padding: 0.5rem;">
                            <strong>الأسبوع الماضي:</strong> إنشاء 15 فاتورة
                        </div>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                confirmButtonText: 'إغلاق'
            });
        }

        function logout() {
            Swal.fire({
                title: 'تسجيل الخروج',
                text: 'هل أنت متأكد من تسجيل الخروج؟',
                icon: 'question',
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'نعم، تسجيل الخروج',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    // مسح بيانات الجلسة
                    sessionStorage.removeItem('userSession');

                    Swal.fire({
                        title: 'تم تسجيل الخروج',
                        text: 'شكراً لاستخدام النظام',
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff',
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        window.location.href = 'login.html';
                    });
                }
            });
        }

        // ==================== إعداد مستمعي الأحداث ====================
        function setupEventListeners() {
            // تحديث البيانات كل 30 ثانية
            setInterval(() => {
                updateDashboard();
            }, 30000);

            // حفظ البيانات عند إغلاق النافذة
            window.addEventListener('beforeunload', () => {
                saveAcademyData();
            });

            // معالجة تغيير حجم النافذة
            window.addEventListener('resize', () => {
                if (window.innerWidth > 1024) {
                    document.getElementById('sidebar').classList.remove('open');
                }
            });

            // اختصارات لوحة المفاتيح
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey) {
                    switch(e.key) {
                        case '1':
                            e.preventDefault();
                            showSection('dashboard');
                            break;
                        case '2':
                            e.preventDefault();
                            showSection('players');
                            break;
                        case '3':
                            e.preventDefault();
                            showSection('coaches');
                            break;
                        case 's':
                            e.preventDefault();
                            saveAcademyData();
                            Swal.fire({
                                title: 'تم الحفظ',
                                text: 'تم حفظ البيانات بنجاح',
                                icon: 'success',
                                timer: 1500,
                                showConfirmButton: false,
                                background: '#1a1a1a',
                                color: '#ffffff'
                            });
                            break;
                    }
                }
            });
        }

        // ==================== وظائف مساعدة ====================
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR'
            }).format(amount);
        }

        function generateId(prefix = 'ID') {
            return prefix + Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
        }

        // ==================== تصدير البيانات ====================
        function exportData(format = 'json') {
            const data = {
                exportDate: new Date().toISOString(),
                academyData: academyData,
                metadata: {
                    version: '1.0',
                    system: 'Academy 7C Management System'
                }
            };

            let content, filename, mimeType;

            switch(format) {
                case 'json':
                    content = JSON.stringify(data, null, 2);
                    filename = `academy-data-${new Date().toISOString().split('T')[0]}.json`;
                    mimeType = 'application/json';
                    break;
                case 'csv':
                    content = convertToCSV(academyData.players);
                    filename = `players-data-${new Date().toISOString().split('T')[0]}.csv`;
                    mimeType = 'text/csv';
                    break;
                default:
                    return;
            }

            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function convertToCSV(data) {
            if (!data || data.length === 0) return '';

            const headers = Object.keys(data[0]);
            const csvContent = [
                headers.join(','),
                ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
            ].join('\n');

            return csvContent;
        }

        // ==================== معالجة الأخطاء ====================
        window.addEventListener('error', (e) => {
            console.error('خطأ في النظام:', e.error);

            Swal.fire({
                title: 'خطأ في النظام',
                text: 'حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.',
                icon: 'error',
                background: '#1a1a1a',
                color: '#ffffff',
                confirmButtonText: 'إعادة تحميل',
                showCancelButton: true,
                cancelButtonText: 'إغلاق'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.reload();
                }
            });
        });

        // ==================== تحديث الوقت الحقيقي ====================
        function updateRealTimeData() {
            // محاكاة تحديث البيانات في الوقت الحقيقي
            const now = new Date();

            // تحديث آخر نشاط
            document.querySelector('.academy-subtitle').textContent =
                `آخر تحديث: ${now.toLocaleTimeString('ar-SA')}`;

            // تحديث عدد الإشعارات (محاكاة)
            const notificationCount = Math.floor(Math.random() * 5) + 1;
            document.getElementById('notificationCount').textContent = notificationCount;
        }

        // تشغيل تحديث الوقت الحقيقي كل دقيقة
        setInterval(updateRealTimeData, 60000);

        // ==================== وظائف إدارة الطلبات ====================
        function updateApplicationsData() {
            if (!academyData.applications) {
                academyData.applications = [];
                return;
            }

            applicationsData = academyData.applications;
            filteredApplications = [...applicationsData];

            updateApplicationsStats();
            updateApplicationsList();
        }

        function updateApplicationsStats() {
            const newApps = applicationsData.filter(app => app.status === 'pending').length;
            const reviewingApps = applicationsData.filter(app => app.status === 'reviewing').length;
            const approvedApps = applicationsData.filter(app => app.status === 'approved').length;
            const avgAiScore = applicationsData.length > 0 ?
                Math.round(applicationsData.reduce((sum, app) => sum + app.aiScore, 0) / applicationsData.length) : 0;

            if (document.getElementById('newApplicationsCount')) {
                document.getElementById('newApplicationsCount').textContent = newApps;
                document.getElementById('reviewingApplicationsCount').textContent = reviewingApps;
                document.getElementById('approvedApplicationsCount').textContent = approvedApps;
                document.getElementById('avgAiScore').textContent = avgAiScore + '%';
            }
        }

        function updateApplicationsList() {
            const container = document.getElementById('applicationsList');
            if (!container) return;

            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageApplications = filteredApplications.slice(startIndex, endIndex);

            if (pageApplications.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                        <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>لا توجد طلبات للعرض</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = pageApplications.map(app => `
                <div class="application-item" style="
                    background: rgba(255,255,255,0.05);
                    border: 1px solid var(--border);
                    border-radius: 12px;
                    padding: 1.5rem;
                    margin-bottom: 1rem;
                    transition: all 0.3s ease;
                    border-right: 4px solid ${getStatusColor(app.status)};
                " onmouseover="this.style.background='rgba(255,255,255,0.1)'"
                   onmouseout="this.style.background='rgba(255,255,255,0.05)'">

                    <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 1rem;">
                        <div style="display: flex; align-items: center; gap: 1rem;">
                            <input type="checkbox" class="app-checkbox" data-id="${app.id}" style="
                                width: 18px; height: 18px; accent-color: var(--brand-primary);
                            ">
                            <div style="
                                width: 50px; height: 50px;
                                background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
                                border-radius: 50%;
                                display: flex; align-items: center; justify-content: center;
                                color: white; font-size: 1.2rem;
                            ">
                                <i class="fas fa-user"></i>
                            </div>
                            <div>
                                <h4 style="color: var(--text-primary); font-size: 1.1rem; margin-bottom: 0.5rem;">
                                    ${app.name}
                                </h4>
                                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.3rem;">
                                    <span style="
                                        background: ${getStatusColor(app.status)};
                                        color: white; padding: 0.2rem 0.6rem; border-radius: 12px; font-size: 0.8rem;
                                    ">${getStatusText(app.status)}</span>
                                    <span style="
                                        background: var(--brand-primary); color: white;
                                        padding: 0.2rem 0.6rem; border-radius: 12px; font-size: 0.8rem;
                                    ">${app.category}</span>
                                    <span style="color: var(--text-secondary); font-size: 0.9rem;">
                                        ${app.age} سنة - ${app.sport}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: left;">
                            <div style="
                                background: linear-gradient(135deg, #8b5cf6, #7c3aed);
                                color: white; padding: 0.5rem; border-radius: 8px; margin-bottom: 0.5rem;
                            ">
                                <div style="font-size: 1.1rem; font-weight: bold;">AI: ${app.aiScore}%</div>
                                <div style="font-size: 0.8rem; opacity: 0.9;">${app.aiRecommendation}</div>
                            </div>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                        <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                            <i class="fas fa-calendar" style="color: var(--brand-primary);"></i>
                            <span>${formatDate(app.submissionDate)}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                            <i class="fas fa-phone" style="color: var(--brand-primary);"></i>
                            <span>${app.phone}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                            <i class="fas fa-envelope" style="color: var(--brand-primary);"></i>
                            <span>${app.email}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                            <i class="fas fa-user-tie" style="color: var(--brand-primary);"></i>
                            <span>${app.parentName}</span>
                        </div>
                    </div>

                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                        <button onclick="viewApplicationDetails('${app.id}')" style="
                            background: var(--info); color: white; border: none;
                            padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                        ">
                            <i class="fas fa-eye"></i> عرض التفاصيل
                        </button>

                        ${app.status === 'pending' || app.status === 'reviewing' ? `
                            <button onclick="approveApplication('${app.id}')" style="
                                background: var(--success); color: white; border: none;
                                padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                            ">
                                <i class="fas fa-check"></i> قبول
                            </button>
                            <button onclick="rejectApplication('${app.id}')" style="
                                background: var(--danger); color: white; border: none;
                                padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                            ">
                                <i class="fas fa-times"></i> رفض
                            </button>
                        ` : ''}

                        ${app.status === 'approved' ? `
                            <button onclick="generateCertificate('${app.id}')" style="
                                background: var(--brand-primary); color: white; border: none;
                                padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                            ">
                                <i class="fas fa-certificate"></i> إصدار سند
                            </button>
                        ` : ''}

                        <button onclick="deleteApplication('${app.id}')" style="
                            background: transparent; color: var(--text-secondary); border: 1px solid var(--border);
                            padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                        ">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            `).join('');

            updateApplicationsCount();
            updatePagination();
        }

        // ==================== وظائف مساعدة للطلبات ====================
        function getStatusColor(status) {
            const colors = {
                'pending': '#fbbf24',
                'reviewing': '#3b82f6',
                'approved': '#10b981',
                'rejected': '#ef4444'
            };
            return colors[status] || '#6b7280';
        }

        function getStatusText(status) {
            const texts = {
                'pending': 'قيد الانتظار',
                'reviewing': 'قيد المراجعة',
                'approved': 'مقبولة',
                'rejected': 'مرفوضة'
            };
            return texts[status] || status;
        }

        function updateApplicationsCount() {
            const container = document.getElementById('applicationsCount');
            if (container) {
                const total = applicationsData.length;
                const filtered = filteredApplications.length;
                const start = (currentPage - 1) * itemsPerPage + 1;
                const end = Math.min(currentPage * itemsPerPage, filtered);

                container.textContent = `عرض ${start}-${end} من ${filtered} طلب (إجمالي: ${total})`;
            }
        }

        function updatePagination() {
            const container = document.getElementById('applicationsPagination');
            if (!container) return;

            const totalPages = Math.ceil(filteredApplications.length / itemsPerPage);

            if (totalPages <= 1) {
                container.innerHTML = '';
                return;
            }

            let paginationHTML = '';

            // زر السابق
            if (currentPage > 1) {
                paginationHTML += `
                    <button onclick="changePage(${currentPage - 1})" style="
                        background: var(--glass); border: 1px solid var(--border);
                        color: var(--text-primary); padding: 0.5rem 1rem; border-radius: 6px;
                        cursor: pointer; margin: 0 0.25rem;
                    ">السابق</button>
                `;
            }

            // أرقام الصفحات
            for (let i = 1; i <= totalPages; i++) {
                if (i === currentPage) {
                    paginationHTML += `
                        <button style="
                            background: var(--brand-primary); border: 1px solid var(--brand-primary);
                            color: white; padding: 0.5rem 1rem; border-radius: 6px;
                            margin: 0 0.25rem; font-weight: bold;
                        ">${i}</button>
                    `;
                } else {
                    paginationHTML += `
                        <button onclick="changePage(${i})" style="
                            background: var(--glass); border: 1px solid var(--border);
                            color: var(--text-primary); padding: 0.5rem 1rem; border-radius: 6px;
                            cursor: pointer; margin: 0 0.25rem;
                        ">${i}</button>
                    `;
                }
            }

            // زر التالي
            if (currentPage < totalPages) {
                paginationHTML += `
                    <button onclick="changePage(${currentPage + 1})" style="
                        background: var(--glass); border: 1px solid var(--border);
                        color: var(--text-primary); padding: 0.5rem 1rem; border-radius: 6px;
                        cursor: pointer; margin: 0 0.25rem;
                    ">التالي</button>
                `;
            }

            container.innerHTML = paginationHTML;
        }

        function changePage(page) {
            currentPage = page;
            updateApplicationsList();
        }

        // ==================== وظائف البحث والفلترة ====================
        function searchApplications() {
            const searchTerm = document.getElementById('applicationsSearchInput').value.toLowerCase();

            filteredApplications = applicationsData.filter(app =>
                app.name.toLowerCase().includes(searchTerm) ||
                app.email.toLowerCase().includes(searchTerm) ||
                app.phone.includes(searchTerm) ||
                app.parentName.toLowerCase().includes(searchTerm) ||
                app.sport.toLowerCase().includes(searchTerm)
            );

            currentPage = 1;
            updateApplicationsList();
        }

        function filterApplicationsByStatus() {
            const status = document.getElementById('applicationsStatusFilter').value;
            const searchTerm = document.getElementById('applicationsSearchInput').value.toLowerCase();

            filteredApplications = applicationsData.filter(app => {
                const matchesStatus = status === 'all' || app.status === status;
                const matchesSearch = !searchTerm ||
                    app.name.toLowerCase().includes(searchTerm) ||
                    app.email.toLowerCase().includes(searchTerm) ||
                    app.phone.includes(searchTerm) ||
                    app.parentName.toLowerCase().includes(searchTerm) ||
                    app.sport.toLowerCase().includes(searchTerm);

                return matchesStatus && matchesSearch;
            });

            currentPage = 1;
            updateApplicationsList();
        }

        function filterApplicationsByCategory() {
            const category = document.getElementById('applicationsCategoryFilter').value;
            const status = document.getElementById('applicationsStatusFilter').value;
            const searchTerm = document.getElementById('applicationsSearchInput').value.toLowerCase();

            filteredApplications = applicationsData.filter(app => {
                const matchesCategory = category === 'all' || app.category === category;
                const matchesStatus = status === 'all' || app.status === status;
                const matchesSearch = !searchTerm ||
                    app.name.toLowerCase().includes(searchTerm) ||
                    app.email.toLowerCase().includes(searchTerm) ||
                    app.phone.includes(searchTerm) ||
                    app.parentName.toLowerCase().includes(searchTerm) ||
                    app.sport.toLowerCase().includes(searchTerm);

                return matchesCategory && matchesStatus && matchesSearch;
            });

            currentPage = 1;
            updateApplicationsList();
        }

        // ==================== وظائف التفاعل مع الطلبات ====================
        function viewApplicationDetails(appId) {
            const app = applicationsData.find(a => a.id === appId);
            if (!app) return;

            const modal = document.getElementById('applicationModal');
            const detailsContainer = document.getElementById('applicationDetails');

            detailsContainer.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <div>
                        <h4 style="color: var(--brand-primary); margin-bottom: 1rem;">معلومات المتقدم</h4>
                        <div style="margin-bottom: 1rem;">
                            <strong>الاسم:</strong> ${app.name}
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <strong>العمر:</strong> ${app.age} سنة
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <strong>الجنس:</strong> ${app.gender}
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <strong>رقم الجوال:</strong> ${app.phone}
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <strong>البريد الإلكتروني:</strong> ${app.email}
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <strong>الفئة العمرية:</strong> ${app.category}
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <strong>الرياضة المطلوبة:</strong> ${app.sport}
                        </div>
                    </div>

                    <div>
                        <h4 style="color: var(--brand-primary); margin-bottom: 1rem;">معلومات ولي الأمر</h4>
                        <div style="margin-bottom: 1rem;">
                            <strong>اسم ولي الأمر:</strong> ${app.parentName}
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <strong>رقم جوال ولي الأمر:</strong> ${app.parentPhone}
                        </div>

                        <h4 style="color: var(--brand-primary); margin: 1.5rem 0 1rem;">الوثائق المطلوبة</h4>
                        <div style="margin-bottom: 1rem;">
                            <strong>الشهادة الطبية:</strong>
                            <span style="color: ${app.medicalCertificate ? 'var(--success)' : 'var(--danger)'};">
                                ${app.medicalCertificate ? '✓ مرفقة' : '✗ غير مرفقة'}
                            </span>
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <strong>موافقة ولي الأمر:</strong>
                            <span style="color: ${app.parentConsent ? 'var(--success)' : 'var(--danger)'};">
                                ${app.parentConsent ? '✓ موافق' : '✗ غير موافق'}
                            </span>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 2rem; padding-top: 1rem; border-top: 1px solid var(--border);">
                    <h4 style="color: var(--brand-primary); margin-bottom: 1rem;">تقييم الذكاء الاصطناعي</h4>
                    <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 1rem; margin-bottom: 1rem;">
                        <div>
                            <strong>النتيجة:</strong> ${app.aiScore}%
                        </div>
                        <div>
                            <strong>التوصية:</strong>
                            <span style="color: ${app.aiRecommendation === 'قبول' ? 'var(--success)' :
                                app.aiRecommendation === 'رفض' ? 'var(--danger)' : 'var(--warning)'};">
                                ${app.aiRecommendation}
                            </span>
                        </div>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <strong>السبب:</strong> ${app.aiReason}
                    </div>
                </div>

                <div style="margin-top: 2rem; padding-top: 1rem; border-top: 1px solid var(--border);">
                    <h4 style="color: var(--brand-primary); margin-bottom: 1rem;">معلومات الطلب</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div>
                            <strong>تاريخ التقديم:</strong> ${formatDate(app.submissionDate)}
                        </div>
                        <div>
                            <strong>الحالة:</strong>
                            <span style="color: ${getStatusColor(app.status)};">
                                ${getStatusText(app.status)}
                            </span>
                        </div>
                        ${app.reviewDate ? `
                            <div>
                                <strong>تاريخ المراجعة:</strong> ${formatDate(app.reviewDate)}
                            </div>
                            <div>
                                <strong>تمت المراجعة بواسطة:</strong> ${app.reviewedBy}
                            </div>
                        ` : ''}
                    </div>
                    ${app.notes ? `
                        <div style="margin-top: 1rem;">
                            <strong>ملاحظات:</strong>
                            <div style="background: rgba(255,255,255,0.05); padding: 1rem; border-radius: 8px; margin-top: 0.5rem;">
                                ${app.notes}
                            </div>
                        </div>
                    ` : ''}
                </div>

                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    ${app.status === 'pending' || app.status === 'reviewing' ? `
                        <button onclick="approveApplication('${app.id}'); closeApplicationModal();" class="btn" style="background: var(--success);">
                            <i class="fas fa-check"></i> قبول الطلب
                        </button>
                        <button onclick="rejectApplication('${app.id}'); closeApplicationModal();" class="btn" style="background: var(--danger);">
                            <i class="fas fa-times"></i> رفض الطلب
                        </button>
                    ` : ''}
                    ${app.status === 'approved' ? `
                        <button onclick="generateCertificate('${app.id}')" class="btn primary">
                            <i class="fas fa-certificate"></i> إصدار سند الاشتراك
                        </button>
                    ` : ''}
                    <button onclick="closeApplicationModal()" class="btn">إغلاق</button>
                </div>
            `;

            modal.style.display = 'flex';
        }

        function closeApplicationModal() {
            document.getElementById('applicationModal').style.display = 'none';
        }

        function approveApplication(appId) {
            const app = applicationsData.find(a => a.id === appId);
            if (!app) return;

            app.status = 'approved';
            app.reviewDate = new Date().toISOString().split('T')[0];
            app.reviewedBy = currentUser.name;
            app.notes = 'تم قبول الطلب بناءً على استيفاء جميع المتطلبات';

            academyData.applications = applicationsData;
            saveAcademyData();
            updateApplicationsData();

            Swal.fire({
                title: 'تم قبول الطلب!',
                text: `تم قبول طلب ${app.name} بنجاح`,
                icon: 'success',
                background: '#1a1a1a',
                color: '#ffffff'
            });
        }

        function rejectApplication(appId) {
            const app = applicationsData.find(a => a.id === appId);
            if (!app) return;

            Swal.fire({
                title: 'رفض الطلب',
                input: 'textarea',
                inputLabel: 'سبب الرفض',
                inputPlaceholder: 'اكتب سبب رفض الطلب...',
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'رفض الطلب',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    app.status = 'rejected';
                    app.reviewDate = new Date().toISOString().split('T')[0];
                    app.reviewedBy = currentUser.name;
                    app.notes = result.value || 'تم رفض الطلب';

                    academyData.applications = applicationsData;
                    saveAcademyData();
                    updateApplicationsData();

                    Swal.fire({
                        title: 'تم رفض الطلب',
                        text: `تم رفض طلب ${app.name}`,
                        icon: 'info',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        function deleteApplication(appId) {
            const app = applicationsData.find(a => a.id === appId);
            if (!app) return;

            Swal.fire({
                title: 'حذف الطلب',
                text: `هل أنت متأكد من حذف طلب ${app.name}؟`,
                icon: 'warning',
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#ef4444'
            }).then((result) => {
                if (result.isConfirmed) {
                    const index = applicationsData.findIndex(a => a.id === appId);
                    if (index > -1) {
                        applicationsData.splice(index, 1);
                        academyData.applications = applicationsData;
                        saveAcademyData();
                        updateApplicationsData();

                        Swal.fire({
                            title: 'تم الحذف',
                            text: 'تم حذف الطلب بنجاح',
                            icon: 'success',
                            background: '#1a1a1a',
                            color: '#ffffff'
                        });
                    }
                }
            });
        }

        // ==================== وظائف إضافية للطلبات ====================
        function addNewApplication() {
            document.getElementById('addApplicationModal').style.display = 'flex';
        }

        function closeAddApplicationModal() {
            document.getElementById('addApplicationModal').style.display = 'none';
            document.getElementById('addApplicationForm').reset();
        }

        function submitNewApplication(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const newApp = {
                id: 'APP' + Date.now(),
                name: formData.get('name'),
                age: parseInt(formData.get('age')),
                gender: parseInt(formData.get('age')) < 16 ? 'غير محدد' : 'غير محدد',
                phone: formData.get('phone'),
                email: formData.get('email'),
                parentName: 'ولي الأمر',
                parentPhone: formData.get('phone'),
                category: formData.get('category'),
                sport: 'كرة القدم',
                medicalCertificate: formData.get('medicalCertificate') === 'on',
                parentConsent: formData.get('parentConsent') === 'on',
                status: 'pending',
                submissionDate: new Date().toISOString().split('T')[0],
                reviewDate: null,
                reviewedBy: null,
                notes: '',
                aiScore: Math.floor(Math.random() * 30) + 70,
                aiRecommendation: 'قبول',
                aiReason: 'طلب جديد يحتاج للمراجعة'
            };

            applicationsData.push(newApp);
            academyData.applications = applicationsData;
            saveAcademyData();
            updateApplicationsData();
            closeAddApplicationModal();

            Swal.fire({
                title: 'تم إضافة الطلب!',
                text: `تم إضافة طلب ${newApp.name} بنجاح`,
                icon: 'success',
                background: '#1a1a1a',
                color: '#ffffff'
            });
        }

        function generateCertificate(appId) {
            const app = applicationsData.find(a => a.id === appId);
            if (!app) return;

            const modal = document.getElementById('certificateModal');
            const content = document.getElementById('certificateContent');

            content.innerHTML = `
                <div style="
                    background: white; color: #000; padding: 3rem; border-radius: 12px;
                    text-align: center; font-family: 'Cairo', sans-serif;
                    border: 3px solid var(--brand-primary);
                ">
                    <div style="margin-bottom: 2rem;">
                        <div style="
                            width: 80px; height: 80px; background: var(--brand-primary);
                            border-radius: 50%; margin: 0 auto 1rem;
                            display: flex; align-items: center; justify-content: center;
                            color: white; font-size: 2rem;
                        ">
                            <i class="fas fa-dumbbell"></i>
                        </div>
                        <h2 style="color: var(--brand-primary); margin-bottom: 0.5rem;">أكاديمية 7C الرياضية</h2>
                        <p style="color: #666;">للتدريب الرياضي المتخصص</p>
                    </div>

                    <div style="margin: 2rem 0; padding: 2rem; background: #f8f9fa; border-radius: 8px;">
                        <h3 style="color: var(--brand-primary); margin-bottom: 1rem;">سند اشتراك</h3>
                        <p style="font-size: 1.1rem; margin-bottom: 1rem;">
                            نشهد بأن <strong>${app.name}</strong>
                        </p>
                        <p style="margin-bottom: 1rem;">
                            قد تم قبوله/ها في أكاديمية 7C الرياضية
                        </p>
                        <p style="margin-bottom: 1rem;">
                            في فئة <strong>${app.category}</strong> - رياضة <strong>${app.sport}</strong>
                        </p>
                        <p style="color: #666; font-size: 0.9rem;">
                            تاريخ الإصدار: ${formatDate(new Date().toISOString().split('T')[0])}
                        </p>
                    </div>

                    <div style="display: flex; justify-content: space-between; align-items: end; margin-top: 2rem;">
                        <div style="text-align: center;">
                            <div style="border-top: 2px solid #000; width: 150px; margin-bottom: 0.5rem;"></div>
                            <p style="font-size: 0.9rem;">توقيع المدير</p>
                        </div>
                        <div style="text-align: center;">
                            <div style="
                                width: 80px; height: 80px; border: 2px solid #000;
                                display: flex; align-items: center; justify-content: center;
                                font-size: 0.8rem; margin-bottom: 0.5rem;
                            ">ختم الأكاديمية</div>
                        </div>
                    </div>
                </div>
            `;

            modal.style.display = 'flex';
        }

        function closeCertificateModal() {
            document.getElementById('certificateModal').style.display = 'none';
        }

        function printCertificate() {
            const content = document.getElementById('certificateContent').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>سند الاشتراك</title>
                        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
                        <style>
                            body { margin: 0; padding: 20px; font-family: 'Cairo', sans-serif; }
                            @media print { body { margin: 0; } }
                        </style>
                    </head>
                    <body>${content}</body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        function downloadCertificate() {
            Swal.fire({
                title: 'تحميل الشهادة',
                text: 'سيتم تطوير ميزة تحميل PDF قريباً',
                icon: 'info',
                background: '#1a1a1a',
                color: '#ffffff'
            });
        }

        function showAiInsights() {
            const modal = document.getElementById('aiInsightsModal');
            const content = document.getElementById('aiInsightsContent');

            // تحليل البيانات
            const totalApps = applicationsData.length;
            const approvalRate = totalApps > 0 ?
                (applicationsData.filter(app => app.status === 'approved').length / totalApps * 100).toFixed(1) : 0;
            const avgAiScore = totalApps > 0 ?
                (applicationsData.reduce((sum, app) => sum + app.aiScore, 0) / totalApps).toFixed(1) : 0;

            // تحليل الفئات العمرية
            const categoryStats = {};
            applicationsData.forEach(app => {
                categoryStats[app.category] = (categoryStats[app.category] || 0) + 1;
            });

            // تحليل الرياضات
            const sportStats = {};
            applicationsData.forEach(app => {
                sportStats[app.sport] = (sportStats[app.sport] || 0) + 1;
            });

            content.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
                    <div style="background: rgba(139, 69, 19, 0.1); padding: 1.5rem; border-radius: 12px; text-align: center;">
                        <h4 style="color: var(--brand-primary); margin-bottom: 1rem;">معدل القبول</h4>
                        <div style="font-size: 2rem; font-weight: bold; color: var(--success);">${approvalRate}%</div>
                        <p style="color: var(--text-secondary); font-size: 0.9rem;">من إجمالي الطلبات</p>
                    </div>

                    <div style="background: rgba(139, 69, 19, 0.1); padding: 1.5rem; border-radius: 12px; text-align: center;">
                        <h4 style="color: var(--brand-primary); margin-bottom: 1rem;">متوسط تقييم AI</h4>
                        <div style="font-size: 2rem; font-weight: bold; color: var(--info);">${avgAiScore}%</div>
                        <p style="color: var(--text-secondary); font-size: 0.9rem;">دقة التوقعات</p>
                    </div>

                    <div style="background: rgba(139, 69, 19, 0.1); padding: 1.5rem; border-radius: 12px; text-align: center;">
                        <h4 style="color: var(--brand-primary); margin-bottom: 1rem;">إجمالي الطلبات</h4>
                        <div style="font-size: 2rem; font-weight: bold; color: var(--text-primary);">${totalApps}</div>
                        <p style="color: var(--text-secondary); font-size: 0.9rem;">طلب مسجل</p>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                    <div>
                        <h4 style="color: var(--brand-primary); margin-bottom: 1rem;">توزيع الفئات العمرية</h4>
                        <div style="background: rgba(255,255,255,0.05); padding: 1rem; border-radius: 8px;">
                            ${Object.entries(categoryStats).map(([category, count]) => `
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span>${category}</span>
                                    <span style="font-weight: bold;">${count} طلب</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div>
                        <h4 style="color: var(--brand-primary); margin-bottom: 1rem;">توزيع الرياضات</h4>
                        <div style="background: rgba(255,255,255,0.05); padding: 1rem; border-radius: 8px;">
                            ${Object.entries(sportStats).map(([sport, count]) => `
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span>${sport}</span>
                                    <span style="font-weight: bold;">${count} طلب</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <div style="background: rgba(139, 69, 19, 0.1); padding: 1.5rem; border-radius: 12px;">
                    <h4 style="color: var(--brand-primary); margin-bottom: 1rem;">توصيات الذكاء الاصطناعي</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 0.5rem; color: var(--success);">
                            <i class="fas fa-check-circle" style="margin-left: 0.5rem;"></i>
                            معدل القبول مرتفع يدل على جودة المتقدمين
                        </li>
                        <li style="margin-bottom: 0.5rem; color: var(--info);">
                            <i class="fas fa-info-circle" style="margin-left: 0.5rem;"></i>
                            يُنصح بزيادة الطاقة الاستيعابية لفئة الناشئين
                        </li>
                        <li style="margin-bottom: 0.5rem; color: var(--warning);">
                            <i class="fas fa-exclamation-triangle" style="margin-left: 0.5rem;"></i>
                            مراجعة معايير القبول للحصول على تنوع أكبر
                        </li>
                    </ul>
                </div>
            `;

            modal.style.display = 'flex';
        }

        function closeAiInsightsModal() {
            document.getElementById('aiInsightsModal').style.display = 'none';
        }

        function exportApplicationsReport() {
            const data = applicationsData.map(app => ({
                'الاسم': app.name,
                'العمر': app.age,
                'الجنس': app.gender,
                'الهاتف': app.phone,
                'البريد الإلكتروني': app.email,
                'ولي الأمر': app.parentName,
                'الفئة': app.category,
                'الرياضة': app.sport,
                'الحالة': getStatusText(app.status),
                'تاريخ التقديم': app.submissionDate,
                'تقييم AI': app.aiScore + '%',
                'توصية AI': app.aiRecommendation
            }));

            const csv = convertToCSV(data);
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `applications-report-${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            Swal.fire({
                title: 'تم التصدير!',
                text: 'تم تصدير تقرير الطلبات بنجاح',
                icon: 'success',
                background: '#1a1a1a',
                color: '#ffffff'
            });
        }

        // ==================== وظائف الإجراءات المجمعة ====================
        function selectAllApplications() {
            const checkboxes = document.querySelectorAll('.app-checkbox');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);

            checkboxes.forEach(cb => {
                cb.checked = !allChecked;
            });
        }

        function bulkApproveApplications() {
            const selectedIds = getSelectedApplicationIds();
            if (selectedIds.length === 0) {
                Swal.fire({
                    title: 'لم يتم تحديد طلبات',
                    text: 'يرجى تحديد الطلبات المراد قبولها',
                    icon: 'warning',
                    background: '#1a1a1a',
                    color: '#ffffff'
                });
                return;
            }

            Swal.fire({
                title: 'قبول الطلبات المحددة',
                text: `هل أنت متأكد من قبول ${selectedIds.length} طلب؟`,
                icon: 'question',
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'نعم، اقبل الكل',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    selectedIds.forEach(id => {
                        const app = applicationsData.find(a => a.id === id);
                        if (app && (app.status === 'pending' || app.status === 'reviewing')) {
                            app.status = 'approved';
                            app.reviewDate = new Date().toISOString().split('T')[0];
                            app.reviewedBy = currentUser.name;
                            app.notes = 'تم القبول ضمن إجراء مجمع';
                        }
                    });

                    academyData.applications = applicationsData;
                    saveAcademyData();
                    updateApplicationsData();

                    Swal.fire({
                        title: 'تم القبول!',
                        text: `تم قبول ${selectedIds.length} طلب بنجاح`,
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        function bulkRejectApplications() {
            const selectedIds = getSelectedApplicationIds();
            if (selectedIds.length === 0) {
                Swal.fire({
                    title: 'لم يتم تحديد طلبات',
                    text: 'يرجى تحديد الطلبات المراد رفضها',
                    icon: 'warning',
                    background: '#1a1a1a',
                    color: '#ffffff'
                });
                return;
            }

            Swal.fire({
                title: 'رفض الطلبات المحددة',
                input: 'textarea',
                inputLabel: 'سبب الرفض',
                inputPlaceholder: 'اكتب سبب رفض الطلبات...',
                text: `سيتم رفض ${selectedIds.length} طلب`,
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'رفض الكل',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    selectedIds.forEach(id => {
                        const app = applicationsData.find(a => a.id === id);
                        if (app && (app.status === 'pending' || app.status === 'reviewing')) {
                            app.status = 'rejected';
                            app.reviewDate = new Date().toISOString().split('T')[0];
                            app.reviewedBy = currentUser.name;
                            app.notes = result.value || 'تم الرفض ضمن إجراء مجمع';
                        }
                    });

                    academyData.applications = applicationsData;
                    saveAcademyData();
                    updateApplicationsData();

                    Swal.fire({
                        title: 'تم الرفض',
                        text: `تم رفض ${selectedIds.length} طلب`,
                        icon: 'info',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        function bulkDeleteApplications() {
            const selectedIds = getSelectedApplicationIds();
            if (selectedIds.length === 0) {
                Swal.fire({
                    title: 'لم يتم تحديد طلبات',
                    text: 'يرجى تحديد الطلبات المراد حذفها',
                    icon: 'warning',
                    background: '#1a1a1a',
                    color: '#ffffff'
                });
                return;
            }

            Swal.fire({
                title: 'حذف الطلبات المحددة',
                text: `هل أنت متأكد من حذف ${selectedIds.length} طلب؟ هذا الإجراء لا يمكن التراجع عنه.`,
                icon: 'warning',
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف الكل',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#ef4444'
            }).then((result) => {
                if (result.isConfirmed) {
                    selectedIds.forEach(id => {
                        const index = applicationsData.findIndex(a => a.id === id);
                        if (index > -1) {
                            applicationsData.splice(index, 1);
                        }
                    });

                    academyData.applications = applicationsData;
                    saveAcademyData();
                    updateApplicationsData();

                    Swal.fire({
                        title: 'تم الحذف',
                        text: `تم حذف ${selectedIds.length} طلب بنجاح`,
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        function getSelectedApplicationIds() {
            const checkboxes = document.querySelectorAll('.app-checkbox:checked');
            return Array.from(checkboxes).map(cb => cb.dataset.id);
        }

        // ==================== وظائف مساعدة إضافية ====================
        function convertToCSV(data) {
            if (!data || data.length === 0) return '';

            const headers = Object.keys(data[0]);
            const csvHeaders = headers.join(',');

            const csvRows = data.map(row => {
                return headers.map(header => {
                    const value = row[header];
                    // تنظيف البيانات وإضافة علامات اقتباس إذا لزم الأمر
                    if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
                        return `"${value.replace(/"/g, '""')}"`;
                    }
                    return value;
                }).join(',');
            });

            return [csvHeaders, ...csvRows].join('\n');
        }

        // ==================== وظائف إدارة المباريات ====================
        function updateMatchesData() {
            if (!academyData.matches) {
                academyData.matches = [];
                return;
            }

            updateMatchesStats();
            updateMatchesList();
            updateTeamsList();
            updateRefereesList();
            updateVenuesList();
        }

        function updateMatchesStats() {
            const scheduledMatches = academyData.matches.filter(match => match.status === 'scheduled').length;
            const liveMatches = academyData.matches.filter(match => match.status === 'live').length;
            const totalTeams = academyData.teams ? academyData.teams.length : 0;

            // حساب متوسط الأهداف
            const finishedMatches = academyData.matches.filter(match => match.status === 'finished' && match.result);
            const totalGoals = finishedMatches.reduce((sum, match) =>
                sum + (match.result.homeScore || 0) + (match.result.awayScore || 0), 0);
            const avgGoals = finishedMatches.length > 0 ? (totalGoals / finishedMatches.length).toFixed(1) : 0;

            if (document.getElementById('scheduledMatchesCount')) {
                document.getElementById('scheduledMatchesCount').textContent = scheduledMatches;
                document.getElementById('liveMatchesCount').textContent = liveMatches;
                document.getElementById('totalTeamsCount').textContent = totalTeams;
                document.getElementById('avgGoalsPerMatch').textContent = avgGoals;
            }
        }

        function updateMatchesList() {
            const container = document.getElementById('matchesList');
            if (!container) return;

            const matches = academyData.matches || [];

            if (matches.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                        <i class="fas fa-futbol" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>لا توجد مباريات مجدولة</p>
                        <button onclick="addNewMatch()" class="btn primary" style="margin-top: 1rem;">
                            <i class="fas fa-plus"></i>
                            إضافة مباراة جديدة
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = matches.map(match => {
                const homeTeam = academyData.teams.find(t => t.id === match.homeTeam);
                const awayTeam = academyData.teams.find(t => t.id === match.awayTeam);
                const venue = academyData.venues.find(v => v.id === match.venue);
                const referee = academyData.referees.find(r => r.id === match.referee);

                return `
                    <div class="match-card ${match.status}" style="margin-bottom: 1rem;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <span style="
                                    background: ${getMatchStatusColor(match.status)};
                                    color: white;
                                    padding: 0.3rem 0.8rem;
                                    border-radius: 20px;
                                    font-size: 0.8rem;
                                    font-weight: 600;
                                ">${getMatchStatusText(match.status)}</span>

                                ${match.status === 'live' ? `
                                    <div class="live-indicator">
                                        <span>مباشر</span>
                                    </div>
                                ` : ''}

                                <span style="color: var(--text-secondary); font-size: 0.9rem;">
                                    ${match.competition} - ${match.round}
                                </span>
                            </div>

                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <span style="color: var(--text-secondary); font-size: 0.9rem;">
                                    ${formatDate(match.date)} - ${match.time}
                                </span>
                                ${match.importance === 'high' ? `
                                    <i class="fas fa-star" style="color: #fbbf24;" title="مباراة مهمة"></i>
                                ` : ''}
                            </div>
                        </div>

                        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 1rem;">
                            <!-- الفريق المضيف -->
                            <div style="display: flex; align-items: center; gap: 1rem; flex: 1;">
                                <div class="team-logo" style="background: ${homeTeam?.colors?.primary || 'var(--brand-primary)'};">
                                    ${homeTeam?.name?.charAt(0) || 'ف'}
                                </div>
                                <div>
                                    <h4 style="color: var(--text-primary); margin-bottom: 0.3rem;">
                                        ${homeTeam?.name || 'فريق غير محدد'}
                                    </h4>
                                    <p style="color: var(--text-secondary); font-size: 0.8rem; margin: 0;">
                                        ${homeTeam?.category || ''} - مضيف
                                    </p>
                                </div>
                            </div>

                            <!-- النتيجة أو الوقت -->
                            <div style="text-align: center; padding: 0 2rem;">
                                ${match.status === 'finished' && match.result ? `
                                    <div class="match-score">
                                        ${match.result.homeScore} - ${match.result.awayScore}
                                    </div>
                                    <div style="color: var(--text-secondary); font-size: 0.8rem;">
                                        انتهت
                                    </div>
                                ` : match.status === 'live' && match.liveScore ? `
                                    <div class="match-score" style="color: #ef4444;">
                                        ${match.liveScore.homeScore} - ${match.liveScore.awayScore}
                                    </div>
                                    <div style="color: #ef4444; font-size: 0.8rem; font-weight: 600;">
                                        ${match.liveScore.minute}' - ${match.liveScore.period}
                                    </div>
                                ` : `
                                    <div style="color: var(--text-primary); font-size: 1.2rem; font-weight: 600;">
                                        ${match.time}
                                    </div>
                                    <div style="color: var(--text-secondary); font-size: 0.8rem;">
                                        ${formatDate(match.date)}
                                    </div>
                                `}
                            </div>

                            <!-- الفريق الضيف -->
                            <div style="display: flex; align-items: center; gap: 1rem; flex: 1; justify-content: flex-end;">
                                <div style="text-align: right;">
                                    <h4 style="color: var(--text-primary); margin-bottom: 0.3rem;">
                                        ${awayTeam?.name || 'فريق غير محدد'}
                                    </h4>
                                    <p style="color: var(--text-secondary); font-size: 0.8rem; margin: 0;">
                                        ${awayTeam?.category || ''} - ضيف
                                    </p>
                                </div>
                                <div class="team-logo" style="background: ${awayTeam?.colors?.primary || 'var(--brand-secondary)'};">
                                    ${awayTeam?.name?.charAt(0) || 'ف'}
                                </div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin-bottom: 1rem; padding: 1rem; background: rgba(255,255,255,0.03); border-radius: 8px;">
                            <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                                <i class="fas fa-map-marker-alt" style="color: var(--brand-primary);"></i>
                                <span>${venue?.name || 'ملعب غير محدد'}</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                                <i class="fas fa-whistle" style="color: var(--brand-primary);"></i>
                                <span>${referee?.name || 'حكم غير محدد'}</span>
                            </div>
                            ${match.weather ? `
                                <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                                    <i class="fas fa-cloud-sun" style="color: var(--brand-primary);"></i>
                                    <span>${match.weather.condition} - ${match.weather.temperature}°</span>
                                </div>
                            ` : ''}
                            ${match.aiPrediction ? `
                                <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                                    <i class="fas fa-brain" style="color: var(--brand-primary);"></i>
                                    <span>توقع AI: ${match.aiPrediction.homeWinProbability}% فوز مضيف</span>
                                </div>
                            ` : ''}
                        </div>

                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                            <button onclick="viewMatchDetails('${match.id}')" style="
                                background: var(--info); color: white; border: none;
                                padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                            ">
                                <i class="fas fa-eye"></i> التفاصيل
                            </button>

                            ${match.status === 'scheduled' ? `
                                <button onclick="startMatch('${match.id}')" style="
                                    background: var(--success); color: white; border: none;
                                    padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                                ">
                                    <i class="fas fa-play"></i> بدء المباراة
                                </button>
                                <button onclick="editMatch('${match.id}')" style="
                                    background: var(--warning); color: white; border: none;
                                    padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                                ">
                                    <i class="fas fa-edit"></i> تعديل
                                </button>
                            ` : ''}

                            ${match.status === 'live' ? `
                                <button onclick="updateLiveMatch('${match.id}')" style="
                                    background: var(--danger); color: white; border: none;
                                    padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                                ">
                                    <i class="fas fa-broadcast-tower"></i> تحديث مباشر
                                </button>
                                <button onclick="endMatch('${match.id}')" style="
                                    background: var(--brand-primary); color: white; border: none;
                                    padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                                ">
                                    <i class="fas fa-flag-checkered"></i> إنهاء المباراة
                                </button>
                            ` : ''}

                            ${match.status === 'finished' ? `
                                <button onclick="viewMatchReport('${match.id}')" style="
                                    background: var(--brand-primary); color: white; border: none;
                                    padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                                ">
                                    <i class="fas fa-chart-bar"></i> تقرير المباراة
                                </button>
                            ` : ''}

                            <button onclick="deleteMatch('${match.id}')" style="
                                background: transparent; color: var(--text-secondary); border: 1px solid var(--border);
                                padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                            ">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // ==================== وظائف مساعدة للمباريات ====================
        function getMatchStatusColor(status) {
            const colors = {
                'scheduled': '#3b82f6',
                'live': '#ef4444',
                'finished': '#10b981',
                'postponed': '#f59e0b',
                'cancelled': '#6b7280'
            };
            return colors[status] || '#6b7280';
        }

        function getMatchStatusText(status) {
            const texts = {
                'scheduled': 'مجدولة',
                'live': 'مباشرة',
                'finished': 'منتهية',
                'postponed': 'مؤجلة',
                'cancelled': 'ملغية'
            };
            return texts[status] || status;
        }

        function showMatchTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.match-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.match-tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // إظهار التبويب المحدد
            document.getElementById(tabName + 'Tab').classList.add('active');
            document.getElementById(tabName + 'TabContent').classList.add('active');
        }

        function updateTeamsList() {
            const container = document.getElementById('teamsList');
            if (!container) return;

            const teams = academyData.teams || [];

            if (teams.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                        <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>لا توجد فرق مسجلة</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = teams.map(team => {
                const coach = academyData.coaches.find(c => c.id === team.coach);
                const captain = academyData.players.find(p => p.id === team.captain);

                return `
                    <div style="
                        background: rgba(255,255,255,0.05);
                        border: 1px solid var(--border);
                        border-radius: 12px;
                        padding: 1.5rem;
                        margin-bottom: 1rem;
                        transition: all 0.3s ease;
                        border-right: 4px solid ${team.colors.primary};
                    " onmouseover="this.style.background='rgba(255,255,255,0.1)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.05)'">

                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 1rem;">
                            <div style="display: flex; align-items: center; gap: 1rem;">
                                <div class="team-logo" style="background: ${team.colors.primary}; width: 60px; height: 60px; font-size: 1.5rem;">
                                    ${team.name.charAt(0)}
                                </div>
                                <div>
                                    <h4 style="color: var(--text-primary); font-size: 1.2rem; margin-bottom: 0.5rem;">
                                        ${team.name}
                                    </h4>
                                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.3rem;">
                                        <span style="
                                            background: var(--brand-primary);
                                            color: white;
                                            padding: 0.2rem 0.6rem;
                                            border-radius: 12px;
                                            font-size: 0.8rem;
                                        ">${team.category}</span>
                                        <span style="
                                            background: var(--info);
                                            color: white;
                                            padding: 0.2rem 0.6rem;
                                            border-radius: 12px;
                                            font-size: 0.8rem;
                                        ">${team.sport}</span>
                                        <span style="color: var(--text-secondary); font-size: 0.9rem;">
                                            التشكيل: ${team.formation}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div style="text-align: left;">
                                <div style="color: var(--success); font-size: 1.2rem; font-weight: bold;">
                                    ${team.points} نقطة
                                </div>
                                <div style="color: var(--text-secondary); font-size: 0.8rem;">
                                    المركز ${team.position}
                                </div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 0.5rem; background: rgba(40, 167, 69, 0.1); border-radius: 8px;">
                                <div style="color: #28a745; font-weight: bold;">${team.wins}</div>
                                <div style="color: var(--text-secondary); font-size: 0.8rem;">فوز</div>
                            </div>
                            <div style="text-align: center; padding: 0.5rem; background: rgba(255, 193, 7, 0.1); border-radius: 8px;">
                                <div style="color: #ffc107; font-weight: bold;">${team.draws}</div>
                                <div style="color: var(--text-secondary); font-size: 0.8rem;">تعادل</div>
                            </div>
                            <div style="text-align: center; padding: 0.5rem; background: rgba(220, 53, 69, 0.1); border-radius: 8px;">
                                <div style="color: #dc3545; font-weight: bold;">${team.losses}</div>
                                <div style="color: var(--text-secondary); font-size: 0.8rem;">خسارة</div>
                            </div>
                            <div style="text-align: center; padding: 0.5rem; background: rgba(23, 162, 184, 0.1); border-radius: 8px;">
                                <div style="color: #17a2b8; font-weight: bold;">${team.goalsFor}:${team.goalsAgainst}</div>
                                <div style="color: var(--text-secondary); font-size: 0.8rem;">الأهداف</div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                                <i class="fas fa-user-tie" style="color: var(--brand-primary);"></i>
                                <span>المدرب: ${coach?.name || 'غير محدد'}</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                                <i class="fas fa-star" style="color: var(--brand-primary);"></i>
                                <span>الكابتن: ${captain?.name || 'غير محدد'}</span>
                            </div>
                        </div>

                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                            <button onclick="viewTeamDetails('${team.id}')" style="
                                background: var(--info); color: white; border: none;
                                padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                            ">
                                <i class="fas fa-eye"></i> التفاصيل
                            </button>

                            <button onclick="editTeam('${team.id}')" style="
                                background: var(--warning); color: white; border: none;
                                padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                            ">
                                <i class="fas fa-edit"></i> تعديل
                            </button>

                            <button onclick="manageTeamPlayers('${team.id}')" style="
                                background: var(--brand-primary); color: white; border: none;
                                padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                            ">
                                <i class="fas fa-users"></i> إدارة اللاعبين
                            </button>

                            <button onclick="viewTeamStats('${team.id}')" style="
                                background: var(--success); color: white; border: none;
                                padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                            ">
                                <i class="fas fa-chart-line"></i> الإحصائيات
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function updateRefereesList() {
            const container = document.getElementById('refereesList');
            if (!container) return;

            const referees = academyData.referees || [];

            if (referees.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                        <i class="fas fa-whistle" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>لا يوجد حكام مسجلون</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = referees.map(referee => `
                <div style="
                    background: rgba(255,255,255,0.05);
                    border: 1px solid var(--border);
                    border-radius: 12px;
                    padding: 1.5rem;
                    margin-bottom: 1rem;
                    transition: all 0.3s ease;
                " onmouseover="this.style.background='rgba(255,255,255,0.1)'"
                   onmouseout="this.style.background='rgba(255,255,255,0.05)'">

                    <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 1rem;">
                        <div style="display: flex; align-items: center; gap: 1rem;">
                            <div style="
                                width: 50px; height: 50px;
                                background: linear-gradient(135deg, #1f2937, #374151);
                                border-radius: 50%;
                                display: flex; align-items: center; justify-content: center;
                                color: white; font-size: 1.2rem;
                            ">
                                <i class="fas fa-whistle"></i>
                            </div>
                            <div>
                                <h4 style="color: var(--text-primary); font-size: 1.1rem; margin-bottom: 0.5rem;">
                                    ${referee.name}
                                </h4>
                                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.3rem;">
                                    <span style="
                                        background: var(--brand-primary);
                                        color: white;
                                        padding: 0.2rem 0.6rem;
                                        border-radius: 12px;
                                        font-size: 0.8rem;
                                    ">${referee.level}</span>
                                    <span style="color: var(--text-secondary); font-size: 0.9rem;">
                                        ${referee.experience} سنوات خبرة
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: left;">
                            <div style="color: var(--success); font-size: 1.2rem; font-weight: bold;">
                                ${referee.rating}/5
                            </div>
                            <div style="color: var(--text-secondary); font-size: 0.8rem;">
                                التقييم
                            </div>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                        <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                            <i class="fas fa-phone" style="color: var(--brand-primary);"></i>
                            <span>${referee.phone}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                            <i class="fas fa-envelope" style="color: var(--brand-primary);"></i>
                            <span>${referee.email}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                            <i class="fas fa-futbol" style="color: var(--brand-primary);"></i>
                            <span>${referee.sports.join(', ')}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                            <i class="fas fa-trophy" style="color: var(--brand-primary);"></i>
                            <span>${referee.matchesRefereed} مباراة</span>
                        </div>
                    </div>

                    <div style="margin-bottom: 1rem;">
                        <strong style="color: var(--text-primary);">الشهادات:</strong>
                        <div style="margin-top: 0.3rem;">
                            ${referee.certifications.map(cert => `
                                <span style="
                                    background: rgba(40, 167, 69, 0.2);
                                    color: #28a745;
                                    padding: 0.2rem 0.5rem;
                                    border-radius: 8px;
                                    font-size: 0.8rem;
                                    margin-left: 0.3rem;
                                    display: inline-block;
                                    margin-bottom: 0.3rem;
                                ">${cert}</span>
                            `).join('')}
                        </div>
                    </div>

                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                        <button onclick="viewRefereeDetails('${referee.id}')" style="
                            background: var(--info); color: white; border: none;
                            padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                        ">
                            <i class="fas fa-eye"></i> التفاصيل
                        </button>

                        <button onclick="editReferee('${referee.id}')" style="
                            background: var(--warning); color: white; border: none;
                            padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                        ">
                            <i class="fas fa-edit"></i> تعديل
                        </button>

                        <button onclick="viewRefereeSchedule('${referee.id}')" style="
                            background: var(--brand-primary); color: white; border: none;
                            padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                        ">
                            <i class="fas fa-calendar"></i> الجدول
                        </button>

                        <button onclick="assignRefereeToMatch('${referee.id}')" style="
                            background: var(--success); color: white; border: none;
                            padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                        ">
                            <i class="fas fa-plus"></i> تعيين لمباراة
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function updateVenuesList() {
            const container = document.getElementById('venuesList');
            if (!container) return;

            const venues = academyData.venues || [];

            if (venues.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                        <i class="fas fa-map-marker-alt" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>لا توجد ملاعب مسجلة</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = venues.map(venue => `
                <div style="
                    background: rgba(255,255,255,0.05);
                    border: 1px solid var(--border);
                    border-radius: 12px;
                    padding: 1.5rem;
                    margin-bottom: 1rem;
                    transition: all 0.3s ease;
                " onmouseover="this.style.background='rgba(255,255,255,0.1)'"
                   onmouseout="this.style.background='rgba(255,255,255,0.05)'">

                    <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 1rem;">
                        <div style="display: flex; align-items: center; gap: 1rem;">
                            <div style="
                                width: 50px; height: 50px;
                                background: linear-gradient(135deg, #059669, #10b981);
                                border-radius: 50%;
                                display: flex; align-items: center; justify-content: center;
                                color: white; font-size: 1.2rem;
                            ">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div>
                                <h4 style="color: var(--text-primary); font-size: 1.1rem; margin-bottom: 0.5rem;">
                                    ${venue.name}
                                </h4>
                                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.3rem;">
                                    <span style="
                                        background: var(--brand-primary);
                                        color: white;
                                        padding: 0.2rem 0.6rem;
                                        border-radius: 12px;
                                        font-size: 0.8rem;
                                    ">${venue.type}</span>
                                    <span style="
                                        background: ${venue.status === 'متاح' ? '#10b981' : '#ef4444'};
                                        color: white;
                                        padding: 0.2rem 0.6rem;
                                        border-radius: 12px;
                                        font-size: 0.8rem;
                                    ">${venue.status}</span>
                                    <span style="color: var(--text-secondary); font-size: 0.9rem;">
                                        ${venue.capacity} متفرج
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: left;">
                            <div style="color: var(--success); font-size: 1.2rem; font-weight: bold;">
                                ${venue.bookingRate} ر.س
                            </div>
                            <div style="color: var(--text-secondary); font-size: 0.8rem;">
                                سعر الحجز
                            </div>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                        <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                            <i class="fas fa-ruler-combined" style="color: var(--brand-primary);"></i>
                            <span>${venue.dimensions}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                            <i class="fas fa-seedling" style="color: var(--brand-primary);"></i>
                            <span>${venue.surface}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                            <i class="fas fa-map-pin" style="color: var(--brand-primary);"></i>
                            <span>${venue.location}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary);">
                            <i class="fas fa-car" style="color: var(--brand-primary);"></i>
                            <span>${venue.parkingSpaces} موقف</span>
                        </div>
                    </div>

                    <div style="margin-bottom: 1rem;">
                        <strong style="color: var(--text-primary);">المرافق:</strong>
                        <div style="margin-top: 0.3rem;">
                            ${venue.facilities.map(facility => `
                                <span style="
                                    background: rgba(59, 130, 246, 0.2);
                                    color: #3b82f6;
                                    padding: 0.2rem 0.5rem;
                                    border-radius: 8px;
                                    font-size: 0.8rem;
                                    margin-left: 0.3rem;
                                    display: inline-block;
                                    margin-bottom: 0.3rem;
                                ">${facility}</span>
                            `).join('')}
                        </div>
                    </div>

                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                        <button onclick="viewVenueDetails('${venue.id}')" style="
                            background: var(--info); color: white; border: none;
                            padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                        ">
                            <i class="fas fa-eye"></i> التفاصيل
                        </button>

                        <button onclick="editVenue('${venue.id}')" style="
                            background: var(--warning); color: white; border: none;
                            padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                        ">
                            <i class="fas fa-edit"></i> تعديل
                        </button>

                        <button onclick="viewVenueSchedule('${venue.id}')" style="
                            background: var(--brand-primary); color: white; border: none;
                            padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                        ">
                            <i class="fas fa-calendar"></i> جدول الحجوزات
                        </button>

                        <button onclick="bookVenue('${venue.id}')" style="
                            background: var(--success); color: white; border: none;
                            padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;
                        ">
                            <i class="fas fa-plus"></i> حجز جديد
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // ==================== الوظائف التفاعلية الأساسية ====================
        function addNewMatch() {
            Swal.fire({
                title: 'إضافة مباراة جديدة',
                html: `
                    <div style="text-align: right;">
                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem;">عنوان المباراة:</label>
                            <input type="text" id="matchTitle" class="swal2-input" placeholder="مثال: نسور 7C ضد صقور الأكاديمية">
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem;">الفريق المضيف:</label>
                                <select id="homeTeam" class="swal2-select">
                                    <option value="">اختر الفريق المضيف</option>
                                    ${academyData.teams.map(team => `<option value="${team.id}">${team.name}</option>`).join('')}
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem;">الفريق الضيف:</label>
                                <select id="awayTeam" class="swal2-select">
                                    <option value="">اختر الفريق الضيف</option>
                                    ${academyData.teams.map(team => `<option value="${team.id}">${team.name}</option>`).join('')}
                                </select>
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem;">التاريخ:</label>
                                <input type="date" id="matchDate" class="swal2-input">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem;">الوقت:</label>
                                <input type="time" id="matchTime" class="swal2-input">
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem;">الملعب:</label>
                                <select id="matchVenue" class="swal2-select">
                                    <option value="">اختر الملعب</option>
                                    ${academyData.venues.map(venue => `<option value="${venue.id}">${venue.name}</option>`).join('')}
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem;">الحكم:</label>
                                <select id="matchReferee" class="swal2-select">
                                    <option value="">اختر الحكم</option>
                                    ${academyData.referees.map(referee => `<option value="${referee.id}">${referee.name}</option>`).join('')}
                                </select>
                            </div>
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem;">البطولة:</label>
                            <select id="matchCompetition" class="swal2-select">
                                <option value="دوري الأكاديمية">دوري الأكاديمية</option>
                                <option value="كأس الأكاديمية">كأس الأكاديمية</option>
                                <option value="مباريات ودية">مباريات ودية</option>
                            </select>
                        </div>
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'إضافة المباراة',
                cancelButtonText: 'إلغاء',
                preConfirm: () => {
                    const title = document.getElementById('matchTitle').value;
                    const homeTeam = document.getElementById('homeTeam').value;
                    const awayTeam = document.getElementById('awayTeam').value;
                    const date = document.getElementById('matchDate').value;
                    const time = document.getElementById('matchTime').value;
                    const venue = document.getElementById('matchVenue').value;
                    const referee = document.getElementById('matchReferee').value;
                    const competition = document.getElementById('matchCompetition').value;

                    if (!title || !homeTeam || !awayTeam || !date || !time || !venue || !referee) {
                        Swal.showValidationMessage('يرجى ملء جميع الحقول المطلوبة');
                        return false;
                    }

                    if (homeTeam === awayTeam) {
                        Swal.showValidationMessage('لا يمكن أن يكون الفريق المضيف والضيف نفس الفريق');
                        return false;
                    }

                    return { title, homeTeam, awayTeam, date, time, venue, referee, competition };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    const newMatch = {
                        id: 'M' + Date.now(),
                        title: result.value.title,
                        homeTeam: result.value.homeTeam,
                        awayTeam: result.value.awayTeam,
                        date: result.value.date,
                        time: result.value.time,
                        venue: result.value.venue,
                        referee: result.value.referee,
                        assistantReferees: [],
                        competition: result.value.competition,
                        round: 'جولة جديدة',
                        status: 'scheduled',
                        importance: 'medium',
                        ticketPrice: 20,
                        expectedAttendance: 200,
                        aiPrediction: {
                            homeWinProbability: 50,
                            drawProbability: 25,
                            awayWinProbability: 25,
                            expectedGoals: { home: 1.5, away: 1.5 },
                            keyFactors: ['مباراة جديدة']
                        }
                    };

                    academyData.matches.push(newMatch);
                    saveAcademyData();
                    updateMatchesData();

                    Swal.fire({
                        title: 'تم إضافة المباراة!',
                        text: `تم إضافة مباراة ${newMatch.title} بنجاح`,
                        icon: 'success',
                        background: '#1a1a1a',
                        color: '#ffffff'
                    });
                }
            });
        }

        function viewMatchDetails(matchId) {
            const match = academyData.matches.find(m => m.id === matchId);
            if (!match) return;

            const homeTeam = academyData.teams.find(t => t.id === match.homeTeam);
            const awayTeam = academyData.teams.find(t => t.id === match.awayTeam);
            const venue = academyData.venues.find(v => v.id === match.venue);
            const referee = academyData.referees.find(r => r.id === match.referee);

            Swal.fire({
                title: 'تفاصيل المباراة',
                html: `
                    <div style="text-align: right;">
                        <h3 style="color: var(--brand-primary); margin-bottom: 1rem;">${match.title}</h3>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                            <div>
                                <h4 style="color: var(--brand-primary);">معلومات المباراة</h4>
                                <p><strong>التاريخ:</strong> ${formatDate(match.date)}</p>
                                <p><strong>الوقت:</strong> ${match.time}</p>
                                <p><strong>البطولة:</strong> ${match.competition}</p>
                                <p><strong>الجولة:</strong> ${match.round}</p>
                                <p><strong>الحالة:</strong> ${getMatchStatusText(match.status)}</p>
                                <p><strong>الأهمية:</strong> ${match.importance}</p>
                            </div>

                            <div>
                                <h4 style="color: var(--brand-primary);">الفرق والملعب</h4>
                                <p><strong>الفريق المضيف:</strong> ${homeTeam?.name || 'غير محدد'}</p>
                                <p><strong>الفريق الضيف:</strong> ${awayTeam?.name || 'غير محدد'}</p>
                                <p><strong>الملعب:</strong> ${venue?.name || 'غير محدد'}</p>
                                <p><strong>الحكم:</strong> ${referee?.name || 'غير محدد'}</p>
                                <p><strong>سعر التذكرة:</strong> ${match.ticketPrice} ر.س</p>
                            </div>
                        </div>

                        ${match.result ? `
                            <div style="margin-bottom: 2rem;">
                                <h4 style="color: var(--brand-primary);">النتيجة النهائية</h4>
                                <div style="text-align: center; font-size: 2rem; font-weight: bold; margin: 1rem 0;">
                                    ${homeTeam?.name} ${match.result.homeScore} - ${match.result.awayScore} ${awayTeam?.name}
                                </div>
                            </div>
                        ` : ''}

                        ${match.aiPrediction ? `
                            <div style="margin-bottom: 2rem;">
                                <h4 style="color: var(--brand-primary);">توقعات الذكاء الاصطناعي</h4>
                                <p><strong>احتمالية فوز المضيف:</strong> ${match.aiPrediction.homeWinProbability}%</p>
                                <p><strong>احتمالية التعادل:</strong> ${match.aiPrediction.drawProbability}%</p>
                                <p><strong>احتمالية فوز الضيف:</strong> ${match.aiPrediction.awayWinProbability}%</p>
                                <p><strong>الأهداف المتوقعة:</strong> ${match.aiPrediction.expectedGoals.home} - ${match.aiPrediction.expectedGoals.away}</p>
                            </div>
                        ` : ''}
                    </div>
                `,
                background: '#1a1a1a',
                color: '#ffffff',
                width: '800px',
                confirmButtonText: 'إغلاق'
            });
        }

        function deleteMatch(matchId) {
            const match = academyData.matches.find(m => m.id === matchId);
            if (!match) return;

            Swal.fire({
                title: 'حذف المباراة',
                text: `هل أنت متأكد من حذف مباراة ${match.title}؟`,
                icon: 'warning',
                background: '#1a1a1a',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#ef4444'
            }).then((result) => {
                if (result.isConfirmed) {
                    const index = academyData.matches.findIndex(m => m.id === matchId);
                    if (index > -1) {
                        academyData.matches.splice(index, 1);
                        saveAcademyData();
                        updateMatchesData();

                        Swal.fire({
                            title: 'تم الحذف',
                            text: 'تم حذف المباراة بنجاح',
                            icon: 'success',
                            background: '#1a1a1a',
                            color: '#ffffff'
                        });
                    }
                }
            });
        }

        // وظائف مؤقتة للأزرار الأخرى
        function addNewTeam() {
            Swal.fire({
                title: 'إضافة فريق جديد',
                text: 'هذه الميزة ستكون متاحة في المرحلة التالية',
                icon: 'info',
                background: '#1a1a1a',
                color: '#ffffff'
            });
        }

        function addNewReferee() {
            Swal.fire({
                title: 'إضافة حكم جديد',
                text: 'هذه الميزة ستكون متاحة في المرحلة التالية',
                icon: 'info',
                background: '#1a1a1a',
                color: '#ffffff'
            });
        }

        function addNewVenue() {
            Swal.fire({
                title: 'إضافة ملعب جديد',
                text: 'هذه الميزة ستكون متاحة في المرحلة التالية',
                icon: 'info',
                background: '#1a1a1a',
                color: '#ffffff'
            });
        }

        // وظائف البحث والفلترة (مؤقتة)
        function searchMatches() {
            // سيتم تطويرها في المرحلة التالية
        }

        function filterMatchesByStatus() {
            // سيتم تطويرها في المرحلة التالية
        }

        function filterMatchesByCompetition() {
            // سيتم تطويرها في المرحلة التالية
        }

        function filterMatchesByDate() {
            // سيتم تطويرها في المرحلة التالية
        }

        console.log('✅ نظام إدارة الأكاديمية مع إدارة المباريات (المرحلة الأولى) جاهز للاستخدام!');
        console.log('🔧 الاختصارات المتاحة:');
        console.log('Ctrl+1: لوحة المعلومات');
        console.log('Ctrl+2: إدارة اللاعبين');
        console.log('Ctrl+3: إدارة المدربين');
        console.log('Ctrl+4: إدارة المباريات');
        console.log('Ctrl+S: حفظ البيانات');
    </script>
</body>
</html>
