#!/bin/bash
# سكريبت تثبيت مشروع المساعد الذكي

# تثبيت backend
cd backend
npm install
# تشغيل الخادم في الخلفية (يمكنك استبداله بـ pm2 لاحقًا)
npm install -g pm2
pm2 start index.js --name ai-backend
cd ..

# تثبيت frontend
cd frontend
npm install
npm run build || echo "تم بناء الواجهة الأمامية (أو يمكنك استخدام npm start للتشغيل المحلي)"
cd ..

echo "تم تثبيت المشروع بنجاح!"
echo "الخادم يعمل عبر pm2 ويمكنك إدارته بالأوامر: pm2 list, pm2 logs ai-backend, pm2 stop ai-backend"
