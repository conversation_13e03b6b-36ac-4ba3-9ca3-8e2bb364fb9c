<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المباريات والبطولات المتطور - أكاديمية 7C الرياضية</title>

    <!-- External Libraries -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

    <!-- AI and Advanced Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/ml-matrix@6.10.4/lib/index.min.js"></script>
    <style>
        :root {
            /* Sports Color Palette - Based on New/index.html */
            --primary-dark: #0f172a;
            --primary-blue: #1e3a8a;
            --accent-blue: #3b82f6;
            --light-blue: #60a5fa;
            --silver: #e2e8f0;
            --white: #ffffff;
            --dark-silver: #94a3b8;

            /* Academy Colors */
            --academy-primary: #8B4513;
            --academy-secondary: #A0522D;
            --academy-accent: #CD853F;
            --success-color: #228B22;
            --warning-color: #DAA520;
            --danger-color: #B22222;
            --text-dark: #2D1B0E;
            --text-light: #8B4513;

            /* Sports Gradients */
            --gradient-primary: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #3b82f6 100%);
            --gradient-academy: linear-gradient(135deg, var(--academy-primary), var(--academy-secondary));
            --gradient-field: linear-gradient(180deg, #16a34a 0%, #15803d 50%, #166534 100%);
            --gradient-silver: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
            --gradient-accent: linear-gradient(45deg, #3b82f6, #60a5fa, #93c5fd);

            /* Glass Effects */
            --glass-bg: rgba(255, 255, 255, 0.9);
            --glass-border: rgba(139, 69, 19, 0.1);
            --glass-shadow: rgba(139, 69, 19, 0.1);

            /* Sports Shadows */
            --shadow-blue: 0 10px 30px rgba(59, 130, 246, 0.3);
            --shadow-academy: 0 8px 32px rgba(139, 69, 19, 0.1);
            --shadow-silver: 0 8px 25px rgba(148, 163, 184, 0.2);
            --shadow-field: 0 15px 35px rgba(22, 101, 52, 0.4);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #ffffff 0%, #fefefe 50%, #ffffff 100%);
            color: var(--text-dark);
            min-height: 100vh;
            direction: rtl;
            position: relative;
            overflow-x: hidden;
        }

        /* Sports Pattern Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(139, 69, 19, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(160, 82, 45, 0.05) 0%, transparent 50%);
            z-index: -2;
        }

        /* Floating Sports Elements */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%238B4513' fill-opacity='0.02'%3E%3Ccircle cx='50' cy='50' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            z-index: -1;
            opacity: 0.3;
            animation: patternFloat 30s ease-in-out infinite;
        }

        @keyframes patternFloat {
            0%, 100% { transform: translateX(0px) translateY(0px); }
            33% { transform: translateX(10px) translateY(-5px); }
            66% { transform: translateX(-5px) translateY(10px); }
        }

        /* Animated Soccer Ball */
        .soccer-ball {
            position: fixed;
            font-size: 2rem;
            color: var(--academy-primary);
            opacity: 0.1;
            animation: ballFloat 15s ease-in-out infinite;
            z-index: -1;
        }

        .soccer-ball:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .soccer-ball:nth-child(2) { top: 20%; right: 15%; animation-delay: 5s; }
        .soccer-ball:nth-child(3) { bottom: 30%; left: 20%; animation-delay: 10s; }

        @keyframes ballFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            position: relative;
            z-index: 1;
        }

        /* Enhanced Sports Header */
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 3rem 2rem;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 25px;
            box-shadow: var(--shadow-academy);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(139, 69, 19, 0.1), transparent);
            animation: headerShine 4s infinite;
        }

        @keyframes headerShine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 900;
            margin-bottom: 1rem;
            background: var(--gradient-academy);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 2;
            animation: titleFloat 6s ease-in-out infinite;
        }

        @keyframes titleFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        .header p {
            font-size: 1.3rem;
            color: var(--text-light);
            position: relative;
            z-index: 2;
            font-weight: 600;
        }

        /* Academy Logo in Header */
        .header::after {
            content: '🏆';
            position: absolute;
            top: 20px;
            right: 30px;
            font-size: 3rem;
            opacity: 0.1;
            animation: trophyGlow 3s ease-in-out infinite;
        }

        @keyframes trophyGlow {
            0%, 100% { opacity: 0.1; transform: scale(1); }
            50% { opacity: 0.2; transform: scale(1.1); }
        }

        /* AI Smart Dashboard */
        .ai-dashboard {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-academy);
            position: relative;
            overflow: hidden;
        }

        .ai-dashboard::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: var(--gradient-academy);
            border-radius: 50%;
            transform: translate(50%, -50%);
            opacity: 0.1;
        }

        .ai-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--success-color);
            font-weight: 600;
        }

        .ai-indicator {
            width: 8px;
            height: 8px;
            background: var(--success-color);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }

        /* Quick Stats Grid */
        .quick-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .stat-card {
            background: white;
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-academy);
        }

        .stat-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 35px rgba(139, 69, 19, 0.2);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            background: var(--gradient-academy);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            animation: iconFloat 3s ease-in-out infinite;
        }

        @keyframes iconFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-3px); }
        }

        .stat-content {
            flex: 1;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 900;
            color: var(--academy-primary);
            line-height: 1;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            color: var(--text-light);
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .stat-trend {
            font-size: 0.8rem;
            font-weight: 600;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            display: inline-block;
        }

        .stat-trend.positive {
            background: rgba(34, 139, 34, 0.1);
            color: var(--success-color);
        }

        .stat-trend.negative {
            background: rgba(178, 34, 34, 0.1);
            color: var(--danger-color);
        }

        /* AI Insights Panel */
        .ai-insights-panel {
            margin: 2rem 0;
        }

        .ai-insights-panel h4 {
            color: var(--academy-primary);
            margin-bottom: 1rem;
            font-size: 1.2rem;
            font-weight: 700;
        }

        .insights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }

        .insight-card {
            background: white;
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            padding: 1rem;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .insight-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-academy);
        }

        .insight-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        .insight-icon.success {
            background: var(--success-color);
        }

        .insight-icon.warning {
            background: var(--warning-color);
        }

        .insight-icon.info {
            background: var(--academy-primary);
        }

        .insight-content h5 {
            color: var(--text-dark);
            font-size: 1rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .insight-content p {
            color: var(--text-light);
            font-size: 0.9rem;
            line-height: 1.4;
        }

        /* Quick Actions */
        .quick-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 2rem;
        }

        .btn-ai {
            background: linear-gradient(135deg, #8b5cf6, #a78bfa);
            background-size: 200% 200%;
            animation: aiGradient 3s ease infinite;
            position: relative;
            overflow: hidden;
        }

        @keyframes aiGradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .btn-ai::after {
            content: '🤖';
            position: absolute;
            top: -5px;
            right: -5px;
            font-size: 0.8rem;
            animation: aiPulse 2s infinite;
        }

        @keyframes aiPulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.8; }
        }

        /* AI Coach Assistant */
        .ai-coach-assistant {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 350px;
            height: 500px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(139, 69, 19, 0.2);
            border: 1px solid var(--glass-border);
            display: none;
            flex-direction: column;
            z-index: 1000;
            animation: slideInUp 0.3s ease;
        }

        @keyframes slideInUp {
            from { transform: translateY(100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .ai-assistant-header {
            background: var(--gradient-academy);
            color: white;
            padding: 1rem;
            border-radius: 20px 20px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .ai-assistant-header h4 {
            margin: 0;
            font-size: 1rem;
            font-weight: 700;
        }

        .ai-close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .ai-close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .ai-chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .ai-chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            max-height: 350px;
        }

        .ai-message, .user-message {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 1rem;
            align-items: flex-start;
        }

        .user-message {
            flex-direction: row-reverse;
        }

        .ai-avatar, .user-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        .ai-avatar {
            background: var(--gradient-academy);
        }

        .user-avatar {
            background: var(--accent-blue);
            color: white;
        }

        .ai-message-content, .user-message-content {
            background: #f8f9fa;
            padding: 0.75rem;
            border-radius: 15px;
            max-width: 80%;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .user-message-content {
            background: var(--accent-blue);
            color: white;
        }

        .ai-chat-input {
            padding: 1rem;
            border-top: 1px solid var(--glass-border);
            display: flex;
            gap: 0.5rem;
        }

        .ai-chat-input input {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            font-size: 0.9rem;
            outline: none;
        }

        .ai-chat-input input:focus {
            border-color: var(--academy-primary);
        }

        .ai-chat-input button {
            background: var(--gradient-academy);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s ease;
        }

        .ai-chat-input button:hover {
            transform: scale(1.1);
        }

        /* AI Chat Toggle Button */
        .ai-chat-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: var(--gradient-academy);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 10px 25px rgba(139, 69, 19, 0.3);
            transition: all 0.3s ease;
            z-index: 999;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .ai-chat-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 15px 35px rgba(139, 69, 19, 0.4);
        }

        .ai-notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--danger-color);
            color: white;
            font-size: 0.7rem;
            font-weight: bold;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .header p {
                font-size: 1rem;
            }

            .quick-stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .insights-grid {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .ai-coach-assistant {
                width: 90%;
                right: 5%;
                left: 5%;
                height: 70vh;
            }

            .ai-chat-toggle {
                bottom: 10px;
                right: 10px;
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }
        }

        @media (max-width: 480px) {
            .quick-stats-grid {
                grid-template-columns: 1fr;
            }

            .stat-card {
                flex-direction: column;
                text-align: center;
                gap: 0.5rem;
            }

            .stat-icon {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }

            .stat-number {
                font-size: 1.8rem;
            }
        }

        /* Enhanced Visual Effects */
        .card:hover::before {
            animation: cardGlow 2s ease-in-out infinite;
        }

        @keyframes cardGlow {
            0%, 100% { opacity: 0.1; }
            50% { opacity: 0.2; }
        }

        /* Loading Animation for AI */
        .ai-loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(139, 69, 19, 0.3);
            border-radius: 50%;
            border-top-color: var(--academy-primary);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Success Animation */
        .success-checkmark {
            display: inline-block;
            animation: checkmark 0.6s ease-in-out;
        }

        @keyframes checkmark {
            0% { transform: scale(0); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        /* Enhanced Sports Navigation */
        .main-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1rem;
            background: var(--glass-bg);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            border: 1px solid var(--glass-border);
            overflow-x: auto;
            box-shadow: var(--shadow-silver);
        }

        .main-tab {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            color: var(--white);
            padding: 1rem 2rem;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .main-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--gradient-accent);
            transition: left 0.3s ease;
            z-index: -1;
        }

        .main-tab:hover::before,
        .main-tab.active::before {
            left: 0;
        }

        .main-tab:hover,
        .main-tab.active {
            transform: translateY(-3px);
            box-shadow: var(--shadow-blue);
            color: var(--white);
        }

        .main-tab i {
            font-size: 1.2rem;
            transition: transform 0.3s ease;
        }

        .main-tab:hover i {
            transform: scale(1.2);
        }

        .tab-content {
            display: none;
            animation: fadeInUp 0.6s ease-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Enhanced Sports Cards */
        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-blue);
            transition: all 0.3s ease;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: var(--gradient-accent);
            border-radius: 50%;
            transform: translate(50%, -50%);
            opacity: 0.1;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-blue), 0 20px 40px rgba(59, 130, 246, 0.2);
        }

        .card:hover::before {
            transform: translate(30%, -30%) scale(1.2);
            opacity: 0.15;
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--glass-border);
            position: relative;
            z-index: 2;
        }

        .card-header h3 {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--white);
        }

        .card-header i {
            color: var(--accent-blue);
            font-size: 1.3rem;
            animation: iconPulse 2s ease-in-out infinite;
        }

        @keyframes iconPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* Enhanced Sports Buttons */
        .btn {
            background: var(--gradient-academy);
            color: white;
            border: none;
            padding: 0.875rem 1.75rem;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            text-decoration: none;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: var(--shadow-academy);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
        }

        .btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(139, 69, 19, 0.3);
        }

        .btn i {
            transition: transform 0.3s ease;
        }

        .btn:hover i {
            transform: scale(1.2);
        }

        .btn-secondary {
            background: var(--gradient-silver);
            color: var(--primary-dark);
        }

        .btn-secondary:hover {
            box-shadow: var(--shadow-silver), 0 15px 30px rgba(148, 163, 184, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #10B981, #34D399);
        }

        .btn-success:hover {
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #F59E0B, #FBBF24);
        }

        .btn-warning:hover {
            box-shadow: 0 10px 25px rgba(245, 158, 11, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #EF4444, #F87171);
        }

        .btn-danger:hover {
            box-shadow: 0 10px 25px rgba(239, 68, 68, 0.4);
        }

        .btn-small {
            padding: 0.625rem 1.25rem;
            font-size: 0.875rem;
        }

        /* AI Button Special Style */
        .btn-ai {
            background: linear-gradient(45deg, #8b5cf6, #a78bfa, #c4b5fd);
            background-size: 300% 300%;
            animation: aiGradient 3s ease infinite;
            position: relative;
        }

        @keyframes aiGradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .btn-ai::after {
            content: '🤖';
            position: absolute;
            top: -5px;
            right: -5px;
            font-size: 0.8rem;
            animation: aiPulse 2s infinite;
        }

        @keyframes aiPulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.8; }
        }

        .grid-2 {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
        }

        .grid-3 {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
        }

        .grid-4 {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #D2691E;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #D2691E;
            box-shadow: 0 0 0 2px rgba(210, 105, 30, 0.3);
        }

        .match-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .match-card:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .match-teams {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .match-score {
            font-size: 2rem;
            font-weight: bold;
            color: #D2691E;
        }

        .match-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .match-info-item {
            text-align: center;
        }

        .match-info-label {
            font-size: 0.8rem;
            opacity: 0.7;
            margin-bottom: 0.25rem;
        }

        .match-info-value {
            font-weight: bold;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-scheduled {
            background: #3B82F6;
            color: white;
        }

        .status-live {
            background: #EF4444;
            color: white;
            animation: pulse 2s infinite;
        }

        .status-finished {
            background: #10B981;
            color: white;
        }

        .status-cancelled {
            background: #6B7280;
            color: white;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* Football Field Styles */
        .football-field {
            width: 100%;
            max-width: 500px;
            aspect-ratio: 2/3;
            margin: 0 auto;
            position: relative;
            border: 2px solid white;
            border-radius: 10px;
            overflow: hidden;
        }

        .field-background {
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #2d5a2d 25%, #3d6a3d 25%, #3d6a3d 50%, #2d5a2d 50%, #2d5a2d 75%, #3d6a3d 75%);
            background-size: 20px 20px;
            position: relative;
        }

        .goal {
            position: absolute;
            width: 30%;
            height: 8%;
            background: rgba(255, 255, 255, 0.3);
            border: 2px solid white;
            left: 50%;
            transform: translateX(-50%);
        }

        .goal-top {
            top: -2px;
            border-bottom: 2px solid white;
            border-top: none;
        }

        .goal-bottom {
            bottom: -2px;
            border-top: 2px solid white;
            border-bottom: none;
        }

        .center-circle {
            position: absolute;
            width: 80px;
            height: 80px;
            border: 2px solid white;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .center-circle::after {
            content: '';
            position: absolute;
            width: 4px;
            height: 4px;
            background: white;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .penalty-area {
            position: absolute;
            width: 60%;
            height: 20%;
            border: 2px solid white;
            left: 50%;
            transform: translateX(-50%);
        }

        .penalty-area-top {
            top: 0;
            border-top: none;
        }

        .penalty-area-bottom {
            bottom: 0;
            border-bottom: none;
        }

        .field-players {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .player-on-field {
            position: absolute;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #8B4513, #D2691E);
            border: 2px solid white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
            color: white;
            cursor: move;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .player-on-field:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.5);
        }

        .player-on-field.goalkeeper {
            background: linear-gradient(135deg, #10B981, #34D399);
        }

        .players-list {
            max-height: 400px;
            overflow-y: auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 1rem;
        }

        .available-player {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            cursor: grab;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .available-player:hover {
            background: rgba(139, 69, 19, 0.2);
            transform: translateX(-5px);
        }

        .available-player.dragging {
            opacity: 0.5;
            cursor: grabbing;
        }

        .player-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .player-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(135deg, #8B4513, #D2691E);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 0.8rem;
        }

        .player-details {
            flex: 1;
        }

        .player-name {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .player-position {
            font-size: 0.8rem;
            opacity: 0.8;
            color: #D2691E;
        }

        .player-rating {
            background: rgba(16, 185, 129, 0.2);
            color: #10B981;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        /* Formation Positions */
        .formation-4-4-2 .player-1 { top: 5%; left: 50%; transform: translateX(-50%); }
        .formation-4-4-2 .player-2 { top: 20%; left: 15%; }
        .formation-4-4-2 .player-3 { top: 20%; left: 35%; }
        .formation-4-4-2 .player-4 { top: 20%; left: 65%; }
        .formation-4-4-2 .player-5 { top: 20%; left: 85%; }
        .formation-4-4-2 .player-6 { top: 45%; left: 15%; }
        .formation-4-4-2 .player-7 { top: 45%; left: 35%; }
        .formation-4-4-2 .player-8 { top: 45%; left: 65%; }
        .formation-4-4-2 .player-9 { top: 45%; left: 85%; }
        .formation-4-4-2 .player-10 { top: 70%; left: 35%; }
        .formation-4-4-2 .player-11 { top: 70%; left: 65%; }

        .formation-4-3-3 .player-1 { top: 5%; left: 50%; transform: translateX(-50%); }
        .formation-4-3-3 .player-2 { top: 20%; left: 15%; }
        .formation-4-3-3 .player-3 { top: 20%; left: 35%; }
        .formation-4-3-3 .player-4 { top: 20%; left: 65%; }
        .formation-4-3-3 .player-5 { top: 20%; left: 85%; }
        .formation-4-3-3 .player-6 { top: 45%; left: 25%; }
        .formation-4-3-3 .player-7 { top: 45%; left: 50%; transform: translateX(-50%); }
        .formation-4-3-3 .player-8 { top: 45%; left: 75%; }
        .formation-4-3-3 .player-9 { top: 70%; left: 20%; }
        .formation-4-3-3 .player-10 { top: 70%; left: 50%; transform: translateX(-50%); }
        .formation-4-3-3 .player-11 { top: 70%; left: 80%; }

        /* Drop Zone Styles */
        .drop-zone {
            position: absolute;
            width: 50px;
            height: 50px;
            border: 2px dashed rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: none;
        }

        .drop-zone.active {
            display: block;
            border-color: #D2691E;
            background: rgba(210, 105, 30, 0.2);
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            animation: fadeIn 0.3s ease-out;
        }

        .modal-content {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border-radius: 15px;
            max-width: 90vw;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            animation: slideIn 0.3s ease-out;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 2rem;
            background: linear-gradient(135deg, #8B4513, #D2691E);
            color: white;
        }

        .modal-header h3 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 2rem;
            color: white;
            overflow-y: auto;
            max-height: calc(90vh - 100px);
        }

        .formation-item:hover {
            background: rgba(139, 69, 19, 0.2) !important;
            transform: translateY(-2px);
        }

        @keyframes slideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* Approval System Styles */
        .approval-status {
            padding: 0.5rem;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
        }

        .approval-status.status-pending {
            background: rgba(245, 158, 11, 0.2);
            color: #F59E0B;
            border: 1px solid #F59E0B;
        }

        .approval-status.status-approved {
            background: rgba(16, 185, 129, 0.2);
            color: #10B981;
            border: 1px solid #10B981;
        }

        .approval-status.status-rejected {
            background: rgba(239, 68, 68, 0.2);
            color: #EF4444;
            border: 1px solid #EF4444;
        }

        .approval-status.status-expired {
            background: rgba(107, 114, 128, 0.2);
            color: #6B7280;
            border: 1px solid #6B7280;
        }

        .player-approval-card {
            transition: all 0.3s ease;
        }

        .player-approval-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.2);
        }

        .approval-request-card {
            transition: all 0.3s ease;
        }

        .approval-request-card:hover {
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 0.1) !important;
        }

        .status-badge.status-pending {
            background: #F59E0B;
            color: white;
        }

        .status-badge.status-approved {
            background: #10B981;
            color: white;
        }

        .status-badge.status-rejected {
            background: #EF4444;
            color: white;
        }

        .status-badge.status-expired {
            background: #6B7280;
            color: white;
        }

        /* Statistics and Analysis Styles */
        .live-stat-card {
            transition: all 0.3s ease;
        }

        .live-stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.2);
        }

        .analysis-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid rgba(139, 69, 19, 0.3);
            overflow-x: auto;
        }

        .analysis-tab {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px 8px 0 0;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            white-space: nowrap;
        }

        .analysis-tab:hover {
            background: rgba(139, 69, 19, 0.3);
        }

        .analysis-tab.active {
            background: linear-gradient(135deg, #8B4513, #D2691E);
            box-shadow: 0 4px 8px rgba(139, 69, 19, 0.3);
        }

        .analysis-tab-content {
            animation: fadeIn 0.5s ease-in;
        }

        .events-timeline {
            max-height: 400px;
            overflow-y: auto;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .event-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border-left: 4px solid;
        }

        .event-item.goal {
            border-left-color: #10B981;
        }

        .event-item.yellow_card {
            border-left-color: #F59E0B;
        }

        .event-item.red_card {
            border-left-color: #EF4444;
        }

        .event-item.substitution {
            border-left-color: #3B82F6;
        }

        .event-time {
            font-weight: bold;
            color: #D2691E;
            min-width: 50px;
        }

        .event-icon {
            font-size: 1.2rem;
            width: 30px;
            text-align: center;
        }

        .event-description {
            flex: 1;
        }

        .heatmap-container {
            position: relative;
            width: 100%;
            max-width: 400px;
            aspect-ratio: 2/3;
            margin: 0 auto;
            background: linear-gradient(45deg, #2d5a2d 25%, #3d6a3d 25%, #3d6a3d 50%, #2d5a2d 50%, #2d5a2d 75%, #3d6a3d 75%);
            background-size: 20px 20px;
            border: 2px solid white;
            border-radius: 10px;
            overflow: hidden;
        }

        .heat-point {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            pointer-events: none;
            animation: pulse 2s infinite;
        }

        .heat-point.low {
            background: rgba(59, 130, 246, 0.6);
        }

        .heat-point.medium {
            background: rgba(245, 158, 11, 0.7);
        }

        .heat-point.high {
            background: rgba(239, 68, 68, 0.8);
        }

        .zone-stat {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }

        .zone-name {
            font-weight: bold;
        }

        .zone-value {
            color: #D2691E;
            font-weight: bold;
        }

        .player-rating-card {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .player-rating-card:hover {
            background: rgba(139, 69, 19, 0.2);
            transform: translateX(-5px);
        }

        .rating-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .rating-excellent {
            background: #10B981;
            color: white;
        }

        .rating-good {
            background: #3B82F6;
            color: white;
        }

        .rating-average {
            background: #F59E0B;
            color: white;
        }

        .rating-poor {
            background: #EF4444;
            color: white;
        }

        .tactical-insight {
            padding: 1rem;
            margin-bottom: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 4px solid #D2691E;
        }

        .insight-title {
            font-weight: bold;
            color: #D2691E;
            margin-bottom: 0.5rem;
        }

        .insight-description {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .performance-metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .performance-metric:last-child {
            border-bottom: none;
        }

        .metric-name {
            font-weight: bold;
        }

        .metric-value {
            color: #D2691E;
            font-weight: bold;
        }

        .trend-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.8rem;
        }

        .trend-up {
            color: #10B981;
        }

        .trend-down {
            color: #EF4444;
        }

        .trend-stable {
            color: #F59E0B;
        }

        /* Chart containers */
        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }

        /* Live tracking indicator */
        .live-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #EF4444;
            border-radius: 20px;
            color: #EF4444;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        .live-indicator::before {
            content: '';
            width: 8px;
            height: 8px;
            background: #EF4444;
            border-radius: 50%;
            animation: pulse 1s infinite;
        }

        @media (max-width: 768px) {
            .grid-2 {
                grid-template-columns: 1fr;
            }

            .grid-3, .grid-4 {
                grid-template-columns: repeat(2, 1fr);
            }

            .main-tabs {
                flex-wrap: wrap;
            }

            .main-tab {
                flex: 1;
                min-width: 120px;
            }

            .match-teams {
                flex-direction: column;
                gap: 0.5rem;
            }

            .match-info {
                grid-template-columns: 1fr;
            }

            .football-field {
                max-width: 300px;
            }

            .player-on-field {
                width: 30px;
                height: 30px;
                font-size: 0.7rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Floating Sports Elements -->
        <div class="soccer-ball">⚽</div>
        <div class="soccer-ball">🏆</div>
        <div class="soccer-ball">⚽</div>

        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-futbol"></i> نظام إدارة المباريات والبطولات المتطور</h1>
            <p>أكاديمية 7C للتدريب الرياضي - مدعوم بالذكاء الاصطناعي والتحليل المتقدم</p>
        </div>

        <!-- AI Smart Dashboard -->
        <div class="ai-dashboard card">
            <div class="card-header">
                <h3><i class="fas fa-brain"></i> لوحة التحكم الذكية</h3>
                <div class="ai-status">
                    <span class="ai-indicator"></span>
                    <span>الذكاء الاصطناعي نشط</span>
                </div>
            </div>

            <!-- Quick Stats Grid -->
            <div class="quick-stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="stat-number" data-target="24">0</h4>
                        <p class="stat-label">المباريات هذا الشهر</p>
                        <span class="stat-trend positive">+15%</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="stat-number" data-target="156">0</h4>
                        <p class="stat-label">اللاعبون النشطون</p>
                        <span class="stat-trend positive">+8%</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="stat-number" data-target="87">0</h4>
                        <p class="stat-label">معدل الفوز %</p>
                        <span class="stat-trend positive">+12%</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="stat-number" data-target="94">0</h4>
                        <p class="stat-label">دقة التوقعات %</p>
                        <span class="stat-trend positive">+5%</span>
                    </div>
                </div>
            </div>

            <!-- AI Insights Panel -->
            <div class="ai-insights-panel">
                <h4><i class="fas fa-lightbulb"></i> رؤى الذكاء الاصطناعي</h4>
                <div class="insights-grid">
                    <div class="insight-card">
                        <div class="insight-icon success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="insight-content">
                            <h5>نقطة قوة</h5>
                            <p>تحسن ملحوظ في دقة التمرير بنسبة 15% في آخر 5 مباريات</p>
                        </div>
                    </div>

                    <div class="insight-card">
                        <div class="insight-icon warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="insight-content">
                            <h5>تحسين مطلوب</h5>
                            <p>ضعف في الدفاع الجانبي الأيمن - يحتاج تعزيز</p>
                        </div>
                    </div>

                    <div class="insight-card">
                        <div class="insight-icon info">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="insight-content">
                            <h5>توصية تكتيكية</h5>
                            <p>استخدام تشكيلة 4-3-3 في المباراة القادمة لاستغلال نقاط ضعف الخصم</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <button class="btn btn-ai" onclick="runAIAnalysis()">
                    <i class="fas fa-brain"></i>
                    تحليل ذكي شامل
                </button>
                <button class="btn btn-success" onclick="predictNextMatch()">
                    <i class="fas fa-crystal-ball"></i>
                    توقع المباراة القادمة
                </button>
                <button class="btn btn-warning" onclick="openAIChat()">
                    <i class="fas fa-comments"></i>
                    مساعد المدرب الذكي
                </button>
                <button class="btn btn-secondary" onclick="generateAdvancedReport()">
                    <i class="fas fa-chart-line"></i>
                    تقرير متقدم
                </button>
            </div>
        </div>

        <!-- AI Smart Dashboard -->
        <div class="ai-dashboard card">
            <div class="card-header">
                <h3><i class="fas fa-brain"></i> لوحة التحكم الذكية</h3>
                <div class="ai-status">
                    <span class="ai-indicator"></span>
                    <span>الذكاء الاصطناعي نشط</span>
                </div>
            </div>

            <!-- Quick Stats Grid -->
            <div class="quick-stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="stat-number" data-target="24">0</h4>
                        <p class="stat-label">المباريات هذا الشهر</p>
                        <span class="stat-trend positive">+15%</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="stat-number" data-target="156">0</h4>
                        <p class="stat-label">اللاعبون النشطون</p>
                        <span class="stat-trend positive">+8%</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="stat-number" data-target="87">0</h4>
                        <p class="stat-label">معدل الفوز %</p>
                        <span class="stat-trend positive">+12%</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="stat-number" data-target="94">0</h4>
                        <p class="stat-label">دقة التوقعات %</p>
                        <span class="stat-trend positive">+5%</span>
                    </div>
                </div>
            </div>

            <!-- AI Insights Panel -->
            <div class="ai-insights-panel">
                <h4><i class="fas fa-lightbulb"></i> رؤى الذكاء الاصطناعي</h4>
                <div class="insights-grid">
                    <div class="insight-card">
                        <div class="insight-icon success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="insight-content">
                            <h5>نقطة قوة</h5>
                            <p>تحسن ملحوظ في دقة التمرير بنسبة 15% في آخر 5 مباريات</p>
                        </div>
                    </div>

                    <div class="insight-card">
                        <div class="insight-icon warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="insight-content">
                            <h5>تحسين مطلوب</h5>
                            <p>ضعف في الدفاع الجانبي الأيمن - يحتاج تعزيز</p>
                        </div>
                    </div>

                    <div class="insight-card">
                        <div class="insight-icon info">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="insight-content">
                            <h5>توصية تكتيكية</h5>
                            <p>استخدام تشكيلة 4-3-3 في المباراة القادمة لاستغلال نقاط ضعف الخصم</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <button class="btn btn-ai" onclick="runAIAnalysis()">
                    <i class="fas fa-brain"></i>
                    تحليل ذكي شامل
                </button>
                <button class="btn btn-success" onclick="predictNextMatch()">
                    <i class="fas fa-crystal-ball"></i>
                    توقع المباراة القادمة
                </button>
                <button class="btn btn-warning" onclick="openAIChat()">
                    <i class="fas fa-comments"></i>
                    مساعد المدرب الذكي
                </button>
                <button class="btn btn-secondary" onclick="generateAdvancedReport()">
                    <i class="fas fa-chart-line"></i>
                    تقرير متقدم
                </button>
            </div>
        </div>

        <!-- AI Coach Assistant (Floating) -->
        <div id="aiCoachAssistant" class="ai-coach-assistant">
            <div class="ai-assistant-header">
                <h4><i class="fas fa-robot"></i> مساعد المدرب الذكي</h4>
                <button class="ai-close-btn" onclick="closeAIChat()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="ai-chat-container">
                <div class="ai-chat-messages" id="aiChatMessages">
                    <div class="ai-message">
                        <div class="ai-avatar">🤖</div>
                        <div class="ai-message-content">
                            <p>مرحباً! أنا مساعدك الذكي لتحليل المباريات والتكتيكات. كيف يمكنني مساعدتك اليوم؟</p>
                        </div>
                    </div>
                </div>
                <div class="ai-chat-input">
                    <input type="text" id="aiChatInput" placeholder="اسأل عن التكتيكات، التحليل، أو أي شيء متعلق بالمباريات..." />
                    <button onclick="sendAIMessage()">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- AI Chat Toggle Button -->
        <div class="ai-chat-toggle" onclick="toggleAIChat()">
            <i class="fas fa-robot"></i>
            <span class="ai-notification-badge" id="aiNotificationBadge">1</span>
        </div>

        <!-- Navigation Tabs -->
        <div class="main-tabs">
            <button class="main-tab active" onclick="showMainTab('matches')" id="matchesTabBtn">
                <i class="fas fa-calendar-alt"></i>
                المباريات
            </button>
            <button class="main-tab" onclick="showMainTab('tournaments')" id="tournamentsTabBtn">
                <i class="fas fa-trophy"></i>
                البطولات
            </button>
            <button class="main-tab" onclick="showMainTab('formations')" id="formationsTabBtn">
                <i class="fas fa-chess-board"></i>
                التشكيلات
            </button>
            <button class="main-tab" onclick="showMainTab('statistics')" id="statisticsTabBtn">
                <i class="fas fa-chart-bar"></i>
                الإحصائيات
            </button>
            <button class="main-tab" onclick="showMainTab('approvals')" id="approvalsTabBtn">
                <i class="fas fa-check-circle"></i>
                الموافقات
            </button>
        </div>

        <!-- Matches Tab -->
        <div id="matchesTab" class="tab-content active">
            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-plus-circle"></i> إجراءات سريعة</h3>
                </div>
                <div class="grid-4">
                    <button class="btn" onclick="openNewMatchModal()">
                        <i class="fas fa-plus"></i>
                        مباراة جديدة
                    </button>
                    <button class="btn btn-secondary" onclick="openScheduleModal()">
                        <i class="fas fa-calendar-plus"></i>
                        جدولة مباريات
                    </button>
                    <button class="btn btn-success" onclick="openLiveMatchModal()">
                        <i class="fas fa-play"></i>
                        بدء مباراة مباشرة
                    </button>
                    <button class="btn btn-warning" onclick="exportMatchesReport()">
                        <i class="fas fa-file-export"></i>
                        تصدير تقرير
                    </button>
                </div>
            </div>

            <!-- Upcoming Matches -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-clock"></i> المباريات القادمة</h3>
                    <button class="btn btn-small" onclick="refreshMatches()">
                        <i class="fas fa-sync"></i>
                        تحديث
                    </button>
                </div>
                <div id="upcomingMatches">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>

            <!-- Recent Matches -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-history"></i> المباريات الأخيرة</h3>
                </div>
                <div id="recentMatches">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Other tabs will be added in subsequent edits -->
        <div id="tournamentsTab" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-trophy"></i> إدارة البطولات</h3>
                </div>
                <p>سيتم تطوير هذا القسم قريباً...</p>
            </div>
        </div>

        <div id="formationsTab" class="tab-content">
            <!-- Formation Controls -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-chess-board"></i> لوحة المدرب التفاعلية</h3>
                    <div style="display: flex; gap: 1rem;">
                        <select id="formationSelect" onchange="changeFormation()" style="padding: 0.5rem; border-radius: 5px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                            <option value="4-4-2">4-4-2</option>
                            <option value="4-3-3">4-3-3</option>
                            <option value="3-5-2">3-5-2</option>
                            <option value="4-5-1">4-5-1</option>
                            <option value="5-3-2">5-3-2</option>
                        </select>
                        <button class="btn btn-small" onclick="saveFormation()">
                            <i class="fas fa-save"></i>
                            حفظ التشكيلة
                        </button>
                        <button class="btn btn-small btn-secondary" onclick="loadFormation()">
                            <i class="fas fa-folder-open"></i>
                            تحميل تشكيلة
                        </button>
                    </div>
                </div>

                <div class="grid-2">
                    <!-- Players List -->
                    <div>
                        <h4 style="margin-bottom: 1rem; color: #D2691E;">
                            <i class="fas fa-users"></i>
                            قائمة اللاعبين المتاحين
                        </h4>
                        <div style="margin-bottom: 1rem;">
                            <select id="categorySelect" onchange="filterPlayersByCategory()" style="width: 100%; padding: 0.5rem; border-radius: 5px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                <option value="all">جميع الفئات</option>
                                <option value="تحت 16">تحت 16</option>
                                <option value="تحت 14">تحت 14</option>
                                <option value="تحت 12">تحت 12</option>
                            </select>
                        </div>
                        <div id="availablePlayers" class="players-list">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Football Field -->
                    <div>
                        <h4 style="margin-bottom: 1rem; color: #D2691E;">
                            <i class="fas fa-futbol"></i>
                            الملعب - التشكيلة الحالية
                        </h4>
                        <div id="footballField" class="football-field">
                            <div class="field-background">
                                <!-- Field markings will be added by CSS -->
                                <div class="goal goal-top"></div>
                                <div class="goal goal-bottom"></div>
                                <div class="center-circle"></div>
                                <div class="penalty-area penalty-area-top"></div>
                                <div class="penalty-area penalty-area-bottom"></div>

                                <!-- Player positions will be added dynamically -->
                                <div id="fieldPlayers" class="field-players">
                                    <!-- Players will be positioned here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Formation Info -->
                <div style="margin-top: 2rem; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 10px;">
                    <h4 style="margin-bottom: 1rem; color: #D2691E;">معلومات التشكيلة</h4>
                    <div class="grid-4">
                        <div class="text-center">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #10B981;" id="playersCount">0</div>
                            <div style="font-size: 0.9rem; opacity: 0.8;">اللاعبين في الملعب</div>
                        </div>
                        <div class="text-center">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #3B82F6;" id="formationName">4-4-2</div>
                            <div style="font-size: 0.9rem; opacity: 0.8;">التشكيلة الحالية</div>
                        </div>
                        <div class="text-center">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #F59E0B;" id="avgRating">0</div>
                            <div style="font-size: 0.9rem; opacity: 0.8;">متوسط التقييم</div>
                        </div>
                        <div class="text-center">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #8B4513;" id="teamChemistry">0%</div>
                            <div style="font-size: 0.9rem; opacity: 0.8;">كيمياء الفريق</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tactical Instructions -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-clipboard-list"></i> التعليمات التكتيكية</h3>
                </div>
                <div class="grid-3">
                    <div>
                        <h5 style="color: #D2691E; margin-bottom: 1rem;">أسلوب اللعب</h5>
                        <select id="playStyle" style="width: 100%; padding: 0.5rem; border-radius: 5px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                            <option value="attacking">هجومي</option>
                            <option value="defensive">دفاعي</option>
                            <option value="balanced">متوازن</option>
                            <option value="counter">هجمات مرتدة</option>
                        </select>
                    </div>
                    <div>
                        <h5 style="color: #D2691E; margin-bottom: 1rem;">الضغط</h5>
                        <select id="pressing" style="width: 100%; padding: 0.5rem; border-radius: 5px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                            <option value="high">ضغط عالي</option>
                            <option value="medium">ضغط متوسط</option>
                            <option value="low">ضغط منخفض</option>
                        </select>
                    </div>
                    <div>
                        <h5 style="color: #D2691E; margin-bottom: 1rem;">عرض الملعب</h5>
                        <select id="width" style="width: 100%; padding: 0.5rem; border-radius: 5px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                            <option value="wide">عريض</option>
                            <option value="narrow">ضيق</option>
                            <option value="mixed">مختلط</option>
                        </select>
                    </div>
                </div>
                <div style="margin-top: 1rem;">
                    <h5 style="color: #D2691E; margin-bottom: 1rem;">ملاحظات إضافية</h5>
                    <textarea id="tacticalNotes" placeholder="أضف تعليمات تكتيكية إضافية..." style="width: 100%; height: 100px; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3); resize: vertical;"></textarea>
                </div>
            </div>
        </div>

        <div id="statisticsTab" class="tab-content">
            <!-- Live Match Statistics -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-play-circle"></i> إحصائيات المباراة المباشرة</h3>
                    <div style="display: flex; gap: 1rem;">
                        <select id="liveMatchSelect" onchange="selectLiveMatch()" style="padding: 0.5rem; border-radius: 5px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                            <option value="">اختر المباراة...</option>
                        </select>
                        <button class="btn btn-success" onclick="startLiveTracking()">
                            <i class="fas fa-play"></i>
                            بدء التتبع المباشر
                        </button>
                        <button class="btn btn-danger" onclick="stopLiveTracking()">
                            <i class="fas fa-stop"></i>
                            إيقاف التتبع
                        </button>
                    </div>
                </div>

                <div id="liveMatchContent" style="display: none;">
                    <!-- Match Timer and Score -->
                    <div class="grid-3 gap-4" style="margin-bottom: 2rem;">
                        <div class="live-stat-card" style="background: rgba(16, 185, 129, 0.1); border: 1px solid #10B981; border-radius: 10px; padding: 1.5rem; text-align: center;">
                            <div style="font-size: 2.5rem; font-weight: bold; color: #10B981;" id="matchTimer">00:00</div>
                            <div style="font-size: 0.9rem; opacity: 0.8;">وقت المباراة</div>
                        </div>
                        <div class="live-stat-card" style="background: rgba(139, 69, 19, 0.1); border: 1px solid #8B4513; border-radius: 10px; padding: 1.5rem; text-align: center;">
                            <div style="font-size: 2.5rem; font-weight: bold; color: #D2691E;" id="currentScore">0 - 0</div>
                            <div style="font-size: 0.9rem; opacity: 0.8;">النتيجة الحالية</div>
                        </div>
                        <div class="live-stat-card" style="background: rgba(59, 130, 246, 0.1); border: 1px solid #3B82F6; border-radius: 10px; padding: 1.5rem; text-align: center;">
                            <div style="font-size: 2.5rem; font-weight: bold; color: #3B82F6;" id="totalEvents">0</div>
                            <div style="font-size: 0.9rem; opacity: 0.8;">إجمالي الأحداث</div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card" style="margin-bottom: 2rem;">
                        <div class="card-header">
                            <h4><i class="fas fa-bolt"></i> إجراءات سريعة</h4>
                        </div>
                        <div class="grid-4 gap-2">
                            <button class="btn btn-success" onclick="recordEvent('goal')">
                                <i class="fas fa-futbol"></i>
                                هدف
                            </button>
                            <button class="btn btn-warning" onclick="recordEvent('yellow_card')">
                                <i class="fas fa-square" style="color: #F59E0B;"></i>
                                بطاقة صفراء
                            </button>
                            <button class="btn btn-danger" onclick="recordEvent('red_card')">
                                <i class="fas fa-square" style="color: #EF4444;"></i>
                                بطاقة حمراء
                            </button>
                            <button class="btn btn-secondary" onclick="recordEvent('substitution')">
                                <i class="fas fa-exchange-alt"></i>
                                تبديل
                            </button>
                            <button class="btn" onclick="recordEvent('corner')">
                                <i class="fas fa-flag"></i>
                                ركنية
                            </button>
                            <button class="btn" onclick="recordEvent('free_kick')">
                                <i class="fas fa-running"></i>
                                ركلة حرة
                            </button>
                            <button class="btn" onclick="recordEvent('offside')">
                                <i class="fas fa-hand-paper"></i>
                                تسلل
                            </button>
                            <button class="btn" onclick="recordEvent('foul')">
                                <i class="fas fa-exclamation-triangle"></i>
                                خطأ
                            </button>
                        </div>
                    </div>

                    <!-- Live Statistics Grid -->
                    <div class="grid-2 gap-4">
                        <!-- Team Statistics -->
                        <div class="card">
                            <div class="card-header">
                                <h4><i class="fas fa-chart-pie"></i> إحصائيات الفريق</h4>
                            </div>
                            <div id="teamStatsContainer">
                                <!-- Will be populated by JavaScript -->
                            </div>
                        </div>

                        <!-- Player Performance -->
                        <div class="card">
                            <div class="card-header">
                                <h4><i class="fas fa-users"></i> أداء اللاعبين</h4>
                            </div>
                            <div id="playerPerformanceContainer">
                                <!-- Will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Events Timeline -->
                    <div class="card" style="margin-top: 2rem;">
                        <div class="card-header">
                            <h4><i class="fas fa-timeline"></i> خط زمني للأحداث</h4>
                        </div>
                        <div id="eventsTimeline" class="events-timeline">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Analysis Dashboard -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-brain"></i> لوحة التحليل الذكي</h3>
                    <div style="display: flex; gap: 1rem;">
                        <select id="analysisMatchSelect" onchange="loadMatchAnalysis()" style="padding: 0.5rem; border-radius: 5px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                            <option value="">اختر المباراة للتحليل...</option>
                        </select>
                        <button class="btn btn-secondary" onclick="generateAIReport()">
                            <i class="fas fa-robot"></i>
                            تحليل ذكي
                        </button>
                        <button class="btn btn-warning" onclick="exportStatisticsCSV()">
                            <i class="fas fa-file-csv"></i>
                            تصدير CSV
                        </button>
                        <button class="btn" onclick="exportCompleteBackup()">
                            <i class="fas fa-database"></i>
                            نسخة احتياطية
                        </button>
                    </div>
                </div>

                <div id="aiAnalysisContent">
                    <!-- AI Analysis Tabs -->
                    <div class="analysis-tabs" style="display: flex; gap: 0.5rem; margin-bottom: 1.5rem; border-bottom: 2px solid rgba(139, 69, 19, 0.3);">
                        <button class="analysis-tab active" onclick="showAnalysisTab('heatmap')" id="heatmapTabBtn">
                            <i class="fas fa-fire"></i>
                            الخريطة الحرارية
                        </button>
                        <button class="analysis-tab" onclick="showAnalysisTab('tactical')" id="tacticalTabBtn">
                            <i class="fas fa-chess"></i>
                            التحليل التكتيكي
                        </button>
                        <button class="analysis-tab" onclick="showAnalysisTab('performance')" id="performanceTabBtn">
                            <i class="fas fa-chart-line"></i>
                            تحليل الأداء
                        </button>
                        <button class="analysis-tab" onclick="showAnalysisTab('comparison')" id="comparisonTabBtn">
                            <i class="fas fa-balance-scale"></i>
                            المقارنات
                        </button>
                    </div>

                    <!-- Heatmap Tab -->
                    <div id="heatmapTab" class="analysis-tab-content">
                        <div class="grid-2 gap-4">
                            <div>
                                <h4 style="margin-bottom: 1rem; color: #D2691E;">
                                    <i class="fas fa-fire"></i>
                                    خريطة حرارية للاعبين
                                </h4>
                                <div id="playerHeatmap" class="heatmap-container">
                                    <!-- Heatmap will be rendered here -->
                                </div>
                            </div>
                            <div>
                                <h4 style="margin-bottom: 1rem; color: #D2691E;">
                                    <i class="fas fa-chart-area"></i>
                                    إحصائيات المناطق
                                </h4>
                                <div id="zoneStatistics">
                                    <!-- Zone statistics will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tactical Analysis Tab -->
                    <div id="tacticalTab" class="analysis-tab-content" style="display: none;">
                        <div class="grid-2 gap-4">
                            <div>
                                <h4 style="margin-bottom: 1rem; color: #D2691E;">
                                    <i class="fas fa-network-wired"></i>
                                    شبكة التمريرات
                                </h4>
                                <canvas id="passingNetworkChart" width="400" height="300"></canvas>
                            </div>
                            <div>
                                <h4 style="margin-bottom: 1rem; color: #D2691E;">
                                    <i class="fas fa-shield-alt"></i>
                                    تحليل الدفاع والهجوم
                                </h4>
                                <div id="tacticalInsights">
                                    <!-- Tactical insights will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Analysis Tab -->
                    <div id="performanceTab" class="analysis-tab-content" style="display: none;">
                        <div class="grid-2 gap-4">
                            <div>
                                <h4 style="margin-bottom: 1rem; color: #D2691E;">
                                    <i class="fas fa-tachometer-alt"></i>
                                    مؤشرات الأداء الرئيسية
                                </h4>
                                <canvas id="performanceRadarChart" width="400" height="300"></canvas>
                            </div>
                            <div>
                                <h4 style="margin-bottom: 1rem; color: #D2691E;">
                                    <i class="fas fa-trophy"></i>
                                    تقييم اللاعبين
                                </h4>
                                <div id="playerRatings">
                                    <!-- Player ratings will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Comparison Tab -->
                    <div id="comparisonTab" class="analysis-tab-content" style="display: none;">
                        <div class="grid-2 gap-4">
                            <div>
                                <h4 style="margin-bottom: 1rem; color: #D2691E;">
                                    <i class="fas fa-chart-bar"></i>
                                    مقارنة المباريات
                                </h4>
                                <canvas id="matchComparisonChart" width="400" height="300"></canvas>
                            </div>
                            <div>
                                <h4 style="margin-bottom: 1rem; color: #D2691E;">
                                    <i class="fas fa-trend-up"></i>
                                    اتجاهات الأداء
                                </h4>
                                <div id="performanceTrends">
                                    <!-- Performance trends will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="approvalsTab" class="tab-content">
            <!-- Approval Controls -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-check-circle"></i> نظام الموافقات الآلي</h3>
                    <div style="display: flex; gap: 1rem;">
                        <button class="btn btn-success" onclick="sendSmartBulkApprovals()">
                            <i class="fas fa-brain"></i>
                            اختيار ذكي وإرسال
                        </button>
                        <button class="btn btn-secondary" onclick="sendBulkApprovals()">
                            <i class="fas fa-paper-plane"></i>
                            إرسال للتشكيلة الحالية
                        </button>
                        <button class="btn btn-secondary" onclick="checkApprovalStatus()">
                            <i class="fas fa-sync"></i>
                            تحديث الحالة
                        </button>
                    </div>
                </div>

                <!-- Approval Statistics -->
                <div class="grid-4" style="margin-bottom: 2rem;">
                    <div class="stat-box" style="background: rgba(16, 185, 129, 0.1); border: 1px solid #10B981; border-radius: 10px; padding: 1rem; text-align: center;">
                        <div style="font-size: 2rem; font-weight: bold; color: #10B981;" id="approvedCount">0</div>
                        <div style="font-size: 0.9rem; opacity: 0.8;">موافقات مؤكدة</div>
                    </div>
                    <div class="stat-box" style="background: rgba(245, 158, 11, 0.1); border: 1px solid #F59E0B; border-radius: 10px; padding: 1rem; text-align: center;">
                        <div style="font-size: 2rem; font-weight: bold; color: #F59E0B;" id="pendingCount">0</div>
                        <div style="font-size: 0.9rem; opacity: 0.8;">في الانتظار</div>
                    </div>
                    <div class="stat-box" style="background: rgba(239, 68, 68, 0.1); border: 1px solid #EF4444; border-radius: 10px; padding: 1rem; text-align: center;">
                        <div style="font-size: 2rem; font-weight: bold; color: #EF4444;" id="rejectedCount">0</div>
                        <div style="font-size: 0.9rem; opacity: 0.8;">مرفوضة</div>
                    </div>
                    <div class="stat-box" style="background: rgba(107, 114, 128, 0.1); border: 1px solid #6B7280; border-radius: 10px; padding: 1rem; text-align: center;">
                        <div style="font-size: 2rem; font-weight: bold; color: #6B7280;" id="expiredCount">0</div>
                        <div style="font-size: 0.9rem; opacity: 0.8;">منتهية الصلاحية</div>
                    </div>
                </div>

                <!-- Match Selection for Approvals -->
                <div style="margin-bottom: 2rem;">
                    <h4 style="margin-bottom: 1rem; color: #D2691E;">
                        <i class="fas fa-futbol"></i>
                        اختيار المباراة لإرسال الموافقات
                    </h4>
                    <select id="matchForApproval" style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                        <option value="">اختر المباراة...</option>
                    </select>
                </div>

                <!-- Selected Players for Match -->
                <div id="selectedPlayersSection" style="display: none;">
                    <h4 style="margin-bottom: 1rem; color: #D2691E;">
                        <i class="fas fa-users"></i>
                        اللاعبون المختارون للمباراة
                    </h4>
                    <div id="selectedPlayersList" class="grid-3 gap-4">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Approval Requests List -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-list"></i> طلبات الموافقة</h3>
                    <div style="display: flex; gap: 1rem;">
                        <select id="approvalFilter" onchange="filterApprovals()" style="padding: 0.5rem; border-radius: 5px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                            <option value="all">جميع الطلبات</option>
                            <option value="pending">في الانتظار</option>
                            <option value="approved">موافق عليها</option>
                            <option value="rejected">مرفوضة</option>
                            <option value="expired">منتهية الصلاحية</option>
                        </select>
                    </div>
                </div>
                <div id="approvalRequestsList">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>

            <!-- WhatsApp Template Preview -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fab fa-whatsapp"></i> معاينة رسالة واتساب</h3>
                </div>
                <div style="background: rgba(37, 211, 102, 0.1); border: 1px solid #25D366; border-radius: 10px; padding: 1.5rem;">
                    <div style="background: white; color: black; padding: 1rem; border-radius: 8px; font-family: Arial, sans-serif; direction: rtl;">
                        <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem; color: #8B4513; font-weight: bold;">
                            <i class="fas fa-futbol"></i>
                            أكاديمية 7C للتدريب الرياضي
                        </div>
                        <div id="whatsappPreview">
                            <p><strong>السلام عليكم ورحمة الله وبركاته</strong></p>
                            <p>نود إعلامكم بأن نجلكم/ابنتكم <strong>[اسم اللاعب]</strong> تم اختياره للمشاركة في المباراة التالية:</p>

                            <div style="background: #f5f5f5; padding: 1rem; border-radius: 5px; margin: 1rem 0;">
                                <p><strong>📅 التاريخ:</strong> [تاريخ المباراة]</p>
                                <p><strong>⏰ الوقت:</strong> [وقت المباراة]</p>
                                <p><strong>📍 المكان:</strong> [مكان المباراة]</p>
                                <p><strong>🆚 المنافس:</strong> [الفريق المنافس]</p>
                                <p><strong>🏆 نوع المباراة:</strong> [نوع المباراة]</p>
                            </div>

                            <p>يرجى الرد بـ:</p>
                            <p>✅ <strong>موافق</strong> للموافقة على المشاركة</p>
                            <p>❌ <strong>غير موافق</strong> في حالة عدم القدرة على المشاركة</p>

                            <p style="color: #e74c3c;"><strong>⚠️ مهلة الرد: 24 ساعة من وقت إرسال هذه الرسالة</strong></p>

                            <p style="font-size: 0.9rem; color: #666;">شكراً لتعاونكم معنا في تطوير مهارات أبنائكم</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global Variables
        let matches = [];
        let tournaments = [];
        let formations = [];
        let currentMatch = null;
        let currentFormation = '4-4-2';
        let playersOnField = [];
        let availablePlayers = [];
        let draggedPlayer = null;
        let approvalRequests = [];
        let parentContacts = {};
        let liveMatch = null;
        let matchEvents = [];
        let matchTimer = null;
        let matchStartTime = null;
        let isLiveTracking = false;
        let playerStats = {};
        let teamStats = {};

        // Sample Data
        const sampleMatches = [
            {
                id: 1,
                homeTeam: 'أكاديمية 7C - تحت 16',
                awayTeam: 'نادي الشباب - تحت 16',
                date: '2024-12-25',
                time: '16:00',
                venue: 'ملعب الأكاديمية الرئيسي',
                type: 'ودية',
                category: 'تحت 16',
                status: 'scheduled',
                homeScore: null,
                awayScore: null
            },
            {
                id: 2,
                homeTeam: 'أكاديمية 7C - تحت 14',
                awayTeam: 'أكاديمية النصر - تحت 14',
                date: '2024-12-22',
                time: '17:30',
                venue: 'ملعب النصر',
                type: 'رسمية',
                category: 'تحت 14',
                status: 'finished',
                homeScore: 2,
                awayScore: 1
            }
        ];

        // Sample Players Data
        const samplePlayers = [
            { id: 1, name: 'أحمد محمد علي', number: '1', position: 'حارس مرمى', category: 'تحت 16', rating: 85, jerseyNumber: '1' },
            { id: 2, name: 'محمد أحمد سالم', number: '2', position: 'مدافع أيمن', category: 'تحت 16', rating: 78, jerseyNumber: '2' },
            { id: 3, name: 'سالم عبدالله محمد', number: '3', position: 'مدافع أيسر', category: 'تحت 16', rating: 76, jerseyNumber: '3' },
            { id: 4, name: 'عبدالله سالم أحمد', number: '4', position: 'مدافع وسط', category: 'تحت 16', rating: 80, jerseyNumber: '4' },
            { id: 5, name: 'خالد محمد عبدالله', number: '5', position: 'مدافع وسط', category: 'تحت 16', rating: 79, jerseyNumber: '5' },
            { id: 6, name: 'فهد أحمد خالد', number: '6', position: 'وسط دفاعي', category: 'تحت 16', rating: 82, jerseyNumber: '6' },
            { id: 7, name: 'عمر سالم فهد', number: '7', position: 'وسط أيمن', category: 'تحت 16', rating: 84, jerseyNumber: '7' },
            { id: 8, name: 'يوسف عمر محمد', number: '8', position: 'وسط مهاجم', category: 'تحت 16', rating: 86, jerseyNumber: '8' },
            { id: 9, name: 'ناصر يوسف عمر', number: '9', position: 'مهاجم', category: 'تحت 16', rating: 88, jerseyNumber: '9' },
            { id: 10, name: 'بندر ناصر يوسف', number: '10', position: 'مهاجم', category: 'تحت 16', rating: 90, jerseyNumber: '10' },
            { id: 11, name: 'تركي بندر ناصر', number: '11', position: 'وسط أيسر', category: 'تحت 16', rating: 83, jerseyNumber: '11' },
            { id: 12, name: 'راشد تركي بندر', number: '12', position: 'مدافع', category: 'تحت 14', rating: 75, jerseyNumber: '12' },
            { id: 13, name: 'سعد راشد تركي', number: '13', position: 'وسط', category: 'تحت 14', rating: 77, jerseyNumber: '13' },
            { id: 14, name: 'مشعل سعد راشد', number: '14', position: 'مهاجم', category: 'تحت 14', rating: 81, jerseyNumber: '14' },
            { id: 15, name: 'عبدالعزيز مشعل سعد', number: '15', position: 'حارس مرمى', category: 'تحت 14', rating: 73, jerseyNumber: '15' }
        ];

        // Formation Templates
        const formationTemplates = {
            '4-4-2': {
                name: '4-4-2',
                positions: [
                    { id: 1, role: 'GK', x: 50, y: 5 },
                    { id: 2, role: 'RB', x: 85, y: 20 },
                    { id: 3, role: 'CB', x: 65, y: 20 },
                    { id: 4, role: 'CB', x: 35, y: 20 },
                    { id: 5, role: 'LB', x: 15, y: 20 },
                    { id: 6, role: 'RM', x: 85, y: 45 },
                    { id: 7, role: 'CM', x: 65, y: 45 },
                    { id: 8, role: 'CM', x: 35, y: 45 },
                    { id: 9, role: 'LM', x: 15, y: 45 },
                    { id: 10, role: 'ST', x: 65, y: 70 },
                    { id: 11, role: 'ST', x: 35, y: 70 }
                ]
            },
            '4-3-3': {
                name: '4-3-3',
                positions: [
                    { id: 1, role: 'GK', x: 50, y: 5 },
                    { id: 2, role: 'RB', x: 85, y: 20 },
                    { id: 3, role: 'CB', x: 65, y: 20 },
                    { id: 4, role: 'CB', x: 35, y: 20 },
                    { id: 5, role: 'LB', x: 15, y: 20 },
                    { id: 6, role: 'CM', x: 75, y: 45 },
                    { id: 7, role: 'CM', x: 50, y: 45 },
                    { id: 8, role: 'CM', x: 25, y: 45 },
                    { id: 9, role: 'RW', x: 80, y: 70 },
                    { id: 10, role: 'ST', x: 50, y: 70 },
                    { id: 11, role: 'LW', x: 20, y: 70 }
                ]
            },
            '3-5-2': {
                name: '3-5-2',
                positions: [
                    { id: 1, role: 'GK', x: 50, y: 5 },
                    { id: 2, role: 'CB', x: 75, y: 20 },
                    { id: 3, role: 'CB', x: 50, y: 20 },
                    { id: 4, role: 'CB', x: 25, y: 20 },
                    { id: 5, role: 'RWB', x: 90, y: 40 },
                    { id: 6, role: 'CM', x: 70, y: 45 },
                    { id: 7, role: 'CM', x: 50, y: 45 },
                    { id: 8, role: 'CM', x: 30, y: 45 },
                    { id: 9, role: 'LWB', x: 10, y: 40 },
                    { id: 10, role: 'ST', x: 65, y: 70 },
                    { id: 11, role: 'ST', x: 35, y: 70 }
                ]
            }
        };

        // Parent Contacts Data
        const parentContactsData = {
            1: { name: 'محمد علي أحمد', phone: '+966501234567', email: '<EMAIL>', whatsapp: '+966501234567' },
            2: { name: 'أحمد سالم محمد', phone: '+966502345678', email: '<EMAIL>', whatsapp: '+966502345678' },
            3: { name: 'سالم محمد عبدالله', phone: '+966503456789', email: '<EMAIL>', whatsapp: '+966503456789' },
            4: { name: 'عبدالله أحمد سالم', phone: '+966504567890', email: '<EMAIL>', whatsapp: '+966504567890' },
            5: { name: 'محمد عبدالله خالد', phone: '+966505678901', email: '<EMAIL>', whatsapp: '+966505678901' },
            6: { name: 'أحمد خالد فهد', phone: '+966506789012', email: '<EMAIL>', whatsapp: '+966506789012' },
            7: { name: 'سالم فهد عمر', phone: '+966507890123', email: '<EMAIL>', whatsapp: '+966507890123' },
            8: { name: 'عمر محمد يوسف', phone: '+966508901234', email: '<EMAIL>', whatsapp: '+966508901234' },
            9: { name: 'يوسف عمر ناصر', phone: '+966509012345', email: '<EMAIL>', whatsapp: '+966509012345' },
            10: { name: 'ناصر يوسف بندر', phone: '+966500123456', email: '<EMAIL>', whatsapp: '+966500123456' },
            11: { name: 'بندر ناصر تركي', phone: '+966501234567', email: '<EMAIL>', whatsapp: '+966501234567' },
            12: { name: 'تركي بندر راشد', phone: '+966502345678', email: '<EMAIL>', whatsapp: '+966502345678' },
            13: { name: 'راشد تركي سعد', phone: '+966503456789', email: '<EMAIL>', whatsapp: '+966503456789' },
            14: { name: 'سعد راشد مشعل', phone: '+966504567890', email: '<EMAIL>', whatsapp: '+966504567890' },
            15: { name: 'مشعل سعد عبدالعزيز', phone: '+966505678901', email: '<EMAIL>', whatsapp: '+966505678901' }
        };

        // Initialize System
        document.addEventListener('DOMContentLoaded', function() {
            loadMatches();
            updateMatchesDisplay();
            initializeFormationSystem();
            initializeApprovalSystem();
            initializeStatisticsSystem();
        });

        // Initialize Statistics System
        function initializeStatisticsSystem() {
            loadStatisticsData();
            updateLiveMatchSelect();
            updateAnalysisMatchSelect();
            initializePlayerStats();
            initializeTeamStats();
        }

        // Load Statistics Data
        function loadStatisticsData() {
            const savedEvents = localStorage.getItem('academy_match_events');
            if (savedEvents) {
                matchEvents = JSON.parse(savedEvents);
            }

            const savedPlayerStats = localStorage.getItem('academy_player_stats');
            if (savedPlayerStats) {
                playerStats = JSON.parse(savedPlayerStats);
            }

            const savedTeamStats = localStorage.getItem('academy_team_stats');
            if (savedTeamStats) {
                teamStats = JSON.parse(savedTeamStats);
            }
        }

        // Save Statistics Data
        function saveStatisticsData() {
            localStorage.setItem('academy_match_events', JSON.stringify(matchEvents));
            localStorage.setItem('academy_player_stats', JSON.stringify(playerStats));
            localStorage.setItem('academy_team_stats', JSON.stringify(teamStats));
        }

        // Initialize Player Stats
        function initializePlayerStats() {
            samplePlayers.forEach(player => {
                if (!playerStats[player.id]) {
                    playerStats[player.id] = {
                        playerId: player.id,
                        playerName: player.name,
                        matches: 0,
                        goals: 0,
                        assists: 0,
                        yellowCards: 0,
                        redCards: 0,
                        minutesPlayed: 0,
                        passes: 0,
                        passAccuracy: 0,
                        tackles: 0,
                        interceptions: 0,
                        shots: 0,
                        shotsOnTarget: 0,
                        fouls: 0,
                        rating: 0,
                        heatmapData: []
                    };
                }
            });
        }

        // Initialize Team Stats
        function initializeTeamStats() {
            if (!teamStats.overall) {
                teamStats = {
                    overall: {
                        matches: 0,
                        wins: 0,
                        draws: 0,
                        losses: 0,
                        goalsFor: 0,
                        goalsAgainst: 0,
                        possession: 0,
                        passAccuracy: 0,
                        shots: 0,
                        shotsOnTarget: 0,
                        corners: 0,
                        fouls: 0,
                        yellowCards: 0,
                        redCards: 0
                    },
                    byMatch: {}
                };
            }
        }

        // Update Live Match Select
        function updateLiveMatchSelect() {
            const select = document.getElementById('liveMatchSelect');
            const upcomingMatches = matches.filter(match =>
                match.status === 'scheduled' && new Date(match.date) >= new Date()
            );

            select.innerHTML = '<option value="">اختر المباراة...</option>' +
                upcomingMatches.map(match =>
                    `<option value="${match.id}">${match.homeTeam} vs ${match.awayTeam} - ${formatDate(match.date)}</option>`
                ).join('');
        }

        // Update Analysis Match Select
        function updateAnalysisMatchSelect() {
            const select = document.getElementById('analysisMatchSelect');
            const finishedMatches = matches.filter(match => match.status === 'finished');

            select.innerHTML = '<option value="">اختر المباراة للتحليل...</option>' +
                finishedMatches.map(match =>
                    `<option value="${match.id}">${match.homeTeam} vs ${match.awayTeam} - ${formatDate(match.date)}</option>`
                ).join('');
        }

        // Select Live Match
        function selectLiveMatch() {
            const matchId = parseInt(document.getElementById('liveMatchSelect').value);
            if (matchId) {
                liveMatch = matches.find(m => m.id === matchId);
                if (liveMatch) {
                    document.getElementById('liveMatchContent').style.display = 'block';
                    initializeLiveMatch();
                }
            } else {
                document.getElementById('liveMatchContent').style.display = 'none';
                liveMatch = null;
            }
        }

        // Initialize Live Match
        function initializeLiveMatch() {
            if (!liveMatch) return;

            // Initialize match stats for this match
            if (!teamStats.byMatch[liveMatch.id]) {
                teamStats.byMatch[liveMatch.id] = {
                    matchId: liveMatch.id,
                    homeScore: 0,
                    awayScore: 0,
                    possession: 50,
                    passes: 0,
                    passAccuracy: 0,
                    shots: 0,
                    shotsOnTarget: 0,
                    corners: 0,
                    fouls: 0,
                    yellowCards: 0,
                    redCards: 0,
                    events: []
                };
            }

            updateLiveMatchDisplay();
            updateTeamStatsDisplay();
            updatePlayerPerformanceDisplay();
            updateEventsTimeline();
        }

        // Start Live Tracking
        function startLiveTracking() {
            if (!liveMatch) {
                showNotification('يرجى اختيار المباراة أولاً', 'warning');
                return;
            }

            isLiveTracking = true;
            matchStartTime = new Date();

            // Start timer
            matchTimer = setInterval(updateMatchTimer, 1000);

            // Update match status
            liveMatch.status = 'live';
            saveMatches();

            showNotification('تم بدء التتبع المباشر للمباراة', 'success');

            // Add live indicator
            const header = document.querySelector('#statisticsTab .card-header h3');
            if (!header.querySelector('.live-indicator')) {
                const liveIndicator = document.createElement('span');
                liveIndicator.className = 'live-indicator';
                liveIndicator.innerHTML = '<i class="fas fa-circle"></i> مباشر';
                header.appendChild(liveIndicator);
            }
        }

        // Stop Live Tracking
        function stopLiveTracking() {
            if (!isLiveTracking) {
                showNotification('لا يوجد تتبع مباشر نشط', 'info');
                return;
            }

            isLiveTracking = false;

            if (matchTimer) {
                clearInterval(matchTimer);
                matchTimer = null;
            }

            // Update match status
            if (liveMatch) {
                liveMatch.status = 'finished';
                liveMatch.homeScore = teamStats.byMatch[liveMatch.id].homeScore;
                liveMatch.awayScore = teamStats.byMatch[liveMatch.id].awayScore;
                saveMatches();
            }

            // Remove live indicator
            const liveIndicator = document.querySelector('.live-indicator');
            if (liveIndicator) {
                liveIndicator.remove();
            }

            showNotification('تم إيقاف التتبع المباشر', 'info');

            // Save all statistics
            saveStatisticsData();

            // Update displays
            updateMatchesDisplay();
            updateAnalysisMatchSelect();
        }

        // Update Match Timer
        function updateMatchTimer() {
            if (!matchStartTime || !isLiveTracking) return;

            const now = new Date();
            const elapsed = Math.floor((now - matchStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;

            const timerDisplay = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('matchTimer').textContent = timerDisplay;
        }

        // Record Event
        function recordEvent(eventType) {
            if (!liveMatch || !isLiveTracking) {
                showNotification('يجب بدء التتبع المباشر أولاً', 'warning');
                return;
            }

            const currentTime = Math.floor((new Date() - matchStartTime) / 1000 / 60); // minutes

            // Show event recording modal
            showEventRecordingModal(eventType, currentTime);
        }

        // Show Event Recording Modal
        function showEventRecordingModal(eventType, currentTime) {
            const eventNames = {
                'goal': 'هدف',
                'yellow_card': 'بطاقة صفراء',
                'red_card': 'بطاقة حمراء',
                'substitution': 'تبديل',
                'corner': 'ركنية',
                'free_kick': 'ركلة حرة',
                'offside': 'تسلل',
                'foul': 'خطأ'
            };

            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 500px;">
                    <div class="modal-header">
                        <h3><i class="fas fa-plus-circle"></i> تسجيل حدث: ${eventNames[eventType]}</h3>
                        <button class="modal-close" onclick="closeModal(this)">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>الدقيقة:</label>
                            <input type="number" id="eventMinute" value="${currentTime}" min="0" max="120" style="width: 100%; padding: 0.5rem; border-radius: 5px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                        </div>

                        ${eventType === 'goal' || eventType === 'yellow_card' || eventType === 'red_card' || eventType === 'substitution' ? `
                            <div class="form-group">
                                <label>اللاعب:</label>
                                <select id="eventPlayer" style="width: 100%; padding: 0.5rem; border-radius: 5px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                    <option value="">اختر اللاعب...</option>
                                    ${playersOnField.map(player => `<option value="${player.id}">${player.name}</option>`).join('')}
                                </select>
                            </div>
                        ` : ''}

                        ${eventType === 'goal' ? `
                            <div class="form-group">
                                <label>نوع الهدف:</label>
                                <select id="goalType" style="width: 100%; padding: 0.5rem; border-radius: 5px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                    <option value="normal">هدف عادي</option>
                                    <option value="penalty">ركلة جزاء</option>
                                    <option value="free_kick">ركلة حرة</option>
                                    <option value="header">ضربة رأس</option>
                                    <option value="own_goal">هدف في المرمى</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>اللاعب المساعد (اختياري):</label>
                                <select id="assistPlayer" style="width: 100%; padding: 0.5rem; border-radius: 5px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                    <option value="">لا يوجد</option>
                                    ${playersOnField.map(player => `<option value="${player.id}">${player.name}</option>`).join('')}
                                </select>
                            </div>
                        ` : ''}

                        <div class="form-group">
                            <label>ملاحظات (اختياري):</label>
                            <textarea id="eventNotes" placeholder="أضف ملاحظات..." style="width: 100%; height: 80px; padding: 0.5rem; border-radius: 5px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3); resize: vertical;"></textarea>
                        </div>

                        <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 1.5rem;">
                            <button class="btn btn-secondary" onclick="closeModal(this)">إلغاء</button>
                            <button class="btn btn-success" onclick="saveEvent('${eventType}')">حفظ الحدث</button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // Save Event
        function saveEvent(eventType) {
            const minute = parseInt(document.getElementById('eventMinute').value);
            const playerId = document.getElementById('eventPlayer')?.value;
            const notes = document.getElementById('eventNotes').value;

            if (isNaN(minute) || minute < 0) {
                showNotification('يرجى إدخال دقيقة صحيحة', 'warning');
                return;
            }

            const event = {
                id: Date.now(),
                matchId: liveMatch.id,
                type: eventType,
                minute: minute,
                timestamp: new Date().toISOString(),
                playerId: playerId || null,
                playerName: playerId ? samplePlayers.find(p => p.id == playerId)?.name : null,
                notes: notes
            };

            // Add specific event data
            if (eventType === 'goal') {
                event.goalType = document.getElementById('goalType').value;
                event.assistPlayerId = document.getElementById('assistPlayer').value || null;
                event.assistPlayerName = event.assistPlayerId ?
                    samplePlayers.find(p => p.id == event.assistPlayerId)?.name : null;

                // Update score
                teamStats.byMatch[liveMatch.id].homeScore++;
                teamStats.overall.goalsFor++;

                // Update player stats
                if (playerId) {
                    playerStats[playerId].goals++;
                }
                if (event.assistPlayerId) {
                    playerStats[event.assistPlayerId].assists++;
                }
            } else if (eventType === 'yellow_card') {
                teamStats.byMatch[liveMatch.id].yellowCards++;
                teamStats.overall.yellowCards++;
                if (playerId) {
                    playerStats[playerId].yellowCards++;
                }
            } else if (eventType === 'red_card') {
                teamStats.byMatch[liveMatch.id].redCards++;
                teamStats.overall.redCards++;
                if (playerId) {
                    playerStats[playerId].redCards++;
                }
            } else if (eventType === 'corner') {
                teamStats.byMatch[liveMatch.id].corners++;
                teamStats.overall.corners++;
            } else if (eventType === 'foul') {
                teamStats.byMatch[liveMatch.id].fouls++;
                teamStats.overall.fouls++;
                if (playerId) {
                    playerStats[playerId].fouls++;
                }
            }

            // Add to events array
            matchEvents.push(event);
            teamStats.byMatch[liveMatch.id].events.push(event);

            // Update displays
            updateLiveMatchDisplay();
            updateTeamStatsDisplay();
            updatePlayerPerformanceDisplay();
            updateEventsTimeline();

            // Save data
            saveStatisticsData();

            closeModal();
            showNotification(`تم تسجيل ${getEventName(eventType)} بنجاح`, 'success');
        }

        // Get Event Name
        function getEventName(eventType) {
            const eventNames = {
                'goal': 'الهدف',
                'yellow_card': 'البطاقة الصفراء',
                'red_card': 'البطاقة الحمراء',
                'substitution': 'التبديل',
                'corner': 'الركنية',
                'free_kick': 'الركلة الحرة',
                'offside': 'التسلل',
                'foul': 'الخطأ'
            };
            return eventNames[eventType] || eventType;
        }

        // Update Live Match Display
        function updateLiveMatchDisplay() {
            if (!liveMatch || !teamStats.byMatch[liveMatch.id]) return;

            const matchStats = teamStats.byMatch[liveMatch.id];

            // Update score
            document.getElementById('currentScore').textContent =
                `${matchStats.homeScore} - ${matchStats.awayScore}`;

            // Update total events
            document.getElementById('totalEvents').textContent = matchStats.events.length;
        }

        // Update Team Stats Display
        function updateTeamStatsDisplay() {
            if (!liveMatch || !teamStats.byMatch[liveMatch.id]) return;

            const matchStats = teamStats.byMatch[liveMatch.id];
            const container = document.getElementById('teamStatsContainer');

            container.innerHTML = `
                <div class="performance-metric">
                    <span class="metric-name">الاستحواذ</span>
                    <span class="metric-value">${matchStats.possession}%</span>
                </div>
                <div class="performance-metric">
                    <span class="metric-name">التمريرات</span>
                    <span class="metric-value">${matchStats.passes}</span>
                </div>
                <div class="performance-metric">
                    <span class="metric-name">دقة التمرير</span>
                    <span class="metric-value">${matchStats.passAccuracy}%</span>
                </div>
                <div class="performance-metric">
                    <span class="metric-name">التسديدات</span>
                    <span class="metric-value">${matchStats.shots}</span>
                </div>
                <div class="performance-metric">
                    <span class="metric-name">التسديدات على المرمى</span>
                    <span class="metric-value">${matchStats.shotsOnTarget}</span>
                </div>
                <div class="performance-metric">
                    <span class="metric-name">الركنيات</span>
                    <span class="metric-value">${matchStats.corners}</span>
                </div>
                <div class="performance-metric">
                    <span class="metric-name">الأخطاء</span>
                    <span class="metric-value">${matchStats.fouls}</span>
                </div>
                <div class="performance-metric">
                    <span class="metric-name">البطاقات الصفراء</span>
                    <span class="metric-value">${matchStats.yellowCards}</span>
                </div>
                <div class="performance-metric">
                    <span class="metric-name">البطاقات الحمراء</span>
                    <span class="metric-value">${matchStats.redCards}</span>
                </div>
            `;
        }

        // Update Player Performance Display
        function updatePlayerPerformanceDisplay() {
            const container = document.getElementById('playerPerformanceContainer');

            if (playersOnField.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-400">لم يتم اختيار تشكيلة للمباراة</p>';
                return;
            }

            container.innerHTML = playersOnField.map(player => {
                const stats = playerStats[player.id];
                const rating = calculatePlayerRating(player.id);
                const ratingClass = getRatingClass(rating);

                return `
                    <div class="player-rating-card">
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <div class="player-avatar">${player.jerseyNumber}</div>
                            <div>
                                <div style="font-weight: bold;">${player.name}</div>
                                <div style="font-size: 0.8rem; opacity: 0.8;">${player.position}</div>
                            </div>
                        </div>
                        <div style="text-align: right;">
                            <div class="rating-badge ${ratingClass}">${rating}</div>
                            <div style="font-size: 0.8rem; opacity: 0.7; margin-top: 0.25rem;">
                                أهداف: ${stats.goals} | تمريرات: ${stats.passes}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Calculate Player Rating
        function calculatePlayerRating(playerId) {
            const stats = playerStats[playerId];
            if (!stats) return 6.0;

            let rating = 6.0; // Base rating

            // Goals bonus
            rating += stats.goals * 0.5;

            // Assists bonus
            rating += stats.assists * 0.3;

            // Cards penalty
            rating -= stats.yellowCards * 0.2;
            rating -= stats.redCards * 1.0;

            // Fouls penalty
            rating -= stats.fouls * 0.1;

            // Ensure rating is between 1 and 10
            return Math.max(1.0, Math.min(10.0, rating)).toFixed(1);
        }

        // Get Rating Class
        function getRatingClass(rating) {
            const numRating = parseFloat(rating);
            if (numRating >= 8.0) return 'rating-excellent';
            if (numRating >= 7.0) return 'rating-good';
            if (numRating >= 6.0) return 'rating-average';
            return 'rating-poor';
        }

        // Update Events Timeline
        function updateEventsTimeline() {
            if (!liveMatch || !teamStats.byMatch[liveMatch.id]) return;

            const container = document.getElementById('eventsTimeline');
            const events = teamStats.byMatch[liveMatch.id].events.sort((a, b) => b.minute - a.minute);

            if (events.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-400">لا توجد أحداث مسجلة</p>';
                return;
            }

            container.innerHTML = events.map(event => {
                const eventIcon = getEventIcon(event.type);
                const eventDescription = getEventDescription(event);

                return `
                    <div class="event-item ${event.type}">
                        <div class="event-time">${event.minute}'</div>
                        <div class="event-icon">${eventIcon}</div>
                        <div class="event-description">
                            <div style="font-weight: bold;">${getEventName(event.type)}</div>
                            <div style="font-size: 0.9rem; opacity: 0.8;">${eventDescription}</div>
                            ${event.notes ? `<div style="font-size: 0.8rem; opacity: 0.7; margin-top: 0.25rem;">${event.notes}</div>` : ''}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Get Event Icon
        function getEventIcon(eventType) {
            const icons = {
                'goal': '<i class="fas fa-futbol" style="color: #10B981;"></i>',
                'yellow_card': '<i class="fas fa-square" style="color: #F59E0B;"></i>',
                'red_card': '<i class="fas fa-square" style="color: #EF4444;"></i>',
                'substitution': '<i class="fas fa-exchange-alt" style="color: #3B82F6;"></i>',
                'corner': '<i class="fas fa-flag" style="color: #8B4513;"></i>',
                'free_kick': '<i class="fas fa-running" style="color: #8B4513;"></i>',
                'offside': '<i class="fas fa-hand-paper" style="color: #F59E0B;"></i>',
                'foul': '<i class="fas fa-exclamation-triangle" style="color: #EF4444;"></i>'
            };
            return icons[eventType] || '<i class="fas fa-circle"></i>';
        }

        // Get Event Description
        function getEventDescription(event) {
            let description = '';

            if (event.playerName) {
                description += event.playerName;
            }

            if (event.type === 'goal') {
                if (event.goalType && event.goalType !== 'normal') {
                    const goalTypes = {
                        'penalty': 'ركلة جزاء',
                        'free_kick': 'ركلة حرة',
                        'header': 'ضربة رأس',
                        'own_goal': 'هدف في المرمى'
                    };
                    description += ` (${goalTypes[event.goalType]})`;
                }

                if (event.assistPlayerName) {
                    description += ` - تمريرة: ${event.assistPlayerName}`;
                }
            }

            return description || 'حدث في المباراة';
        }

        // ==================== AI Analysis System ====================

        // Show Analysis Tab
        function showAnalysisTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.analysis-tab-content').forEach(tab => {
                tab.style.display = 'none';
            });

            // Remove active class from all buttons
            document.querySelectorAll('.analysis-tab').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName + 'Tab').style.display = 'block';

            // Add active class to clicked button
            document.getElementById(tabName + 'TabBtn').classList.add('active');

            // Load tab content
            switch(tabName) {
                case 'heatmap':
                    generateHeatmap();
                    break;
                case 'tactical':
                    generateTacticalAnalysis();
                    break;
                case 'performance':
                    generatePerformanceAnalysis();
                    break;
                case 'comparison':
                    generateComparisonAnalysis();
                    break;
            }
        }

        // Load Match Analysis
        function loadMatchAnalysis() {
            const matchId = parseInt(document.getElementById('analysisMatchSelect').value);
            if (matchId) {
                const match = matches.find(m => m.id === matchId);
                if (match && teamStats.byMatch[matchId]) {
                    showAnalysisTab('heatmap'); // Default to heatmap
                }
            }
        }

        // Generate Heatmap
        function generateHeatmap() {
            const matchId = parseInt(document.getElementById('analysisMatchSelect').value);
            if (!matchId || !teamStats.byMatch[matchId]) {
                document.getElementById('playerHeatmap').innerHTML = '<p class="text-center text-gray-400">اختر مباراة للتحليل</p>';
                return;
            }

            const container = document.getElementById('playerHeatmap');
            container.innerHTML = '';

            // Create field background
            const field = document.createElement('div');
            field.className = 'heatmap-container';

            // Generate random heatmap data for demo
            const heatPoints = generateHeatmapData(matchId);

            heatPoints.forEach(point => {
                const heatPoint = document.createElement('div');
                heatPoint.className = `heat-point ${point.intensity}`;
                heatPoint.style.left = `${point.x}%`;
                heatPoint.style.top = `${point.y}%`;
                heatPoint.title = `نشاط: ${point.activity}`;
                field.appendChild(heatPoint);
            });

            container.appendChild(field);

            // Update zone statistics
            updateZoneStatistics(heatPoints);
        }

        // Generate Heatmap Data
        function generateHeatmapData(matchId) {
            const points = [];
            const zones = [
                { name: 'منطقة الدفاع', x: [10, 40], y: [10, 30] },
                { name: 'منطقة الوسط', x: [20, 80], y: [30, 70] },
                { name: 'منطقة الهجوم', x: [60, 90], y: [70, 90] }
            ];

            zones.forEach(zone => {
                const pointCount = Math.floor(Math.random() * 8) + 3; // 3-10 points per zone

                for (let i = 0; i < pointCount; i++) {
                    const x = Math.random() * (zone.x[1] - zone.x[0]) + zone.x[0];
                    const y = Math.random() * (zone.y[1] - zone.y[0]) + zone.y[0];
                    const activity = Math.floor(Math.random() * 100) + 1;

                    let intensity = 'low';
                    if (activity > 70) intensity = 'high';
                    else if (activity > 40) intensity = 'medium';

                    points.push({
                        x: x,
                        y: y,
                        activity: activity,
                        intensity: intensity,
                        zone: zone.name
                    });
                }
            });

            return points;
        }

        // Update Zone Statistics
        function updateZoneStatistics(heatPoints) {
            const container = document.getElementById('zoneStatistics');
            const zones = {};

            // Group points by zone
            heatPoints.forEach(point => {
                if (!zones[point.zone]) {
                    zones[point.zone] = {
                        name: point.zone,
                        totalActivity: 0,
                        pointCount: 0,
                        highIntensity: 0
                    };
                }

                zones[point.zone].totalActivity += point.activity;
                zones[point.zone].pointCount++;
                if (point.intensity === 'high') {
                    zones[point.zone].highIntensity++;
                }
            });

            container.innerHTML = Object.values(zones).map(zone => {
                const avgActivity = Math.round(zone.totalActivity / zone.pointCount);
                const intensityPercentage = Math.round((zone.highIntensity / zone.pointCount) * 100);

                return `
                    <div class="zone-stat">
                        <div>
                            <div class="zone-name">${zone.name}</div>
                            <div style="font-size: 0.8rem; opacity: 0.7;">نشاط عالي: ${intensityPercentage}%</div>
                        </div>
                        <div class="zone-value">${avgActivity}%</div>
                    </div>
                `;
            }).join('');
        }

        // Generate Tactical Analysis
        function generateTacticalAnalysis() {
            const matchId = parseInt(document.getElementById('analysisMatchSelect').value);
            if (!matchId || !teamStats.byMatch[matchId]) {
                document.getElementById('tacticalInsights').innerHTML = '<p class="text-center text-gray-400">اختر مباراة للتحليل</p>';
                return;
            }

            // Generate passing network chart
            generatePassingNetworkChart(matchId);

            // Generate tactical insights
            generateTacticalInsights(matchId);
        }

        // Generate Passing Network Chart
        function generatePassingNetworkChart(matchId) {
            const ctx = document.getElementById('passingNetworkChart');
            if (!ctx) return;

            // Simulate passing data
            const passingData = generatePassingData();

            new Chart(ctx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'شبكة التمريرات',
                        data: passingData.nodes,
                        backgroundColor: 'rgba(139, 69, 19, 0.8)',
                        borderColor: '#8B4513',
                        borderWidth: 2,
                        pointRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: 'white' }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const player = passingData.players[context.dataIndex];
                                    return `${player.name}: ${player.passes} تمريرة`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: false,
                            min: 0,
                            max: 100
                        },
                        y: {
                            display: false,
                            min: 0,
                            max: 100
                        }
                    }
                }
            });
        }

        // Generate Passing Data
        function generatePassingData() {
            const players = playersOnField.slice(0, 8); // Use first 8 players for demo
            const nodes = [];
            const playerData = [];

            players.forEach((player, index) => {
                const x = 20 + (index % 3) * 30 + Math.random() * 10;
                const y = 20 + Math.floor(index / 3) * 25 + Math.random() * 10;
                const passes = Math.floor(Math.random() * 50) + 20;

                nodes.push({ x, y });
                playerData.push({
                    name: player.name,
                    passes: passes,
                    accuracy: Math.floor(Math.random() * 30) + 70
                });
            });

            return { nodes, players: playerData };
        }

        // Generate Tactical Insights
        function generateTacticalInsights(matchId) {
            const container = document.getElementById('tacticalInsights');
            const matchStats = teamStats.byMatch[matchId];

            const insights = [
                {
                    title: 'فعالية الهجوم',
                    description: `تم تسجيل ${matchStats.homeScore} أهداف من ${matchStats.shots} تسديدة، بمعدل تحويل ${Math.round((matchStats.homeScore / Math.max(matchStats.shots, 1)) * 100)}%`,
                    type: matchStats.homeScore > 2 ? 'positive' : 'neutral'
                },
                {
                    title: 'الانضباط التكتيكي',
                    description: `${matchStats.yellowCards} بطاقة صفراء و ${matchStats.redCards} بطاقة حمراء تشير إلى ${matchStats.yellowCards < 3 ? 'انضباط جيد' : 'حاجة لتحسين الانضباط'}`,
                    type: matchStats.yellowCards < 3 ? 'positive' : 'warning'
                },
                {
                    title: 'اللعب الجماعي',
                    description: `${matchStats.passes} تمريرة بدقة ${matchStats.passAccuracy}% تظهر ${matchStats.passAccuracy > 80 ? 'تفاهم ممتاز' : 'حاجة لتحسين التمرير'}`,
                    type: matchStats.passAccuracy > 80 ? 'positive' : 'warning'
                },
                {
                    title: 'الكرات الثابتة',
                    description: `${matchStats.corners} ركنية تم الحصول عليها، مما يشير إلى ${matchStats.corners > 5 ? 'ضغط هجومي جيد' : 'حاجة لمزيد من الضغط'}`,
                    type: matchStats.corners > 5 ? 'positive' : 'neutral'
                }
            ];

            container.innerHTML = insights.map(insight => `
                <div class="tactical-insight">
                    <div class="insight-title">${insight.title}</div>
                    <div class="insight-description">${insight.description}</div>
                </div>
            `).join('');
        }

        // Generate Performance Analysis
        function generatePerformanceAnalysis() {
            const matchId = parseInt(document.getElementById('analysisMatchSelect').value);
            if (!matchId || !teamStats.byMatch[matchId]) {
                document.getElementById('playerRatings').innerHTML = '<p class="text-center text-gray-400">اختر مباراة للتحليل</p>';
                return;
            }

            // Generate performance radar chart
            generatePerformanceRadarChart(matchId);

            // Generate player ratings
            generatePlayerRatingsDisplay(matchId);
        }

        // Generate Performance Radar Chart
        function generatePerformanceRadarChart(matchId) {
            const ctx = document.getElementById('performanceRadarChart');
            if (!ctx) return;

            const matchStats = teamStats.byMatch[matchId];

            // Calculate performance metrics (0-100 scale)
            const metrics = {
                'الهجوم': Math.min(100, (matchStats.homeScore / 3) * 100),
                'التمرير': matchStats.passAccuracy,
                'الدفاع': Math.max(0, 100 - (matchStats.fouls * 10)),
                'الانضباط': Math.max(0, 100 - (matchStats.yellowCards * 15) - (matchStats.redCards * 30)),
                'الكرات الثابتة': Math.min(100, (matchStats.corners / 8) * 100),
                'الفعالية': Math.min(100, (matchStats.shotsOnTarget / Math.max(matchStats.shots, 1)) * 100)
            };

            new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: Object.keys(metrics),
                    datasets: [{
                        label: 'أداء الفريق',
                        data: Object.values(metrics),
                        backgroundColor: 'rgba(139, 69, 19, 0.2)',
                        borderColor: '#8B4513',
                        borderWidth: 2,
                        pointBackgroundColor: '#D2691E',
                        pointBorderColor: '#8B4513',
                        pointRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: 'white' }
                        }
                    },
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                color: 'white',
                                stepSize: 20
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.2)'
                            },
                            angleLines: {
                                color: 'rgba(255, 255, 255, 0.2)'
                            },
                            pointLabels: {
                                color: 'white',
                                font: {
                                    size: 12
                                }
                            }
                        }
                    }
                }
            });
        }

        // Generate Player Ratings Display
        function generatePlayerRatingsDisplay(matchId) {
            const container = document.getElementById('playerRatings');

            if (playersOnField.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-400">لا توجد تشكيلة للمباراة</p>';
                return;
            }

            // Calculate match-specific ratings
            const playerRatings = playersOnField.map(player => {
                const stats = playerStats[player.id];
                const rating = calculateMatchRating(player.id, matchId);
                const performance = getPerformanceAnalysis(player.id, matchId);

                return {
                    ...player,
                    rating: rating,
                    performance: performance,
                    stats: stats
                };
            }).sort((a, b) => parseFloat(b.rating) - parseFloat(a.rating));

            container.innerHTML = playerRatings.map(player => `
                <div class="player-rating-card">
                    <div style="display: flex; align-items: center; gap: 0.75rem;">
                        <div class="player-avatar">${player.jerseyNumber}</div>
                        <div style="flex: 1;">
                            <div style="font-weight: bold;">${player.name}</div>
                            <div style="font-size: 0.8rem; opacity: 0.8;">${player.position}</div>
                            <div style="font-size: 0.8rem; margin-top: 0.25rem;">
                                ${player.performance.highlights.slice(0, 2).join(' • ')}
                            </div>
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <div class="rating-badge ${getRatingClass(player.rating)}">${player.rating}</div>
                        <div style="font-size: 0.8rem; opacity: 0.7; margin-top: 0.25rem;">
                            ${player.performance.trend}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Calculate Match Rating
        function calculateMatchRating(playerId, matchId) {
            const stats = playerStats[playerId];
            const matchEvents = teamStats.byMatch[matchId].events.filter(e => e.playerId == playerId);

            let rating = 6.5; // Base rating for participation

            // Event-based rating adjustments
            matchEvents.forEach(event => {
                switch(event.type) {
                    case 'goal':
                        rating += 1.0;
                        break;
                    case 'yellow_card':
                        rating -= 0.3;
                        break;
                    case 'red_card':
                        rating -= 1.5;
                        break;
                    case 'foul':
                        rating -= 0.1;
                        break;
                }
            });

            // Position-specific adjustments
            const player = samplePlayers.find(p => p.id == playerId);
            if (player) {
                if (player.position.includes('حارس') && teamStats.byMatch[matchId].awayScore === 0) {
                    rating += 0.5; // Clean sheet bonus for goalkeeper
                }
                if (player.position.includes('مهاجم') && matchEvents.some(e => e.type === 'goal')) {
                    rating += 0.3; // Striker goal bonus
                }
            }

            return Math.max(1.0, Math.min(10.0, rating)).toFixed(1);
        }

        // Get Performance Analysis
        function getPerformanceAnalysis(playerId, matchId) {
            const stats = playerStats[playerId];
            const matchEvents = teamStats.byMatch[matchId].events.filter(e => e.playerId == playerId);

            const highlights = [];
            let trend = '';

            // Analyze events
            const goals = matchEvents.filter(e => e.type === 'goal').length;
            const cards = matchEvents.filter(e => e.type === 'yellow_card' || e.type === 'red_card').length;
            const fouls = matchEvents.filter(e => e.type === 'foul').length;

            if (goals > 0) {
                highlights.push(`${goals} هدف`);
                trend = 'أداء هجومي ممتاز';
            }

            if (cards === 0 && fouls <= 1) {
                highlights.push('انضباط عالي');
                if (!trend) trend = 'أداء منضبط';
            } else if (cards > 0) {
                highlights.push(`${cards} بطاقة`);
                trend = 'يحتاج انضباط أكثر';
            }

            if (fouls > 3) {
                highlights.push(`${fouls} أخطاء`);
                if (!trend) trend = 'أداء عدواني';
            }

            if (highlights.length === 0) {
                highlights.push('أداء مستقر');
                trend = 'أداء عادي';
            }

            return { highlights, trend };
        }

        // Generate Comparison Analysis
        function generateComparisonAnalysis() {
            const matchId = parseInt(document.getElementById('analysisMatchSelect').value);
            if (!matchId) {
                document.getElementById('performanceTrends').innerHTML = '<p class="text-center text-gray-400">اختر مباراة للتحليل</p>';
                return;
            }

            // Generate match comparison chart
            generateMatchComparisonChart();

            // Generate performance trends
            generatePerformanceTrends(matchId);
        }

        // Generate Match Comparison Chart
        function generateMatchComparisonChart() {
            const ctx = document.getElementById('matchComparisonChart');
            if (!ctx) return;

            // Get last 5 matches for comparison
            const recentMatches = Object.keys(teamStats.byMatch)
                .map(id => ({ id: parseInt(id), ...teamStats.byMatch[id] }))
                .sort((a, b) => b.id - a.id)
                .slice(0, 5)
                .reverse();

            if (recentMatches.length === 0) {
                return;
            }

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: recentMatches.map((_, index) => `المباراة ${index + 1}`),
                    datasets: [
                        {
                            label: 'الأهداف المسجلة',
                            data: recentMatches.map(m => m.homeScore),
                            borderColor: '#10B981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4
                        },
                        {
                            label: 'الأهداف المستقبلة',
                            data: recentMatches.map(m => m.awayScore),
                            borderColor: '#EF4444',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            tension: 0.4
                        },
                        {
                            label: 'دقة التمرير %',
                            data: recentMatches.map(m => m.passAccuracy),
                            borderColor: '#3B82F6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: 'white' }
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: 'white' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            ticks: { color: 'white' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            title: {
                                display: true,
                                text: 'الأهداف',
                                color: 'white'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            ticks: { color: 'white' },
                            grid: { drawOnChartArea: false },
                            title: {
                                display: true,
                                text: 'دقة التمرير %',
                                color: 'white'
                            }
                        }
                    }
                }
            });
        }

        // Generate Performance Trends
        function generatePerformanceTrends(currentMatchId) {
            const container = document.getElementById('performanceTrends');

            // Calculate trends based on recent matches
            const recentMatches = Object.keys(teamStats.byMatch)
                .map(id => ({ id: parseInt(id), ...teamStats.byMatch[id] }))
                .sort((a, b) => b.id - a.id)
                .slice(0, 5);

            if (recentMatches.length < 2) {
                container.innerHTML = '<p class="text-center text-gray-400">يحتاج مباريات أكثر للمقارنة</p>';
                return;
            }

            const trends = calculateTrends(recentMatches);

            container.innerHTML = `
                <div class="performance-metric">
                    <span class="metric-name">اتجاه الأهداف</span>
                    <span class="metric-value">
                        ${trends.goals.value}
                        <span class="trend-indicator trend-${trends.goals.direction}">
                            <i class="fas fa-arrow-${trends.goals.direction === 'up' ? 'up' : trends.goals.direction === 'down' ? 'down' : 'right'}"></i>
                            ${trends.goals.change}%
                        </span>
                    </span>
                </div>
                <div class="performance-metric">
                    <span class="metric-name">اتجاه دقة التمرير</span>
                    <span class="metric-value">
                        ${trends.passing.value}%
                        <span class="trend-indicator trend-${trends.passing.direction}">
                            <i class="fas fa-arrow-${trends.passing.direction === 'up' ? 'up' : trends.passing.direction === 'down' ? 'down' : 'right'}"></i>
                            ${trends.passing.change}%
                        </span>
                    </span>
                </div>
                <div class="performance-metric">
                    <span class="metric-name">اتجاه الانضباط</span>
                    <span class="metric-value">
                        ${trends.discipline.value}
                        <span class="trend-indicator trend-${trends.discipline.direction}">
                            <i class="fas fa-arrow-${trends.discipline.direction === 'up' ? 'up' : trends.discipline.direction === 'down' ? 'down' : 'right'}"></i>
                            ${trends.discipline.change}%
                        </span>
                    </span>
                </div>
                <div class="performance-metric">
                    <span class="metric-name">اتجاه الفعالية الهجومية</span>
                    <span class="metric-value">
                        ${trends.efficiency.value}%
                        <span class="trend-indicator trend-${trends.efficiency.direction}">
                            <i class="fas fa-arrow-${trends.efficiency.direction === 'up' ? 'up' : trends.efficiency.direction === 'down' ? 'down' : 'right'}"></i>
                            ${trends.efficiency.change}%
                        </span>
                    </span>
                </div>
            `;
        }

        // Calculate Trends
        function calculateTrends(matches) {
            const latest = matches[0];
            const previous = matches[1];

            const trends = {};

            // Goals trend
            const goalsDiff = latest.homeScore - previous.homeScore;
            trends.goals = {
                value: latest.homeScore,
                change: previous.homeScore > 0 ? Math.round((goalsDiff / previous.homeScore) * 100) : 0,
                direction: goalsDiff > 0 ? 'up' : goalsDiff < 0 ? 'down' : 'stable'
            };

            // Passing trend
            const passingDiff = latest.passAccuracy - previous.passAccuracy;
            trends.passing = {
                value: latest.passAccuracy,
                change: Math.abs(Math.round(passingDiff)),
                direction: passingDiff > 5 ? 'up' : passingDiff < -5 ? 'down' : 'stable'
            };

            // Discipline trend (fewer cards = better)
            const latestCards = latest.yellowCards + latest.redCards * 2;
            const previousCards = previous.yellowCards + previous.redCards * 2;
            const disciplineDiff = previousCards - latestCards; // Reversed because fewer cards is better
            trends.discipline = {
                value: latestCards === 0 ? 'ممتاز' : latestCards <= 2 ? 'جيد' : 'يحتاج تحسين',
                change: previousCards > 0 ? Math.round(Math.abs(disciplineDiff / previousCards) * 100) : 0,
                direction: disciplineDiff > 0 ? 'up' : disciplineDiff < 0 ? 'down' : 'stable'
            };

            // Efficiency trend
            const latestEfficiency = latest.shots > 0 ? Math.round((latest.shotsOnTarget / latest.shots) * 100) : 0;
            const previousEfficiency = previous.shots > 0 ? Math.round((previous.shotsOnTarget / previous.shots) * 100) : 0;
            const efficiencyDiff = latestEfficiency - previousEfficiency;
            trends.efficiency = {
                value: latestEfficiency,
                change: Math.abs(efficiencyDiff),
                direction: efficiencyDiff > 5 ? 'up' : efficiencyDiff < -5 ? 'down' : 'stable'
            };

            return trends;
        }

        // ==================== Enhanced Export Functions ====================

        // Export Statistics as CSV
        function exportStatisticsCSV() {
            const csvData = [];

            // Headers
            csvData.push([
                'اسم اللاعب',
                'المركز',
                'الفئة',
                'عدد المباريات',
                'الأهداف',
                'التمريرات',
                'البطاقات الصفراء',
                'البطاقات الحمراء',
                'التقييم العام',
                'دقائق اللعب'
            ]);

            // Player data
            Object.values(playerStats).forEach(stats => {
                const player = samplePlayers.find(p => p.id == stats.playerId);
                if (player) {
                    csvData.push([
                        stats.playerName,
                        player.position,
                        player.category,
                        stats.matches,
                        stats.goals,
                        stats.passes,
                        stats.yellowCards,
                        stats.redCards,
                        stats.rating,
                        stats.minutesPlayed
                    ]);
                }
            });

            // Convert to CSV string
            const csvString = csvData.map(row =>
                row.map(cell => `"${cell}"`).join(',')
            ).join('\n');

            // Download
            const blob = new Blob(['\ufeff' + csvString], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `player_statistics_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            showNotification('تم تصدير إحصائيات اللاعبين بصيغة CSV', 'success');
        }

        // Export Match Statistics as PDF Report
        function exportMatchStatisticsPDF(matchId) {
            const match = matches.find(m => m.id === matchId);
            const matchStats = teamStats.byMatch[matchId];
            const matchEventsData = matchEvents.filter(e => e.matchId === matchId);

            if (!match || !matchStats) {
                showNotification('بيانات المباراة غير متوفرة', 'error');
                return;
            }

            // Create comprehensive report
            const reportHTML = `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير المباراة - ${match.homeTeam} vs ${match.awayTeam}</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
                        .header { text-align: center; border-bottom: 2px solid #8B4513; padding-bottom: 20px; margin-bottom: 30px; }
                        .match-info { background: #f5f5f5; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
                        .stats-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin-bottom: 20px; }
                        .stat-box { background: #fff; border: 1px solid #ddd; padding: 15px; text-align: center; border-radius: 5px; }
                        .events-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                        .events-table th, .events-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                        .events-table th { background: #8B4513; color: white; }
                        .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>تقرير المباراة</h1>
                        <h2>${match.homeTeam} vs ${match.awayTeam}</h2>
                        <p>${formatDate(match.date)} - ${match.time}</p>
                        <h3 style="color: #8B4513;">${match.homeScore} - ${match.awayScore}</h3>
                    </div>

                    <div class="match-info">
                        <h3>معلومات المباراة</h3>
                        <p><strong>المكان:</strong> ${match.venue}</p>
                        <p><strong>النوع:</strong> ${match.type}</p>
                        <p><strong>الفئة:</strong> ${match.category}</p>
                        <p><strong>النتيجة:</strong> ${match.homeScore > match.awayScore ? 'فوز' : match.homeScore < match.awayScore ? 'خسارة' : 'تعادل'}</p>
                    </div>

                    <h3>الإحصائيات</h3>
                    <div class="stats-grid">
                        <div class="stat-box">
                            <h4>الاستحواذ</h4>
                            <p style="font-size: 24px; color: #8B4513;">${matchStats.possession}%</p>
                        </div>
                        <div class="stat-box">
                            <h4>التسديدات</h4>
                            <p style="font-size: 24px; color: #8B4513;">${matchStats.shots}</p>
                        </div>
                        <div class="stat-box">
                            <h4>الركنيات</h4>
                            <p style="font-size: 24px; color: #8B4513;">${matchStats.corners}</p>
                        </div>
                        <div class="stat-box">
                            <h4>دقة التمرير</h4>
                            <p style="font-size: 24px; color: #8B4513;">${matchStats.passAccuracy}%</p>
                        </div>
                        <div class="stat-box">
                            <h4>البطاقات الصفراء</h4>
                            <p style="font-size: 24px; color: #F59E0B;">${matchStats.yellowCards}</p>
                        </div>
                        <div class="stat-box">
                            <h4>البطاقات الحمراء</h4>
                            <p style="font-size: 24px; color: #EF4444;">${matchStats.redCards}</p>
                        </div>
                    </div>

                    ${matchEventsData.length > 0 ? `
                        <h3>أحداث المباراة</h3>
                        <table class="events-table">
                            <thead>
                                <tr>
                                    <th>الدقيقة</th>
                                    <th>الحدث</th>
                                    <th>اللاعب</th>
                                    <th>التفاصيل</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${matchEventsData.sort((a, b) => a.minute - b.minute).map(event => `
                                    <tr>
                                        <td>${event.minute}'</td>
                                        <td>${getEventName(event.type)}</td>
                                        <td>${event.playerName || '-'}</td>
                                        <td>${getEventDescription(event)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    ` : ''}

                    <div class="footer">
                        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المباريات - أكاديمية 7C للتدريب الرياضي</p>
                        <p>تاريخ الإنشاء: ${new Date().toLocaleString('ar-SA')}</p>
                    </div>
                </body>
                </html>
            `;

            // Create and download HTML file (can be converted to PDF)
            const blob = new Blob([reportHTML], { type: 'text/html;charset=utf-8' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `match_report_${match.homeTeam.replace(/\s+/g, '_')}_vs_${match.awayTeam.replace(/\s+/g, '_')}_${match.date}.html`;
            link.click();

            showNotification('تم تصدير تقرير المباراة بصيغة HTML', 'success');
        }

        // Export All Data Backup
        function exportCompleteBackup() {
            const backupData = {
                version: '1.0',
                exportDate: new Date().toISOString(),
                exportedBy: 'نظام إدارة المباريات - أكاديمية 7C',
                data: {
                    matches: matches,
                    playerStats: playerStats,
                    teamStats: teamStats,
                    matchEvents: matchEvents,
                    approvalRequests: approvalRequests,
                    formations: formations,
                    players: samplePlayers,
                    parentContacts: parentContactsData
                },
                summary: {
                    totalMatches: matches.length,
                    totalPlayers: samplePlayers.length,
                    totalEvents: matchEvents.length,
                    totalApprovals: approvalRequests.length,
                    totalFormations: formations.length
                }
            };

            const dataStr = JSON.stringify(backupData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `academy_7c_complete_backup_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('تم تصدير نسخة احتياطية كاملة من جميع البيانات', 'success');
        }

        // Generate AI Report
        function generateAIReport() {
            const matchId = parseInt(document.getElementById('analysisMatchSelect').value);
            if (!matchId) {
                showNotification('يرجى اختيار مباراة للتحليل', 'warning');
                return;
            }

            const match = matches.find(m => m.id === matchId);
            const matchStats = teamStats.byMatch[matchId];

            if (!match || !matchStats) {
                showNotification('بيانات المباراة غير متوفرة', 'error');
                return;
            }

            // Generate comprehensive AI report
            const report = {
                matchInfo: {
                    homeTeam: match.homeTeam,
                    awayTeam: match.awayTeam,
                    date: match.date,
                    finalScore: `${matchStats.homeScore} - ${matchStats.awayScore}`
                },
                performance: {
                    overall: calculateOverallPerformance(matchStats),
                    strengths: identifyStrengths(matchStats),
                    weaknesses: identifyWeaknesses(matchStats),
                    recommendations: generateRecommendations(matchStats)
                },
                playerAnalysis: generatePlayerAnalysis(matchId),
                tacticalAnalysis: generateTacticalSummary(matchStats),
                timestamp: new Date().toISOString()
            };

            // Export report
            const dataStr = JSON.stringify(report, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `ai_match_report_${match.homeTeam.replace(/\s+/g, '_')}_vs_${match.awayTeam.replace(/\s+/g, '_')}_${match.date}.json`;
            link.click();

            showNotification('تم إنشاء وتصدير التقرير الذكي بنجاح!', 'success');
        }

        // Calculate Overall Performance
        function calculateOverallPerformance(matchStats) {
            let score = 50; // Base score

            // Goal scoring
            score += matchStats.homeScore * 10;

            // Goal conceding
            score -= matchStats.awayScore * 8;

            // Passing accuracy
            score += (matchStats.passAccuracy - 70) * 0.5;

            // Discipline
            score -= matchStats.yellowCards * 3;
            score -= matchStats.redCards * 10;

            // Shots efficiency
            if (matchStats.shots > 0) {
                const efficiency = (matchStats.shotsOnTarget / matchStats.shots) * 100;
                score += (efficiency - 50) * 0.3;
            }

            return Math.max(0, Math.min(100, Math.round(score)));
        }

        // Identify Strengths
        function identifyStrengths(matchStats) {
            const strengths = [];

            if (matchStats.homeScore >= 2) {
                strengths.push('قوة هجومية ممتازة');
            }

            if (matchStats.passAccuracy >= 85) {
                strengths.push('دقة تمرير عالية');
            }

            if (matchStats.yellowCards <= 1 && matchStats.redCards === 0) {
                strengths.push('انضباط تكتيكي ممتاز');
            }

            if (matchStats.corners >= 6) {
                strengths.push('ضغط هجومي مستمر');
            }

            if (matchStats.shots > 0 && (matchStats.shotsOnTarget / matchStats.shots) >= 0.6) {
                strengths.push('فعالية في التسديد');
            }

            return strengths.length > 0 ? strengths : ['أداء مستقر'];
        }

        // Identify Weaknesses
        function identifyWeaknesses(matchStats) {
            const weaknesses = [];

            if (matchStats.homeScore === 0) {
                weaknesses.push('ضعف في التهديف');
            }

            if (matchStats.passAccuracy < 70) {
                weaknesses.push('دقة تمرير منخفضة');
            }

            if (matchStats.yellowCards >= 4 || matchStats.redCards > 0) {
                weaknesses.push('مشاكل في الانضباط');
            }

            if (matchStats.fouls >= 15) {
                weaknesses.push('كثرة الأخطاء');
            }

            if (matchStats.shots > 0 && (matchStats.shotsOnTarget / matchStats.shots) < 0.3) {
                weaknesses.push('ضعف في دقة التسديد');
            }

            return weaknesses;
        }

        // Generate Recommendations
        function generateRecommendations(matchStats) {
            const recommendations = [];

            if (matchStats.homeScore < 2) {
                recommendations.push('التركيز على تدريبات التهديف والانتهاء');
            }

            if (matchStats.passAccuracy < 80) {
                recommendations.push('تحسين دقة التمرير من خلال تدريبات الاستحواذ');
            }

            if (matchStats.yellowCards >= 3) {
                recommendations.push('العمل على الانضباط التكتيكي وتجنب الأخطاء غير الضرورية');
            }

            if (matchStats.corners < 3) {
                recommendations.push('زيادة الضغط الهجومي والوصول لمناطق خطيرة');
            }

            return recommendations.length > 0 ? recommendations : ['الحفاظ على المستوى الحالي'];
        }

        // Generate Player Analysis
        function generatePlayerAnalysis(matchId) {
            return playersOnField.map(player => {
                const rating = calculateMatchRating(player.id, matchId);
                const performance = getPerformanceAnalysis(player.id, matchId);

                return {
                    name: player.name,
                    position: player.position,
                    rating: parseFloat(rating),
                    performance: performance.trend,
                    highlights: performance.highlights
                };
            }).sort((a, b) => b.rating - a.rating);
        }

        // Generate Tactical Summary
        function generateTacticalSummary(matchStats) {
            return {
                formation: currentFormation,
                possession: `${matchStats.possession}%`,
                playStyle: matchStats.passAccuracy > 80 ? 'تمرير قصير' : 'لعب مباشر',
                defensiveStability: matchStats.awayScore <= 1 ? 'مستقر' : 'يحتاج تحسين',
                attackingThreat: matchStats.homeScore >= 2 ? 'عالي' : matchStats.homeScore === 1 ? 'متوسط' : 'منخفض'
            };
        }

        // Initialize Approval System
        function initializeApprovalSystem() {
            parentContacts = { ...parentContactsData };
            loadApprovalData();
            updateMatchesForApproval();
            updateApprovalStats();
            updateApprovalRequestsList();
        }

        // Load Approval Data
        function loadApprovalData() {
            const savedApprovals = localStorage.getItem('academy_approvals');
            if (savedApprovals) {
                approvalRequests = JSON.parse(savedApprovals);
            }
        }

        // Save Approval Data
        function saveApprovalData() {
            localStorage.setItem('academy_approvals', JSON.stringify(approvalRequests));
        }

        // Update Matches for Approval
        function updateMatchesForApproval() {
            const select = document.getElementById('matchForApproval');
            const upcomingMatches = matches.filter(match =>
                match.status === 'scheduled' && new Date(match.date) >= new Date()
            );

            select.innerHTML = '<option value="">اختر المباراة...</option>' +
                upcomingMatches.map(match =>
                    `<option value="${match.id}">${match.homeTeam} vs ${match.awayTeam} - ${formatDate(match.date)}</option>`
                ).join('');

            select.addEventListener('change', function() {
                if (this.value) {
                    showSelectedPlayersForMatch(parseInt(this.value));
                } else {
                    document.getElementById('selectedPlayersSection').style.display = 'none';
                }
            });
        }

        // Show Selected Players for Match
        function showSelectedPlayersForMatch(matchId) {
            const match = matches.find(m => m.id === matchId);
            if (!match) return;

            const section = document.getElementById('selectedPlayersSection');
            const container = document.getElementById('selectedPlayersList');

            // For demo, use current formation players or show all available players
            const selectedPlayers = playersOnField.length > 0 ? playersOnField :
                samplePlayers.filter(p => p.category === match.category).slice(0, 11);

            if (selectedPlayers.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-400">لم يتم اختيار لاعبين للمباراة بعد</p>';
                section.style.display = 'block';
                return;
            }

            container.innerHTML = selectedPlayers.map(player => {
                const parent = parentContacts[player.id];
                const existingRequest = approvalRequests.find(req =>
                    req.matchId === matchId && req.playerId === player.id
                );

                return `
                    <div class="player-approval-card" style="background: rgba(255,255,255,0.05); border-radius: 10px; padding: 1rem; border: 1px solid rgba(255,255,255,0.1);">
                        <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1rem;">
                            <div class="player-avatar">${player.jerseyNumber}</div>
                            <div>
                                <div style="font-weight: bold;">${player.name}</div>
                                <div style="font-size: 0.8rem; opacity: 0.8;">${player.position}</div>
                            </div>
                        </div>

                        <div style="font-size: 0.9rem; margin-bottom: 1rem;">
                            <div><strong>ولي الأمر:</strong> ${parent ? parent.name : 'غير محدد'}</div>
                            <div><strong>الهاتف:</strong> ${parent ? parent.phone : 'غير محدد'}</div>
                        </div>

                        ${existingRequest ? `
                            <div class="approval-status status-${existingRequest.status}" style="padding: 0.5rem; border-radius: 5px; text-align: center; font-weight: bold;">
                                ${getApprovalStatusText(existingRequest.status)}
                            </div>
                            <div style="font-size: 0.8rem; opacity: 0.7; text-align: center; margin-top: 0.5rem;">
                                ${new Date(existingRequest.sentAt).toLocaleString('ar-SA')}
                            </div>
                        ` : `
                            <button class="btn btn-small btn-success" onclick="sendIndividualApproval(${matchId}, ${player.id})" style="width: 100%;">
                                <i class="fas fa-paper-plane"></i>
                                إرسال طلب موافقة
                            </button>
                        `}
                    </div>
                `;
            }).join('');

            section.style.display = 'block';
        }

        // Send Individual Approval
        function sendIndividualApproval(matchId, playerId) {
            const match = matches.find(m => m.id === matchId);
            const player = samplePlayers.find(p => p.id === playerId);
            const parent = parentContacts[playerId];

            if (!match || !player || !parent) {
                showNotification('بيانات غير مكتملة', 'error');
                return;
            }

            const approvalRequest = {
                id: Date.now(),
                matchId: matchId,
                playerId: playerId,
                playerName: player.name,
                parentName: parent.name,
                parentPhone: parent.phone,
                parentWhatsApp: parent.whatsapp,
                matchDetails: {
                    homeTeam: match.homeTeam,
                    awayTeam: match.awayTeam,
                    date: match.date,
                    time: match.time,
                    venue: match.venue,
                    type: match.type
                },
                status: 'pending',
                sentAt: new Date().toISOString(),
                expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
                response: null,
                responseAt: null
            };

            approvalRequests.push(approvalRequest);
            saveApprovalData();

            // Simulate sending WhatsApp message
            sendWhatsAppApproval(approvalRequest);

            // Update UI
            showSelectedPlayersForMatch(matchId);
            updateApprovalStats();
            updateApprovalRequestsList();

            showNotification(`تم إرسال طلب موافقة لولي أمر ${player.name}`, 'success');
        }

        // Send Bulk Approvals
        function sendBulkApprovals() {
            const matchId = parseInt(document.getElementById('matchForApproval').value);
            if (!matchId) {
                showNotification('يرجى اختيار المباراة أولاً', 'warning');
                return;
            }

            const match = matches.find(m => m.id === matchId);
            const selectedPlayers = playersOnField.length > 0 ? playersOnField :
                samplePlayers.filter(p => p.category === match.category).slice(0, 11);

            if (selectedPlayers.length === 0) {
                showNotification('لا يوجد لاعبون مختارون للمباراة', 'warning');
                return;
            }

            let sentCount = 0;
            selectedPlayers.forEach(player => {
                const existingRequest = approvalRequests.find(req =>
                    req.matchId === matchId && req.playerId === player.id
                );

                if (!existingRequest) {
                    sendIndividualApproval(matchId, player.id);
                    sentCount++;
                }
            });

            if (sentCount > 0) {
                showNotification(`تم إرسال ${sentCount} طلب موافقة`, 'success');
            } else {
                showNotification('تم إرسال جميع طلبات الموافقة مسبقاً', 'info');
            }
        }

        // Send WhatsApp Approval
        function sendWhatsAppApproval(request) {
            const message = `🏃‍♂️ أكاديمية 7C للتدريب الرياضي

السلام عليكم ورحمة الله وبركاته

نود إعلامكم بأن نجلكم/ابنتكم *${request.playerName}* تم اختياره للمشاركة في المباراة التالية:

📅 *التاريخ:* ${formatDate(request.matchDetails.date)}
⏰ *الوقت:* ${request.matchDetails.time}
📍 *المكان:* ${request.matchDetails.venue}
🆚 *المنافس:* ${request.matchDetails.awayTeam}
🏆 *نوع المباراة:* ${request.matchDetails.type}

يرجى الرد بـ:
✅ *موافق* للموافقة على المشاركة
❌ *غير موافق* في حالة عدم القدرة على المشاركة

⚠️ *مهلة الرد: 24 ساعة من وقت إرسال هذه الرسالة*

شكراً لتعاونكم معنا في تطوير مهارات أبنائكم

رقم الطلب: ${request.id}`;

            // In real implementation, this would use WhatsApp Business API
            console.log('WhatsApp message sent to:', request.parentWhatsApp);
            console.log('Message:', message);

            // Simulate opening WhatsApp (for demo purposes)
            // const whatsappUrl = `https://wa.me/${request.parentWhatsApp.replace('+', '')}?text=${encodeURIComponent(message)}`;
            // window.open(whatsappUrl, '_blank');
        }

        // Update Approval Stats
        function updateApprovalStats() {
            const approved = approvalRequests.filter(req => req.status === 'approved').length;
            const pending = approvalRequests.filter(req => req.status === 'pending' && new Date(req.expiresAt) > new Date()).length;
            const rejected = approvalRequests.filter(req => req.status === 'rejected').length;
            const expired = approvalRequests.filter(req => req.status === 'pending' && new Date(req.expiresAt) <= new Date()).length;

            document.getElementById('approvedCount').textContent = approved;
            document.getElementById('pendingCount').textContent = pending;
            document.getElementById('rejectedCount').textContent = rejected;
            document.getElementById('expiredCount').textContent = expired;
        }

        // Update Approval Requests List
        function updateApprovalRequestsList() {
            const container = document.getElementById('approvalRequestsList');
            const filter = document.getElementById('approvalFilter').value;

            let filteredRequests = approvalRequests;
            if (filter !== 'all') {
                if (filter === 'expired') {
                    filteredRequests = approvalRequests.filter(req =>
                        req.status === 'pending' && new Date(req.expiresAt) <= new Date()
                    );
                } else {
                    filteredRequests = approvalRequests.filter(req => req.status === filter);
                }
            }

            if (filteredRequests.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-400">لا توجد طلبات موافقة</p>';
                return;
            }

            container.innerHTML = filteredRequests.sort((a, b) => new Date(b.sentAt) - new Date(a.sentAt))
                .map(request => createApprovalRequestCard(request)).join('');
        }

        // Create Approval Request Card
        function createApprovalRequestCard(request) {
            const isExpired = request.status === 'pending' && new Date(request.expiresAt) <= new Date();
            const actualStatus = isExpired ? 'expired' : request.status;
            const statusClass = `status-${actualStatus}`;
            const statusText = getApprovalStatusText(actualStatus);

            return `
                <div class="approval-request-card" style="background: rgba(255,255,255,0.05); border-radius: 10px; padding: 1.5rem; margin-bottom: 1rem; border: 1px solid rgba(255,255,255,0.1);">
                    <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 1rem;">
                        <div>
                            <h4 style="margin-bottom: 0.5rem;">${request.playerName}</h4>
                            <p style="font-size: 0.9rem; opacity: 0.8;">ولي الأمر: ${request.parentName}</p>
                        </div>
                        <span class="status-badge ${statusClass}">${statusText}</span>
                    </div>

                    <div style="background: rgba(255,255,255,0.05); padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                        <h5 style="margin-bottom: 0.5rem; color: #D2691E;">تفاصيل المباراة</h5>
                        <div style="font-size: 0.9rem;">
                            <p><strong>المباراة:</strong> ${request.matchDetails.homeTeam} vs ${request.matchDetails.awayTeam}</p>
                            <p><strong>التاريخ:</strong> ${formatDate(request.matchDetails.date)} - ${request.matchDetails.time}</p>
                            <p><strong>المكان:</strong> ${request.matchDetails.venue}</p>
                            <p><strong>النوع:</strong> ${request.matchDetails.type}</p>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                        <div style="text-align: center;">
                            <div style="font-size: 0.8rem; opacity: 0.7;">تاريخ الإرسال</div>
                            <div style="font-weight: bold;">${new Date(request.sentAt).toLocaleString('ar-SA')}</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 0.8rem; opacity: 0.7;">تاريخ الانتهاء</div>
                            <div style="font-weight: bold; color: ${isExpired ? '#EF4444' : '#F59E0B'};">
                                ${new Date(request.expiresAt).toLocaleString('ar-SA')}
                            </div>
                        </div>
                        ${request.responseAt ? `
                            <div style="text-align: center;">
                                <div style="font-size: 0.8rem; opacity: 0.7;">تاريخ الرد</div>
                                <div style="font-weight: bold;">${new Date(request.responseAt).toLocaleString('ar-SA')}</div>
                            </div>
                        ` : ''}
                    </div>

                    <div style="display: flex; gap: 0.5rem; justify-content: flex-end;">
                        ${request.status === 'pending' && !isExpired ? `
                            <button class="btn btn-small btn-success" onclick="simulateApprovalResponse(${request.id}, 'approved')">
                                <i class="fas fa-check"></i>
                                محاكاة موافقة
                            </button>
                            <button class="btn btn-small btn-danger" onclick="simulateApprovalResponse(${request.id}, 'rejected')">
                                <i class="fas fa-times"></i>
                                محاكاة رفض
                            </button>
                        ` : ''}
                        <button class="btn btn-small btn-secondary" onclick="resendApproval(${request.id})">
                            <i class="fas fa-redo"></i>
                            إعادة إرسال
                        </button>
                        <button class="btn btn-small" onclick="viewApprovalDetails(${request.id})">
                            <i class="fas fa-eye"></i>
                            التفاصيل
                        </button>
                    </div>
                </div>
            `;
        }

        // Get Approval Status Text
        function getApprovalStatusText(status) {
            const statusMap = {
                'pending': 'في الانتظار',
                'approved': 'موافق',
                'rejected': 'مرفوض',
                'expired': 'منتهي الصلاحية'
            };
            return statusMap[status] || 'غير محدد';
        }

        // Simulate Approval Response (for demo)
        function simulateApprovalResponse(requestId, response) {
            const request = approvalRequests.find(req => req.id === requestId);
            if (!request) return;

            request.status = response;
            request.response = response;
            request.responseAt = new Date().toISOString();

            saveApprovalData();
            updateApprovalStats();
            updateApprovalRequestsList();

            const responseText = response === 'approved' ? 'الموافقة' : 'الرفض';
            showNotification(`تم تسجيل ${responseText} من ولي أمر ${request.playerName}`, 'success');

            // If rejected, suggest alternatives
            if (response === 'rejected') {
                setTimeout(() => {
                    suggestAlternativePlayer(request);
                }, 1000);
            }
        }

        // Suggest Alternative Player
        function suggestAlternativePlayer(rejectedRequest) {
            const match = matches.find(m => m.id === rejectedRequest.matchId);
            if (!match) return;

            const rejectedPlayer = samplePlayers.find(p => p.id === rejectedRequest.playerId);
            if (!rejectedPlayer) return;

            // Find alternative players with same position and category
            const alternatives = samplePlayers.filter(player =>
                player.category === rejectedPlayer.category &&
                player.position === rejectedPlayer.position &&
                player.id !== rejectedPlayer.id &&
                !playersOnField.find(p => p.id === player.id) &&
                !approvalRequests.find(req =>
                    req.matchId === match.id &&
                    req.playerId === player.id &&
                    req.status !== 'rejected'
                )
            ).sort((a, b) => b.rating - a.rating);

            if (alternatives.length > 0) {
                const alternative = alternatives[0];
                const confirmMessage = `تم رفض مشاركة ${rejectedPlayer.name}.\n\nهل تريد إرسال طلب موافقة للبديل المقترح؟\n\n${alternative.name} - ${alternative.position} (تقييم: ${alternative.rating})`;

                if (confirm(confirmMessage)) {
                    sendIndividualApproval(match.id, alternative.id);
                }
            } else {
                showNotification(`لا يوجد بديل متاح لمركز ${rejectedPlayer.position}`, 'warning');
            }
        }

        // Resend Approval
        function resendApproval(requestId) {
            const request = approvalRequests.find(req => req.id === requestId);
            if (!request) return;

            // Update expiry time
            request.expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString();
            request.status = 'pending';
            request.response = null;
            request.responseAt = null;

            saveApprovalData();
            sendWhatsAppApproval(request);
            updateApprovalStats();
            updateApprovalRequestsList();

            showNotification(`تم إعادة إرسال طلب الموافقة لولي أمر ${request.playerName}`, 'success');
        }

        // View Approval Details
        function viewApprovalDetails(requestId) {
            const request = approvalRequests.find(req => req.id === requestId);
            if (!request) return;

            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 600px;">
                    <div class="modal-header">
                        <h3><i class="fas fa-info-circle"></i> تفاصيل طلب الموافقة</h3>
                        <button class="modal-close" onclick="closeModal(this)">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div style="background: rgba(255,255,255,0.05); padding: 1.5rem; border-radius: 10px;">
                            <h4 style="margin-bottom: 1rem; color: #D2691E;">معلومات اللاعب</h4>
                            <p><strong>الاسم:</strong> ${request.playerName}</p>
                            <p><strong>ولي الأمر:</strong> ${request.parentName}</p>
                            <p><strong>الهاتف:</strong> ${request.parentPhone}</p>
                            <p><strong>واتساب:</strong> ${request.parentWhatsApp}</p>
                        </div>

                        <div style="background: rgba(255,255,255,0.05); padding: 1.5rem; border-radius: 10px; margin: 1rem 0;">
                            <h4 style="margin-bottom: 1rem; color: #D2691E;">تفاصيل المباراة</h4>
                            <p><strong>المباراة:</strong> ${request.matchDetails.homeTeam} vs ${request.matchDetails.awayTeam}</p>
                            <p><strong>التاريخ:</strong> ${formatDate(request.matchDetails.date)}</p>
                            <p><strong>الوقت:</strong> ${request.matchDetails.time}</p>
                            <p><strong>المكان:</strong> ${request.matchDetails.venue}</p>
                            <p><strong>النوع:</strong> ${request.matchDetails.type}</p>
                        </div>

                        <div style="background: rgba(255,255,255,0.05); padding: 1.5rem; border-radius: 10px;">
                            <h4 style="margin-bottom: 1rem; color: #D2691E;">حالة الطلب</h4>
                            <p><strong>الحالة:</strong> <span class="status-badge status-${request.status}">${getApprovalStatusText(request.status)}</span></p>
                            <p><strong>تاريخ الإرسال:</strong> ${new Date(request.sentAt).toLocaleString('ar-SA')}</p>
                            <p><strong>تاريخ الانتهاء:</strong> ${new Date(request.expiresAt).toLocaleString('ar-SA')}</p>
                            ${request.responseAt ? `<p><strong>تاريخ الرد:</strong> ${new Date(request.responseAt).toLocaleString('ar-SA')}</p>` : ''}
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // Filter Approvals
        function filterApprovals() {
            updateApprovalRequestsList();
        }

        // Check Approval Status
        function checkApprovalStatus() {
            // Check for expired requests
            let expiredCount = 0;
            let autoRemindersSent = 0;

            approvalRequests.forEach(request => {
                if (request.status === 'pending') {
                    const expiresAt = new Date(request.expiresAt);
                    const now = new Date();
                    const hoursUntilExpiry = (expiresAt - now) / (1000 * 60 * 60);

                    if (expiresAt <= now) {
                        expiredCount++;
                    } else if (hoursUntilExpiry <= 2 && !request.reminderSent) {
                        // Send automatic reminder 2 hours before expiry
                        sendApprovalReminder(request);
                        request.reminderSent = true;
                        autoRemindersSent++;
                    }
                }
            });

            // Save updated requests
            saveApprovalData();
            updateApprovalStats();
            updateApprovalRequestsList();

            let message = 'تم تحديث حالة جميع الطلبات';
            if (expiredCount > 0) {
                message += ` - ${expiredCount} طلب منتهي الصلاحية`;
            }
            if (autoRemindersSent > 0) {
                message += ` - تم إرسال ${autoRemindersSent} تذكير تلقائي`;
            }

            showNotification(message, expiredCount > 0 ? 'warning' : 'success');
        }

        // Send Approval Reminder
        function sendApprovalReminder(request) {
            const reminderMessage = `⏰ تذكير مهم - أكاديمية 7C

السلام عليكم ${request.parentName}

هذا تذكير بأن طلب موافقة مشاركة نجلكم/ابنتكم ${request.playerName} في المباراة سينتهي خلال ساعتين.

تفاصيل المباراة:
🆚 ${request.matchDetails.homeTeam} vs ${request.matchDetails.awayTeam}
📅 ${formatDate(request.matchDetails.date)} - ${request.matchDetails.time}
📍 ${request.matchDetails.venue}

يرجى الرد بـ:
✅ موافق للموافقة
❌ غير موافق في حالة عدم القدرة

رقم الطلب: ${request.id}`;

            console.log('Reminder sent to:', request.parentWhatsApp, reminderMessage);

            // In real implementation, this would use actual WhatsApp API
            // sendWhatsAppMessage(request.parentWhatsApp, reminderMessage);
        }

        // Enhanced Bulk Approvals with Smart Selection
        function sendSmartBulkApprovals() {
            const matchId = parseInt(document.getElementById('matchForApproval').value);
            if (!matchId) {
                showNotification('يرجى اختيار المباراة أولاً', 'warning');
                return;
            }

            const match = matches.find(m => m.id === matchId);
            if (!match) {
                showNotification('المباراة غير موجودة', 'error');
                return;
            }

            // Smart player selection based on attendance and performance
            const smartSelection = selectPlayersSmartly(match.category);

            if (smartSelection.length === 0) {
                showNotification('لا يوجد لاعبون مناسبون للمباراة', 'warning');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 700px;">
                    <div class="modal-header">
                        <h3><i class="fas fa-brain"></i> الاختيار الذكي للاعبين</h3>
                        <button class="modal-close" onclick="closeModal(this)">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div style="background: rgba(59, 130, 246, 0.1); border: 1px solid #3B82F6; border-radius: 8px; padding: 1rem; margin-bottom: 1.5rem;">
                            <h5 style="color: #3B82F6; margin-bottom: 0.5rem;"><i class="fas fa-info-circle"></i> الاختيار الذكي</h5>
                            <p style="font-size: 0.9rem; margin: 0;">تم اختيار اللاعبين بناءً على معدل الحضور، الأداء، والتقييم العام.</p>
                        </div>

                        <h4 style="margin-bottom: 1rem; color: #D2691E;">اللاعبون المختارون (${smartSelection.length})</h4>

                        <div style="max-height: 400px; overflow-y: auto;">
                            ${smartSelection.map(player => `
                                <div class="player-approval-card" style="margin-bottom: 1rem;">
                                    <div style="display: flex; align-items: center; gap: 1rem;">
                                        <input type="checkbox" id="player_${player.id}" checked style="transform: scale(1.2);">
                                        <div class="player-avatar">${player.jerseyNumber}</div>
                                        <div style="flex: 1;">
                                            <div style="font-weight: bold;">${player.name}</div>
                                            <div style="font-size: 0.8rem; opacity: 0.8;">${player.position}</div>
                                            <div style="font-size: 0.8rem; color: #10B981;">
                                                تقييم: ${player.rating} | حضور: ${player.attendanceRate}%
                                            </div>
                                        </div>
                                        <div style="text-align: right;">
                                            <div class="rating-badge ${getRatingClass(player.rating)}">${player.rating}</div>
                                            <div style="font-size: 0.8rem; opacity: 0.7; margin-top: 0.25rem;">
                                                ${player.selectionReason}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>

                        <div style="display: flex; gap: 1rem; justify-content: space-between; margin-top: 2rem;">
                            <div>
                                <button type="button" class="btn btn-secondary" onclick="selectAllPlayers(true)">
                                    <i class="fas fa-check-square"></i>
                                    تحديد الكل
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="selectAllPlayers(false)" style="margin-right: 0.5rem;">
                                    <i class="fas fa-square"></i>
                                    إلغاء التحديد
                                </button>
                            </div>
                            <div>
                                <button type="button" class="btn btn-secondary" onclick="closeModal(this)">إلغاء</button>
                                <button type="button" class="btn btn-success" onclick="sendSelectedApprovals(${matchId})" style="margin-right: 0.5rem;">
                                    <i class="fas fa-paper-plane"></i>
                                    إرسال الموافقات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // Select Players Smartly
        function selectPlayersSmartly(category) {
            const categoryPlayers = samplePlayers.filter(p => p.category === category);

            // Calculate smart scores for each player
            const playersWithScores = categoryPlayers.map(player => {
                let score = 0;
                let reasons = [];

                // Base rating score (40% weight)
                score += (player.rating / 100) * 40;

                // Attendance rate (30% weight) - simulate attendance data
                const attendanceRate = 75 + Math.random() * 25; // 75-100%
                score += (attendanceRate / 100) * 30;

                if (attendanceRate > 90) reasons.push('حضور ممتاز');
                else if (attendanceRate > 80) reasons.push('حضور جيد');

                // Performance in recent matches (20% weight)
                const recentPerformance = 70 + Math.random() * 30; // 70-100%
                score += (recentPerformance / 100) * 20;

                if (recentPerformance > 85) reasons.push('أداء متميز');

                // Discipline (10% weight)
                const disciplineScore = 90 + Math.random() * 10; // 90-100%
                score += (disciplineScore / 100) * 10;

                if (disciplineScore > 95) reasons.push('انضباط عالي');

                return {
                    ...player,
                    smartScore: score,
                    attendanceRate: Math.round(attendanceRate),
                    selectionReason: reasons.length > 0 ? reasons.join(', ') : 'لاعب مناسب'
                };
            });

            // Sort by smart score and return top 16 players
            return playersWithScores
                .sort((a, b) => b.smartScore - a.smartScore)
                .slice(0, 16);
        }

        // Select All Players
        function selectAllPlayers(select) {
            const checkboxes = document.querySelectorAll('input[id^="player_"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = select;
            });
        }

        // Send Selected Approvals
        function sendSelectedApprovals(matchId) {
            const selectedPlayers = [];
            const checkboxes = document.querySelectorAll('input[id^="player_"]:checked');

            checkboxes.forEach(checkbox => {
                const playerId = parseInt(checkbox.id.replace('player_', ''));
                selectedPlayers.push(playerId);
            });

            if (selectedPlayers.length === 0) {
                showNotification('يرجى اختيار لاعب واحد على الأقل', 'warning');
                return;
            }

            let sentCount = 0;
            selectedPlayers.forEach(playerId => {
                const existingRequest = approvalRequests.find(req =>
                    req.matchId === matchId && req.playerId === playerId
                );

                if (!existingRequest) {
                    sendIndividualApproval(matchId, playerId);
                    sentCount++;
                }
            });

            closeModal();

            if (sentCount > 0) {
                showNotification(`تم إرسال ${sentCount} طلب موافقة للاعبين المختارين`, 'success');
            } else {
                showNotification('تم إرسال جميع طلبات الموافقة مسبقاً', 'info');
            }
        }

        // Initialize Formation System
        function initializeFormationSystem() {
            availablePlayers = [...samplePlayers];
            loadFormationData();
            updateAvailablePlayersList();
            setupFormation(currentFormation);
            updateFormationStats();
        }

        // Load Formation Data
        function loadFormationData() {
            const savedFormations = localStorage.getItem('academy_formations');
            if (savedFormations) {
                formations = JSON.parse(savedFormations);
            }

            const savedPlayersOnField = localStorage.getItem('current_formation_players');
            if (savedPlayersOnField) {
                playersOnField = JSON.parse(savedPlayersOnField);
            }
        }

        // Save Formation Data
        function saveFormationData() {
            localStorage.setItem('academy_formations', JSON.stringify(formations));
            localStorage.setItem('current_formation_players', JSON.stringify(playersOnField));
        }

        // Update Available Players List
        function updateAvailablePlayersList() {
            const container = document.getElementById('availablePlayers');
            const category = document.getElementById('categorySelect').value;

            let filteredPlayers = availablePlayers.filter(player => {
                const notOnField = !playersOnField.find(p => p.id === player.id);
                const categoryMatch = category === 'all' || player.category === category;
                return notOnField && categoryMatch;
            });

            if (filteredPlayers.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-400">لا يوجد لاعبون متاحون</p>';
                return;
            }

            container.innerHTML = filteredPlayers.map(player => `
                <div class="available-player" draggable="true" data-player-id="${player.id}">
                    <div class="player-info">
                        <div class="player-avatar">${player.jerseyNumber}</div>
                        <div class="player-details">
                            <div class="player-name">${player.name}</div>
                            <div class="player-position">${player.position}</div>
                        </div>
                    </div>
                    <div class="player-rating">${player.rating}</div>
                </div>
            `).join('');

            // Add drag event listeners
            container.querySelectorAll('.available-player').forEach(playerElement => {
                playerElement.addEventListener('dragstart', handleDragStart);
                playerElement.addEventListener('dragend', handleDragEnd);
            });
        }

        // Setup Formation
        function setupFormation(formationName) {
            const field = document.getElementById('footballField');
            const playersContainer = document.getElementById('fieldPlayers');

            // Clear existing players
            playersContainer.innerHTML = '';

            // Remove existing formation classes
            field.className = 'football-field';
            field.classList.add(`formation-${formationName}`);

            const template = formationTemplates[formationName];
            if (!template) return;

            // Create position slots
            template.positions.forEach((position, index) => {
                const positionSlot = document.createElement('div');
                positionSlot.className = 'drop-zone';
                positionSlot.style.left = `${position.x}%`;
                positionSlot.style.top = `${position.y}%`;
                positionSlot.style.transform = 'translate(-50%, -50%)';
                positionSlot.dataset.positionId = position.id;
                positionSlot.dataset.role = position.role;

                // Add drop event listeners
                positionSlot.addEventListener('dragover', handleDragOver);
                positionSlot.addEventListener('drop', handleDrop);

                playersContainer.appendChild(positionSlot);
            });

            // Place existing players
            playersOnField.forEach(player => {
                if (player.positionId) {
                    placePlayerOnField(player, player.positionId);
                }
            });

            updateFormationStats();
        }

        // Change Formation
        function changeFormation() {
            const newFormation = document.getElementById('formationSelect').value;

            if (playersOnField.length > 0) {
                if (!confirm('تغيير التشكيلة سيؤدي إلى إزالة جميع اللاعبين من الملعب. هل تريد المتابعة؟')) {
                    document.getElementById('formationSelect').value = currentFormation;
                    return;
                }

                // Move all players back to available list
                playersOnField.forEach(player => {
                    const originalPlayer = samplePlayers.find(p => p.id === player.id);
                    if (originalPlayer && !availablePlayers.find(p => p.id === player.id)) {
                        availablePlayers.push(originalPlayer);
                    }
                });
                playersOnField = [];
            }

            currentFormation = newFormation;
            document.getElementById('formationName').textContent = newFormation;
            setupFormation(newFormation);
            updateAvailablePlayersList();
            saveFormationData();
        }

        // Drag and Drop Handlers
        function handleDragStart(e) {
            draggedPlayer = {
                id: parseInt(e.target.dataset.playerId),
                element: e.target
            };
            e.target.classList.add('dragging');

            // Show drop zones
            document.querySelectorAll('.drop-zone').forEach(zone => {
                if (!zone.querySelector('.player-on-field')) {
                    zone.classList.add('active');
                }
            });
        }

        function handleDragEnd(e) {
            e.target.classList.remove('dragging');
            draggedPlayer = null;

            // Hide drop zones
            document.querySelectorAll('.drop-zone').forEach(zone => {
                zone.classList.remove('active');
            });
        }

        function handleDragOver(e) {
            e.preventDefault();
        }

        function handleDrop(e) {
            e.preventDefault();

            if (!draggedPlayer) return;

            const positionId = parseInt(e.target.dataset.positionId);
            const role = e.target.dataset.role;

            // Check if position is already occupied
            if (e.target.querySelector('.player-on-field')) {
                showNotification('هذا المركز محجوز بالفعل', 'warning');
                return;
            }

            // Get player data
            const playerData = availablePlayers.find(p => p.id === draggedPlayer.id);
            if (!playerData) return;

            // Add player to field
            const fieldPlayer = {
                ...playerData,
                positionId: positionId,
                role: role
            };

            playersOnField.push(fieldPlayer);

            // Remove from available players
            availablePlayers = availablePlayers.filter(p => p.id !== draggedPlayer.id);

            // Place player on field
            placePlayerOnField(fieldPlayer, positionId);

            // Update displays
            updateAvailablePlayersList();
            updateFormationStats();
            saveFormationData();

            showNotification(`تم إضافة ${playerData.name} إلى التشكيلة`, 'success');
        }

        // Place Player on Field
        function placePlayerOnField(player, positionId) {
            const dropZone = document.querySelector(`[data-position-id="${positionId}"]`);
            if (!dropZone) return;

            const playerElement = document.createElement('div');
            playerElement.className = 'player-on-field';
            if (player.role === 'GK') {
                playerElement.classList.add('goalkeeper');
            }

            playerElement.innerHTML = player.jerseyNumber;
            playerElement.title = `${player.name} - ${player.position} (${player.rating})`;
            playerElement.dataset.playerId = player.id;

            // Add click to remove
            playerElement.addEventListener('click', () => removePlayerFromField(player.id));

            dropZone.appendChild(playerElement);
            dropZone.classList.remove('active');
        }

        // Remove Player from Field
        function removePlayerFromField(playerId) {
            const player = playersOnField.find(p => p.id === playerId);
            if (!player) return;

            if (confirm(`هل تريد إزالة ${player.name} من التشكيلة؟`)) {
                // Remove from field
                playersOnField = playersOnField.filter(p => p.id !== playerId);

                // Add back to available players
                const originalPlayer = samplePlayers.find(p => p.id === playerId);
                if (originalPlayer) {
                    availablePlayers.push(originalPlayer);
                }

                // Remove from DOM
                const playerElement = document.querySelector(`[data-player-id="${playerId}"]`);
                if (playerElement && playerElement.classList.contains('player-on-field')) {
                    playerElement.remove();
                }

                // Update displays
                updateAvailablePlayersList();
                updateFormationStats();
                saveFormationData();

                showNotification(`تم إزالة ${player.name} من التشكيلة`, 'info');
            }
        }

        // Update Formation Stats
        function updateFormationStats() {
            const playersCount = playersOnField.length;
            const avgRating = playersCount > 0 ?
                Math.round(playersOnField.reduce((sum, p) => sum + p.rating, 0) / playersCount) : 0;

            // Calculate team chemistry (simplified)
            const chemistry = calculateTeamChemistry();

            document.getElementById('playersCount').textContent = playersCount;
            document.getElementById('formationName').textContent = currentFormation;
            document.getElementById('avgRating').textContent = avgRating;
            document.getElementById('teamChemistry').textContent = chemistry + '%';
        }

        // Calculate Team Chemistry
        function calculateTeamChemistry() {
            if (playersOnField.length === 0) return 0;

            let chemistry = 50; // Base chemistry

            // Bonus for full team
            if (playersOnField.length === 11) chemistry += 20;

            // Bonus for players in correct positions
            playersOnField.forEach(player => {
                const isCorrectPosition = checkPositionMatch(player.position, player.role);
                if (isCorrectPosition) chemistry += 3;
            });

            // Bonus for high-rated players
            const avgRating = playersOnField.reduce((sum, p) => sum + p.rating, 0) / playersOnField.length;
            if (avgRating > 80) chemistry += 10;
            else if (avgRating > 75) chemistry += 5;

            return Math.min(100, Math.max(0, chemistry));
        }

        // Check Position Match
        function checkPositionMatch(playerPosition, fieldRole) {
            const positionMap = {
                'حارس مرمى': ['GK'],
                'مدافع أيمن': ['RB', 'RWB'],
                'مدافع أيسر': ['LB', 'LWB'],
                'مدافع وسط': ['CB'],
                'مدافع': ['CB', 'RB', 'LB'],
                'وسط دفاعي': ['CM', 'CDM'],
                'وسط أيمن': ['RM', 'CM'],
                'وسط أيسر': ['LM', 'CM'],
                'وسط مهاجم': ['CAM', 'CM'],
                'وسط': ['CM', 'RM', 'LM', 'CAM', 'CDM'],
                'مهاجم': ['ST', 'CF'],
                'جناح أيمن': ['RW', 'RM'],
                'جناح أيسر': ['LW', 'LM']
            };

            return positionMap[playerPosition]?.includes(fieldRole) || false;
        }

        // Filter Players by Category
        function filterPlayersByCategory() {
            updateAvailablePlayersList();
        }

        // Save Formation
        function saveFormation() {
            if (playersOnField.length === 0) {
                showNotification('لا يمكن حفظ تشكيلة فارغة', 'warning');
                return;
            }

            const formationName = prompt('أدخل اسم التشكيلة:');
            if (!formationName) return;

            const formation = {
                id: Date.now(),
                name: formationName,
                formation: currentFormation,
                players: [...playersOnField],
                createdAt: new Date().toISOString(),
                stats: {
                    playersCount: playersOnField.length,
                    avgRating: Math.round(playersOnField.reduce((sum, p) => sum + p.rating, 0) / playersOnField.length),
                    chemistry: calculateTeamChemistry()
                }
            };

            formations.push(formation);
            saveFormationData();

            showNotification(`تم حفظ التشكيلة "${formationName}" بنجاح`, 'success');
        }

        // Load Formation
        function loadFormation() {
            if (formations.length === 0) {
                showNotification('لا توجد تشكيلات محفوظة', 'info');
                return;
            }

            // Create modal to select formation
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 600px;">
                    <div class="modal-header">
                        <h3><i class="fas fa-folder-open"></i> تحميل تشكيلة محفوظة</h3>
                        <button class="modal-close" onclick="closeModal(this)">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="formations-list">
                            ${formations.map(formation => `
                                <div class="formation-item" style="padding: 1rem; margin-bottom: 1rem; background: rgba(255,255,255,0.1); border-radius: 8px; cursor: pointer;" onclick="selectFormation(${formation.id})">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <div>
                                            <h4 style="margin-bottom: 0.5rem;">${formation.name}</h4>
                                            <p style="font-size: 0.9rem; opacity: 0.8;">التشكيلة: ${formation.formation} | اللاعبين: ${formation.stats.playersCount} | التقييم: ${formation.stats.avgRating}</p>
                                        </div>
                                        <div style="text-align: center;">
                                            <div style="font-size: 1.2rem; font-weight: bold; color: #D2691E;">${formation.stats.chemistry}%</div>
                                            <div style="font-size: 0.8rem; opacity: 0.7;">كيمياء الفريق</div>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // Select Formation
        function selectFormation(formationId) {
            const formation = formations.find(f => f.id === formationId);
            if (!formation) return;

            // Clear current formation
            playersOnField.forEach(player => {
                const originalPlayer = samplePlayers.find(p => p.id === player.id);
                if (originalPlayer && !availablePlayers.find(p => p.id === player.id)) {
                    availablePlayers.push(originalPlayer);
                }
            });

            // Load formation
            currentFormation = formation.formation;
            document.getElementById('formationSelect').value = currentFormation;

            playersOnField = [...formation.players];

            // Remove loaded players from available list
            formation.players.forEach(player => {
                availablePlayers = availablePlayers.filter(p => p.id !== player.id);
            });

            setupFormation(currentFormation);
            updateAvailablePlayersList();
            updateFormationStats();
            saveFormationData();

            closeModal();
            showNotification(`تم تحميل التشكيلة "${formation.name}" بنجاح`, 'success');
        }

        // Close Modal
        function closeModal(button) {
            const modal = button ? button.closest('.modal-overlay') : document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        }

        // Tab Management
        function showMainTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all buttons
            document.querySelectorAll('.main-tab').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName + 'Tab').classList.add('active');

            // Add active class to clicked button
            document.getElementById(tabName + 'TabBtn').classList.add('active');

            // Initialize tab-specific content
            if (tabName === 'formations') {
                initializeFormationSystem();
            } else if (tabName === 'approvals') {
                initializeApprovalSystem();
            } else if (tabName === 'statistics') {
                initializeStatisticsSystem();
            }
        }

        // Load Matches Data
        function loadMatches() {
            const savedMatches = localStorage.getItem('academy_matches');
            if (savedMatches) {
                matches = JSON.parse(savedMatches);
            } else {
                matches = sampleMatches;
                saveMatches();
            }
        }

        // Save Matches Data
        function saveMatches() {
            localStorage.setItem('academy_matches', JSON.stringify(matches));
        }

        // Update Matches Display
        function updateMatchesDisplay() {
            updateUpcomingMatches();
            updateRecentMatches();
        }

        // Update Upcoming Matches
        function updateUpcomingMatches() {
            const container = document.getElementById('upcomingMatches');
            const upcoming = matches.filter(match => 
                match.status === 'scheduled' && new Date(match.date) >= new Date()
            ).sort((a, b) => new Date(a.date) - new Date(b.date));

            if (upcoming.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-400">لا توجد مباريات قادمة</p>';
                return;
            }

            container.innerHTML = upcoming.map(match => createMatchCard(match)).join('');
        }

        // Update Recent Matches
        function updateRecentMatches() {
            const container = document.getElementById('recentMatches');
            const recent = matches.filter(match => 
                match.status === 'finished' || new Date(match.date) < new Date()
            ).sort((a, b) => new Date(b.date) - new Date(a.date)).slice(0, 5);

            if (recent.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-400">لا توجد مباريات سابقة</p>';
                return;
            }

            container.innerHTML = recent.map(match => createMatchCard(match)).join('');
        }

        // Create Match Card HTML
        function createMatchCard(match) {
            const statusText = getStatusText(match.status);
            const statusClass = `status-${match.status}`;
            
            return `
                <div class="match-card">
                    <div class="match-header">
                        <div class="match-teams">
                            <span>${match.homeTeam}</span>
                            ${match.status === 'finished' ? 
                                `<span class="match-score">${match.homeScore} - ${match.awayScore}</span>` :
                                '<span style="color: #D2691E;">VS</span>'
                            }
                            <span>${match.awayTeam}</span>
                        </div>
                        <span class="status-badge ${statusClass}">${statusText}</span>
                    </div>
                    
                    <div class="match-info">
                        <div class="match-info-item">
                            <div class="match-info-label">التاريخ</div>
                            <div class="match-info-value">${formatDate(match.date)}</div>
                        </div>
                        <div class="match-info-item">
                            <div class="match-info-label">الوقت</div>
                            <div class="match-info-value">${match.time}</div>
                        </div>
                        <div class="match-info-item">
                            <div class="match-info-label">المكان</div>
                            <div class="match-info-value">${match.venue}</div>
                        </div>
                        <div class="match-info-item">
                            <div class="match-info-label">النوع</div>
                            <div class="match-info-value">${match.type}</div>
                        </div>
                        <div class="match-info-item">
                            <div class="match-info-label">الفئة</div>
                            <div class="match-info-value">${match.category}</div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 1rem; display: flex; gap: 0.5rem; justify-content: flex-end;">
                        ${match.status === 'scheduled' ? `
                            <button class="btn btn-small btn-success" onclick="startMatch(${match.id})">
                                <i class="fas fa-play"></i>
                                بدء المباراة
                            </button>
                            <button class="btn btn-small" onclick="editMatch(${match.id})">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </button>
                        ` : ''}
                        <button class="btn btn-small btn-secondary" onclick="viewMatchDetails(${match.id})">
                            <i class="fas fa-eye"></i>
                            التفاصيل
                        </button>
                    </div>
                </div>
            `;
        }

        // Utility Functions
        function getStatusText(status) {
            const statusMap = {
                'scheduled': 'مجدولة',
                'live': 'مباشرة',
                'finished': 'منتهية',
                'cancelled': 'ملغية'
            };
            return statusMap[status] || 'غير محدد';
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // ==================== Real Match Management Functions ====================

        // Open New Match Modal
        function openNewMatchModal() {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 700px; max-height: 90vh; overflow-y: auto;">
                    <div class="modal-header">
                        <h3><i class="fas fa-plus-circle"></i> إضافة مباراة جديدة</h3>
                        <button class="modal-close" onclick="closeModal(this)">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="newMatchForm" onsubmit="saveNewMatch(event)">
                            <div class="grid-2 gap-4">
                                <div class="form-group">
                                    <label>الفريق المضيف *</label>
                                    <input type="text" id="homeTeam" required placeholder="أكاديمية 7C - تحت 16" style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                </div>
                                <div class="form-group">
                                    <label>الفريق الضيف *</label>
                                    <input type="text" id="awayTeam" required placeholder="نادي الشباب - تحت 16" style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                </div>
                            </div>

                            <div class="grid-2 gap-4">
                                <div class="form-group">
                                    <label>التاريخ *</label>
                                    <input type="date" id="matchDate" required style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                </div>
                                <div class="form-group">
                                    <label>الوقت *</label>
                                    <input type="time" id="matchTime" required style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                </div>
                            </div>

                            <div class="form-group">
                                <label>مكان المباراة *</label>
                                <input type="text" id="matchVenue" required placeholder="ملعب الأكاديمية الرئيسي" style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                            </div>

                            <div class="grid-2 gap-4">
                                <div class="form-group">
                                    <label>نوع المباراة *</label>
                                    <select id="matchType" required style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                        <option value="">اختر نوع المباراة</option>
                                        <option value="ودية">مباراة ودية</option>
                                        <option value="رسمية">مباراة رسمية</option>
                                        <option value="بطولة داخلية">بطولة داخلية</option>
                                        <option value="بطولة خارجية">بطولة خارجية</option>
                                        <option value="كأس">مباراة كأس</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>الفئة العمرية *</label>
                                    <select id="matchCategory" required style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                        <option value="">اختر الفئة العمرية</option>
                                        <option value="تحت 12">تحت 12 سنة</option>
                                        <option value="تحت 14">تحت 14 سنة</option>
                                        <option value="تحت 16">تحت 16 سنة</option>
                                        <option value="تحت 18">تحت 18 سنة</option>
                                        <option value="الكبار">الكبار</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>ملاحظات إضافية</label>
                                <textarea id="matchNotes" placeholder="أضف أي ملاحظات خاصة بالمباراة..." style="width: 100%; height: 100px; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3); resize: vertical;"></textarea>
                            </div>

                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="sendNotifications" checked style="margin-left: 0.5rem;">
                                    إرسال إشعارات للاعبين وأولياء الأمور
                                </label>
                            </div>

                            <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                                <button type="button" class="btn btn-secondary" onclick="closeModal(this)">إلغاء</button>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i>
                                    حفظ المباراة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // Set default date to tomorrow
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            document.getElementById('matchDate').value = tomorrow.toISOString().split('T')[0];

            // Set default time
            document.getElementById('matchTime').value = '17:00';
        }

        // Save New Match
        function saveNewMatch(event) {
            event.preventDefault();

            const formData = {
                homeTeam: document.getElementById('homeTeam').value.trim(),
                awayTeam: document.getElementById('awayTeam').value.trim(),
                date: document.getElementById('matchDate').value,
                time: document.getElementById('matchTime').value,
                venue: document.getElementById('matchVenue').value.trim(),
                type: document.getElementById('matchType').value,
                category: document.getElementById('matchCategory').value,
                notes: document.getElementById('matchNotes').value.trim(),
                sendNotifications: document.getElementById('sendNotifications').checked
            };

            // Validation
            if (!formData.homeTeam || !formData.awayTeam || !formData.date ||
                !formData.time || !formData.venue || !formData.type || !formData.category) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            // Check if date is not in the past
            const matchDateTime = new Date(`${formData.date}T${formData.time}`);
            if (matchDateTime <= new Date()) {
                showNotification('لا يمكن جدولة مباراة في الماضي', 'error');
                return;
            }

            // Create new match object
            const newMatch = {
                id: Date.now(),
                homeTeam: formData.homeTeam,
                awayTeam: formData.awayTeam,
                date: formData.date,
                time: formData.time,
                venue: formData.venue,
                type: formData.type,
                category: formData.category,
                notes: formData.notes,
                status: 'scheduled',
                homeScore: null,
                awayScore: null,
                createdAt: new Date().toISOString(),
                createdBy: 'المدرب الحالي' // In real app, this would be the logged-in user
            };

            // Add to matches array
            matches.push(newMatch);

            // Save to localStorage
            saveMatches();

            // Update displays
            updateMatchesDisplay();
            updateLiveMatchSelect();
            updateAnalysisMatchSelect();
            updateMatchesForApproval();

            // Send notifications if requested
            if (formData.sendNotifications) {
                setTimeout(() => {
                    sendMatchNotifications(newMatch);
                }, 500);
            }

            // Close modal and show success
            closeModal();
            showNotification(`تم إنشاء المباراة بنجاح: ${formData.homeTeam} vs ${formData.awayTeam}`, 'success');
        }

        // Send Match Notifications
        function sendMatchNotifications(match) {
            // In a real application, this would integrate with actual notification services
            const message = `🏃‍♂️ أكاديمية 7C للتدريب الرياضي

تم جدولة مباراة جديدة:

🆚 ${match.homeTeam} vs ${match.awayTeam}
📅 التاريخ: ${formatDate(match.date)}
⏰ الوقت: ${match.time}
📍 المكان: ${match.venue}
🏆 النوع: ${match.type}
👥 الفئة: ${match.category}

${match.notes ? `📝 ملاحظات: ${match.notes}` : ''}

سيتم إرسال تفاصيل التشكيلة والموافقات قريباً.`;

            console.log('Notification sent:', message);
            showNotification('تم إرسال الإشعارات للاعبين وأولياء الأمور', 'info');
        }

        // Edit Match
        function editMatch(matchId) {
            const match = matches.find(m => m.id === matchId);
            if (!match) {
                showNotification('المباراة غير موجودة', 'error');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 700px; max-height: 90vh; overflow-y: auto;">
                    <div class="modal-header">
                        <h3><i class="fas fa-edit"></i> تعديل المباراة</h3>
                        <button class="modal-close" onclick="closeModal(this)">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="editMatchForm" onsubmit="saveMatchEdit(event, ${matchId})">
                            <div class="grid-2 gap-4">
                                <div class="form-group">
                                    <label>الفريق المضيف *</label>
                                    <input type="text" id="editHomeTeam" required value="${match.homeTeam}" style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                </div>
                                <div class="form-group">
                                    <label>الفريق الضيف *</label>
                                    <input type="text" id="editAwayTeam" required value="${match.awayTeam}" style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                </div>
                            </div>

                            <div class="grid-2 gap-4">
                                <div class="form-group">
                                    <label>التاريخ *</label>
                                    <input type="date" id="editMatchDate" required value="${match.date}" style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                </div>
                                <div class="form-group">
                                    <label>الوقت *</label>
                                    <input type="time" id="editMatchTime" required value="${match.time}" style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                </div>
                            </div>

                            <div class="form-group">
                                <label>مكان المباراة *</label>
                                <input type="text" id="editMatchVenue" required value="${match.venue}" style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                            </div>

                            <div class="grid-2 gap-4">
                                <div class="form-group">
                                    <label>نوع المباراة *</label>
                                    <select id="editMatchType" required style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                        <option value="ودية" ${match.type === 'ودية' ? 'selected' : ''}>مباراة ودية</option>
                                        <option value="رسمية" ${match.type === 'رسمية' ? 'selected' : ''}>مباراة رسمية</option>
                                        <option value="بطولة داخلية" ${match.type === 'بطولة داخلية' ? 'selected' : ''}>بطولة داخلية</option>
                                        <option value="بطولة خارجية" ${match.type === 'بطولة خارجية' ? 'selected' : ''}>بطولة خارجية</option>
                                        <option value="كأس" ${match.type === 'كأس' ? 'selected' : ''}>مباراة كأس</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>الفئة العمرية *</label>
                                    <select id="editMatchCategory" required style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                        <option value="تحت 12" ${match.category === 'تحت 12' ? 'selected' : ''}>تحت 12 سنة</option>
                                        <option value="تحت 14" ${match.category === 'تحت 14' ? 'selected' : ''}>تحت 14 سنة</option>
                                        <option value="تحت 16" ${match.category === 'تحت 16' ? 'selected' : ''}>تحت 16 سنة</option>
                                        <option value="تحت 18" ${match.category === 'تحت 18' ? 'selected' : ''}>تحت 18 سنة</option>
                                        <option value="الكبار" ${match.category === 'الكبار' ? 'selected' : ''}>الكبار</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>حالة المباراة</label>
                                <select id="editMatchStatus" style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                    <option value="scheduled" ${match.status === 'scheduled' ? 'selected' : ''}>مجدولة</option>
                                    <option value="live" ${match.status === 'live' ? 'selected' : ''}>مباشرة</option>
                                    <option value="finished" ${match.status === 'finished' ? 'selected' : ''}>منتهية</option>
                                    <option value="cancelled" ${match.status === 'cancelled' ? 'selected' : ''}>ملغية</option>
                                </select>
                            </div>

                            ${match.status === 'finished' ? `
                                <div class="grid-2 gap-4">
                                    <div class="form-group">
                                        <label>أهداف الفريق المضيف</label>
                                        <input type="number" id="editHomeScore" min="0" value="${match.homeScore || 0}" style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                    </div>
                                    <div class="form-group">
                                        <label>أهداف الفريق الضيف</label>
                                        <input type="number" id="editAwayScore" min="0" value="${match.awayScore || 0}" style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                    </div>
                                </div>
                            ` : ''}

                            <div class="form-group">
                                <label>ملاحظات إضافية</label>
                                <textarea id="editMatchNotes" style="width: 100%; height: 100px; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3); resize: vertical;">${match.notes || ''}</textarea>
                            </div>

                            <div style="display: flex; gap: 1rem; justify-content: space-between; margin-top: 2rem;">
                                <button type="button" class="btn btn-danger" onclick="confirmDeleteMatch(${matchId})">
                                    <i class="fas fa-trash"></i>
                                    حذف المباراة
                                </button>
                                <div style="display: flex; gap: 1rem;">
                                    <button type="button" class="btn btn-secondary" onclick="closeModal(this)">إلغاء</button>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save"></i>
                                        حفظ التعديلات
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // Add event listener for status change
            document.getElementById('editMatchStatus').addEventListener('change', function() {
                if (this.value === 'finished' && !document.getElementById('editHomeScore')) {
                    // Reload modal with score fields
                    closeModal();
                    setTimeout(() => editMatch(matchId), 100);
                }
            });
        }

        // Save Match Edit
        function saveMatchEdit(event, matchId) {
            event.preventDefault();

            const match = matches.find(m => m.id === matchId);
            if (!match) {
                showNotification('المباراة غير موجودة', 'error');
                return;
            }

            const formData = {
                homeTeam: document.getElementById('editHomeTeam').value.trim(),
                awayTeam: document.getElementById('editAwayTeam').value.trim(),
                date: document.getElementById('editMatchDate').value,
                time: document.getElementById('editMatchTime').value,
                venue: document.getElementById('editMatchVenue').value.trim(),
                type: document.getElementById('editMatchType').value,
                category: document.getElementById('editMatchCategory').value,
                status: document.getElementById('editMatchStatus').value,
                notes: document.getElementById('editMatchNotes').value.trim()
            };

            // Validation
            if (!formData.homeTeam || !formData.awayTeam || !formData.date ||
                !formData.time || !formData.venue || !formData.type || !formData.category) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            // Update match object
            match.homeTeam = formData.homeTeam;
            match.awayTeam = formData.awayTeam;
            match.date = formData.date;
            match.time = formData.time;
            match.venue = formData.venue;
            match.type = formData.type;
            match.category = formData.category;
            match.status = formData.status;
            match.notes = formData.notes;
            match.updatedAt = new Date().toISOString();

            // Update scores if finished
            if (formData.status === 'finished') {
                const homeScore = document.getElementById('editHomeScore');
                const awayScore = document.getElementById('editAwayScore');
                if (homeScore && awayScore) {
                    match.homeScore = parseInt(homeScore.value) || 0;
                    match.awayScore = parseInt(awayScore.value) || 0;
                }
            }

            // Save to localStorage
            saveMatches();

            // Update displays
            updateMatchesDisplay();
            updateLiveMatchSelect();
            updateAnalysisMatchSelect();
            updateMatchesForApproval();

            // Close modal and show success
            closeModal();
            showNotification('تم حفظ التعديلات بنجاح', 'success');
        }

        // Confirm Delete Match
        function confirmDeleteMatch(matchId) {
            const match = matches.find(m => m.id === matchId);
            if (!match) {
                showNotification('المباراة غير موجودة', 'error');
                return;
            }

            const confirmMessage = `هل أنت متأكد من حذف هذه المباراة؟

${match.homeTeam} vs ${match.awayTeam}
${formatDate(match.date)} - ${match.time}

⚠️ تحذير: سيتم حذف جميع البيانات المرتبطة بهذه المباراة بما في ذلك:
• الإحصائيات والأحداث
• طلبات الموافقة
• التشكيلات المحفوظة

هذا الإجراء لا يمكن التراجع عنه.`;

            if (confirm(confirmMessage)) {
                deleteMatch(matchId);
            }
        }

        // Delete Match
        function deleteMatch(matchId) {
            const matchIndex = matches.findIndex(m => m.id === matchId);
            if (matchIndex === -1) {
                showNotification('المباراة غير موجودة', 'error');
                return;
            }

            const match = matches[matchIndex];

            // Create backup before deletion
            const backup = {
                match: { ...match },
                deletedAt: new Date().toISOString(),
                deletedBy: 'المدرب الحالي'
            };

            // Save backup
            let deletedMatches = JSON.parse(localStorage.getItem('deleted_matches') || '[]');
            deletedMatches.push(backup);
            localStorage.setItem('deleted_matches', JSON.stringify(deletedMatches));

            // Remove from matches array
            matches.splice(matchIndex, 1);

            // Remove related data
            delete teamStats.byMatch[matchId];
            matchEvents = matchEvents.filter(event => event.matchId !== matchId);
            approvalRequests = approvalRequests.filter(req => req.matchId !== matchId);

            // Save all data
            saveMatches();
            saveStatisticsData();
            saveApprovalData();

            // Update displays
            updateMatchesDisplay();
            updateLiveMatchSelect();
            updateAnalysisMatchSelect();
            updateMatchesForApproval();

            // Close modal and show success
            closeModal();
            showNotification(`تم حذف المباراة: ${match.homeTeam} vs ${match.awayTeam}`, 'success');
        }

        // View Match Details
        function viewMatchDetails(matchId) {
            const match = matches.find(m => m.id === matchId);
            if (!match) {
                showNotification('المباراة غير موجودة', 'error');
                return;
            }

            const matchStats = teamStats.byMatch[matchId];
            const matchApprovals = approvalRequests.filter(req => req.matchId === matchId);
            const matchEventsData = matchEvents.filter(event => event.matchId === matchId);

            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 800px; max-height: 90vh; overflow-y: auto;">
                    <div class="modal-header">
                        <h3><i class="fas fa-info-circle"></i> تفاصيل المباراة</h3>
                        <button class="modal-close" onclick="closeModal(this)">&times;</button>
                    </div>
                    <div class="modal-body">
                        <!-- Match Info -->
                        <div class="card" style="background: rgba(255,255,255,0.05); margin-bottom: 1.5rem;">
                            <div style="text-align: center; padding: 1.5rem;">
                                <h2 style="margin-bottom: 1rem; color: #D2691E;">${match.homeTeam} vs ${match.awayTeam}</h2>
                                ${match.status === 'finished' && match.homeScore !== null ?
                                    `<div style="font-size: 3rem; font-weight: bold; color: #10B981; margin: 1rem 0;">${match.homeScore} - ${match.awayScore}</div>` :
                                    `<div style="font-size: 1.5rem; color: #F59E0B; margin: 1rem 0;">VS</div>`
                                }
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1.5rem;">
                                    <div>
                                        <div style="font-size: 0.9rem; opacity: 0.7;">التاريخ</div>
                                        <div style="font-weight: bold;">${formatDate(match.date)}</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 0.9rem; opacity: 0.7;">الوقت</div>
                                        <div style="font-weight: bold;">${match.time}</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 0.9rem; opacity: 0.7;">المكان</div>
                                        <div style="font-weight: bold;">${match.venue}</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 0.9rem; opacity: 0.7;">النوع</div>
                                        <div style="font-weight: bold;">${match.type}</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 0.9rem; opacity: 0.7;">الفئة</div>
                                        <div style="font-weight: bold;">${match.category}</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 0.9rem; opacity: 0.7;">الحالة</div>
                                        <div><span class="status-badge status-${match.status}">${getStatusText(match.status)}</span></div>
                                    </div>
                                </div>
                                ${match.notes ? `
                                    <div style="margin-top: 1.5rem; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                                        <div style="font-size: 0.9rem; opacity: 0.7; margin-bottom: 0.5rem;">ملاحظات</div>
                                        <div>${match.notes}</div>
                                    </div>
                                ` : ''}
                            </div>
                        </div>

                        <!-- Statistics -->
                        ${matchStats ? `
                            <div class="card" style="background: rgba(255,255,255,0.05); margin-bottom: 1.5rem;">
                                <h4 style="margin-bottom: 1rem; color: #D2691E;"><i class="fas fa-chart-bar"></i> إحصائيات المباراة</h4>
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
                                    <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                                        <div style="font-size: 1.5rem; font-weight: bold; color: #10B981;">${matchStats.possession}%</div>
                                        <div style="font-size: 0.9rem; opacity: 0.8;">الاستحواذ</div>
                                    </div>
                                    <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                                        <div style="font-size: 1.5rem; font-weight: bold; color: #3B82F6;">${matchStats.shots}</div>
                                        <div style="font-size: 0.9rem; opacity: 0.8;">التسديدات</div>
                                    </div>
                                    <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                                        <div style="font-size: 1.5rem; font-weight: bold; color: #F59E0B;">${matchStats.corners}</div>
                                        <div style="font-size: 0.9rem; opacity: 0.8;">الركنيات</div>
                                    </div>
                                    <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                                        <div style="font-size: 1.5rem; font-weight: bold; color: #EF4444;">${matchStats.yellowCards + matchStats.redCards}</div>
                                        <div style="font-size: 0.9rem; opacity: 0.8;">البطاقات</div>
                                    </div>
                                </div>
                            </div>
                        ` : ''}

                        <!-- Events -->
                        ${matchEventsData.length > 0 ? `
                            <div class="card" style="background: rgba(255,255,255,0.05); margin-bottom: 1.5rem;">
                                <h4 style="margin-bottom: 1rem; color: #D2691E;"><i class="fas fa-timeline"></i> أحداث المباراة</h4>
                                <div style="max-height: 300px; overflow-y: auto;">
                                    ${matchEventsData.sort((a, b) => a.minute - b.minute).map(event => `
                                        <div class="event-item ${event.type}" style="margin-bottom: 0.5rem;">
                                            <div class="event-time">${event.minute}'</div>
                                            <div class="event-icon">${getEventIcon(event.type)}</div>
                                            <div class="event-description">
                                                <div style="font-weight: bold;">${getEventName(event.type)}</div>
                                                <div style="font-size: 0.9rem; opacity: 0.8;">${getEventDescription(event)}</div>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}

                        <!-- Approvals -->
                        ${matchApprovals.length > 0 ? `
                            <div class="card" style="background: rgba(255,255,255,0.05); margin-bottom: 1.5rem;">
                                <h4 style="margin-bottom: 1rem; color: #D2691E;"><i class="fas fa-check-circle"></i> حالة الموافقات</h4>
                                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 1rem; margin-bottom: 1rem;">
                                    <div style="text-align: center; padding: 1rem; background: rgba(16, 185, 129, 0.1); border-radius: 8px;">
                                        <div style="font-size: 1.5rem; font-weight: bold; color: #10B981;">${matchApprovals.filter(a => a.status === 'approved').length}</div>
                                        <div style="font-size: 0.9rem; opacity: 0.8;">موافق</div>
                                    </div>
                                    <div style="text-align: center; padding: 1rem; background: rgba(245, 158, 11, 0.1); border-radius: 8px;">
                                        <div style="font-size: 1.5rem; font-weight: bold; color: #F59E0B;">${matchApprovals.filter(a => a.status === 'pending').length}</div>
                                        <div style="font-size: 0.9rem; opacity: 0.8;">في الانتظار</div>
                                    </div>
                                    <div style="text-align: center; padding: 1rem; background: rgba(239, 68, 68, 0.1); border-radius: 8px;">
                                        <div style="font-size: 1.5rem; font-weight: bold; color: #EF4444;">${matchApprovals.filter(a => a.status === 'rejected').length}</div>
                                        <div style="font-size: 0.9rem; opacity: 0.8;">مرفوض</div>
                                    </div>
                                    <div style="text-align: center; padding: 1rem; background: rgba(107, 114, 128, 0.1); border-radius: 8px;">
                                        <div style="font-size: 1.5rem; font-weight: bold; color: #6B7280;">${matchApprovals.filter(a => a.status === 'pending' && new Date(a.expiresAt) <= new Date()).length}</div>
                                        <div style="font-size: 0.9rem; opacity: 0.8;">منتهي</div>
                                    </div>
                                </div>
                            </div>
                        ` : ''}

                        <!-- Actions -->
                        <div style="display: flex; gap: 1rem; justify-content: center; margin-top: 2rem;">
                            ${match.status === 'scheduled' ? `
                                <button class="btn btn-success" onclick="startMatchFromDetails(${matchId})">
                                    <i class="fas fa-play"></i>
                                    بدء المباراة
                                </button>
                            ` : ''}
                            <button class="btn btn-secondary" onclick="editMatch(${matchId}); closeModal();">
                                <i class="fas fa-edit"></i>
                                تعديل المباراة
                            </button>
                            ${match.status === 'finished' ? `
                                <button class="btn" onclick="generateMatchReport(${matchId})">
                                    <i class="fas fa-file-alt"></i>
                                    تقرير JSON
                                </button>
                                <button class="btn btn-warning" onclick="exportMatchStatisticsPDF(${matchId})">
                                    <i class="fas fa-file-pdf"></i>
                                    تقرير HTML
                                </button>
                            ` : ''}
                            <button class="btn btn-warning" onclick="exportMatchData(${matchId})">
                                <i class="fas fa-download"></i>
                                تصدير البيانات
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // Start Match
        function startMatch(matchId) {
            const match = matches.find(m => m.id === matchId);
            if (!match) {
                showNotification('المباراة غير موجودة', 'error');
                return;
            }

            if (match.status !== 'scheduled') {
                showNotification('لا يمكن بدء هذه المباراة - الحالة غير صحيحة', 'warning');
                return;
            }

            // Switch to statistics tab and start live tracking
            showMainTab('statistics');

            // Set the match for live tracking
            document.getElementById('liveMatchSelect').value = matchId;
            selectLiveMatch();

            // Start live tracking
            setTimeout(() => {
                startLiveTracking();
            }, 500);

            showNotification(`تم بدء المباراة: ${match.homeTeam} vs ${match.awayTeam}`, 'success');
        }

        // Start Match from Details Modal
        function startMatchFromDetails(matchId) {
            closeModal();
            startMatch(matchId);
        }

        // Open Schedule Modal
        function openScheduleModal() {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 900px; max-height: 90vh; overflow-y: auto;">
                    <div class="modal-header">
                        <h3><i class="fas fa-calendar-plus"></i> جدولة مباريات متعددة</h3>
                        <button class="modal-close" onclick="closeModal(this)">&times;</button>
                    </div>
                    <div class="modal-body">
                        <!-- Quick Schedule Templates -->
                        <div class="card" style="background: rgba(255,255,255,0.05); margin-bottom: 1.5rem;">
                            <h4 style="margin-bottom: 1rem; color: #D2691E;"><i class="fas fa-magic"></i> قوالب الجدولة السريعة</h4>
                            <div class="grid-3 gap-4">
                                <button class="btn btn-secondary" onclick="createWeeklySchedule()">
                                    <i class="fas fa-calendar-week"></i>
                                    جدولة أسبوعية
                                </button>
                                <button class="btn btn-secondary" onclick="createTournamentSchedule()">
                                    <i class="fas fa-trophy"></i>
                                    جدولة بطولة
                                </button>
                                <button class="btn btn-secondary" onclick="createFriendlySchedule()">
                                    <i class="fas fa-handshake"></i>
                                    مباريات ودية
                                </button>
                            </div>
                        </div>

                        <!-- Manual Schedule Form -->
                        <form id="scheduleForm" onsubmit="saveSchedule(event)">
                            <div class="grid-2 gap-4">
                                <div class="form-group">
                                    <label>نوع الجدولة *</label>
                                    <select id="scheduleType" required onchange="updateScheduleForm()" style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                        <option value="">اختر نوع الجدولة</option>
                                        <option value="single">مباراة واحدة</option>
                                        <option value="weekly">جدولة أسبوعية</option>
                                        <option value="tournament">بطولة</option>
                                        <option value="league">دوري</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>الفئة العمرية *</label>
                                    <select id="scheduleCategory" required style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                        <option value="">اختر الفئة العمرية</option>
                                        <option value="تحت 12">تحت 12 سنة</option>
                                        <option value="تحت 14">تحت 14 سنة</option>
                                        <option value="تحت 16">تحت 16 سنة</option>
                                        <option value="تحت 18">تحت 18 سنة</option>
                                        <option value="الكبار">الكبار</option>
                                    </select>
                                </div>
                            </div>

                            <div id="scheduleDetails" style="display: none;">
                                <!-- Will be populated based on schedule type -->
                            </div>

                            <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                                <button type="button" class="btn btn-secondary" onclick="closeModal(this)">إلغاء</button>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-calendar-check"></i>
                                    إنشاء الجدولة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // Update Schedule Form
        function updateScheduleForm() {
            const scheduleType = document.getElementById('scheduleType').value;
            const detailsContainer = document.getElementById('scheduleDetails');

            if (!scheduleType) {
                detailsContainer.style.display = 'none';
                return;
            }

            let detailsHTML = '';

            switch(scheduleType) {
                case 'weekly':
                    detailsHTML = `
                        <div class="grid-2 gap-4">
                            <div class="form-group">
                                <label>تاريخ البداية *</label>
                                <input type="date" id="startDate" required style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                            </div>
                            <div class="form-group">
                                <label>عدد الأسابيع *</label>
                                <input type="number" id="weeksCount" min="1" max="20" value="4" required style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                            </div>
                        </div>
                        <div class="form-group">
                            <label>أيام المباريات</label>
                            <div style="display: grid; grid-template-columns: repeat(7, 1fr); gap: 0.5rem; margin-top: 0.5rem;">
                                <label style="display: flex; align-items: center; gap: 0.5rem;"><input type="checkbox" value="0"> الأحد</label>
                                <label style="display: flex; align-items: center; gap: 0.5rem;"><input type="checkbox" value="1"> الاثنين</label>
                                <label style="display: flex; align-items: center; gap: 0.5rem;"><input type="checkbox" value="2"> الثلاثاء</label>
                                <label style="display: flex; align-items: center; gap: 0.5rem;"><input type="checkbox" value="3"> الأربعاء</label>
                                <label style="display: flex; align-items: center; gap: 0.5rem;"><input type="checkbox" value="4"> الخميس</label>
                                <label style="display: flex; align-items: center; gap: 0.5rem;"><input type="checkbox" value="5" checked> الجمعة</label>
                                <label style="display: flex; align-items: center; gap: 0.5rem;"><input type="checkbox" value="6" checked> السبت</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>وقت المباراة *</label>
                            <input type="time" id="matchTime" value="17:00" required style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                        </div>
                    `;
                    break;
                case 'tournament':
                    detailsHTML = `
                        <div class="grid-2 gap-4">
                            <div class="form-group">
                                <label>اسم البطولة *</label>
                                <input type="text" id="tournamentName" required placeholder="بطولة أكاديمية 7C" style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                            </div>
                            <div class="form-group">
                                <label>عدد الفرق *</label>
                                <select id="teamsCount" required style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                    <option value="4">4 فرق</option>
                                    <option value="8">8 فرق</option>
                                    <option value="16">16 فريق</option>
                                </select>
                            </div>
                        </div>
                        <div class="grid-2 gap-4">
                            <div class="form-group">
                                <label>تاريخ البداية *</label>
                                <input type="date" id="tournamentStartDate" required style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                            </div>
                            <div class="form-group">
                                <label>نوع البطولة *</label>
                                <select id="tournamentType" required style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                    <option value="knockout">خروج المغلوب</option>
                                    <option value="league">دوري</option>
                                    <option value="groups">مجموعات</option>
                                </select>
                            </div>
                        </div>
                    `;
                    break;
                default:
                    detailsHTML = `
                        <div class="form-group">
                            <label>تفاصيل إضافية</label>
                            <textarea placeholder="أضف تفاصيل الجدولة..." style="width: 100%; height: 100px; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3); resize: vertical;"></textarea>
                        </div>
                    `;
            }

            detailsContainer.innerHTML = detailsHTML;
            detailsContainer.style.display = 'block';

            // Set default dates
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const startDateInput = document.getElementById('startDate') || document.getElementById('tournamentStartDate');
            if (startDateInput) {
                startDateInput.value = tomorrow.toISOString().split('T')[0];
            }
        }

        // Export Matches Report
        function exportMatchesReport() {
            const reportData = {
                generatedAt: new Date().toISOString(),
                generatedBy: 'نظام إدارة المباريات - أكاديمية 7C',
                summary: {
                    totalMatches: matches.length,
                    scheduledMatches: matches.filter(m => m.status === 'scheduled').length,
                    finishedMatches: matches.filter(m => m.status === 'finished').length,
                    liveMatches: matches.filter(m => m.status === 'live').length,
                    cancelledMatches: matches.filter(m => m.status === 'cancelled').length
                },
                matches: matches.map(match => ({
                    ...match,
                    statistics: teamStats.byMatch[match.id] || null,
                    events: matchEvents.filter(e => e.matchId === match.id),
                    approvals: approvalRequests.filter(a => a.matchId === match.id)
                })),
                categories: [...new Set(matches.map(m => m.category))],
                venues: [...new Set(matches.map(m => m.venue))],
                matchTypes: [...new Set(matches.map(m => m.type))]
            };

            // Export as JSON
            const dataStr = JSON.stringify(reportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `matches_report_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('تم تصدير تقرير المباريات بنجاح!', 'success');
        }

        // Export Match Data
        function exportMatchData(matchId) {
            const match = matches.find(m => m.id === matchId);
            if (!match) {
                showNotification('المباراة غير موجودة', 'error');
                return;
            }

            const matchData = {
                match: match,
                statistics: teamStats.byMatch[matchId] || null,
                events: matchEvents.filter(e => e.matchId === matchId),
                approvals: approvalRequests.filter(a => a.matchId === matchId),
                exportedAt: new Date().toISOString()
            };

            const dataStr = JSON.stringify(matchData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `match_${match.homeTeam.replace(/\s+/g, '_')}_vs_${match.awayTeam.replace(/\s+/g, '_')}_${match.date}.json`;
            link.click();

            showNotification('تم تصدير بيانات المباراة بنجاح!', 'success');
        }

        // Refresh Matches
        function refreshMatches() {
            // Reload from localStorage
            loadMatches();

            // Update all displays
            updateMatchesDisplay();
            updateLiveMatchSelect();
            updateAnalysisMatchSelect();
            updateMatchesForApproval();

            // Update statistics
            updateApprovalStats();

            showNotification('تم تحديث جميع بيانات المباريات', 'success');
        }

        // Save Schedule
        function saveSchedule(event) {
            event.preventDefault();

            const scheduleType = document.getElementById('scheduleType').value;
            const category = document.getElementById('scheduleCategory').value;

            if (!scheduleType || !category) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            let newMatches = [];

            switch(scheduleType) {
                case 'weekly':
                    newMatches = createWeeklyMatches();
                    break;
                case 'tournament':
                    newMatches = createTournamentMatches();
                    break;
                default:
                    showNotification('نوع الجدولة غير مدعوم حالياً', 'warning');
                    return;
            }

            if (newMatches.length === 0) {
                showNotification('لم يتم إنشاء أي مباريات', 'warning');
                return;
            }

            // Add to matches array
            matches.push(...newMatches);

            // Save to localStorage
            saveMatches();

            // Update displays
            updateMatchesDisplay();
            updateLiveMatchSelect();
            updateAnalysisMatchSelect();
            updateMatchesForApproval();

            // Close modal and show success
            closeModal();
            showNotification(`تم إنشاء ${newMatches.length} مباراة بنجاح`, 'success');
        }

        // Create Weekly Matches
        function createWeeklyMatches() {
            const startDate = new Date(document.getElementById('startDate').value);
            const weeksCount = parseInt(document.getElementById('weeksCount').value);
            const matchTime = document.getElementById('matchTime').value;
            const category = document.getElementById('scheduleCategory').value;

            const selectedDays = Array.from(document.querySelectorAll('#scheduleDetails input[type="checkbox"]:checked'))
                .map(cb => parseInt(cb.value));

            if (selectedDays.length === 0) {
                showNotification('يرجى اختيار يوم واحد على الأقل', 'warning');
                return [];
            }

            const newMatches = [];
            const opponents = [
                'نادي الشباب',
                'نادي الهلال',
                'نادي النصر',
                'نادي الاتحاد',
                'نادي الأهلي',
                'نادي التعاون',
                'نادي الفيصلي',
                'نادي الرائد'
            ];

            for (let week = 0; week < weeksCount; week++) {
                selectedDays.forEach(dayOfWeek => {
                    const matchDate = new Date(startDate);
                    matchDate.setDate(startDate.getDate() + (week * 7) + (dayOfWeek - startDate.getDay()));

                    if (matchDate >= new Date()) { // Only future dates
                        const opponent = opponents[Math.floor(Math.random() * opponents.length)];

                        newMatches.push({
                            id: Date.now() + Math.random(),
                            homeTeam: `أكاديمية 7C - ${category}`,
                            awayTeam: `${opponent} - ${category}`,
                            date: matchDate.toISOString().split('T')[0],
                            time: matchTime,
                            venue: 'ملعب الأكاديمية الرئيسي',
                            type: 'ودية',
                            category: category,
                            notes: `مباراة من الجدولة الأسبوعية - الأسبوع ${week + 1}`,
                            status: 'scheduled',
                            homeScore: null,
                            awayScore: null,
                            createdAt: new Date().toISOString(),
                            createdBy: 'نظام الجدولة التلقائية'
                        });
                    }
                });
            }

            return newMatches;
        }

        // Create Tournament Matches
        function createTournamentMatches() {
            const tournamentName = document.getElementById('tournamentName').value;
            const teamsCount = parseInt(document.getElementById('teamsCount').value);
            const startDate = new Date(document.getElementById('tournamentStartDate').value);
            const tournamentType = document.getElementById('tournamentType').value;
            const category = document.getElementById('scheduleCategory').value;

            if (!tournamentName) {
                showNotification('يرجى إدخال اسم البطولة', 'warning');
                return [];
            }

            const teams = [
                `أكاديمية 7C - ${category}`,
                `نادي الشباب - ${category}`,
                `نادي الهلال - ${category}`,
                `نادي النصر - ${category}`,
                `نادي الاتحاد - ${category}`,
                `نادي الأهلي - ${category}`,
                `نادي التعاون - ${category}`,
                `نادي الفيصلي - ${category}`
            ].slice(0, teamsCount);

            const newMatches = [];
            let matchDate = new Date(startDate);

            if (tournamentType === 'knockout') {
                // Create knockout tournament
                let currentRound = teams.slice();
                let roundNumber = 1;

                while (currentRound.length > 1) {
                    const roundMatches = [];

                    for (let i = 0; i < currentRound.length; i += 2) {
                        if (i + 1 < currentRound.length) {
                            roundMatches.push({
                                id: Date.now() + Math.random(),
                                homeTeam: currentRound[i],
                                awayTeam: currentRound[i + 1],
                                date: matchDate.toISOString().split('T')[0],
                                time: '17:00',
                                venue: 'ملعب الأكاديمية الرئيسي',
                                type: 'بطولة خارجية',
                                category: category,
                                notes: `${tournamentName} - الدور ${roundNumber}`,
                                status: 'scheduled',
                                homeScore: null,
                                awayScore: null,
                                createdAt: new Date().toISOString(),
                                createdBy: 'نظام الجدولة التلقائية'
                            });

                            matchDate.setDate(matchDate.getDate() + 1);
                        }
                    }

                    newMatches.push(...roundMatches);
                    currentRound = currentRound.filter((_, index) => index % 2 === 0); // Simulate winners
                    roundNumber++;
                    matchDate.setDate(matchDate.getDate() + 2); // Gap between rounds
                }
            } else if (tournamentType === 'league') {
                // Create league tournament (everyone plays everyone)
                for (let i = 0; i < teams.length; i++) {
                    for (let j = i + 1; j < teams.length; j++) {
                        newMatches.push({
                            id: Date.now() + Math.random(),
                            homeTeam: teams[i],
                            awayTeam: teams[j],
                            date: matchDate.toISOString().split('T')[0],
                            time: '17:00',
                            venue: 'ملعب الأكاديمية الرئيسي',
                            type: 'بطولة خارجية',
                            category: category,
                            notes: `${tournamentName} - دوري`,
                            status: 'scheduled',
                            homeScore: null,
                            awayScore: null,
                            createdAt: new Date().toISOString(),
                            createdBy: 'نظام الجدولة التلقائية'
                        });

                        matchDate.setDate(matchDate.getDate() + 2);
                    }
                }
            }

            return newMatches;
        }

        // Quick Schedule Functions
        function createWeeklySchedule() {
            document.getElementById('scheduleType').value = 'weekly';
            updateScheduleForm();
        }

        function createTournamentSchedule() {
            document.getElementById('scheduleType').value = 'tournament';
            updateScheduleForm();
        }

        function createFriendlySchedule() {
            document.getElementById('scheduleType').value = 'weekly';
            updateScheduleForm();
            // Set default values for friendly matches
            setTimeout(() => {
                document.getElementById('weeksCount').value = '2';
                document.querySelector('#scheduleDetails input[value="5"]').checked = true; // Friday
                document.querySelector('#scheduleDetails input[value="6"]').checked = true; // Saturday
            }, 100);
        }

        // Generate Match Report
        function generateMatchReport(matchId) {
            const match = matches.find(m => m.id === matchId);
            if (!match || match.status !== 'finished') {
                showNotification('لا يمكن إنشاء تقرير لهذه المباراة', 'warning');
                return;
            }

            const matchStats = teamStats.byMatch[matchId];
            const matchEventsData = matchEvents.filter(event => event.matchId === matchId);
            const matchApprovals = approvalRequests.filter(req => req.matchId === matchId);

            const report = {
                matchInfo: {
                    id: match.id,
                    homeTeam: match.homeTeam,
                    awayTeam: match.awayTeam,
                    date: match.date,
                    time: match.time,
                    venue: match.venue,
                    type: match.type,
                    category: match.category,
                    finalScore: `${match.homeScore} - ${match.awayScore}`,
                    result: match.homeScore > match.awayScore ? 'فوز' :
                           match.homeScore < match.awayScore ? 'خسارة' : 'تعادل'
                },
                statistics: matchStats || {},
                events: matchEventsData.map(event => ({
                    minute: event.minute,
                    type: getEventName(event.type),
                    player: event.playerName || 'غير محدد',
                    description: getEventDescription(event)
                })),
                playerPerformance: playersOnField.map(player => ({
                    name: player.name,
                    position: player.position,
                    rating: calculateMatchRating(player.id, matchId),
                    events: matchEventsData.filter(e => e.playerId == player.id).length
                })).sort((a, b) => parseFloat(b.rating) - parseFloat(a.rating)),
                approvals: {
                    total: matchApprovals.length,
                    approved: matchApprovals.filter(a => a.status === 'approved').length,
                    rejected: matchApprovals.filter(a => a.status === 'rejected').length,
                    pending: matchApprovals.filter(a => a.status === 'pending').length
                },
                generatedAt: new Date().toISOString(),
                generatedBy: 'نظام إدارة المباريات - أكاديمية 7C'
            };

            // Export report
            const dataStr = JSON.stringify(report, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `match_report_${match.homeTeam.replace(/\s+/g, '_')}_vs_${match.awayTeam.replace(/\s+/g, '_')}_${match.date}.json`;
            link.click();

            showNotification('تم إنشاء تقرير المباراة بنجاح!', 'success');
        }

        // Open Live Match Modal
        function openLiveMatchModal() {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 600px;">
                    <div class="modal-header">
                        <h3><i class="fas fa-play"></i> بدء مباراة مباشرة</h3>
                        <button class="modal-close" onclick="closeModal(this)">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>اختر المباراة المجدولة *</label>
                            <select id="liveMatchSelect2" required style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                <option value="">اختر المباراة...</option>
                                ${matches.filter(m => m.status === 'scheduled').map(match =>
                                    `<option value="${match.id}">${match.homeTeam} vs ${match.awayTeam} - ${formatDate(match.date)}</option>`
                                ).join('')}
                            </select>
                        </div>

                        <div id="selectedMatchInfo" style="display: none; background: rgba(255,255,255,0.05); padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                            <!-- Match info will be displayed here -->
                        </div>

                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="useCurrentFormation" checked style="margin-left: 0.5rem;">
                                استخدام التشكيلة الحالية (${playersOnField.length} لاعب)
                            </label>
                        </div>

                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="sendLiveNotifications" checked style="margin-left: 0.5rem;">
                                إرسال إشعارات مباشرة للمتابعين
                            </label>
                        </div>

                        <div style="background: rgba(59, 130, 246, 0.1); border: 1px solid #3B82F6; border-radius: 8px; padding: 1rem; margin: 1rem 0;">
                            <h5 style="color: #3B82F6; margin-bottom: 0.5rem;"><i class="fas fa-info-circle"></i> ملاحظة مهمة</h5>
                            <p style="font-size: 0.9rem; margin: 0;">سيتم تحويلك تلقائياً إلى تبويب الإحصائيات لبدء التتبع المباشر للمباراة.</p>
                        </div>

                        <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                            <button type="button" class="btn btn-secondary" onclick="closeModal(this)">إلغاء</button>
                            <button type="button" class="btn btn-success" onclick="startLiveMatch()">
                                <i class="fas fa-play"></i>
                                بدء المباراة المباشرة
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // Add event listener for match selection
            document.getElementById('liveMatchSelect2').addEventListener('change', function() {
                const matchId = parseInt(this.value);
                if (matchId) {
                    const match = matches.find(m => m.id === matchId);
                    if (match) {
                        document.getElementById('selectedMatchInfo').style.display = 'block';
                        document.getElementById('selectedMatchInfo').innerHTML = `
                            <h5 style="color: #D2691E; margin-bottom: 1rem;">تفاصيل المباراة المختارة</h5>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem;">
                                <div><strong>التاريخ:</strong> ${formatDate(match.date)}</div>
                                <div><strong>الوقت:</strong> ${match.time}</div>
                                <div><strong>المكان:</strong> ${match.venue}</div>
                                <div><strong>النوع:</strong> ${match.type}</div>
                                <div><strong>الفئة:</strong> ${match.category}</div>
                                <div><strong>الحالة:</strong> <span class="status-badge status-${match.status}">${getStatusText(match.status)}</span></div>
                            </div>
                        `;
                    }
                } else {
                    document.getElementById('selectedMatchInfo').style.display = 'none';
                }
            });
        }

        // Start Live Match
        function startLiveMatch() {
            const matchId = parseInt(document.getElementById('liveMatchSelect2').value);
            const useCurrentFormation = document.getElementById('useCurrentFormation').checked;
            const sendNotifications = document.getElementById('sendLiveNotifications').checked;

            if (!matchId) {
                showNotification('يرجى اختيار المباراة', 'warning');
                return;
            }

            const match = matches.find(m => m.id === matchId);
            if (!match) {
                showNotification('المباراة غير موجودة', 'error');
                return;
            }

            if (match.status !== 'scheduled') {
                showNotification('لا يمكن بدء هذه المباراة - الحالة غير صحيحة', 'warning');
                return;
            }

            if (useCurrentFormation && playersOnField.length === 0) {
                showNotification('لا توجد تشكيلة محددة. يرجى إنشاء تشكيلة أولاً', 'warning');
                return;
            }

            // Close modal
            closeModal();

            // Switch to statistics tab
            showMainTab('statistics');

            // Set the match for live tracking
            setTimeout(() => {
                document.getElementById('liveMatchSelect').value = matchId;
                selectLiveMatch();

                // Start live tracking
                setTimeout(() => {
                    startLiveTracking();

                    // Send notifications if requested
                    if (sendNotifications) {
                        sendLiveMatchNotifications(match);
                    }
                }, 500);
            }, 300);

            showNotification(`تم بدء المباراة المباشرة: ${match.homeTeam} vs ${match.awayTeam}`, 'success');
        }

        // Send Live Match Notifications
        function sendLiveMatchNotifications(match) {
            const message = `🔴 مباراة مباشرة الآن!

${match.homeTeam} vs ${match.awayTeam}

📍 ${match.venue}
🏆 ${match.type}
👥 ${match.category}

تابعوا المباراة مباشرة من خلال تطبيق أكاديمية 7C`;

            console.log('Live match notification sent:', message);
            showNotification('تم إرسال إشعارات المباراة المباشرة', 'info');
        }

        // ==================== Enhanced Formation Functions ====================

        // Create Custom Formation
        function createCustomFormation() {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 600px;">
                    <div class="modal-header">
                        <h3><i class="fas fa-plus"></i> إنشاء تشكيلة مخصصة</h3>
                        <button class="modal-close" onclick="closeModal(this)">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="customFormationForm" onsubmit="saveCustomFormation(event)">
                            <div class="form-group">
                                <label>اسم التشكيلة *</label>
                                <input type="text" id="customFormationName" required placeholder="مثال: 4-2-3-1 هجومية" style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                            </div>

                            <div class="form-group">
                                <label>نمط التشكيلة *</label>
                                <select id="customFormationPattern" required style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                    <option value="">اختر النمط الأساسي</option>
                                    <option value="4-4-2">4-4-2</option>
                                    <option value="4-3-3">4-3-3</option>
                                    <option value="3-5-2">3-5-2</option>
                                    <option value="4-5-1">4-5-1</option>
                                    <option value="5-3-2">5-3-2</option>
                                    <option value="4-2-3-1">4-2-3-1</option>
                                    <option value="3-4-3">3-4-3</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>أسلوب اللعب</label>
                                <select id="customPlayStyle" style="width: 100%; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                    <option value="balanced">متوازن</option>
                                    <option value="attacking">هجومي</option>
                                    <option value="defensive">دفاعي</option>
                                    <option value="counter">هجمات مرتدة</option>
                                    <option value="possession">استحواذ</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>وصف التشكيلة</label>
                                <textarea id="customFormationDescription" placeholder="اكتب وصفاً للتشكيلة ومتى تُستخدم..." style="width: 100%; height: 100px; padding: 0.75rem; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.3); resize: vertical;"></textarea>
                            </div>

                            <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                                <button type="button" class="btn btn-secondary" onclick="closeModal(this)">إلغاء</button>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i>
                                    حفظ التشكيلة المخصصة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // Save Custom Formation
        function saveCustomFormation(event) {
            event.preventDefault();

            const name = document.getElementById('customFormationName').value.trim();
            const pattern = document.getElementById('customFormationPattern').value;
            const playStyle = document.getElementById('customPlayStyle').value;
            const description = document.getElementById('customFormationDescription').value.trim();

            if (!name || !pattern) {
                showNotification('يرجى ملء الحقول المطلوبة', 'warning');
                return;
            }

            if (playersOnField.length === 0) {
                showNotification('يرجى وضع اللاعبين في التشكيلة أولاً', 'warning');
                return;
            }

            const customFormation = {
                id: Date.now(),
                name: name,
                pattern: pattern,
                playStyle: playStyle,
                description: description,
                players: [...playersOnField],
                createdAt: new Date().toISOString(),
                stats: {
                    playersCount: playersOnField.length,
                    avgRating: Math.round(playersOnField.reduce((sum, p) => sum + p.rating, 0) / playersOnField.length),
                    chemistry: calculateTeamChemistry()
                },
                isCustom: true
            };

            formations.push(customFormation);
            saveFormationData();

            // Add to formation templates for future use
            formationTemplates[pattern] = formationTemplates[pattern] || {
                name: pattern,
                positions: playersOnField.map((player, index) => ({
                    id: index + 1,
                    role: player.role || 'CM',
                    x: 20 + (index % 4) * 20,
                    y: 20 + Math.floor(index / 4) * 20
                }))
            };

            closeModal();
            showNotification(`تم حفظ التشكيلة المخصصة "${name}" بنجاح`, 'success');
        }

        // Notification System
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : type === 'warning' ? '#F59E0B' : '#3B82F6'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                font-weight: bold;
                max-width: 300px;
                animation: slideIn 0.3s ease-out;
            `;
            
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-in forwards';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // ==================== AI Functions ====================

        // Animate Statistics Counters
        function animateCounters() {
            document.querySelectorAll('.stat-number').forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const increment = target / 100;
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    counter.textContent = Math.floor(current);
                }, 20);
            });
        }

        // Run AI Analysis
        function runAIAnalysis() {
            showNotification('🤖 تشغيل التحليل الذكي الشامل...', 'info');

            // Simulate AI processing
            setTimeout(() => {
                const insights = [
                    {
                        type: 'success',
                        title: 'نقطة قوة جديدة',
                        content: 'تحسن كبير في معدل الاستحواذ بنسبة 18% مقارنة بالشهر الماضي'
                    },
                    {
                        type: 'warning',
                        title: 'تحذير تكتيكي',
                        content: 'انخفاض في فعالية الهجمات المرتدة - يحتاج تدريب إضافي'
                    },
                    {
                        type: 'info',
                        title: 'توصية ذكية',
                        content: 'استخدام اللاعب رقم 10 كصانع ألعاب أساسي في المباريات المهمة'
                    }
                ];

                updateAIInsights(insights);
                showNotification('✅ تم إكمال التحليل الذكي بنجاح!', 'success');
            }, 3000);
        }

        // Update AI Insights
        function updateAIInsights(insights) {
            const insightsGrid = document.querySelector('.insights-grid');
            insightsGrid.innerHTML = '';

            insights.forEach(insight => {
                const insightCard = document.createElement('div');
                insightCard.className = 'insight-card';
                insightCard.innerHTML = `
                    <div class="insight-icon ${insight.type}">
                        <i class="fas fa-${insight.type === 'success' ? 'check-circle' : insight.type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    </div>
                    <div class="insight-content">
                        <h5>${insight.title}</h5>
                        <p>${insight.content}</p>
                    </div>
                `;
                insightsGrid.appendChild(insightCard);
            });
        }

        // Predict Next Match
        function predictNextMatch() {
            showNotification('🔮 تحليل البيانات وتوقع النتيجة...', 'info');

            setTimeout(() => {
                const prediction = {
                    homeTeam: 'أكاديمية 7C',
                    awayTeam: 'نادي الشباب',
                    predictedScore: '2-1',
                    winProbability: 78,
                    confidence: 94.2
                };

                showPredictionModal(prediction);
            }, 2500);
        }

        // Show Prediction Modal
        function showPredictionModal(prediction) {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 600px; text-align: center;">
                    <div class="modal-header">
                        <h3><i class="fas fa-crystal-ball"></i> توقعات الذكاء الاصطناعي</h3>
                        <button class="modal-close" onclick="closeModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div style="background: linear-gradient(135deg, #8B4513, #A0522D); color: white; padding: 2rem; border-radius: 15px; margin-bottom: 2rem;">
                            <h2 style="margin-bottom: 1rem;">${prediction.homeTeam} vs ${prediction.awayTeam}</h2>
                            <div style="font-size: 3rem; font-weight: bold; margin: 1rem 0;">${prediction.predictedScore}</div>
                            <div style="font-size: 1.2rem;">احتمالية الفوز: ${prediction.winProbability}%</div>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem; margin-bottom: 2rem;">
                            <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 10px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #8B4513;">${prediction.confidence}%</div>
                                <div style="font-size: 0.9rem; color: #666;">دقة التوقع</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 10px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #8B4513;">15</div>
                                <div style="font-size: 0.9rem; color: #666;">عوامل التحليل</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 10px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #8B4513;">50</div>
                                <div style="font-size: 0.9rem; color: #666;">مباراة مرجعية</div>
                            </div>
                        </div>

                        <div style="background: rgba(59, 130, 246, 0.1); padding: 1rem; border-radius: 10px;">
                            <h5 style="color: #3B82F6; margin-bottom: 0.5rem;">توصيات تكتيكية</h5>
                            <ul style="text-align: right; margin: 0; padding-right: 1rem;">
                                <li>التركيز على الهجمات من الجانب الأيسر</li>
                                <li>استخدام الضغط العالي في الثلث الأول</li>
                                <li>الاستفادة من الكرات الثابتة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            showNotification('✅ تم إنشاء توقعات المباراة بنجاح!', 'success');
        }

        // AI Chat Functions
        let aiChatOpen = false;

        function toggleAIChat() {
            const assistant = document.getElementById('aiCoachAssistant');
            const badge = document.getElementById('aiNotificationBadge');

            if (aiChatOpen) {
                assistant.style.display = 'none';
                aiChatOpen = false;
            } else {
                assistant.style.display = 'flex';
                aiChatOpen = true;
                badge.style.display = 'none';
            }
        }

        function openAIChat() {
            if (!aiChatOpen) {
                toggleAIChat();
            }
        }

        function closeAIChat() {
            const assistant = document.getElementById('aiCoachAssistant');
            assistant.style.display = 'none';
            aiChatOpen = false;
        }

        function sendAIMessage() {
            const input = document.getElementById('aiChatInput');
            const message = input.value.trim();

            if (!message) return;

            // Add user message
            addChatMessage('user', message);
            input.value = '';

            // Show typing indicator
            showTypingIndicator();

            // Generate AI response
            setTimeout(() => {
                hideTypingIndicator();
                const response = generateAIResponse(message);
                addChatMessage('ai', response);
            }, 1500);
        }

        function addChatMessage(sender, message) {
            const messagesContainer = document.getElementById('aiChatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `${sender}-message`;

            if (sender === 'ai') {
                messageDiv.innerHTML = `
                    <div class="ai-avatar">🤖</div>
                    <div class="ai-message-content">
                        <p>${message}</p>
                    </div>
                `;
            } else {
                messageDiv.innerHTML = `
                    <div class="user-avatar">👤</div>
                    <div class="user-message-content">
                        <p>${message}</p>
                    </div>
                `;
            }

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function showTypingIndicator() {
            const messagesContainer = document.getElementById('aiChatMessages');
            const typingDiv = document.createElement('div');
            typingDiv.id = 'typingIndicator';
            typingDiv.className = 'ai-message';
            typingDiv.innerHTML = `
                <div class="ai-avatar">🤖</div>
                <div class="ai-message-content">
                    <p>يكتب... <span style="animation: pulse 1s infinite;">●●●</span></p>
                </div>
            `;
            messagesContainer.appendChild(typingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        function generateAIResponse(userMessage) {
            const responses = {
                'تشكيلة': 'بناءً على تحليل الخصم، أنصح بتشكيلة 4-3-3 مع التركيز على الأجنحة. هذا سيمنحك تفوقاً عددياً في الوسط وخيارات هجومية متنوعة.',
                'تحليل': 'تحليل الأداء يظهر تحسناً في دقة التمرير بنسبة 12%. أنصح بالتركيز على تطوير الانتقال السريع من الدفاع للهجوم.',
                'خصم': 'الفريق المنافس يعتمد على الهجمات المرتدة السريعة. أنصح بالضغط العالي ومنع وصول الكرة لمهاجمهم الرئيسي.',
                'لاعب': 'بناءً على الإحصائيات، اللاعب رقم 10 يظهر أداءً متميزاً في صناعة اللعب. أنصح بإعطائه حرية أكبر في الحركة.',
                'تكتيك': 'للمباراة القادمة، أنصح بالتركيز على الكرات الثابتة - لديكم ميزة في الطول والقوة البدنية.',
                'default': 'شكراً لسؤالك! أنا هنا لمساعدتك في التحليل التكتيكي وتطوير الأداء. هل تريد تحليلاً محدداً لمباراة معينة؟'
            };

            for (let key in responses) {
                if (userMessage.includes(key)) {
                    return responses[key];
                }
            }

            return responses.default;
        }

        // Generate Advanced Report
        function generateAdvancedReport() {
            showNotification('📊 إنشاء التقرير المتقدم...', 'info');

            setTimeout(() => {
                const reportData = {
                    generatedAt: new Date().toISOString(),
                    summary: {
                        totalMatches: matches.length,
                        winRate: 87,
                        avgGoalsScored: 2.3,
                        avgGoalsConceded: 1.1,
                        topScorer: 'محمد أحمد (12 هدف)',
                        bestFormation: '4-3-3'
                    },
                    aiInsights: [
                        'تحسن ملحوظ في الأداء الدفاعي',
                        'زيادة فعالية الهجمات من الأجنحة',
                        'تطور في دقة التمرير الطويل'
                    ]
                };

                downloadReport(reportData);
                showNotification('✅ تم إنشاء التقرير المتقدم بنجاح!', 'success');
            }, 2000);
        }

        function downloadReport(data) {
            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `advanced_report_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
        }

        // Initialize AI System
        function initializeAI() {
            // Animate counters
            setTimeout(animateCounters, 1000);

            // Add event listener for Enter key in AI chat
            document.getElementById('aiChatInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendAIMessage();
                }
            });

            // Show AI notification after 5 seconds
            setTimeout(() => {
                const badge = document.getElementById('aiNotificationBadge');
                badge.style.display = 'flex';
            }, 5000);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeAI();

            // Add smooth scrolling
            document.documentElement.style.scrollBehavior = 'smooth';

            // Add loading animation
            setTimeout(() => {
                document.body.style.opacity = '1';
                document.body.style.transform = 'translateY(0)';
            }, 100);
        });

        // Add initial loading state
        document.body.style.opacity = '0';
        document.body.style.transform = 'translateY(20px)';
        document.body.style.transition = 'all 0.6s ease';
    </script>
</body>
</html>
