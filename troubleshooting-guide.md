# دليل حل مشاكل نظام إدارة المدربين - أكاديمية 7C

## 🚨 المشاكل الشائعة وحلولها

### 1. مشك<PERSON><PERSON> "Failed to fetch" في API

#### الأعراض:
- ظهور رسالة "Failed to fetch" في لوحة التحكم
- عدم تحميل بيانات المدربين
- فشل الاتصال بقاعدة البيانات

#### الحلول:

**أ) فحص مسار API:**
```javascript
// تأكد من أن مسار API صحيح في admin-advanced.html
const COACHES_API_URL = './api/coaches_management.php';
```

**ب) فحص أذونات الملفات:**
```bash
# تأكد من أذونات الملفات على الخادم
chmod 644 api/coaches_management.php
chmod 755 api/
```

**ج) فحص إعدادات PHP:**
- تأكد من تفعيل `allow_url_fopen`
- تأكد من عدم وجود أخطاء PHP
- فحص ملف error_log

### 2. مشكلة الاتصال بقاعدة البيانات

#### الأعراض:
- رسالة "فشل الاتصال بقاعدة بيانات المدربين"
- عدم ظهور البيانات

#### الحلول:

**أ) فحص بيانات الاتصال:**
```php
// في api/coaches_management.php
$host = 'localhost';
$dbname = 'coaches7c';
$username = 'komaro';
$password = 'ZbShaker@14';
```

**ب) إنشاء قاعدة البيانات:**
```sql
-- تشغيل هذا الكود في phpMyAdmin أو MySQL
CREATE DATABASE IF NOT EXISTS coaches7c CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

**ج) فحص صلاحيات المستخدم:**
```sql
-- منح الصلاحيات للمستخدم
GRANT ALL PRIVILEGES ON coaches7c.* TO 'komaro'@'localhost';
FLUSH PRIVILEGES;
```

### 3. مشكلة CORS (Cross-Origin Resource Sharing)

#### الأعراض:
- رسائل خطأ CORS في المتصفح
- منع الطلبات من المتصفح

#### الحلول:

**أ) إضافة headers في PHP:**
```php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
```

**ب) إعداد .htaccess:**
```apache
# إضافة هذا في ملف .htaccess
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
```

### 4. مشكلة عدم ظهور البيانات

#### الأعراض:
- الصفحة تحمل لكن لا تظهر بيانات
- رسالة "لا توجد بيانات"

#### الحلول:

**أ) فحص الجداول:**
```sql
-- فحص وجود الجداول
SHOW TABLES FROM coaches7c;

-- فحص بيانات الجدول
SELECT * FROM coaches LIMIT 5;
```

**ب) إدراج بيانات تجريبية:**
```sql
-- إدراج مدرب تجريبي
INSERT INTO coaches (id, name, email, specialization, status) 
VALUES ('C001', 'مدرب تجريبي', '<EMAIL>', 'كرة القدم', 1);
```

### 5. مشكلة الترميز (Encoding)

#### الأعراض:
- ظهور رموز غريبة بدلاً من النص العربي
- مشاكل في عرض الخطوط

#### الحلول:

**أ) إعداد UTF-8:**
```php
// في بداية ملف PHP
header('Content-Type: application/json; charset=utf-8');
```

**ب) إعداد قاعدة البيانات:**
```sql
-- تغيير ترميز قاعدة البيانات
ALTER DATABASE coaches7c CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

**ج) إعداد الجداول:**
```sql
-- تغيير ترميز الجداول
ALTER TABLE coaches CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 🔧 أدوات التشخيص

### 1. ملف التشخيص السريع
افتح `debug-coaches-api.html` لفحص:
- حالة API
- الاتصال بقاعدة البيانات
- وجود الجداول
- عمليات CRUD

### 2. ملف الاختبار الشامل
افتح `test-coaches-system.html` لاختبار:
- جميع مكونات النظام
- التكامل بين الأنظمة
- أداء النظام

### 3. فحص سجلات الأخطاء

**أ) سجل أخطاء PHP:**
```bash
# مسار سجل الأخطاء عادة
tail -f /var/log/apache2/error.log
# أو
tail -f /var/log/php_errors.log
```

**ب) سجل أخطاء المتصفح:**
- افتح Developer Tools (F12)
- اذهب إلى تبويب Console
- ابحث عن رسائل الخطأ

## 📋 قائمة فحص سريعة

### قبل النشر:
- [ ] فحص اتصال قاعدة البيانات
- [ ] فحص أذونات الملفات
- [ ] اختبار API endpoints
- [ ] فحص الترميز العربي
- [ ] اختبار على متصفحات مختلفة

### عند حدوث مشكلة:
- [ ] فحص سجل الأخطاء
- [ ] تشغيل أداة التشخيص
- [ ] فحص Network tab في المتصفح
- [ ] اختبار API مباشرة
- [ ] فحص إعدادات قاعدة البيانات

## 🆘 الحصول على المساعدة

### معلومات مفيدة للدعم:
1. **نسخة PHP:** `<?php echo PHP_VERSION; ?>`
2. **نسخة MySQL:** `SELECT VERSION();`
3. **رسائل الخطأ الكاملة**
4. **خطوات إعادة إنتاج المشكلة**
5. **متصفح ونظام التشغيل**

### ملفات مهمة للفحص:
- `api/coaches_management.php` - API الرئيسي
- `admin-advanced.html` - النظام الإداري
- `coach-dashboard.html` - لوحة المدربين
- `js/coach-dashboard-integration.js` - ملف التكامل

## 🔄 إعادة تعيين النظام

### في حالة الطوارئ:
```sql
-- حذف وإعادة إنشاء قاعدة البيانات
DROP DATABASE IF EXISTS coaches7c;
CREATE DATABASE coaches7c CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

ثم افتح `api/coaches_management.php` في المتصفح لإعادة إنشاء الجداول تلقائياً.

---

**ملاحظة:** احتفظ بنسخة احتياطية من البيانات قبل تطبيق أي حلول!
