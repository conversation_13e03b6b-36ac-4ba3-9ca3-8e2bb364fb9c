// ==================== Plugin: إدارة الاشتراكات ====================
export const SubscriptionsManagementPlugin = {
    id: 'subscriptions',
    name: 'إدارة الاشتراكات',
    init() {
        // إضافة CSS مدمج للواجهة الاحترافية
        if (!document.getElementById('subscriptions-plugin-style')) {
            const style = document.createElement('style');
            style.id = 'subscriptions-plugin-style';
            style.innerHTML = `
                .plugin-section {background: #181f2a; color: #fff; border-radius: 18px; box-shadow: 0 2px 16px #0002; max-width: 900px; margin: 32px auto 0; padding: 32px 24px 24px; font-family: 'Cairo',sans-serif;}
                .plugin-header {display: flex; align-items: center; justify-content: space-between; margin-bottom: 18px;}
                .plugin-header h2 {font-size: 1.5rem; font-weight: bold; margin: 0;}
                .plugin-btn {background: linear-gradient(90deg,#4f8cff,#6f6bff); color: #fff; border: none; border-radius: 8px; padding: 10px 22px; font-size: 1rem; font-weight: 600; cursor: pointer; transition: background 0.2s;}
                .plugin-btn:hover {background: linear-gradient(90deg,#6f6bff,#4f8cff);}
                .plugin-table {width: 100%; border-collapse: collapse; background: #232b3b; border-radius: 12px; overflow: hidden; margin-top: 10px;}
                .plugin-table th, .plugin-table td {padding: 12px 8px; text-align: center;}
                .plugin-table th {background: #232b3b; color: #8bb4ff; font-weight: 700; border-bottom: 2px solid #2d3950;}
                .plugin-table tr {transition: background 0.2s;}
                .plugin-table tr:hover {background: #232b3bcc;}
                .plugin-table td {border-bottom: 1px solid #232b3b;}
                .plugin-action-btn {background: #2d3950; color: #8bb4ff; border: none; border-radius: 6px; padding: 6px 16px; margin: 0 2px; font-size: 0.95rem; cursor: pointer; transition: background 0.2s;}
                .plugin-action-btn:hover {background: #4f8cff; color: #fff;}
                .plugin-modal-bg {position: fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.45); z-index: 9999; display: flex; align-items: center; justify-content: center;}
                .plugin-modal {background: #232b3b; border-radius: 16px; padding: 32px 24px 24px; min-width: 320px; max-width: 95vw; box-shadow: 0 4px 32px #0005;}
                .plugin-modal h3 {margin-top:0; color:#8bb4ff; font-size:1.2rem; margin-bottom:18px;}
                .plugin-modal label {display:block; margin-bottom:6px; color:#b6cfff; font-size:1rem;}
                .plugin-modal input {width:100%; padding:8px 10px; border-radius:6px; border:none; background:#181f2a; color:#fff; margin-bottom:16px; font-size:1rem;}
                .plugin-modal .modal-actions {display:flex; justify-content:flex-end; gap:10px;}
                .plugin-modal .plugin-btn {padding:8px 18px; font-size:1rem;}
            `;
            document.head.appendChild(style);
        }
        // إنشاء واجهة إدارة الاشتراكات إذا لم تكن موجودة
        if (!document.getElementById('subscriptions-plugin-container')) {
            const container = document.createElement('section');
            container.id = 'subscriptions-plugin-container';
            container.className = 'plugin-section';
            container.innerHTML = `
                <div class="plugin-header">
                    <h2>إدارة الاشتراكات</h2>
                    <button id="add-subscription-btn" class="plugin-btn">إضافة اشتراك</button>
                </div>
                <table class="plugin-table" id="subscriptions-table">
                    <thead>
                        <tr>
                            <th>اسم المشترك</th>
                            <th>نوع الاشتراك</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="subscriptions-table-body">
                        <!-- سيتم تعبئة الاشتراكات هنا -->
                    </tbody>
                </table>
            `;
            document.body.prepend(container);
        }
        this.renderSubscriptions();
        document.getElementById('add-subscription-btn').onclick = () => this.openSubscriptionModal();
    },
    destroy() {
        // إزالة واجهة إدارة الاشتراكات وCSS
        const container = document.getElementById('subscriptions-plugin-container');
        if (container) container.remove();
        const style = document.getElementById('subscriptions-plugin-style');
        if (style) style.remove();
        this.closeModal();
    },
    renderSubscriptions() {
        const subs = JSON.parse(localStorage.getItem('plugin_subscriptions') || '[]');
        const tbody = document.getElementById('subscriptions-table-body');
        if (!tbody) return;
        tbody.innerHTML = subs.length ? subs.map((s, i) => `
            <tr>
                <td>${s.name || ''}</td>
                <td>${s.type || ''}</td>
                <td>${s.start || ''}</td>
                <td>${s.end || ''}</td>
                <td>
                    <button class="plugin-action-btn" onclick="window.PluginManager.plugins.subscriptions.openSubscriptionModal(${i})">تعديل</button>
                    <button class="plugin-action-btn" onclick="window.PluginManager.plugins.subscriptions.deleteSubscription(${i})">حذف</button>
                </td>
            </tr>
        `).join('') : '<tr><td colspan="5">لا يوجد اشتراكات</td></tr>';
    },
    openSubscriptionModal(index = null) {
        this.closeModal();
        const subs = JSON.parse(localStorage.getItem('plugin_subscriptions') || '[]');
        const sub = index !== null ? subs[index] : { name: '', type: '', start: '', end: '' };
        const modalBg = document.createElement('div');
        modalBg.className = 'plugin-modal-bg';
        modalBg.id = 'subscriptions-plugin-modal-bg';
        modalBg.innerHTML = `
            <div class="plugin-modal">
                <h3>${index !== null ? 'تعديل اشتراك' : 'إضافة اشتراك جديد'}</h3>
                <label>اسم المشترك</label>
                <input id="modal-sub-name" type="text" value="${sub.name || ''}" placeholder="مثال: أحمد علي" />
                <label>نوع الاشتراك</label>
                <input id="modal-sub-type" type="text" value="${sub.type || ''}" placeholder="شهري/سنوي/مميز" />
                <label>تاريخ البداية</label>
                <input id="modal-sub-start" type="date" value="${sub.start || ''}" />
                <label>تاريخ النهاية</label>
                <input id="modal-sub-end" type="date" value="${sub.end || ''}" />
                <div class="modal-actions">
                    <button class="plugin-btn" id="save-sub-btn">${index !== null ? 'حفظ التعديلات' : 'إضافة'}</button>
                    <button class="plugin-btn" id="cancel-sub-btn" style="background:#2d3950;">إلغاء</button>
                </div>
            </div>
        `;
        document.body.appendChild(modalBg);
        document.getElementById('cancel-sub-btn').onclick = () => this.closeModal();
        document.getElementById('save-sub-btn').onclick = () => {
            const name = document.getElementById('modal-sub-name').value.trim();
            const type = document.getElementById('modal-sub-type').value.trim();
            const start = document.getElementById('modal-sub-start').value;
            const end = document.getElementById('modal-sub-end').value;
            if (!name || !type || !start || !end) {
                alert('يرجى تعبئة جميع الحقول');
                return;
            }
            if (index !== null) {
                subs[index] = { name, type, start, end };
            } else {
                subs.push({ name, type, start, end });
            }
            localStorage.setItem('plugin_subscriptions', JSON.stringify(subs));
            this.closeModal();
            this.renderSubscriptions();
        };
        setTimeout(() => {
            document.getElementById('modal-sub-name').focus();
        }, 100);
    },
    closeModal() {
        const modalBg = document.getElementById('subscriptions-plugin-modal-bg');
        if (modalBg) modalBg.remove();
    },
    deleteSubscription(index) {
        if (!confirm('هل أنت متأكد من حذف الاشتراك؟')) return;
        const subs = JSON.parse(localStorage.getItem('plugin_subscriptions') || '[]');
        subs.splice(index, 1);
        localStorage.setItem('plugin_subscriptions', JSON.stringify(subs));
        this.renderSubscriptions();
    }
};
