<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الرسائل الذكي - أكاديمية 7C</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D2691E;
            --background-dark: #1a1a1a;
            --background-light: #f8f9fa;
            --text-dark: #2d3748;
            --text-light: #718096;
            --border-color: #e2e8f0;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --info-color: #3b82f6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--background-dark);
            color: #ffffff;
            overflow-x: hidden;
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0;
        }

        /* Header Styles */
        .header {
            background: linear-gradient(135deg, var(--background-dark) 0%, #2d2d2d 100%);
            border-bottom: 2px solid var(--primary-color);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .logo-text h1 {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 0.25rem;
        }

        .logo-text p {
            font-size: 0.875rem;
            color: var(--text-light);
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* Main Layout */
        .main-container {
            display: flex;
            height: calc(100vh - 100px);
            max-width: 1400px;
            margin: 0 auto;
            gap: 1px;
            background: #333;
        }

        /* Conversations List */
        .conversations-panel {
            width: 30%;
            min-width: 300px;
            background: var(--background-dark);
            border-right: 1px solid #333;
            display: flex;
            flex-direction: column;
        }

        .conversations-header {
            padding: 1.5rem;
            border-bottom: 1px solid #333;
            background: #2d2d2d;
        }

        .conversations-header h2 {
            font-size: 1.25rem;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 1rem;
        }

        .search-box {
            width: 100%;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #ffffff;
            font-size: 0.875rem;
        }

        .search-box::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
        }

        .conversations-list {
            flex: 1;
            overflow-y: auto;
            padding: 0.5rem;
        }

        .conversation-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            margin-bottom: 0.5rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .conversation-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary-color);
        }

        .conversation-item.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-color: var(--primary-color);
        }

        .conversation-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-left: 1rem;
            position: relative;
        }

        .conversation-avatar.online::after {
            content: '';
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            background: #10b981;
            border: 2px solid var(--background-dark);
            border-radius: 50%;
        }

        .conversation-info {
            flex: 1;
            min-width: 0;
        }

        .conversation-name {
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 0.25rem;
        }

        .conversation-preview {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.7);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .conversation-meta {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.25rem;
        }

        .conversation-time {
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.5);
        }

        .unread-badge {
            background: var(--error-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: bold;
        }

        /* Messages Panel */
        .messages-panel {
            flex: 1;
            background: var(--background-dark);
            display: flex;
            flex-direction: column;
        }

        .messages-header {
            padding: 1.5rem;
            border-bottom: 1px solid #333;
            background: #2d2d2d;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .chat-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .chat-details h3 {
            font-size: 1.125rem;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 0.25rem;
        }

        .chat-status {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .chat-actions {
            display: flex;
            gap: 0.5rem;
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background: #1a1a1a;
        }

        .message {
            display: flex;
            margin-bottom: 1rem;
            animation: fadeInUp 0.3s ease;
        }

        .message.sent {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 0.75rem 1rem;
            border-radius: 18px;
            position: relative;
        }

        .message.received .message-content {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border-bottom-right-radius: 4px;
        }

        .message.sent .message-content {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-bottom-left-radius: 4px;
        }

        .message-time {
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.5);
            margin-top: 0.25rem;
            text-align: right;
        }

        .message-input-container {
            padding: 1rem;
            border-top: 1px solid #333;
            background: #2d2d2d;
        }

        .message-input-wrapper {
            display: flex;
            align-items: flex-end;
            gap: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            padding: 0.5rem;
        }

        .message-input {
            flex: 1;
            background: transparent;
            border: none;
            color: #ffffff;
            padding: 0.75rem 1rem;
            border-radius: 20px;
            resize: none;
            max-height: 120px;
            min-height: 40px;
        }

        .message-input:focus {
            outline: none;
        }

        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .send-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .send-button:hover {
            transform: scale(1.1);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .attachment-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            margin-left: 0.5rem;
        }

        .attachment-button:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .attachment-menu {
            position: absolute;
            bottom: 100%;
            left: 1rem;
            background: var(--background-dark);
            border: 1px solid #333;
            border-radius: 12px;
            padding: 0.5rem;
            display: flex;
            gap: 0.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            margin-bottom: 0.5rem;
        }

        .attachment-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 60px;
        }

        .attachment-option:hover {
            background: var(--primary-color);
        }

        .attachment-option i {
            font-size: 1.25rem;
        }

        .attachment-option span {
            font-size: 0.75rem;
        }

        .image-preview {
            padding: 1rem;
            border-top: 1px solid #333;
            background: #2d2d2d;
        }

        .preview-container {
            position: relative;
            display: inline-block;
            max-width: 200px;
        }

        .preview-container img {
            width: 100%;
            height: auto;
            border-radius: 8px;
            max-height: 150px;
            object-fit: cover;
        }

        .remove-preview {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--error-color);
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
        }

        .caption-input {
            width: 100%;
            margin-top: 0.5rem;
            padding: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: white;
            font-size: 0.875rem;
        }

        .caption-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .message-image {
            max-width: 300px;
            max-height: 200px;
            border-radius: 12px;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .message-image:hover {
            transform: scale(1.02);
        }

        .message-file {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .message-file:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .file-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.25rem;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .file-size {
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.7);
        }

        /* Smart Replies */
        .smart-replies {
            padding: 1rem;
            border-top: 1px solid #333;
            background: #2d2d2d;
        }

        .smart-replies-title {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 0.5rem;
        }

        .smart-replies-container {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .smart-reply {
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }

        .smart-reply:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        /* Priority indicators */
        .priority-high {
            border-left: 4px solid var(--error-color);
        }

        .priority-medium {
            border-left: 4px solid var(--warning-color);
        }

        .priority-low {
            border-left: 4px solid var(--success-color);
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .modal.hidden {
            display: none;
        }

        .modal-content {
            background: var(--background-dark);
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
            border: 1px solid #333;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid #333;
        }

        .modal-header h2 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #ffffff;
            margin: 0;
        }

        .close-button {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .close-button:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            padding: 1.5rem;
            border-top: 1px solid #333;
        }

        /* Settings Styles */
        .settings-section {
            margin-bottom: 2rem;
        }

        .settings-section h3 {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #333;
        }

        .setting-item {
            margin-bottom: 1.5rem;
        }

        .setting-label {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 500;
            color: #ffffff;
            cursor: pointer;
            margin-bottom: 0.5rem;
        }

        .setting-label input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--primary-color);
        }

        .setting-label select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: white;
            padding: 0.5rem;
            margin-right: auto;
        }

        .setting-description {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
            margin-right: 2.25rem;
            line-height: 1.4;
        }

        /* Image preview in SweetAlert */
        .swal-image-preview {
            max-width: 90vw;
            max-height: 90vh;
            object-fit: contain;
        }

        /* Academy Messages Styles */
        .academy-form .form-group {
            margin-bottom: 1.5rem;
        }

        .academy-form label {
            display: block;
            font-weight: 500;
            color: #ffffff;
            margin-bottom: 0.5rem;
        }

        .academy-form .form-control {
            width: 100%;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            font-size: 0.875rem;
        }

        .academy-form .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
        }

        .academy-form .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .academy-form textarea.form-control {
            resize: vertical;
            min-height: 120px;
        }

        .academy-form input[type="checkbox"] {
            margin-left: 0.5rem;
            accent-color: var(--primary-color);
        }

        /* Nudge Options */
        .nudge-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            padding: 1rem 0;
        }

        .nudge-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            padding: 1.5rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nudge-option:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .nudge-option i {
            font-size: 2rem;
        }

        .nudge-option span {
            font-size: 0.875rem;
            font-weight: 500;
        }

        /* Translation Popup */
        .translation-popup {
            position: absolute;
            background: var(--background-dark);
            border: 1px solid #333;
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            max-width: 300px;
        }

        .translation-popup .translation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .translation-popup .translation-text {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.875rem;
            line-height: 1.4;
        }

        .translation-popup .translation-lang {
            font-size: 0.75rem;
            color: var(--primary-color);
            font-weight: 500;
        }

        /* Video Message Styles */
        .message-video {
            max-width: 300px;
            max-height: 200px;
            border-radius: 12px;
            margin-bottom: 0.5rem;
        }

        .video-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .video-duration {
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.7);
        }

        /* Backup Status */
        .backup-status {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.75rem;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .backup-status.success {
            background: var(--success-color);
        }

        .backup-status.error {
            background: var(--error-color);
        }

        /* Performance Indicators */
        .performance-indicator {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem;
            border-radius: 8px;
            font-size: 0.75rem;
            z-index: 1000;
            display: none;
        }

        .performance-indicator.show {
            display: block;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
                height: calc(100vh - 80px);
            }

            .conversations-panel {
                width: 100%;
                height: 40%;
                min-width: auto;
            }

            .messages-panel {
                height: 60%;
            }

            .header-content {
                padding: 0 1rem;
            }

            .logo-text h1 {
                font-size: 1.25rem;
            }

            .logo-text p {
                display: none;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Scrollbar Styles */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }

        /* Empty State */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: rgba(255, 255, 255, 0.5);
            text-align: center;
            padding: 2rem;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: #ffffff;
        }

        .empty-state p {
            font-size: 1rem;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">7C</div>
                <div class="logo-text">
                    <h1>نظام الرسائل الذكي</h1>
                    <p>أكاديمية 7C للتدريب الرياضي</p>
                </div>
            </div>
            <div class="header-actions">
                <a href="admin-applications.html" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للنظام الرئيسي
                </a>
                <button class="btn btn-primary" onclick="openNewMessageModal()">
                    <i class="fas fa-plus"></i>
                    رسالة جديدة
                </button>
                <button class="btn btn-secondary" onclick="openAcademyMessagesModal()">
                    <i class="fas fa-university"></i>
                    مراسلة الأكاديميات
                </button>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Conversations Panel -->
        <div class="conversations-panel">
            <div class="conversations-header">
                <h2>المحادثات</h2>
                <input type="text" class="search-box" placeholder="البحث في المحادثات..." id="conversations-search">
            </div>
            <div class="conversations-list" id="conversations-list">
                <!-- Conversations will be loaded here -->
            </div>
        </div>

        <!-- Messages Panel -->
        <div class="messages-panel">
            <div id="empty-chat-state" class="empty-state">
                <i class="fas fa-comments"></i>
                <h3>مرحباً بك في نظام الرسائل الذكي</h3>
                <p>اختر محادثة من القائمة أو ابدأ محادثة جديدة<br>للتواصل مع أعضاء الأكاديمية</p>
            </div>
            
            <div id="active-chat" class="hidden" style="display: flex; flex-direction: column; height: 100%;">
                <div class="messages-header">
                    <div class="chat-info">
                        <div class="chat-avatar" id="chat-avatar">A</div>
                        <div class="chat-details">
                            <h3 id="chat-name">اسم المستخدم</h3>
                            <div class="chat-status" id="chat-status">متصل الآن</div>
                        </div>
                    </div>
                    <div class="chat-actions">
                        <button class="btn btn-secondary" onclick="openNudgeModal()" title="إرسال نكزة">
                            <i class="fas fa-hand-paper"></i>
                        </button>
                        <button class="btn btn-secondary" onclick="openPrivacySettings()" title="إعدادات الخصوصية">
                            <i class="fas fa-shield-alt"></i>
                        </button>
                        <button class="btn btn-secondary" onclick="toggleChatInfo()" title="معلومات المحادثة">
                            <i class="fas fa-info-circle"></i>
                        </button>
                        <button class="btn btn-secondary" onclick="clearChat()" title="مسح المحادثة">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                
                <div class="messages-container" id="messages-container">
                    <!-- Messages will be loaded here -->
                </div>
                
                <div class="message-input-container">
                    <div class="message-input-wrapper">
                        <button class="attachment-button" onclick="toggleAttachmentMenu()" title="إرفاق ملف">
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <textarea class="message-input" id="message-input" placeholder="اكتب رسالتك هنا..." rows="1"></textarea>
                        <button class="send-button" id="send-button" onclick="sendMessage()">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>

                    <!-- Attachment Menu -->
                    <div id="attachment-menu" class="attachment-menu hidden">
                        <button onclick="selectImage()" class="attachment-option">
                            <i class="fas fa-image"></i>
                            <span>صورة</span>
                        </button>
                        <button onclick="selectFile()" class="attachment-option">
                            <i class="fas fa-file"></i>
                            <span>ملف</span>
                        </button>
                    </div>

                    <!-- Hidden file inputs -->
                    <input type="file" id="image-input" accept="image/*" style="display: none;" onchange="handleImageSelect(event)">
                    <input type="file" id="file-input" accept=".txt,.pdf,.doc,.docx" style="display: none;" onchange="handleFileSelect(event)">

                    <!-- Image preview -->
                    <div id="image-preview" class="image-preview hidden">
                        <div class="preview-container">
                            <img id="preview-image" src="" alt="معاينة الصورة">
                            <button onclick="removeImagePreview()" class="remove-preview">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <input type="text" id="image-caption" placeholder="إضافة تعليق (اختياري)..." class="caption-input">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Privacy Settings Modal -->
    <div id="privacy-modal" class="modal hidden">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h2>إعدادات الخصوصية والأمان</h2>
                <button onclick="closePrivacyModal()" class="close-button">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h3>الخصوصية</h3>
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="hide-last-seen" onchange="updatePrivacySetting('hideLastSeen', this.checked)">
                            <span>إخفاء آخر ظهور</span>
                        </label>
                        <p class="setting-description">منع الآخرين من رؤية آخر مرة كنت متصلاً فيها</p>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="hide-message-preview" onchange="updatePrivacySetting('hideMessagePreview', this.checked)">
                            <span>إخفاء معاينة الرسائل</span>
                        </label>
                        <p class="setting-description">إخفاء محتوى الرسائل من قائمة المحادثات</p>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="read-receipts" onchange="updatePrivacySetting('readReceipts', this.checked)">
                            <span>إيصالات القراءة</span>
                        </label>
                        <p class="setting-description">إظهار علامة التسليم والقراءة للرسائل</p>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>الأمان</h3>
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="encrypt-messages" onchange="updatePrivacySetting('encryptMessages', this.checked)">
                            <span>تشفير الرسائل</span>
                        </label>
                        <p class="setting-description">تشفير محتوى الرسائل قبل الحفظ</p>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="allow-screenshots" onchange="updatePrivacySetting('allowScreenshots', this.checked)">
                            <span>السماح بلقطات الشاشة</span>
                        </label>
                        <p class="setting-description">السماح بالتقاط لقطات شاشة للمحادثات</p>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>الحذف التلقائي</h3>
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="auto-delete" onchange="updatePrivacySetting('autoDeleteMessages', this.checked)">
                            <span>تفعيل الحذف التلقائي</span>
                        </label>
                        <p class="setting-description">حذف الرسائل تلقائياً بعد فترة محددة</p>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">
                            <span>مدة الاحتفاظ بالرسائل:</span>
                            <select id="auto-delete-days" onchange="updatePrivacySetting('autoDeleteDays', parseInt(this.value))">
                                <option value="7">7 أيام</option>
                                <option value="30">30 يوم</option>
                                <option value="90">90 يوم</option>
                                <option value="365">سنة واحدة</option>
                            </select>
                        </label>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>الذكاء الاصطناعي</h3>
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="smart-replies" onchange="updateAISetting('enableSmartReplies', this.checked)">
                            <span>الردود الذكية</span>
                        </label>
                        <p class="setting-description">اقتراح ردود سريعة بناءً على محتوى الرسائل</p>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="priority-detection" onchange="updateAISetting('enablePriorityDetection', this.checked)">
                            <span>كشف الأولوية</span>
                        </label>
                        <p class="setting-description">تصنيف الرسائل حسب الأولوية تلقائياً</p>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="content-filter" onchange="updateAISetting('enableContentFilter', this.checked)">
                            <span>فلتر المحتوى</span>
                        </label>
                        <p class="setting-description">كشف وتنقية المحتوى غير المناسب</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="resetSettings()">إعادة تعيين</button>
                <button class="btn btn-primary" onclick="closePrivacyModal()">حفظ</button>
            </div>
        </div>
    </div>

    <!-- Academy Messages Modal -->
    <div id="academy-messages-modal" class="modal hidden">
        <div class="modal-content" style="max-width: 700px;">
            <div class="modal-header">
                <h2>مراسلة الأكاديميات الأخرى</h2>
                <button onclick="closeAcademyMessagesModal()" class="close-button">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="academy-form">
                    <div class="form-group">
                        <label>اختر الأكاديمية:</label>
                        <select id="target-academy" class="form-control">
                            <option value="">اختر الأكاديمية المستهدفة</option>
                            <option value="academy_riyadh">أكاديمية الرياض الرياضية</option>
                            <option value="academy_jeddah">أكاديمية جدة للتدريب</option>
                            <option value="academy_dammam">أكاديمية الدمام الرياضية</option>
                            <option value="academy_mecca">أكاديمية مكة للناشئين</option>
                            <option value="academy_medina">أكاديمية المدينة الرياضية</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>نوع الرسالة:</label>
                        <select id="message-template" class="form-control" onchange="loadMessageTemplate()">
                            <option value="">اختر نوع الرسالة</option>
                            <option value="partnership">طلب شراكة</option>
                            <option value="friendly_match">طلب مباراة ودية</option>
                            <option value="training_camp">دعوة لمعسكر تدريبي</option>
                            <option value="experience_exchange">تبادل خبرات</option>
                            <option value="tournament_invitation">دعوة لبطولة</option>
                            <option value="custom">رسالة مخصصة</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>موضوع الرسالة:</label>
                        <input type="text" id="message-subject" class="form-control" placeholder="أدخل موضوع الرسالة">
                    </div>

                    <div class="form-group">
                        <label>محتوى الرسالة:</label>
                        <textarea id="message-content" class="form-control" rows="8" placeholder="اكتب محتوى الرسالة هنا..."></textarea>
                    </div>

                    <div class="form-group">
                        <label>الأولوية:</label>
                        <select id="message-priority" class="form-control">
                            <option value="low">عادية</option>
                            <option value="medium">مهمة</option>
                            <option value="high">عاجلة</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="request-receipt"> طلب إيصال استلام
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeAcademyMessagesModal()">إلغاء</button>
                <button class="btn btn-primary" onclick="sendAcademyMessage()">إرسال الرسالة</button>
            </div>
        </div>
    </div>

    <!-- New Message Modal -->
    <div id="new-message-modal" class="modal hidden">
        <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
                <h2>رسالة جديدة</h2>
                <button onclick="closeNewMessageModal()" class="close-button">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>إلى:</label>
                    <select id="new-message-recipient" class="form-control">
                        <option value="">اختر المستقبل</option>
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </select>
                </div>
                <div class="form-group">
                    <label>الرسالة:</label>
                    <textarea id="new-message-content" class="form-control" rows="4" placeholder="اكتب رسالتك هنا..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeNewMessageModal()">إلغاء</button>
                <button class="btn btn-primary" onclick="sendNewMessage()">إرسال</button>
            </div>
        </div>
    </div>

    <!-- Nudge Modal -->
    <div id="nudge-modal" class="modal hidden">
        <div class="modal-content" style="max-width: 400px;">
            <div class="modal-header">
                <h2>إرسال نكزة</h2>
                <button onclick="closeNudgeModal()" class="close-button">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="nudge-options">
                    <button class="nudge-option" onclick="sendNudge('wave')">
                        <i class="fas fa-hand-paper"></i>
                        <span>تحية</span>
                    </button>
                    <button class="nudge-option" onclick="sendNudge('attention')">
                        <i class="fas fa-exclamation"></i>
                        <span>انتباه</span>
                    </button>
                    <button class="nudge-option" onclick="sendNudge('urgent')">
                        <i class="fas fa-bell"></i>
                        <span>عاجل</span>
                    </button>
                    <button class="nudge-option" onclick="sendNudge('question')">
                        <i class="fas fa-question"></i>
                        <span>استفسار</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // ==================== Global Variables ====================
        let conversations = [];
        let messages = {};
        let currentChatId = null;
        let currentUser = {
            id: 'admin',
            name: 'المدير العام',
            role: 'admin',
            avatar: 'A',
            status: 'online'
        };

        // Privacy and Security Settings
        let privacySettings = {
            hideLastSeen: false,
            autoDeleteMessages: false,
            autoDeleteDays: 30,
            encryptMessages: true,
            hideMessagePreview: false,
            allowScreenshots: true,
            readReceipts: true
        };

        // AI Settings
        let aiSettings = {
            enableSmartReplies: true,
            enablePriorityDetection: true,
            enableContentFilter: true,
            language: 'ar'
        };

        // ==================== Security and Encryption ====================

        // Simple encryption/decryption using Web Crypto API
        async function generateEncryptionKey() {
            return await window.crypto.subtle.generateKey(
                {
                    name: "AES-GCM",
                    length: 256,
                },
                true,
                ["encrypt", "decrypt"]
            );
        }

        // Encrypt message content
        async function encryptMessage(message) {
            if (!privacySettings.encryptMessages) return message;

            try {
                const encoder = new TextEncoder();
                const data = encoder.encode(message);

                // Use a simple key derivation for demo purposes
                const keyMaterial = await window.crypto.subtle.importKey(
                    "raw",
                    encoder.encode("academy7c-secret-key-2024"),
                    { name: "PBKDF2" },
                    false,
                    ["deriveBits", "deriveKey"]
                );

                const key = await window.crypto.subtle.deriveKey(
                    {
                        name: "PBKDF2",
                        salt: encoder.encode("academy7c-salt"),
                        iterations: 100000,
                        hash: "SHA-256",
                    },
                    keyMaterial,
                    { name: "AES-GCM", length: 256 },
                    false,
                    ["encrypt"]
                );

                const iv = window.crypto.getRandomValues(new Uint8Array(12));
                const encrypted = await window.crypto.subtle.encrypt(
                    { name: "AES-GCM", iv: iv },
                    key,
                    data
                );

                // Combine IV and encrypted data
                const combined = new Uint8Array(iv.length + encrypted.byteLength);
                combined.set(iv);
                combined.set(new Uint8Array(encrypted), iv.length);

                return btoa(String.fromCharCode(...combined));
            } catch (error) {
                console.warn('Encryption failed, using plain text:', error);
                return message;
            }
        }

        // Decrypt message content
        async function decryptMessage(encryptedMessage) {
            if (!privacySettings.encryptMessages) return encryptedMessage;

            try {
                const encoder = new TextEncoder();
                const decoder = new TextDecoder();

                // Decode base64
                const combined = new Uint8Array(atob(encryptedMessage).split('').map(c => c.charCodeAt(0)));

                // Extract IV and encrypted data
                const iv = combined.slice(0, 12);
                const encrypted = combined.slice(12);

                // Derive the same key
                const keyMaterial = await window.crypto.subtle.importKey(
                    "raw",
                    encoder.encode("academy7c-secret-key-2024"),
                    { name: "PBKDF2" },
                    false,
                    ["deriveBits", "deriveKey"]
                );

                const key = await window.crypto.subtle.deriveKey(
                    {
                        name: "PBKDF2",
                        salt: encoder.encode("academy7c-salt"),
                        iterations: 100000,
                        hash: "SHA-256",
                    },
                    keyMaterial,
                    { name: "AES-GCM", length: 256 },
                    false,
                    ["decrypt"]
                );

                const decrypted = await window.crypto.subtle.decrypt(
                    { name: "AES-GCM", iv: iv },
                    key,
                    encrypted
                );

                return decoder.decode(decrypted);
            } catch (error) {
                console.warn('Decryption failed, using encrypted text:', error);
                return encryptedMessage;
            }
        }

        // Content filtering for inappropriate content
        function filterContent(message) {
            if (!aiSettings.enableContentFilter) return { filtered: message, flagged: false };

            const inappropriateWords = [
                'كلمة سيئة', 'محتوى غير مناسب', 'spam', 'إعلان'
            ];

            let filtered = message;
            let flagged = false;

            inappropriateWords.forEach(word => {
                if (message.toLowerCase().includes(word.toLowerCase())) {
                    filtered = filtered.replace(new RegExp(word, 'gi'), '***');
                    flagged = true;
                }
            });

            return { filtered, flagged };
        }

        // Screenshot detection (basic implementation)
        function detectScreenshot() {
            if (!privacySettings.allowScreenshots) {
                document.addEventListener('keydown', function(e) {
                    // Detect common screenshot shortcuts
                    if ((e.ctrlKey && e.shiftKey && e.key === 'S') ||
                        (e.key === 'PrintScreen') ||
                        (e.metaKey && e.shiftKey && e.key === '3') ||
                        (e.metaKey && e.shiftKey && e.key === '4')) {

                        e.preventDefault();
                        showNotification('تم منع التقاط الشاشة لحماية الخصوصية', 'warning');

                        // Log screenshot attempt
                        console.warn('Screenshot attempt detected and blocked');
                        return false;
                    }
                });

                // Detect right-click context menu
                document.addEventListener('contextmenu', function(e) {
                    e.preventDefault();
                    showNotification('تم تعطيل القائمة السياقية لحماية الخصوصية', 'info');
                });
            }
        }

        // ==================== Data Management ====================

        // Load conversations from localStorage
        function loadConversations() {
            const stored = localStorage.getItem('academy_conversations');
            if (stored) {
                conversations = JSON.parse(stored);
            } else {
                // Initialize with sample data
                conversations = [
                    {
                        id: 'conv_1',
                        participants: ['admin', 'player_1'],
                        name: 'محمد أحمد الزهراني',
                        avatar: 'م',
                        lastMessage: 'شكراً لكم على التدريب الرائع اليوم',
                        lastMessageTime: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
                        unreadCount: 2,
                        status: 'online'
                    },
                    {
                        id: 'conv_2',
                        participants: ['admin', 'player_2'],
                        name: 'سارة علي الشهري',
                        avatar: 'س',
                        lastMessage: 'متى موعد التدريب القادم؟',
                        lastMessageTime: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
                        unreadCount: 0,
                        status: 'offline'
                    },
                    {
                        id: 'conv_3',
                        participants: ['admin', 'coach_1'],
                        name: 'المدرب أحمد محمد',
                        avatar: 'أ',
                        lastMessage: 'تم تحديث جدول التدريب',
                        lastMessageTime: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString(),
                        unreadCount: 1,
                        status: 'online'
                    }
                ];
                saveConversations();
            }
        }

        // Save conversations to localStorage
        function saveConversations() {
            localStorage.setItem('academy_conversations', JSON.stringify(conversations));
        }

        // Load messages from localStorage
        function loadMessages() {
            const stored = localStorage.getItem('academy_messages');
            if (stored) {
                messages = JSON.parse(stored);
            } else {
                // Initialize with sample messages
                messages = {
                    'conv_1': [
                        {
                            id: 'msg_1',
                            senderId: 'player_1',
                            content: 'السلام عليكم، أريد الاستفسار عن جدول التدريب',
                            timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
                            read: true
                        },
                        {
                            id: 'msg_2',
                            senderId: 'admin',
                            content: 'وعليكم السلام، جدول التدريب متاح في التطبيق',
                            timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),
                            read: true
                        },
                        {
                            id: 'msg_3',
                            senderId: 'player_1',
                            content: 'شكراً لكم على التدريب الرائع اليوم',
                            timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
                            read: false
                        }
                    ],
                    'conv_2': [
                        {
                            id: 'msg_4',
                            senderId: 'player_2',
                            content: 'متى موعد التدريب القادم؟',
                            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
                            read: true
                        }
                    ],
                    'conv_3': [
                        {
                            id: 'msg_5',
                            senderId: 'coach_1',
                            content: 'تم تحديث جدول التدريب',
                            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString(),
                            read: false
                        }
                    ]
                };
                saveMessages();
            }
        }

        // Save messages to localStorage
        function saveMessages() {
            localStorage.setItem('academy_messages', JSON.stringify(messages));
        }

        // ==================== UI Rendering ====================

        // Render conversations list
        function renderConversations() {
            const conversationsList = document.getElementById('conversations-list');

            if (conversations.length === 0) {
                conversationsList.innerHTML = `
                    <div class="empty-state" style="padding: 2rem;">
                        <i class="fas fa-comments" style="font-size: 2rem; margin-bottom: 1rem; color: var(--primary-color);"></i>
                        <p>لا توجد محادثات بعد</p>
                    </div>
                `;
                return;
            }

            conversationsList.innerHTML = conversations.map(conv => `
                <div class="conversation-item ${currentChatId === conv.id ? 'active' : ''}"
                     onclick="openChat('${conv.id}')">
                    <div class="conversation-avatar ${conv.status === 'online' ? 'online' : ''}">
                        ${conv.avatar}
                    </div>
                    <div class="conversation-info">
                        <div class="conversation-name">${conv.name}</div>
                        <div class="conversation-preview">${conv.lastMessage}</div>
                    </div>
                    <div class="conversation-meta">
                        <div class="conversation-time">${formatTime(conv.lastMessageTime)}</div>
                        ${conv.unreadCount > 0 ? `<div class="unread-badge">${conv.unreadCount}</div>` : ''}
                    </div>
                </div>
            `).join('');
        }

        // Render messages for current chat (updated)
        async function renderMessages() {
            if (!currentChatId) return;

            const messagesContainer = document.getElementById('messages-container');
            const chatMessages = messages[currentChatId] || [];

            if (chatMessages.length === 0) {
                messagesContainer.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-comment-dots"></i>
                        <h3>لا توجد رسائل بعد</h3>
                        <p>ابدأ المحادثة بإرسال رسالة</p>
                    </div>
                `;
                return;
            }

            // Decrypt and render messages
            const renderedMessages = await Promise.all(chatMessages.map(async (msg) => {
                let content = '';
                let priorityClass = '';

                // Add priority class
                if (msg.priority) {
                    priorityClass = `priority-${msg.priority}`;
                }

                // Handle different message types
                if (msg.type === 'image') {
                    content = `
                        <img src="${msg.imageData}" alt="صورة" class="message-image" onclick="openImageModal('${msg.imageData}')">
                        ${msg.content ? `<div>${msg.content}</div>` : ''}
                    `;
                } else if (msg.type === 'file') {
                    const fileIcon = getFileIcon(msg.fileName);
                    content = `
                        <div class="message-file" onclick="downloadFile('${msg.fileData}', '${msg.fileName}')">
                            <div class="file-icon">
                                <i class="fas ${fileIcon}"></i>
                            </div>
                            <div class="file-info">
                                <div class="file-name">${msg.fileName}</div>
                                <div class="file-size">${formatFileSize(msg.fileSize)}</div>
                            </div>
                        </div>
                    `;
                } else {
                    // Text message - decrypt if needed
                    const decryptedContent = msg.originalContent || await decryptMessage(msg.content);
                    content = decryptedContent;
                }

                return `
                    <div class="message ${msg.senderId === currentUser.id ? 'sent' : 'received'}">
                        <div class="message-content ${priorityClass}">
                            ${content}
                            <div class="message-time">
                                ${formatTime(msg.timestamp)}
                                ${msg.flagged ? '<i class="fas fa-exclamation-triangle" title="تم تعديل المحتوى"></i>' : ''}
                                ${msg.senderId === currentUser.id && privacySettings.readReceipts ?
                                    '<i class="fas fa-check-double" title="تم التسليم"></i>' : ''}
                            </div>
                        </div>
                    </div>
                `;
            }));

            messagesContainer.innerHTML = renderedMessages.join('');

            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // Mark messages as read
            markMessagesAsRead(currentChatId);
        }

        // Get file icon based on extension
        function getFileIcon(fileName) {
            const extension = fileName.split('.').pop().toLowerCase();

            switch (extension) {
                case 'pdf':
                    return 'fa-file-pdf';
                case 'doc':
                case 'docx':
                    return 'fa-file-word';
                case 'txt':
                    return 'fa-file-alt';
                case 'jpg':
                case 'jpeg':
                case 'png':
                case 'gif':
                    return 'fa-file-image';
                default:
                    return 'fa-file';
            }
        }

        // Open image in modal
        function openImageModal(imageSrc) {
            Swal.fire({
                imageUrl: imageSrc,
                imageAlt: 'صورة',
                showConfirmButton: false,
                showCloseButton: true,
                background: 'var(--background-dark)',
                customClass: {
                    image: 'swal-image-preview'
                }
            });
        }

        // Download file
        function downloadFile(fileData, fileName) {
            const link = document.createElement('a');
            link.href = fileData;
            link.download = fileName;
            link.click();
        }

        // ==================== Chat Functions ====================

        // Open a chat conversation
        function openChat(chatId) {
            currentChatId = chatId;
            const conversation = conversations.find(c => c.id === chatId);

            if (!conversation) return;

            // Update UI
            document.getElementById('empty-chat-state').style.display = 'none';
            document.getElementById('active-chat').style.display = 'flex';

            // Update chat header
            document.getElementById('chat-avatar').textContent = conversation.avatar;
            document.getElementById('chat-name').textContent = conversation.name;
            document.getElementById('chat-status').textContent = conversation.status === 'online' ? 'متصل الآن' : 'آخر ظهور منذ قليل';

            // Render messages and conversations
            renderMessages();
            renderConversations();
        }

        // ==================== AI Features ====================

        // Generate smart replies based on message content
        function generateSmartReplies(messageContent) {
            if (!aiSettings.enableSmartReplies) return [];

            const content = messageContent.toLowerCase();
            let replies = [];

            // Training-related responses
            if (content.includes('تدريب') || content.includes('موعد') || content.includes('جدول')) {
                replies = [
                    'سأتحقق من الجدول وأعود إليك',
                    'التدريب القادم يوم الأحد الساعة 4 مساءً',
                    'يمكنك مراجعة الجدول في التطبيق'
                ];
            }
            // Greeting responses
            else if (content.includes('سلام') || content.includes('مرحبا') || content.includes('أهلا')) {
                replies = [
                    'وعليكم السلام ورحمة الله',
                    'أهلاً وسهلاً بك',
                    'مرحباً، كيف يمكنني مساعدتك؟'
                ];
            }
            // Question responses
            else if (content.includes('؟') || content.includes('كيف') || content.includes('متى') || content.includes('أين')) {
                replies = [
                    'سأجيب على استفسارك قريباً',
                    'دعني أتحقق من المعلومات',
                    'سأتواصل معك بالتفاصيل'
                ];
            }
            // Thank you responses
            else if (content.includes('شكر') || content.includes('ممتاز') || content.includes('رائع')) {
                replies = [
                    'العفو، دائماً في الخدمة',
                    'سعيد لمساعدتك',
                    'شكراً لك على كلماتك الطيبة'
                ];
            }
            // Default responses
            else {
                replies = [
                    'تم استلام رسالتك',
                    'سأرد عليك قريباً',
                    'شكراً لتواصلك معنا'
                ];
            }

            return replies.slice(0, 3); // Return max 3 replies
        }

        // Detect message priority
        function detectMessagePriority(content) {
            if (!aiSettings.enablePriorityDetection) return 'low';

            const urgentKeywords = ['عاجل', 'طارئ', 'مهم جداً', 'فوري', 'ضروري'];
            const importantKeywords = ['مهم', 'استفسار', 'مشكلة', 'موعد', 'تأكيد'];

            const lowerContent = content.toLowerCase();

            if (urgentKeywords.some(keyword => lowerContent.includes(keyword))) {
                return 'high';
            } else if (importantKeywords.some(keyword => lowerContent.includes(keyword))) {
                return 'medium';
            }

            return 'low';
        }

        // Show smart replies
        function showSmartReplies(messageContent) {
            const smartReplies = generateSmartReplies(messageContent);
            if (smartReplies.length === 0) return;

            const smartRepliesContainer = document.getElementById('smart-replies-container');
            if (!smartRepliesContainer) {
                // Create smart replies section
                const messagesPanel = document.querySelector('#active-chat');
                const inputContainer = document.querySelector('.message-input-container');

                const smartRepliesSection = document.createElement('div');
                smartRepliesSection.className = 'smart-replies';
                smartRepliesSection.id = 'smart-replies-section';
                smartRepliesSection.innerHTML = `
                    <div class="smart-replies-title">ردود سريعة مقترحة:</div>
                    <div class="smart-replies-container" id="smart-replies-container"></div>
                `;

                messagesPanel.insertBefore(smartRepliesSection, inputContainer);
            }

            const container = document.getElementById('smart-replies-container');
            container.innerHTML = smartReplies.map(reply => `
                <button class="smart-reply" onclick="useSmartReply('${reply.replace(/'/g, "\\'")}')">
                    ${reply}
                </button>
            `).join('');

            // Auto-hide after 10 seconds
            setTimeout(() => {
                const section = document.getElementById('smart-replies-section');
                if (section) section.remove();
            }, 10000);
        }

        // Use smart reply
        function useSmartReply(reply) {
            const messageInput = document.getElementById('message-input');
            messageInput.value = reply;
            messageInput.focus();

            // Remove smart replies section
            const section = document.getElementById('smart-replies-section');
            if (section) section.remove();
        }

        // ==================== Media and File Handling ====================

        // Toggle attachment menu
        function toggleAttachmentMenu() {
            const menu = document.getElementById('attachment-menu');
            menu.classList.toggle('hidden');
        }

        // Select image
        function selectImage() {
            document.getElementById('image-input').click();
            toggleAttachmentMenu();
        }

        // Select file
        function selectFile() {
            document.getElementById('file-input').click();
            toggleAttachmentMenu();
        }

        // Handle image selection
        function handleImageSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Check file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                showNotification('حجم الصورة كبير جداً (الحد الأقصى 5 ميجابايت)', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('image-preview');
                const previewImage = document.getElementById('preview-image');

                previewImage.src = e.target.result;
                preview.classList.remove('hidden');
            };
            reader.readAsDataURL(file);
        }

        // Handle file selection
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Check file size (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                showNotification('حجم الملف كبير جداً (الحد الأقصى 10 ميجابايت)', 'error');
                return;
            }

            // Send file immediately
            sendFileMessage(file);
        }

        // Remove image preview
        function removeImagePreview() {
            const preview = document.getElementById('image-preview');
            const previewImage = document.getElementById('preview-image');
            const captionInput = document.getElementById('image-caption');

            preview.classList.add('hidden');
            previewImage.src = '';
            captionInput.value = '';

            // Reset file input
            document.getElementById('image-input').value = '';
        }

        // Send image message
        function sendImageMessage() {
            const previewImage = document.getElementById('preview-image');
            const captionInput = document.getElementById('image-caption');

            if (!previewImage.src || !currentChatId) return;

            const newMessage = {
                id: 'msg_' + Date.now(),
                senderId: currentUser.id,
                type: 'image',
                content: captionInput.value.trim() || '',
                imageData: previewImage.src,
                timestamp: new Date().toISOString(),
                read: true
            };

            // Add message to current chat
            if (!messages[currentChatId]) {
                messages[currentChatId] = [];
            }
            messages[currentChatId].push(newMessage);

            // Update conversation
            const conversation = conversations.find(c => c.id === currentChatId);
            if (conversation) {
                conversation.lastMessage = '📷 صورة';
                conversation.lastMessageTime = newMessage.timestamp;
            }

            // Save and update UI
            saveMessages();
            saveConversations();
            renderMessages();
            renderConversations();
            removeImagePreview();

            showNotification('تم إرسال الصورة', 'success');
        }

        // Send file message
        function sendFileMessage(file) {
            if (!currentChatId) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const newMessage = {
                    id: 'msg_' + Date.now(),
                    senderId: currentUser.id,
                    type: 'file',
                    content: '',
                    fileName: file.name,
                    fileSize: file.size,
                    fileData: e.target.result,
                    timestamp: new Date().toISOString(),
                    read: true
                };

                // Add message to current chat
                if (!messages[currentChatId]) {
                    messages[currentChatId] = [];
                }
                messages[currentChatId].push(newMessage);

                // Update conversation
                const conversation = conversations.find(c => c.id === currentChatId);
                if (conversation) {
                    conversation.lastMessage = '📎 ' + file.name;
                    conversation.lastMessageTime = newMessage.timestamp;
                }

                // Save and update UI
                saveMessages();
                saveConversations();
                renderMessages();
                renderConversations();

                showNotification('تم إرسال الملف', 'success');
            };
            reader.readAsDataURL(file);
        }

        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Send a new message (updated)
        async function sendMessage() {
            if (!currentChatId) return;

            // Check if sending image
            const imagePreview = document.getElementById('image-preview');
            if (!imagePreview.classList.contains('hidden')) {
                sendImageMessage();
                return;
            }

            const messageInput = document.getElementById('message-input');
            const content = messageInput.value.trim();

            if (!content) return;

            // Filter content
            const filteredResult = filterContent(content);
            if (filteredResult.flagged) {
                showNotification('تم تعديل الرسالة لإزالة المحتوى غير المناسب', 'warning');
            }

            // Encrypt content
            const encryptedContent = await encryptMessage(filteredResult.filtered);

            // Detect priority
            const priority = detectMessagePriority(content);

            const newMessage = {
                id: 'msg_' + Date.now(),
                senderId: currentUser.id,
                type: 'text',
                content: encryptedContent,
                originalContent: filteredResult.filtered, // Store for display
                priority: priority,
                timestamp: new Date().toISOString(),
                read: true,
                flagged: filteredResult.flagged
            };

            // Add message to current chat
            if (!messages[currentChatId]) {
                messages[currentChatId] = [];
            }
            messages[currentChatId].push(newMessage);

            // Update conversation last message
            const conversation = conversations.find(c => c.id === currentChatId);
            if (conversation) {
                conversation.lastMessage = privacySettings.hideMessagePreview ?
                    'رسالة جديدة' : filteredResult.filtered;
                conversation.lastMessageTime = newMessage.timestamp;
            }

            // Save and update UI
            saveMessages();
            saveConversations();
            renderMessages();
            renderConversations();

            // Clear input
            messageInput.value = '';
            messageInput.style.height = 'auto';

            // Show smart replies for the last received message
            const chatMessages = messages[currentChatId] || [];
            const lastReceivedMessage = chatMessages
                .filter(msg => msg.senderId !== currentUser.id)
                .pop();

            if (lastReceivedMessage && aiSettings.enableSmartReplies) {
                showSmartReplies(lastReceivedMessage.originalContent || lastReceivedMessage.content);
            }

            // Show notification
            showNotification('تم إرسال الرسالة', 'success');
        }

        // Mark messages as read
        function markMessagesAsRead(chatId) {
            if (!messages[chatId]) return;

            let hasUnread = false;
            messages[chatId].forEach(msg => {
                if (msg.senderId !== currentUser.id && !msg.read) {
                    msg.read = true;
                    hasUnread = true;
                }
            });

            if (hasUnread) {
                // Update conversation unread count
                const conversation = conversations.find(c => c.id === chatId);
                if (conversation) {
                    conversation.unreadCount = 0;
                }

                saveMessages();
                saveConversations();
                renderConversations();
            }
        }

        // ==================== Utility Functions ====================

        // Format timestamp
        function formatTime(timestamp) {
            const date = new Date(timestamp);
            const now = new Date();
            const diffInHours = (now - date) / (1000 * 60 * 60);

            if (diffInHours < 1) {
                const minutes = Math.floor((now - date) / (1000 * 60));
                return minutes < 1 ? 'الآن' : `منذ ${minutes} دقيقة`;
            } else if (diffInHours < 24) {
                return `منذ ${Math.floor(diffInHours)} ساعة`;
            } else {
                return date.toLocaleDateString('ar-SA', {
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }
        }

        // Show notification
        function showNotification(message, type = 'info') {
            // Use SweetAlert2 for notifications
            const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });

            Toast.fire({
                icon: type,
                title: message
            });
        }

        // ==================== Event Listeners ====================

        // Message input auto-resize
        document.addEventListener('DOMContentLoaded', function() {
            const messageInput = document.getElementById('message-input');

            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            // Send message on Enter (but not Shift+Enter)
            messageInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Search conversations
            const searchInput = document.getElementById('conversations-search');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const filteredConversations = conversations.filter(conv =>
                    conv.name.toLowerCase().includes(searchTerm) ||
                    conv.lastMessage.toLowerCase().includes(searchTerm)
                );

                // Temporarily update conversations for rendering
                const originalConversations = [...conversations];
                conversations = filteredConversations;
                renderConversations();
                conversations = originalConversations;
            });

            // Check browser compatibility first
            const browserFeatures = checkBrowserCompatibility();
            loadPolyfills();

            // Initialize data and UI
            loadConversations();
            loadMessages();
            loadPrivacySettings();
            loadAcademyMessages();

            // Initialize backup system
            if (browserFeatures.indexedDB) {
                initBackupDB().then(() => {
                    startAutomaticBackup();
                    createBackup(); // Initial backup
                }).catch(error => {
                    console.error('❌ فشل في تهيئة نظام النسخ الاحتياطي:', error);
                });
            }

            // Render UI with performance monitoring
            measurePerformance('conversationRenderTime', () => renderConversations());

            // Request notification permission
            requestNotificationPermission();

            // Add demo controls for testing
            addDemoControls();

            // Apply privacy settings
            if (!privacySettings.allowScreenshots) {
                detectScreenshot();
            }

            // Auto-delete old messages
            autoDeleteOldMessages();

            // Start performance monitoring
            startPerformanceMonitoring();

            // Simulate connection status updates every 30 seconds
            setInterval(() => {
                // Randomly update user status
                conversations.forEach(conv => {
                    if (Math.random() > 0.8) {
                        conv.status = conv.status === 'online' ? 'offline' : 'online';
                    }
                });

                measurePerformance('conversationRenderTime', () => renderConversations());

                // Auto-delete old messages periodically
                autoDeleteOldMessages();

                // Optimize memory usage periodically
                if (Math.random() > 0.9) {
                    optimizeMemoryUsage();
                }
            }, 30000);

            // Handle image preview send button
            const imagePreview = document.getElementById('image-preview');
            if (imagePreview) {
                const sendImageBtn = document.createElement('button');
                sendImageBtn.className = 'btn btn-primary';
                sendImageBtn.innerHTML = '<i class="fas fa-paper-plane"></i> إرسال';
                sendImageBtn.onclick = sendImageMessage;
                imagePreview.appendChild(sendImageBtn);
            }

            // Override renderMessages with optimized version
            window.renderMessages = optimizedRenderMessages;

            console.log('✅ تم تحميل نظام الرسائل الذكي المتقدم بجميع الميزات');
            showNotification('مرحباً بك في نظام الرسائل الذكي المتقدم مع جميع الميزات', 'success');

            // Show feature summary
            setTimeout(() => {
                console.log(`
🎉 نظام الرسائل الذكي المتقدم جاهز!

الميزات المتاحة:
✅ رسائل نصية مع تشفير
✅ دعم الصور والملفات
✅ ردود ذكية بالذكاء الاصطناعي
✅ نظام النكز التفاعلي
✅ ترجمة الرسائل
✅ مراسلة الأكاديميات
✅ نسخ احتياطي تلقائي
✅ تحسين الأداء
✅ حماية الخصوصية

استمتع بالتجربة! 🚀
                `);
            }, 2000);
        });

        // Close modals when clicking outside
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                e.target.classList.add('hidden');
            }

            // Close attachment menu when clicking outside
            const attachmentMenu = document.getElementById('attachment-menu');
            if (attachmentMenu && !attachmentMenu.contains(e.target) &&
                !e.target.closest('.attachment-button')) {
                attachmentMenu.classList.add('hidden');
            }
        });

        // ==================== Notifications Integration ====================

        // Request notification permission
        function requestNotificationPermission() {
            if ('Notification' in window && Notification.permission === 'default') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        showNotification('تم تفعيل إشعارات الرسائل', 'success');
                    }
                });
            }
        }

        // Send browser notification
        function sendBrowserNotification(title, body, icon = null) {
            if ('Notification' in window && Notification.permission === 'granted') {
                const notification = new Notification(title, {
                    body: body,
                    icon: icon || '/favicon.ico',
                    badge: '/favicon.ico',
                    tag: 'academy-message',
                    requireInteraction: false,
                    silent: false
                });

                // Auto close after 5 seconds
                setTimeout(() => notification.close(), 5000);

                // Focus window when notification is clicked
                notification.onclick = function() {
                    window.focus();
                    notification.close();
                };
            }
        }

        // Simulate receiving a new message (for demo purposes)
        function simulateNewMessage() {
            const sampleMessages = [
                'مرحباً، كيف حالك؟',
                'متى موعد التدريب القادم؟',
                'شكراً لك على المساعدة',
                'هل يمكنني الحصول على معلومات إضافية؟',
                'تم تحديث الجدول الزمني'
            ];

            const randomMessage = sampleMessages[Math.floor(Math.random() * sampleMessages.length)];
            const randomConv = conversations[Math.floor(Math.random() * conversations.length)];

            if (randomConv) {
                const newMessage = {
                    id: 'msg_' + Date.now(),
                    senderId: randomConv.participants.find(p => p !== currentUser.id),
                    content: randomMessage,
                    timestamp: new Date().toISOString(),
                    read: false
                };

                // Add message
                if (!messages[randomConv.id]) {
                    messages[randomConv.id] = [];
                }
                messages[randomConv.id].push(newMessage);

                // Update conversation
                randomConv.lastMessage = randomMessage;
                randomConv.lastMessageTime = newMessage.timestamp;
                randomConv.unreadCount = (randomConv.unreadCount || 0) + 1;

                // Save data
                saveMessages();
                saveConversations();

                // Update UI
                renderConversations();
                if (currentChatId === randomConv.id) {
                    renderMessages();
                }

                // Send notification
                sendBrowserNotification(
                    'رسالة جديدة من ' + randomConv.name,
                    randomMessage
                );

                showNotification(`رسالة جديدة من ${randomConv.name}`, 'info');
            }
        }

        // ==================== Privacy Settings Functions ====================

        // Load privacy settings
        function loadPrivacySettings() {
            const stored = localStorage.getItem('academy_privacy_settings');
            if (stored) {
                privacySettings = { ...privacySettings, ...JSON.parse(stored) };
            }

            const storedAI = localStorage.getItem('academy_ai_settings');
            if (storedAI) {
                aiSettings = { ...aiSettings, ...JSON.parse(storedAI) };
            }
        }

        // Save privacy settings
        function savePrivacySettings() {
            localStorage.setItem('academy_privacy_settings', JSON.stringify(privacySettings));
            localStorage.setItem('academy_ai_settings', JSON.stringify(aiSettings));
        }

        // Open privacy settings modal
        function openPrivacySettings() {
            const modal = document.getElementById('privacy-modal');

            // Update checkboxes with current settings
            document.getElementById('hide-last-seen').checked = privacySettings.hideLastSeen;
            document.getElementById('hide-message-preview').checked = privacySettings.hideMessagePreview;
            document.getElementById('read-receipts').checked = privacySettings.readReceipts;
            document.getElementById('encrypt-messages').checked = privacySettings.encryptMessages;
            document.getElementById('allow-screenshots').checked = privacySettings.allowScreenshots;
            document.getElementById('auto-delete').checked = privacySettings.autoDeleteMessages;
            document.getElementById('auto-delete-days').value = privacySettings.autoDeleteDays;
            document.getElementById('smart-replies').checked = aiSettings.enableSmartReplies;
            document.getElementById('priority-detection').checked = aiSettings.enablePriorityDetection;
            document.getElementById('content-filter').checked = aiSettings.enableContentFilter;

            modal.classList.remove('hidden');
        }

        // Close privacy settings modal
        function closePrivacyModal() {
            const modal = document.getElementById('privacy-modal');
            modal.classList.add('hidden');
            savePrivacySettings();

            // Apply screenshot protection
            if (!privacySettings.allowScreenshots) {
                detectScreenshot();
            }

            showNotification('تم حفظ إعدادات الخصوصية', 'success');
        }

        // Update privacy setting
        function updatePrivacySetting(setting, value) {
            privacySettings[setting] = value;

            // Apply immediate effects
            if (setting === 'hideMessagePreview') {
                renderConversations();
            }
        }

        // Update AI setting
        function updateAISetting(setting, value) {
            aiSettings[setting] = value;
        }

        // Reset settings to default
        function resetSettings() {
            Swal.fire({
                title: 'إعادة تعيين الإعدادات',
                text: 'هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'نعم، أعد التعيين',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Reset to defaults
                    privacySettings = {
                        hideLastSeen: false,
                        autoDeleteMessages: false,
                        autoDeleteDays: 30,
                        encryptMessages: true,
                        hideMessagePreview: false,
                        allowScreenshots: true,
                        readReceipts: true
                    };

                    aiSettings = {
                        enableSmartReplies: true,
                        enablePriorityDetection: true,
                        enableContentFilter: true,
                        language: 'ar'
                    };

                    // Update UI
                    openPrivacySettings();
                    showNotification('تم إعادة تعيين الإعدادات', 'success');
                }
            });
        }

        // Auto-delete old messages
        function autoDeleteOldMessages() {
            if (!privacySettings.autoDeleteMessages) return;

            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - privacySettings.autoDeleteDays);

            let deletedCount = 0;
            Object.keys(messages).forEach(chatId => {
                const originalLength = messages[chatId].length;
                messages[chatId] = messages[chatId].filter(msg =>
                    new Date(msg.timestamp) > cutoffDate
                );
                deletedCount += originalLength - messages[chatId].length;
            });

            if (deletedCount > 0) {
                saveMessages();
                renderMessages();
                console.log(`تم حذف ${deletedCount} رسالة قديمة تلقائياً`);
            }
        }

        // ==================== Academy Messages System ====================

        // Message templates for academy communication
        const messageTemplates = {
            partnership: {
                subject: "طلب شراكة استراتيجية",
                content: `السلام عليكم ورحمة الله وبركاته،

نتشرف في أكاديمية 7C للتدريب الرياضي بالتواصل معكم لبحث إمكانية إقامة شراكة استراتيجية بين أكاديميتينا.

نحن نؤمن بأن التعاون بين الأكاديميات الرياضية يساهم في تطوير الرياضة وإعداد جيل من الرياضيين المتميزين.

نود مناقشة:
- تبادل الخبرات التدريبية
- تنظيم معسكرات مشتركة
- إقامة بطولات ودية

نتطلع لردكم الكريم.

مع أطيب التحيات،
إدارة أكاديمية 7C`
            },
            friendly_match: {
                subject: "دعوة لمباراة ودية",
                content: `السلام عليكم ورحمة الله وبركاته،

يسعدنا في أكاديمية 7C دعوتكم لإقامة مباراة ودية بين فرق أكاديميتينا.

تفاصيل المباراة المقترحة:
- التاريخ: [يرجى تحديد التاريخ المناسب]
- المكان: [يمكن التنسيق]
- الفئة العمرية: [حسب الاتفاق]

هذه المباراة ستكون فرصة رائعة لتبادل الخبرات وتطوير مهارات اللاعبين.

نتطلع لموافقتكم.

مع أطيب التحيات،
إدارة أكاديمية 7C`
            },
            training_camp: {
                subject: "دعوة لمعسكر تدريبي مشترك",
                content: `السلام عليكم ورحمة الله وبركاته،

نتشرف بدعوتكم للمشاركة في معسكر تدريبي مشترك تنظمه أكاديمية 7C.

تفاصيل المعسكر:
- المدة: [عدد الأيام]
- المكان: مرافق أكاديمية 7C
- البرنامج: تدريبات مكثفة ومحاضرات تطويرية
- المدربون: نخبة من المدربين المعتمدين

سيكون المعسكر فرصة ممتازة لتطوير مهارات اللاعبين وتبادل الخبرات.

للتسجيل والاستفسار، يرجى التواصل معنا.

مع أطيب التحيات،
إدارة أكاديمية 7C`
            },
            experience_exchange: {
                subject: "برنامج تبادل الخبرات",
                content: `السلام عليكم ورحمة الله وبركاته،

نقترح في أكاديمية 7C إقامة برنامج تبادل خبرات بين أكاديميتينا.

البرنامج يشمل:
- تبادل زيارات المدربين
- مشاركة المناهج التدريبية
- تبادل اللاعبين للتدريب
- ورش عمل مشتركة

نؤمن بأن هذا التعاون سيعود بالنفع على الجميع ويساهم في رفع مستوى التدريب.

نتطلع لآرائكم ومقترحاتكم.

مع أطيب التحيات،
إدارة أكاديمية 7C`
            },
            tournament_invitation: {
                subject: "دعوة للمشاركة في بطولة",
                content: `السلام عليكم ورحمة الله وبركاته،

يسرنا دعوتكم للمشاركة في البطولة التي تنظمها أكاديمية 7C.

تفاصيل البطولة:
- اسم البطولة: [اسم البطولة]
- التاريخ: [تاريخ البطولة]
- المكان: [مكان إقامة البطولة]
- الفئات المشاركة: [الفئات العمرية]
- الجوائز: [تفاصيل الجوائز]

نتطلع لمشاركتكم في هذا الحدث الرياضي المميز.

للتسجيل، يرجى التواصل معنا في أقرب وقت.

مع أطيب التحيات،
إدارة أكاديمية 7C`
            }
        };

        // Academy messages storage
        let academyMessages = [];

        // Load academy messages
        function loadAcademyMessages() {
            const stored = localStorage.getItem('academy_messages');
            if (stored) {
                academyMessages = JSON.parse(stored);
            }
        }

        // Save academy messages
        function saveAcademyMessages() {
            localStorage.setItem('academy_messages', JSON.stringify(academyMessages));
        }

        // Open academy messages modal
        function openAcademyMessagesModal() {
            const modal = document.getElementById('academy-messages-modal');
            modal.classList.remove('hidden');
        }

        // Close academy messages modal
        function closeAcademyMessagesModal() {
            const modal = document.getElementById('academy-messages-modal');
            modal.classList.add('hidden');

            // Reset form
            document.getElementById('target-academy').value = '';
            document.getElementById('message-template').value = '';
            document.getElementById('message-subject').value = '';
            document.getElementById('message-content').value = '';
            document.getElementById('message-priority').value = 'low';
            document.getElementById('request-receipt').checked = false;
        }

        // Load message template
        function loadMessageTemplate() {
            const templateType = document.getElementById('message-template').value;
            const subjectField = document.getElementById('message-subject');
            const contentField = document.getElementById('message-content');

            if (templateType && messageTemplates[templateType]) {
                subjectField.value = messageTemplates[templateType].subject;
                contentField.value = messageTemplates[templateType].content;
            } else if (templateType === 'custom') {
                subjectField.value = '';
                contentField.value = '';
            }
        }

        // Send academy message
        function sendAcademyMessage() {
            const targetAcademy = document.getElementById('target-academy').value;
            const subject = document.getElementById('message-subject').value.trim();
            const content = document.getElementById('message-content').value.trim();
            const priority = document.getElementById('message-priority').value;
            const requestReceipt = document.getElementById('request-receipt').checked;

            if (!targetAcademy || !subject || !content) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            const academyMessage = {
                id: 'academy_msg_' + Date.now(),
                targetAcademy: targetAcademy,
                subject: subject,
                content: content,
                priority: priority,
                requestReceipt: requestReceipt,
                timestamp: new Date().toISOString(),
                status: 'sent',
                readReceipt: false
            };

            academyMessages.push(academyMessage);
            saveAcademyMessages();
            closeAcademyMessagesModal();

            // Simulate sending
            showNotification('تم إرسال الرسالة للأكاديمية بنجاح', 'success');

            // Simulate receipt after 5 seconds if requested
            if (requestReceipt) {
                setTimeout(() => {
                    academyMessage.readReceipt = true;
                    saveAcademyMessages();
                    showNotification('تم استلام الرسالة من قبل الأكاديمية المستهدفة', 'info');
                }, 5000);
            }
        }

        // ==================== Advanced Features ====================

        // Nudge system
        function openNudgeModal() {
            if (!currentChatId) {
                showNotification('يرجى اختيار محادثة أولاً', 'warning');
                return;
            }

            const modal = document.getElementById('nudge-modal');
            modal.classList.remove('hidden');
        }

        function closeNudgeModal() {
            const modal = document.getElementById('nudge-modal');
            modal.classList.add('hidden');
        }

        function sendNudge(type) {
            if (!currentChatId) return;

            const nudgeMessages = {
                wave: '👋 يلوح لك',
                attention: '⚠️ يطلب انتباهك',
                urgent: '🔔 رسالة عاجلة',
                question: '❓ لديه استفسار'
            };

            const nudgeMessage = {
                id: 'nudge_' + Date.now(),
                senderId: currentUser.id,
                type: 'nudge',
                nudgeType: type,
                content: nudgeMessages[type],
                timestamp: new Date().toISOString(),
                read: true
            };

            // Add nudge to current chat
            if (!messages[currentChatId]) {
                messages[currentChatId] = [];
            }
            messages[currentChatId].push(nudgeMessage);

            // Update conversation
            const conversation = conversations.find(c => c.id === currentChatId);
            if (conversation) {
                conversation.lastMessage = nudgeMessages[type];
                conversation.lastMessageTime = nudgeMessage.timestamp;
            }

            // Save and update UI
            saveMessages();
            saveConversations();
            renderMessages();
            renderConversations();
            closeNudgeModal();

            // Play nudge sound
            playNudgeSound();

            showNotification('تم إرسال النكزة', 'success');
        }

        // Play nudge sound
        function playNudgeSound() {
            // Create audio context for nudge sound
            if ('AudioContext' in window || 'webkitAudioContext' in window) {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
            }
        }

        // Translation system
        async function translateMessage(text, targetLang = 'en') {
            // Simulate translation (in real app, use Google Translate API)
            const translations = {
                'مرحبا': 'Hello',
                'شكرا': 'Thank you',
                'كيف حالك': 'How are you',
                'التدريب': 'Training',
                'موعد': 'Appointment',
                'جدول': 'Schedule'
            };

            // Simple word-by-word translation for demo
            let translated = text;
            Object.keys(translations).forEach(arabic => {
                translated = translated.replace(new RegExp(arabic, 'g'), translations[arabic]);
            });

            return translated !== text ? translated : 'Translation not available';
        }

        // Show translation popup
        async function showTranslation(messageElement, originalText) {
            const translated = await translateMessage(originalText);

            // Remove existing popup
            const existingPopup = document.querySelector('.translation-popup');
            if (existingPopup) {
                existingPopup.remove();
            }

            // Create translation popup
            const popup = document.createElement('div');
            popup.className = 'translation-popup';
            popup.innerHTML = `
                <div class="translation-header">
                    <span class="translation-lang">English</span>
                    <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; cursor: pointer;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="translation-text">${translated}</div>
            `;

            // Position popup
            const rect = messageElement.getBoundingClientRect();
            popup.style.position = 'fixed';
            popup.style.top = (rect.top - 80) + 'px';
            popup.style.left = rect.left + 'px';

            document.body.appendChild(popup);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (popup.parentElement) {
                    popup.remove();
                }
            }, 5000);
        }

        // ==================== Modal Functions ====================

        function openNewMessageModal() {
            const modal = document.getElementById('new-message-modal');
            const recipientSelect = document.getElementById('new-message-recipient');

            // Populate recipients
            recipientSelect.innerHTML = '<option value="">اختر المستقبل</option>';
            conversations.forEach(conv => {
                const option = document.createElement('option');
                option.value = conv.id;
                option.textContent = conv.name;
                recipientSelect.appendChild(option);
            });

            modal.classList.remove('hidden');
        }

        function closeNewMessageModal() {
            const modal = document.getElementById('new-message-modal');
            modal.classList.add('hidden');
            document.getElementById('new-message-recipient').value = '';
            document.getElementById('new-message-content').value = '';
        }

        function sendNewMessage() {
            const recipientId = document.getElementById('new-message-recipient').value;
            const content = document.getElementById('new-message-content').value.trim();

            if (!recipientId || !content) {
                showNotification('يرجى اختيار المستقبل وكتابة الرسالة', 'error');
                return;
            }

            // Switch to the conversation and send message
            openChat(recipientId);
            document.getElementById('message-input').value = content;
            sendMessage();
            closeNewMessageModal();
        }

        function toggleChatInfo() {
            if (!currentChatId) return;

            const conversation = conversations.find(c => c.id === currentChatId);
            if (!conversation) return;

            Swal.fire({
                title: 'معلومات المحادثة',
                html: `
                    <div style="text-align: right;">
                        <p><strong>الاسم:</strong> ${conversation.name}</p>
                        <p><strong>الحالة:</strong> ${conversation.status === 'online' ? 'متصل' : 'غير متصل'}</p>
                        <p><strong>عدد الرسائل:</strong> ${(messages[currentChatId] || []).length}</p>
                        <p><strong>آخر رسالة:</strong> ${formatTime(conversation.lastMessageTime)}</p>
                        <hr style="margin: 1rem 0; border-color: #333;">
                        <button onclick="openNudgeModal(); Swal.close();" class="btn btn-secondary" style="margin: 0.25rem;">
                            <i class="fas fa-hand-paper"></i> إرسال نكزة
                        </button>
                        <button onclick="showTranslation(document.querySelector('.message-content'), '${conversation.lastMessage}'); Swal.close();" class="btn btn-secondary" style="margin: 0.25rem;">
                            <i class="fas fa-language"></i> ترجمة
                        </button>
                    </div>
                `,
                icon: 'info',
                confirmButtonText: 'موافق',
                background: 'var(--background-dark)',
                color: '#ffffff',
                showCloseButton: true
            });
        }

        // ==================== Backup System ====================

        // Backup configuration
        const backupConfig = {
            enabled: true,
            interval: 5 * 60 * 1000, // 5 minutes
            maxBackups: 10,
            compressionEnabled: true
        };

        // Backup storage using IndexedDB
        let backupDB;

        // Initialize IndexedDB for backups
        function initBackupDB() {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open('AcademyMessagesBackup', 1);

                request.onerror = () => reject(request.error);
                request.onsuccess = () => {
                    backupDB = request.result;
                    resolve(backupDB);
                };

                request.onupgradeneeded = (event) => {
                    const db = event.target.result;

                    // Create backups store
                    if (!db.objectStoreNames.contains('backups')) {
                        const backupStore = db.createObjectStore('backups', { keyPath: 'id' });
                        backupStore.createIndex('timestamp', 'timestamp', { unique: false });
                    }
                };
            });
        }

        // Create backup
        async function createBackup() {
            if (!backupConfig.enabled || !backupDB) return;

            try {
                const backupData = {
                    conversations: conversations,
                    messages: messages,
                    privacySettings: privacySettings,
                    aiSettings: aiSettings,
                    academyMessages: academyMessages
                };

                // Compress data if enabled
                let dataToStore = JSON.stringify(backupData);
                if (backupConfig.compressionEnabled) {
                    dataToStore = await compressData(dataToStore);
                }

                const backup = {
                    id: 'backup_' + Date.now(),
                    timestamp: new Date().toISOString(),
                    data: dataToStore,
                    compressed: backupConfig.compressionEnabled,
                    size: new Blob([dataToStore]).size
                };

                // Store backup
                const transaction = backupDB.transaction(['backups'], 'readwrite');
                const store = transaction.objectStore('backups');
                await store.add(backup);

                // Clean old backups
                await cleanOldBackups();

                showBackupStatus('تم إنشاء نسخة احتياطية', 'success');
                console.log('✅ تم إنشاء نسخة احتياطية:', backup.id);

            } catch (error) {
                console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);
                showBackupStatus('فشل في إنشاء النسخة الاحتياطية', 'error');
            }
        }

        // Restore from backup
        async function restoreFromBackup(backupId) {
            if (!backupDB) return;

            try {
                const transaction = backupDB.transaction(['backups'], 'readonly');
                const store = transaction.objectStore('backups');
                const backup = await store.get(backupId);

                if (!backup) {
                    throw new Error('النسخة الاحتياطية غير موجودة');
                }

                // Decompress if needed
                let dataToRestore = backup.data;
                if (backup.compressed) {
                    dataToRestore = await decompressData(dataToRestore);
                }

                const backupData = JSON.parse(dataToRestore);

                // Restore data
                conversations = backupData.conversations || [];
                messages = backupData.messages || {};
                privacySettings = backupData.privacySettings || privacySettings;
                aiSettings = backupData.aiSettings || aiSettings;
                academyMessages = backupData.academyMessages || [];

                // Save to localStorage
                saveConversations();
                saveMessages();
                savePrivacySettings();
                saveAcademyMessages();

                // Update UI
                renderConversations();
                renderMessages();

                showNotification('تم استعادة النسخة الاحتياطية بنجاح', 'success');
                console.log('✅ تم استعادة النسخة الاحتياطية:', backupId);

            } catch (error) {
                console.error('❌ خطأ في استعادة النسخة الاحتياطية:', error);
                showNotification('فشل في استعادة النسخة الاحتياطية', 'error');
            }
        }

        // Clean old backups
        async function cleanOldBackups() {
            if (!backupDB) return;

            try {
                const transaction = backupDB.transaction(['backups'], 'readwrite');
                const store = transaction.objectStore('backups');
                const index = store.index('timestamp');

                const allBackups = await index.getAll();

                if (allBackups.length > backupConfig.maxBackups) {
                    // Sort by timestamp and remove oldest
                    allBackups.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
                    const toDelete = allBackups.slice(0, allBackups.length - backupConfig.maxBackups);

                    for (const backup of toDelete) {
                        await store.delete(backup.id);
                    }

                    console.log(`🗑️ تم حذف ${toDelete.length} نسخة احتياطية قديمة`);
                }
            } catch (error) {
                console.error('❌ خطأ في تنظيف النسخ الاحتياطية:', error);
            }
        }

        // Simple compression using gzip-like algorithm
        async function compressData(data) {
            // Simple compression simulation (in real app, use pako.js or similar)
            return btoa(data);
        }

        // Simple decompression
        async function decompressData(compressedData) {
            return atob(compressedData);
        }

        // Show backup status
        function showBackupStatus(message, type) {
            const statusElement = document.getElementById('backup-status') || createBackupStatusElement();
            statusElement.textContent = message;
            statusElement.className = `backup-status ${type}`;
            statusElement.style.display = 'block';

            setTimeout(() => {
                statusElement.style.display = 'none';
            }, 3000);
        }

        // Create backup status element
        function createBackupStatusElement() {
            const element = document.createElement('div');
            element.id = 'backup-status';
            element.className = 'backup-status';
            element.style.display = 'none';
            document.body.appendChild(element);
            return element;
        }

        // Start automatic backup
        function startAutomaticBackup() {
            if (!backupConfig.enabled) return;

            setInterval(createBackup, backupConfig.interval);
            console.log(`🔄 تم تفعيل النسخ الاحتياطي التلقائي كل ${backupConfig.interval / 60000} دقائق`);
        }

        // ==================== Performance Optimization ====================

        // Performance monitoring
        const performanceMetrics = {
            messageRenderTime: 0,
            conversationRenderTime: 0,
            memoryUsage: 0,
            lastUpdate: Date.now()
        };

        // Measure performance
        function measurePerformance(operation, fn) {
            const start = performance.now();
            const result = fn();
            const end = performance.now();

            performanceMetrics[operation] = end - start;
            performanceMetrics.lastUpdate = Date.now();

            // Show performance indicator if slow
            if (end - start > 100) {
                showPerformanceWarning(operation, end - start);
            }

            return result;
        }

        // Show performance warning
        function showPerformanceWarning(operation, time) {
            const indicator = document.getElementById('performance-indicator') || createPerformanceIndicator();
            indicator.textContent = `${operation}: ${time.toFixed(2)}ms`;
            indicator.classList.add('show');

            setTimeout(() => {
                indicator.classList.remove('show');
            }, 3000);
        }

        // Create performance indicator
        function createPerformanceIndicator() {
            const element = document.createElement('div');
            element.id = 'performance-indicator';
            element.className = 'performance-indicator';
            document.body.appendChild(element);
            return element;
        }

        // Optimize message rendering with virtual scrolling
        function optimizedRenderMessages() {
            if (!currentChatId) return;

            return measurePerformance('messageRenderTime', () => {
                const messagesContainer = document.getElementById('messages-container');
                const chatMessages = messages[currentChatId] || [];

                // Virtual scrolling for large message lists
                if (chatMessages.length > 100) {
                    renderVirtualMessages(messagesContainer, chatMessages);
                } else {
                    renderAllMessages(messagesContainer, chatMessages);
                }
            });
        }

        // Virtual scrolling implementation
        function renderVirtualMessages(container, allMessages) {
            const containerHeight = container.clientHeight;
            const messageHeight = 80; // Estimated message height
            const visibleCount = Math.ceil(containerHeight / messageHeight) + 5; // Buffer

            const scrollTop = container.scrollTop;
            const startIndex = Math.floor(scrollTop / messageHeight);
            const endIndex = Math.min(startIndex + visibleCount, allMessages.length);

            const visibleMessages = allMessages.slice(startIndex, endIndex);

            // Render only visible messages
            container.innerHTML = visibleMessages.map((msg, index) => {
                const actualIndex = startIndex + index;
                return renderSingleMessage(msg, actualIndex);
            }).join('');

            // Maintain scroll position
            container.style.paddingTop = (startIndex * messageHeight) + 'px';
            container.style.paddingBottom = ((allMessages.length - endIndex) * messageHeight) + 'px';
        }

        // Render all messages (for smaller lists)
        async function renderAllMessages(container, chatMessages) {
            if (chatMessages.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-comment-dots"></i>
                        <h3>لا توجد رسائل بعد</h3>
                        <p>ابدأ المحادثة بإرسال رسالة</p>
                    </div>
                `;
                return;
            }

            const renderedMessages = await Promise.all(chatMessages.map(renderSingleMessage));
            container.innerHTML = renderedMessages.join('');

            // Scroll to bottom
            container.scrollTop = container.scrollHeight;

            // Mark messages as read
            markMessagesAsRead(currentChatId);
        }

        // Render single message
        async function renderSingleMessage(msg, index = 0) {
            let content = '';
            let priorityClass = '';

            // Add priority class
            if (msg.priority) {
                priorityClass = `priority-${msg.priority}`;
            }

            // Handle different message types
            if (msg.type === 'image') {
                content = `
                    <img src="${msg.imageData}" alt="صورة" class="message-image" onclick="openImageModal('${msg.imageData}')">
                    ${msg.content ? `<div>${msg.content}</div>` : ''}
                `;
            } else if (msg.type === 'file') {
                const fileIcon = getFileIcon(msg.fileName);
                content = `
                    <div class="message-file" onclick="downloadFile('${msg.fileData}', '${msg.fileName}')">
                        <div class="file-icon">
                            <i class="fas ${fileIcon}"></i>
                        </div>
                        <div class="file-info">
                            <div class="file-name">${msg.fileName}</div>
                            <div class="file-size">${formatFileSize(msg.fileSize)}</div>
                        </div>
                    </div>
                `;
            } else if (msg.type === 'nudge') {
                content = `
                    <div class="nudge-message">
                        <i class="fas fa-hand-paper"></i>
                        ${msg.content}
                    </div>
                `;
            } else if (msg.type === 'video') {
                content = `
                    <video class="message-video" controls>
                        <source src="${msg.videoData}" type="video/mp4">
                        متصفحك لا يدعم تشغيل الفيديو
                    </video>
                    ${msg.content ? `<div>${msg.content}</div>` : ''}
                `;
            } else {
                // Text message - decrypt if needed
                const decryptedContent = msg.originalContent || await decryptMessage(msg.content);
                content = `
                    <div class="message-text" ondblclick="showTranslation(this, '${decryptedContent.replace(/'/g, "\\'")}')">
                        ${decryptedContent}
                    </div>
                `;
            }

            return `
                <div class="message ${msg.senderId === currentUser.id ? 'sent' : 'received'}" data-message-id="${msg.id}">
                    <div class="message-content ${priorityClass}">
                        ${content}
                        <div class="message-time">
                            ${formatTime(msg.timestamp)}
                            ${msg.flagged ? '<i class="fas fa-exclamation-triangle" title="تم تعديل المحتوى"></i>' : ''}
                            ${msg.senderId === currentUser.id && privacySettings.readReceipts ?
                                '<i class="fas fa-check-double" title="تم التسليم"></i>' : ''}
                        </div>
                    </div>
                </div>
            `;
        }

        // Memory management
        function optimizeMemoryUsage() {
            // Clear old message cache
            Object.keys(messages).forEach(chatId => {
                if (messages[chatId].length > 1000) {
                    // Keep only last 500 messages
                    messages[chatId] = messages[chatId].slice(-500);
                }
            });

            // Clear old academy messages
            if (academyMessages.length > 100) {
                academyMessages = academyMessages.slice(-50);
                saveAcademyMessages();
            }

            // Force garbage collection if available
            if (window.gc) {
                window.gc();
            }

            console.log('🧹 تم تحسين استخدام الذاكرة');
        }

        // Browser compatibility checks
        function checkBrowserCompatibility() {
            const features = {
                localStorage: typeof Storage !== 'undefined',
                indexedDB: 'indexedDB' in window,
                webCrypto: 'crypto' in window && 'subtle' in window.crypto,
                notifications: 'Notification' in window,
                audioContext: 'AudioContext' in window || 'webkitAudioContext' in window,
                fileReader: 'FileReader' in window
            };

            const unsupported = Object.keys(features).filter(feature => !features[feature]);

            if (unsupported.length > 0) {
                console.warn('⚠️ ميزات غير مدعومة:', unsupported);
                showNotification(`بعض الميزات قد لا تعمل في متصفحك: ${unsupported.join(', ')}`, 'warning');
            } else {
                console.log('✅ جميع الميزات مدعومة في هذا المتصفح');
            }

            return features;
        }

        // Polyfills for older browsers
        function loadPolyfills() {
            // Promise polyfill for IE
            if (!window.Promise) {
                console.log('Loading Promise polyfill...');
                // In real app, load polyfill from CDN
            }

            // Fetch polyfill for IE
            if (!window.fetch) {
                console.log('Loading Fetch polyfill...');
                // In real app, load polyfill from CDN
            }
        }

        // Performance monitoring interval
        function startPerformanceMonitoring() {
            setInterval(() => {
                // Monitor memory usage
                if (performance.memory) {
                    performanceMetrics.memoryUsage = performance.memory.usedJSHeapSize;

                    // Optimize if memory usage is high
                    if (performance.memory.usedJSHeapSize > 50 * 1024 * 1024) { // 50MB
                        optimizeMemoryUsage();
                    }
                }

                // Log performance metrics
                console.log('📊 Performance Metrics:', performanceMetrics);
            }, 60000); // Every minute
        }

        function clearChat() {
            if (!currentChatId) return;

            Swal.fire({
                title: 'تأكيد مسح المحادثة',
                text: 'هل أنت متأكد من مسح جميع رسائل هذه المحادثة؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'نعم، امسح',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    messages[currentChatId] = [];
                    saveMessages();
                    renderMessages();
                    showNotification('تم مسح المحادثة', 'success');
                }
            });
        }

        // ==================== Demo Functions ====================

        // Add demo button for testing
        function addDemoControls() {
            const header = document.querySelector('.header-actions');
            if (header) {
                const demoButton = document.createElement('button');
                demoButton.className = 'btn btn-secondary';
                demoButton.innerHTML = '<i class="fas fa-flask"></i> رسالة تجريبية';
                demoButton.onclick = simulateNewMessage;
                header.insertBefore(demoButton, header.firstChild);
            }
        }
    </script>
</body>
</html>
