<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة بيانات تجريبية - نظام QR Code</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #1a1a1a;
            color: #ffffff;
        }
        .btn {
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
        }
        .card {
            background: #2d2d2d;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #1e40af;
        }
    </style>
</head>
<body>
    <div class="container mx-auto p-6">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-blue-400 mb-4">
                <i class="fas fa-database text-2xl ml-3"></i>
                إضافة بيانات تجريبية لنظام QR Code
            </h1>
            <p class="text-gray-400">إضافة لاعبين تجريبيين لاختبار نظام QR Code</p>
        </div>

        <div class="card">
            <h3 class="text-xl font-bold mb-4 text-blue-400">
                <i class="fas fa-users ml-2"></i>
                إضافة لاعبين تجريبيين
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <button onclick="addTestPlayers()" class="btn">
                    <i class="fas fa-plus ml-2"></i>
                    إضافة 5 لاعبين تجريبيين
                </button>
                <button onclick="clearAllData()" class="btn bg-red-600">
                    <i class="fas fa-trash ml-2"></i>
                    مسح جميع البيانات
                </button>
                <button onclick="openQRSystem()" class="btn bg-green-600">
                    <i class="fas fa-qrcode ml-2"></i>
                    فتح نظام QR Code
                </button>
                <button onclick="openAdminPanel()" class="btn bg-purple-600">
                    <i class="fas fa-tachometer-alt ml-2"></i>
                    فتح لوحة الإدارة
                </button>
            </div>

            <div id="status" class="text-center text-gray-400">
                جاهز لإضافة البيانات التجريبية
            </div>
        </div>

        <div class="card">
            <h3 class="text-xl font-bold mb-4 text-green-400">
                <i class="fas fa-info-circle ml-2"></i>
                معلومات النظام
            </h3>
            <div id="systemInfo" class="space-y-2">
                <!-- سيتم ملؤها بالجافا سكريبت -->
            </div>
        </div>
    </div>

    <script>
        // بيانات اللاعبين التجريبية
        const testPlayers = [
            {
                id: 'DEMO001',
                firstName: 'أحمد',
                familyName: 'محمد',
                academicNumber: '7C-2024-DEMO001',
                age: 16,
                category: 'ناشئين',
                joinDate: '2024-01-15',
                loyaltyPoints: 50,
                subscriptionStatus: 'active',
                phone: '0501234567',
                email: '<EMAIL>'
            },
            {
                id: 'DEMO002',
                firstName: 'سارة',
                familyName: 'علي',
                academicNumber: '7C-2024-DEMO002',
                age: 14,
                category: 'براعم',
                joinDate: '2024-02-01',
                loyaltyPoints: 30,
                subscriptionStatus: 'active',
                phone: '0507654321',
                email: '<EMAIL>'
            },
            {
                id: 'DEMO003',
                firstName: 'محمد',
                familyName: 'خالد',
                academicNumber: '7C-2024-DEMO003',
                age: 18,
                category: 'شباب',
                joinDate: '2024-01-20',
                loyaltyPoints: 75,
                subscriptionStatus: 'active',
                phone: '0551234567',
                email: '<EMAIL>'
            },
            {
                id: 'DEMO004',
                firstName: 'فاطمة',
                familyName: 'أحمد',
                academicNumber: '7C-2024-DEMO004',
                age: 12,
                category: 'براعم',
                joinDate: '2024-03-01',
                loyaltyPoints: 25,
                subscriptionStatus: 'active',
                phone: '0509876543',
                email: '<EMAIL>'
            },
            {
                id: 'DEMO005',
                firstName: 'عبدالله',
                familyName: 'سالم',
                academicNumber: '7C-2024-DEMO005',
                age: 17,
                category: 'شباب',
                joinDate: '2024-02-15',
                loyaltyPoints: 60,
                subscriptionStatus: 'active',
                phone: '0556789012',
                email: '<EMAIL>'
            }
        ];

        // إضافة اللاعبين التجريبيين
        function addTestPlayers() {
            try {
                let addedCount = 0;
                
                testPlayers.forEach(player => {
                    const key = `academy_player_${player.academicNumber}`;
                    
                    // التحقق من عدم وجود اللاعب مسبقاً
                    if (!localStorage.getItem(key)) {
                        localStorage.setItem(key, JSON.stringify(player));
                        addedCount++;
                    }
                });
                
                updateStatus(`✅ تم إضافة ${addedCount} لاعب تجريبي بنجاح`);
                updateSystemInfo();
                
                Swal.fire({
                    icon: 'success',
                    title: 'تم بنجاح!',
                    text: `تم إضافة ${addedCount} لاعب تجريبي`,
                    background: '#2d2d2d',
                    color: '#ffffff'
                });
                
            } catch (error) {
                console.error('خطأ في إضافة اللاعبين:', error);
                updateStatus('❌ خطأ في إضافة اللاعبين');
                
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ في إضافة اللاعبين التجريبيين',
                    background: '#2d2d2d',
                    color: '#ffffff'
                });
            }
        }

        // مسح جميع البيانات
        function clearAllData() {
            Swal.fire({
                title: 'تأكيد المسح',
                text: 'هل أنت متأكد من مسح جميع البيانات التجريبية؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، امسح',
                cancelButtonText: 'إلغاء',
                background: '#2d2d2d',
                color: '#ffffff'
            }).then((result) => {
                if (result.isConfirmed) {
                    try {
                        // مسح اللاعبين التجريبيين
                        testPlayers.forEach(player => {
                            const key = `academy_player_${player.academicNumber}`;
                            localStorage.removeItem(key);
                        });
                        
                        // مسح بيانات QR Code
                        localStorage.removeItem('qr_codes');
                        localStorage.removeItem('qr_scan_history');
                        localStorage.removeItem('qr_settings');
                        
                        updateStatus('🗑️ تم مسح جميع البيانات التجريبية');
                        updateSystemInfo();
                        
                        Swal.fire({
                            icon: 'success',
                            title: 'تم المسح',
                            text: 'تم مسح جميع البيانات التجريبية بنجاح',
                            background: '#2d2d2d',
                            color: '#ffffff'
                        });
                        
                    } catch (error) {
                        console.error('خطأ في مسح البيانات:', error);
                        updateStatus('❌ خطأ في مسح البيانات');
                    }
                }
            });
        }

        // فتح نظام QR Code
        function openQRSystem() {
            window.open('qr-code-system.html', '_blank');
        }

        // فتح لوحة الإدارة
        function openAdminPanel() {
            window.open('admin-advanced.html', '_blank');
        }

        // تحديث الحالة
        function updateStatus(message) {
            const statusElement = document.getElementById('status');
            if (statusElement) {
                statusElement.innerHTML = message;
                statusElement.className = message.includes('✅') ? 'text-center text-green-400' :
                                        message.includes('❌') ? 'text-center text-red-400' :
                                        message.includes('🗑️') ? 'text-center text-yellow-400' :
                                        'text-center text-gray-400';
            }
        }

        // تحديث معلومات النظام
        function updateSystemInfo() {
            try {
                const systemInfoElement = document.getElementById('systemInfo');
                if (!systemInfoElement) return;
                
                // عدد اللاعبين
                let playersCount = 0;
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('academy_player_')) {
                        playersCount++;
                    }
                }
                
                // عدد QR Codes
                const qrCodes = JSON.parse(localStorage.getItem('qr_codes') || '[]');
                
                // عدد عمليات المسح
                const scanHistory = JSON.parse(localStorage.getItem('qr_scan_history') || '[]');
                
                systemInfoElement.innerHTML = `
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-blue-900 bg-opacity-50 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-blue-400">${playersCount}</div>
                            <div class="text-sm text-gray-400">إجمالي اللاعبين</div>
                        </div>
                        <div class="bg-green-900 bg-opacity-50 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-green-400">${qrCodes.length}</div>
                            <div class="text-sm text-gray-400">رموز QR المُنشأة</div>
                        </div>
                        <div class="bg-purple-900 bg-opacity-50 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-purple-400">${scanHistory.length}</div>
                            <div class="text-sm text-gray-400">عمليات المسح</div>
                        </div>
                    </div>
                    <div class="mt-4 text-sm text-gray-400">
                        <p><strong>آخر تحديث:</strong> ${new Date().toLocaleString('ar-SA')}</p>
                    </div>
                `;
                
            } catch (error) {
                console.error('خطأ في تحديث معلومات النظام:', error);
            }
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateSystemInfo();
            updateStatus('جاهز لإضافة البيانات التجريبية');
        });
    </script>
</body>
</html>
