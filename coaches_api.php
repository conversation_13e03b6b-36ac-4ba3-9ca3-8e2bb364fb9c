<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'komaro';
$password = 'ZbShaker@14';
$database = 'coaches7c';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage()
    ]);
    exit();
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'get_all_coaches':
            getAllCoaches($pdo);
            break;
            
        case 'add_coach':
            addCoach($pdo, $input['coach']);
            break;
            
        case 'update_coach':
            updateCoach($pdo, $input['coachId'], $input['coach']);
            break;
            
        case 'delete_coach':
            deleteCoach($pdo, $input['coachId']);
            break;
            
        case 'get_coach':
            getCoach($pdo, $input['coachId']);
            break;
            
        default:
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'إجراء غير صحيح'
            ]);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage()
    ]);
}

// دالة جلب جميع المدربين
function getAllCoaches($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT 
                id, name, specialty, experience, status, rating, 
                phone, email, photo, join_date as joinDate, 
                players_count as playersCount, salary, certificates, notes
            FROM coaches 
            ORDER BY join_date DESC
        ");
        $stmt->execute();
        $coaches = $stmt->fetchAll();
        
        // تحويل الشهادات من JSON إلى مصفوفة
        foreach ($coaches as &$coach) {
            if ($coach['certificates']) {
                $coach['certificates'] = json_decode($coach['certificates'], true) ?: [];
            } else {
                $coach['certificates'] = [];
            }
        }
        
        echo json_encode([
            'success' => true,
            'coaches' => $coaches
        ]);
    } catch (PDOException $e) {
        throw new Exception('خطأ في جلب بيانات المدربين: ' . $e->getMessage());
    }
}

// دالة إضافة مدرب جديد
function addCoach($pdo, $coachData) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO coaches (
                name, specialty, experience, status, rating, phone, email, 
                photo, join_date, players_count, salary, certificates, notes
            ) VALUES (
                :name, :specialty, :experience, :status, :rating, :phone, :email,
                :photo, :join_date, :players_count, :salary, :certificates, :notes
            )
        ");
        
        // تحضير البيانات
        $certificates = isset($coachData['certificates']) ? json_encode($coachData['certificates']) : null;
        $photo = $coachData['photo'] ?? generateDefaultPhoto($coachData['name']);
        
        $stmt->execute([
            ':name' => $coachData['name'],
            ':specialty' => $coachData['specialty'],
            ':experience' => $coachData['experience'],
            ':status' => $coachData['status'] ?? 'نشط',
            ':rating' => $coachData['rating'] ?? 0,
            ':phone' => $coachData['phone'] ?? null,
            ':email' => $coachData['email'] ?? null,
            ':photo' => $photo,
            ':join_date' => $coachData['joinDate'] ?? date('Y-m-d'),
            ':players_count' => $coachData['playersCount'] ?? 0,
            ':salary' => $coachData['salary'] ?? null,
            ':certificates' => $certificates,
            ':notes' => $coachData['notes'] ?? null
        ]);
        
        $coachId = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة المدرب بنجاح',
            'coach_id' => $coachId
        ]);
    } catch (PDOException $e) {
        throw new Exception('خطأ في إضافة المدرب: ' . $e->getMessage());
    }
}

// دالة تحديث بيانات مدرب
function updateCoach($pdo, $coachId, $coachData) {
    try {
        $stmt = $pdo->prepare("
            UPDATE coaches SET 
                name = :name, specialty = :specialty, experience = :experience,
                status = :status, phone = :phone, email = :email, salary = :salary,
                certificates = :certificates, notes = :notes
            WHERE id = :id
        ");
        
        $certificates = isset($coachData['certificates']) ? json_encode($coachData['certificates']) : null;
        
        $stmt->execute([
            ':id' => $coachId,
            ':name' => $coachData['name'],
            ':specialty' => $coachData['specialty'],
            ':experience' => $coachData['experience'],
            ':status' => $coachData['status'] ?? 'نشط',
            ':phone' => $coachData['phone'] ?? null,
            ':email' => $coachData['email'] ?? null,
            ':salary' => $coachData['salary'] ?? null,
            ':certificates' => $certificates,
            ':notes' => $coachData['notes'] ?? null
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث بيانات المدرب بنجاح'
        ]);
    } catch (PDOException $e) {
        throw new Exception('خطأ في تحديث المدرب: ' . $e->getMessage());
    }
}

// دالة حذف مدرب
function deleteCoach($pdo, $coachId) {
    try {
        $stmt = $pdo->prepare("DELETE FROM coaches WHERE id = :id");
        $stmt->execute([':id' => $coachId]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'تم حذف المدرب بنجاح'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'لم يتم العثور على المدرب'
            ]);
        }
    } catch (PDOException $e) {
        throw new Exception('خطأ في حذف المدرب: ' . $e->getMessage());
    }
}

// دالة جلب مدرب واحد
function getCoach($pdo, $coachId) {
    try {
        $stmt = $pdo->prepare("
            SELECT 
                id, name, specialty, experience, status, rating, 
                phone, email, photo, join_date as joinDate, 
                players_count as playersCount, salary, certificates, notes
            FROM coaches 
            WHERE id = :id
        ");
        $stmt->execute([':id' => $coachId]);
        $coach = $stmt->fetch();
        
        if ($coach) {
            if ($coach['certificates']) {
                $coach['certificates'] = json_decode($coach['certificates'], true) ?: [];
            } else {
                $coach['certificates'] = [];
            }
            
            echo json_encode([
                'success' => true,
                'coach' => $coach
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'لم يتم العثور على المدرب'
            ]);
        }
    } catch (PDOException $e) {
        throw new Exception('خطأ في جلب بيانات المدرب: ' . $e->getMessage());
    }
}

// دالة توليد صورة افتراضية
function generateDefaultPhoto($name) {
    $firstChar = mb_substr($name, 0, 1, 'UTF-8');
    $colors = ['4F46E5', 'EC4899', '059669', '7C3AED', 'DC2626', '2563EB'];
    $color = $colors[array_rand($colors)];
    return "https://via.placeholder.com/50x50/{$color}/white?text=" . urlencode($firstChar);
}

// إنشاء جدول المدربين إذا لم يكن موجوداً
function createCoachesTable($pdo) {
    $sql = "
    CREATE TABLE IF NOT EXISTS coaches (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        specialty VARCHAR(100) NOT NULL,
        experience VARCHAR(50) NOT NULL,
        status ENUM('نشط', 'معلق', 'إجازة') DEFAULT 'نشط',
        rating DECIMAL(2,1) DEFAULT 0,
        phone VARCHAR(20),
        email VARCHAR(255),
        photo TEXT,
        join_date DATE NOT NULL,
        players_count INT DEFAULT 0,
        salary DECIMAL(10,2),
        certificates JSON,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    try {
        $pdo->exec($sql);
    } catch (PDOException $e) {
        error_log('خطأ في إنشاء جدول المدربين: ' . $e->getMessage());
    }
}

// إنشاء الجدول عند تشغيل الملف لأول مرة
createCoachesTable($pdo);
?>
