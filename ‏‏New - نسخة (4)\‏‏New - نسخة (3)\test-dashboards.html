<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار لوحات التحكم - أكاديمية 7C</title>
    
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            /* الألوان الأساسية */
            --primary-bg: #1a1a1a;
            --secondary-bg: #2d2d2d;
            --brand-primary: #1e40af;
            --brand-secondary: #3b82f6;
            --accent-color: #60a5fa;
            --accent-dark: #1e293b;

            /* ألوان الحالة */
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --info: #3b82f6;

            /* ألوان الواجهة */
            --glass: rgba(255,255,255,0.1);
            --glass-strong: rgba(255,255,255,0.15);
            --border: rgba(255,255,255,0.2);
            --border-strong: rgba(255,255,255,0.3);

            /* ألوان النصوص */
            --text-primary: #ffffff;
            --text-secondary: #e2e8f0;
            --text-muted: #94a3b8;

            /* الظلال والتأثيرات */
            --shadow-dark: 0 8px 32px rgba(0, 0, 0, 0.3);
            --shadow-glow: 0 0 20px rgba(30, 64, 175, 0.3);
            --shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.4);

            /* متغيرات إضافية للتخصيص */
            --header-bg: var(--glass);
            --card-bg: var(--glass);
            --button-bg: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            --logo-bg: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));

            /* انتقالات سلسة */
            --transition-fast: 0.2s ease;
            --transition-normal: 0.3s ease;
            --transition-slow: 0.5s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--accent-dark) 100%);
            min-height: 100vh;
            color: var(--text-primary);
            padding: 0;
            margin: 0;
            transition: all var(--transition-normal);
        }

        /* شريط الأدوات العلوي */
        .toolbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: var(--header-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border);
            padding: 1rem 2rem;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all var(--transition-normal);
        }

        .toolbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* شعار الأكاديمية في الشريط العلوي */
        .academy-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 700;
            color: var(--text-primary);
            text-decoration: none;
            transition: all var(--transition-normal);
        }

        .academy-logo:hover {
            transform: scale(1.05);
            color: var(--brand-primary);
        }

        .academy-logo-icon {
            width: 40px;
            height: 40px;
            background: var(--logo-bg);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            box-shadow: var(--shadow-glow);
            animation: logoGlow 3s ease-in-out infinite;
        }

        @keyframes logoGlow {
            0%, 100% { box-shadow: var(--shadow-glow); }
            50% { box-shadow: 0 0 30px rgba(30, 64, 175, 0.5); }
        }

        /* أزرار التحكم */
        .control-btn {
            background: var(--glass);
            border: 1px solid var(--border);
            border-radius: 8px;
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            cursor: pointer;
            transition: all var(--transition-normal);
            font-family: inherit;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .control-btn:hover {
            background: var(--glass-strong);
            border-color: var(--border-strong);
            transform: translateY(-2px);
        }

        .control-btn.active {
            background: var(--button-bg);
            border-color: var(--brand-primary);
        }

        /* لوحة تخصيص الألوان */
        .color-customizer {
            position: fixed;
            top: 80px;
            right: 2rem;
            width: 350px;
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 1.5rem;
            z-index: 999;
            transform: translateX(100%);
            opacity: 0;
            visibility: hidden;
            transition: all var(--transition-normal);
            max-height: 80vh;
            overflow-y: auto;
        }

        .color-customizer.active {
            transform: translateX(0);
            opacity: 1;
            visibility: visible;
        }

        .customizer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
        }

        .customizer-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .close-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            transition: all var(--transition-fast);
        }

        .close-btn:hover {
            color: var(--danger);
            background: rgba(239, 68, 68, 0.1);
        }

        /* مجموعات الألوان */
        .color-group {
            margin-bottom: 1.5rem;
        }

        .color-group-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .color-controls {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 0.5rem;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .color-label {
            font-size: 0.9rem;
            color: var(--text-muted);
        }

        .color-input {
            width: 50px;
            height: 35px;
            border: 2px solid var(--border);
            border-radius: 8px;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .color-input:hover {
            border-color: var(--brand-primary);
            transform: scale(1.1);
        }

        /* قوالب الألوان الجاهزة */
        .color-presets {
            margin-bottom: 1.5rem;
        }

        .presets-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
        }

        .preset-btn {
            background: var(--glass);
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 0.75rem;
            cursor: pointer;
            transition: all var(--transition-normal);
            text-align: center;
            font-size: 0.85rem;
            color: var(--text-secondary);
        }

        .preset-btn:hover {
            background: var(--glass-strong);
            border-color: var(--brand-primary);
            color: var(--text-primary);
        }

        /* أزرار الإجراءات */
        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            margin-top: 1.5rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border);
        }

        .action-btn {
            background: var(--glass);
            border: 1px solid var(--border);
            border-radius: 8px;
            color: var(--text-primary);
            padding: 0.75rem;
            cursor: pointer;
            transition: all var(--transition-normal);
            font-family: inherit;
            font-size: 0.9rem;
            text-align: center;
        }

        .action-btn:hover {
            background: var(--glass-strong);
            transform: translateY(-2px);
        }

        .action-btn.primary {
            background: var(--button-bg);
            border-color: var(--brand-primary);
        }

        .action-btn.danger {
            background: linear-gradient(135deg, var(--danger), #dc2626);
            border-color: var(--danger);
        }

        /* تحديث المحتوى الرئيسي */
        .main-content {
            padding: 6rem 2rem 2rem;
            transition: all var(--transition-normal);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .logo {
            width: 120px;
            height: 120px;
            background: var(--logo-bg);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 3rem;
            color: white;
            box-shadow: var(--shadow-glow);
            animation: pulse 3s infinite;
            transition: all var(--transition-normal);
        }

        .logo:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 0 40px rgba(30, 64, 175, 0.5);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .title {
            font-size: 2.8rem;
            font-weight: 800;
            background: var(--button-bg);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            transition: all var(--transition-normal);
        }

        .subtitle {
            color: var(--text-secondary);
            font-size: 1.3rem;
            margin-bottom: 2rem;
            transition: all var(--transition-normal);
        }

        .dashboards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .dashboard-card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--button-bg);
            border-radius: 20px 20px 0 0;
            transition: all var(--transition-normal);
        }

        .dashboard-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: var(--shadow-hover);
            border-color: var(--brand-primary);
        }

        .dashboard-card:hover::before {
            height: 6px;
            background: linear-gradient(90deg, var(--brand-primary), var(--accent-color), var(--brand-secondary));
        }

        .dashboard-icon {
            width: 90px;
            height: 90px;
            background: var(--button-bg);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2.2rem;
            color: white;
            box-shadow: var(--shadow-glow);
            transition: all var(--transition-normal);
        }

        .dashboard-card:hover .dashboard-icon {
            transform: scale(1.1) rotate(10deg);
            box-shadow: 0 0 30px rgba(30, 64, 175, 0.5);
        }

        .dashboard-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
            transition: all var(--transition-normal);
        }

        .dashboard-desc {
            color: var(--text-secondary);
            font-size: 1rem;
            margin-bottom: 1.5rem;
            line-height: 1.7;
            transition: all var(--transition-normal);
        }

        .dashboard-btn {
            background: var(--button-bg);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            padding: 1rem 1.5rem;
            cursor: pointer;
            transition: all var(--transition-normal);
            font-family: inherit;
            text-decoration: none;
            display: inline-block;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
        }

        .dashboard-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left var(--transition-slow);
        }

        .dashboard-btn:hover::before {
            left: 100%;
        }

        .dashboard-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 30px rgba(30, 64, 175, 0.4);
        }

        .credentials {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 2rem;
            margin-top: 2rem;
            transition: all var(--transition-normal);
        }

        .credentials:hover {
            border-color: var(--border-strong);
            box-shadow: var(--shadow-dark);
        }

        .credentials h3 {
            color: var(--brand-primary);
            margin-bottom: 1.5rem;
            text-align: center;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .cred-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
        }

        .cred-item {
            background: var(--glass);
            border: 1px solid var(--border);
            border-radius: 12px;
            padding: 1.5rem;
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .cred-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--button-bg);
            border-radius: 12px 12px 0 0;
        }

        .cred-item:hover {
            transform: translateY(-5px);
            border-color: var(--brand-primary);
            box-shadow: 0 10px 25px rgba(30, 64, 175, 0.2);
        }

        .cred-role {
            font-weight: 700;
            color: var(--brand-primary);
            margin-bottom: 0.75rem;
            font-size: 1.1rem;
        }

        .cred-details {
            font-size: 1rem;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* وضع الليل/النهار */
        .theme-toggle {
            position: relative;
            width: 60px;
            height: 30px;
            background: var(--glass);
            border: 1px solid var(--border);
            border-radius: 15px;
            cursor: pointer;
            transition: all var(--transition-normal);
        }

        .theme-toggle::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 22px;
            height: 22px;
            background: var(--button-bg);
            border-radius: 50%;
            transition: all var(--transition-normal);
        }

        .theme-toggle.night::before {
            transform: translateX(28px);
            background: linear-gradient(135deg, #1e293b, #334155);
        }

        /* تأثيرات إضافية */
        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--brand-primary);
            border-radius: 50%;
            opacity: 0.3;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* استعلامات الوسائط المحسنة */
        @media (max-width: 1024px) {
            .color-customizer {
                width: 300px;
            }

            .toolbar {
                padding: 0.75rem 1rem;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 5rem 1rem 2rem;
            }

            .toolbar {
                flex-direction: column;
                gap: 1rem;
                padding: 1rem;
            }

            .toolbar-right,
            .toolbar-left {
                width: 100%;
                justify-content: center;
            }

            .color-customizer {
                right: 1rem;
                left: 1rem;
                width: auto;
                top: 120px;
            }

            .title {
                font-size: 2.2rem;
            }

            .dashboards-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .dashboard-card {
                padding: 1.5rem;
            }

            .logo {
                width: 100px;
                height: 100px;
                font-size: 2.5rem;
            }
        }

        @media (max-width: 480px) {
            .main-content {
                padding: 4rem 0.5rem 1rem;
            }

            .dashboard-card {
                padding: 1rem;
                border-radius: 16px;
            }

            .dashboard-icon {
                width: 70px;
                height: 70px;
                font-size: 1.8rem;
            }

            .dashboard-title {
                font-size: 1.2rem;
            }

            .dashboard-desc {
                font-size: 0.9rem;
            }

            .cred-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }

        /* تحسينات الأداء */
        .dashboard-card,
        .control-btn,
        .color-customizer {
            will-change: transform;
        }

        /* تأثيرات التحميل */
        .loading {
            opacity: 0;
            animation: fadeIn 0.5s ease-in-out forwards;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- شريط الأدوات العلوي -->
    <div class="toolbar">
        <div class="toolbar-right">
            <a href="#" class="academy-logo">
                <div class="academy-logo-icon">
                    <i class="fas fa-dumbbell"></i>
                </div>
                <span>أكاديمية 7C</span>
            </a>
        </div>
        <div class="toolbar-left">
            <button class="control-btn" onclick="toggleColorCustomizer()">
                <i class="fas fa-palette"></i>
                تخصيص الألوان
            </button>
            <div class="theme-toggle" onclick="toggleTheme()" title="تبديل الوضع الليلي/النهاري">
            </div>
            <button class="control-btn" onclick="resetToDefaults()">
                <i class="fas fa-undo"></i>
                إعادة تعيين
            </button>
        </div>
    </div>

    <!-- أداة تخصيص الألوان -->
    <div class="color-customizer" id="colorCustomizer">
        <div class="customizer-header">
            <h3 class="customizer-title">
                <i class="fas fa-palette" style="margin-left: 0.5rem;"></i>
                تخصيص الألوان
            </h3>
            <button class="close-btn" onclick="toggleColorCustomizer()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- قوالب الألوان الجاهزة -->
        <div class="color-presets">
            <div class="color-group-title">
                <i class="fas fa-swatchbook"></i>
                قوالب جاهزة
            </div>
            <div class="presets-grid">
                <button class="preset-btn" onclick="applyPreset('blue')">
                    الأزرق الكلاسيكي
                </button>
                <button class="preset-btn" onclick="applyPreset('green')">
                    الأخضر الطبيعي
                </button>
                <button class="preset-btn" onclick="applyPreset('orange')">
                    البرتقالي الدافئ
                </button>
                <button class="preset-btn" onclick="applyPreset('purple')">
                    البنفسجي الملكي
                </button>
                <button class="preset-btn" onclick="applyPreset('red')">
                    الأحمر القوي
                </button>
                <button class="preset-btn" onclick="applyPreset('dark')">
                    الداكن الأنيق
                </button>
            </div>
        </div>

        <!-- ألوان العلامة التجارية -->
        <div class="color-group">
            <div class="color-group-title">
                <i class="fas fa-star"></i>
                ألوان العلامة التجارية
            </div>
            <div class="color-controls">
                <label class="color-label">اللون الأساسي</label>
                <input type="color" class="color-input" id="brandPrimary" value="#1e40af" onchange="updateColor('--brand-primary', this.value)">
            </div>
            <div class="color-controls">
                <label class="color-label">اللون الثانوي</label>
                <input type="color" class="color-input" id="brandSecondary" value="#3b82f6" onchange="updateColor('--brand-secondary', this.value)">
            </div>
            <div class="color-controls">
                <label class="color-label">لون التمييز</label>
                <input type="color" class="color-input" id="accentColor" value="#60a5fa" onchange="updateColor('--accent-color', this.value)">
            </div>
        </div>

        <!-- ألوان الخلفية -->
        <div class="color-group">
            <div class="color-group-title">
                <i class="fas fa-fill-drip"></i>
                ألوان الخلفية
            </div>
            <div class="color-controls">
                <label class="color-label">الخلفية الأساسية</label>
                <input type="color" class="color-input" id="primaryBg" value="#1a1a1a" onchange="updateColor('--primary-bg', this.value)">
            </div>
            <div class="color-controls">
                <label class="color-label">الخلفية الثانوية</label>
                <input type="color" class="color-input" id="secondaryBg" value="#2d2d2d" onchange="updateColor('--secondary-bg', this.value)">
            </div>
            <div class="color-controls">
                <label class="color-label">الخلفية الداكنة</label>
                <input type="color" class="color-input" id="accentDark" value="#1e293b" onchange="updateColor('--accent-dark', this.value)">
            </div>
        </div>

        <!-- ألوان النصوص -->
        <div class="color-group">
            <div class="color-group-title">
                <i class="fas fa-font"></i>
                ألوان النصوص
            </div>
            <div class="color-controls">
                <label class="color-label">النص الأساسي</label>
                <input type="color" class="color-input" id="textPrimary" value="#ffffff" onchange="updateColor('--text-primary', this.value)">
            </div>
            <div class="color-controls">
                <label class="color-label">النص الثانوي</label>
                <input type="color" class="color-input" id="textSecondary" value="#e2e8f0" onchange="updateColor('--text-secondary', this.value)">
            </div>
            <div class="color-controls">
                <label class="color-label">النص الباهت</label>
                <input type="color" class="color-input" id="textMuted" value="#94a3b8" onchange="updateColor('--text-muted', this.value)">
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="action-buttons">
            <button class="action-btn primary" onclick="saveColorSettings()">
                <i class="fas fa-save"></i>
                حفظ الإعدادات
            </button>
            <button class="action-btn" onclick="exportColorSettings()">
                <i class="fas fa-download"></i>
                تصدير
            </button>
            <button class="action-btn" onclick="importColorSettings()">
                <i class="fas fa-upload"></i>
                استيراد
            </button>
            <button class="action-btn danger" onclick="resetColors()">
                <i class="fas fa-undo"></i>
                إعادة تعيين
            </button>
        </div>
    </div>

    <!-- العناصر العائمة للتأثير البصري -->
    <div class="floating-elements" id="floatingElements"></div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <div class="container">
            <div class="header">
                <div class="logo loading">
                    <i class="fas fa-dumbbell"></i>
                </div>
                <h1 class="title loading">أكاديمية 7C الرياضية</h1>
                <p class="subtitle loading">اختبار لوحات التحكم المتخصصة</p>
            </div>

            <div class="dashboards-grid">
                <div class="dashboard-card loading">
                    <div class="dashboard-icon">
                        <i class="fas fa-crown"></i>
                    </div>
                    <h3 class="dashboard-title">لوحة تحكم المدير</h3>
                    <p class="dashboard-desc">إدارة شاملة للأكاديمية مع تحليلات الذكاء الاصطناعي والتقارير المتقدمة</p>
                    <a href="admin-dashboard-7c.html" class="dashboard-btn">
                        <i class="fas fa-arrow-left" style="margin-right: 0.5rem;"></i>
                        دخول لوحة المدير
                    </a>
                </div>

                <div class="dashboard-card loading">
                    <div class="dashboard-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <h3 class="dashboard-title">لوحة تحكم المدرب</h3>
                    <p class="dashboard-desc">إدارة اللاعبين والحصص التدريبية مع نظام تقييم متطور</p>
                    <a href="coach-dashboard.html" class="dashboard-btn">
                        <i class="fas fa-arrow-left" style="margin-right: 0.5rem;"></i>
                        دخول لوحة المدرب
                    </a>
                </div>

                <div class="dashboard-card loading">
                    <div class="dashboard-icon">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <h3 class="dashboard-title">لوحة تحكم ولي الأمر</h3>
                    <p class="dashboard-desc">متابعة تقدم الأطفال والتواصل مع المدربين ومراقبة الأداء</p>
                    <a href="parent-dashboard.html" class="dashboard-btn">
                        <i class="fas fa-arrow-left" style="margin-right: 0.5rem;"></i>
                        دخول لوحة ولي الأمر
                    </a>
                </div>

                <div class="dashboard-card loading">
                    <div class="dashboard-icon">
                        <i class="fas fa-running"></i>
                    </div>
                    <h3 class="dashboard-title">لوحة تحكم اللاعب</h3>
                    <p class="dashboard-desc">عرض الأداء الشخصي والجدول الزمني والإنجازات (قيد التطوير)</p>
                    <a href="player-dashboard.html" class="dashboard-btn" style="opacity: 0.6;">
                        <i class="fas fa-tools" style="margin-right: 0.5rem;"></i>
                        قيد التطوير
                    </a>
                </div>

                <div class="dashboard-card loading">
                    <div class="dashboard-icon">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <h3 class="dashboard-title">لوحة تحكم المشرف</h3>
                    <p class="dashboard-desc">مراقبة العمليات والتقارير الإشرافية (قيد التطوير)</p>
                    <a href="supervisor-dashboard.html" class="dashboard-btn" style="opacity: 0.6;">
                        <i class="fas fa-tools" style="margin-right: 0.5rem;"></i>
                        قيد التطوير
                    </a>
                </div>

                <div class="dashboard-card loading">
                    <div class="dashboard-icon">
                        <i class="fas fa-sign-in-alt"></i>
                    </div>
                    <h3 class="dashboard-title">نظام تسجيل الدخول</h3>
                    <p class="dashboard-desc">مصادقة متقدمة مع دعم البيومترية والأمان المتطور</p>
                    <a href="login.html" class="dashboard-btn">
                        <i class="fas fa-arrow-left" style="margin-right: 0.5rem;"></i>
                        صفحة تسجيل الدخول
                    </a>
                </div>
            </div>

            <div class="credentials loading">
                <h3>بيانات الاختبار</h3>
                <div class="cred-grid">
                    <div class="cred-item">
                        <div class="cred-role">المدير</div>
                        <div class="cred-details">
                            البريد: <EMAIL><br>
                            كلمة المرور: admin123<br>
                            الجوال: 0501234567
                        </div>
                    </div>
                    <div class="cred-item">
                        <div class="cred-role">المدرب</div>
                        <div class="cred-details">
                            البريد: <EMAIL><br>
                            كلمة المرور: coach123<br>
                            الجوال: 0509876543
                        </div>
                    </div>
                    <div class="cred-item">
                        <div class="cred-role">ولي الأمر</div>
                        <div class="cred-details">
                            البريد: <EMAIL><br>
                            كلمة المرور: parent123<br>
                            الجوال: 0555544332
                        </div>
                    </div>
                    <div class="cred-item">
                        <div class="cred-role">اللاعب</div>
                        <div class="cred-details">
                            البريد: <EMAIL><br>
                            كلمة المرور: player123<br>
                            الجوال: 0551122334
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // ==================== متغيرات عامة ====================
        let isNightMode = false;
        let currentColorSettings = {};

        // قوالب الألوان الجاهزة
        const colorPresets = {
            blue: {
                '--brand-primary': '#1e40af',
                '--brand-secondary': '#3b82f6',
                '--accent-color': '#60a5fa',
                '--primary-bg': '#1a1a1a',
                '--secondary-bg': '#2d2d2d',
                '--accent-dark': '#1e293b'
            },
            green: {
                '--brand-primary': '#059669',
                '--brand-secondary': '#10b981',
                '--accent-color': '#34d399',
                '--primary-bg': '#1a1a1a',
                '--secondary-bg': '#2d2d2d',
                '--accent-dark': '#1e3a2e'
            },
            orange: {
                '--brand-primary': '#ea580c',
                '--brand-secondary': '#f97316',
                '--accent-color': '#fb923c',
                '--primary-bg': '#1a1a1a',
                '--secondary-bg': '#2d2d2d',
                '--accent-dark': '#3a2317'
            },
            purple: {
                '--brand-primary': '#7c3aed',
                '--brand-secondary': '#8b5cf6',
                '--accent-color': '#a78bfa',
                '--primary-bg': '#1a1a1a',
                '--secondary-bg': '#2d2d2d',
                '--accent-dark': '#2e1065'
            },
            red: {
                '--brand-primary': '#dc2626',
                '--brand-secondary': '#ef4444',
                '--accent-color': '#f87171',
                '--primary-bg': '#1a1a1a',
                '--secondary-bg': '#2d2d2d',
                '--accent-dark': '#3f1f1f'
            },
            dark: {
                '--brand-primary': '#374151',
                '--brand-secondary': '#4b5563',
                '--accent-color': '#6b7280',
                '--primary-bg': '#111827',
                '--secondary-bg': '#1f2937',
                '--accent-dark': '#0f172a'
            }
        };

        // ==================== دوال تخصيص الألوان ====================

        // تبديل أداة تخصيص الألوان
        function toggleColorCustomizer() {
            const customizer = document.getElementById('colorCustomizer');
            customizer.classList.toggle('active');
        }

        // تحديث لون معين
        function updateColor(property, value) {
            document.documentElement.style.setProperty(property, value);
            currentColorSettings[property] = value;

            // تحديث المتغيرات المشتقة
            updateDerivedColors();

            // حفظ تلقائي
            saveColorSettings();
        }

        // تحديث الألوان المشتقة
        function updateDerivedColors() {
            const primary = currentColorSettings['--brand-primary'] || getComputedStyle(document.documentElement).getPropertyValue('--brand-primary');
            const secondary = currentColorSettings['--brand-secondary'] || getComputedStyle(document.documentElement).getPropertyValue('--brand-secondary');

            // تحديث متغيرات CSS المشتقة
            document.documentElement.style.setProperty('--button-bg', `linear-gradient(135deg, ${primary}, ${secondary})`);
            document.documentElement.style.setProperty('--logo-bg', `linear-gradient(135deg, ${primary}, ${secondary})`);
            document.documentElement.style.setProperty('--shadow-glow', `0 0 20px ${primary}33`);
        }

        // تطبيق قالب ألوان جاهز
        function applyPreset(presetName) {
            const preset = colorPresets[presetName];
            if (!preset) return;

            Object.entries(preset).forEach(([property, value]) => {
                document.documentElement.style.setProperty(property, value);
                currentColorSettings[property] = value;

                // تحديث حقول الإدخال
                const inputId = property.replace('--', '').replace(/-([a-z])/g, (g) => g[1].toUpperCase());
                const input = document.getElementById(inputId);
                if (input) {
                    input.value = value;
                }
            });

            updateDerivedColors();
            saveColorSettings();

            // إظهار إشعار
            showNotification(`تم تطبيق قالب ${getPresetName(presetName)} بنجاح`, 'success');
        }

        // الحصول على اسم القالب بالعربية
        function getPresetName(presetName) {
            const names = {
                blue: 'الأزرق الكلاسيكي',
                green: 'الأخضر الطبيعي',
                orange: 'البرتقالي الدافئ',
                purple: 'البنفسجي الملكي',
                red: 'الأحمر القوي',
                dark: 'الداكن الأنيق'
            };
            return names[presetName] || presetName;
        }

        // حفظ إعدادات الألوان
        function saveColorSettings() {
            try {
                localStorage.setItem('7c_color_settings', JSON.stringify(currentColorSettings));
                localStorage.setItem('7c_night_mode', isNightMode);
                console.log('✅ تم حفظ إعدادات الألوان');
            } catch (error) {
                console.error('❌ خطأ في حفظ إعدادات الألوان:', error);
                showNotification('خطأ في حفظ الإعدادات', 'error');
            }
        }

        // تحميل إعدادات الألوان
        function loadColorSettings() {
            try {
                const savedSettings = localStorage.getItem('7c_color_settings');
                const savedNightMode = localStorage.getItem('7c_night_mode');

                if (savedSettings) {
                    currentColorSettings = JSON.parse(savedSettings);
                    Object.entries(currentColorSettings).forEach(([property, value]) => {
                        document.documentElement.style.setProperty(property, value);

                        // تحديث حقول الإدخال
                        const inputId = property.replace('--', '').replace(/-([a-z])/g, (g) => g[1].toUpperCase());
                        const input = document.getElementById(inputId);
                        if (input) {
                            input.value = value;
                        }
                    });
                    updateDerivedColors();
                }

                if (savedNightMode === 'true') {
                    isNightMode = true;
                    document.querySelector('.theme-toggle').classList.add('night');
                    applyNightMode();
                }

                console.log('✅ تم تحميل إعدادات الألوان');
            } catch (error) {
                console.error('❌ خطأ في تحميل إعدادات الألوان:', error);
            }
        }

        // إعادة تعيين الألوان للافتراضية
        function resetColors() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الألوان للإعدادات الافتراضية؟')) {
                // مسح الإعدادات المحفوظة
                localStorage.removeItem('7c_color_settings');
                localStorage.removeItem('7c_night_mode');

                // إعادة تحميل الصفحة لتطبيق الإعدادات الافتراضية
                location.reload();
            }
        }

        // تصدير إعدادات الألوان
        function exportColorSettings() {
            try {
                const exportData = {
                    colorSettings: currentColorSettings,
                    nightMode: isNightMode,
                    exportDate: new Date().toISOString(),
                    version: '1.0'
                };

                const dataStr = JSON.stringify(exportData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `7c_color_settings_${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                showNotification('تم تصدير إعدادات الألوان بنجاح', 'success');
            } catch (error) {
                console.error('❌ خطأ في تصدير الإعدادات:', error);
                showNotification('خطأ في تصدير الإعدادات', 'error');
            }
        }

        // استيراد إعدادات الألوان
        function importColorSettings() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';

            input.onchange = (e) => {
                const file = e.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const importData = JSON.parse(e.target.result);

                        if (importData.colorSettings) {
                            currentColorSettings = importData.colorSettings;
                            Object.entries(currentColorSettings).forEach(([property, value]) => {
                                document.documentElement.style.setProperty(property, value);

                                // تحديث حقول الإدخال
                                const inputId = property.replace('--', '').replace(/-([a-z])/g, (g) => g[1].toUpperCase());
                                const input = document.getElementById(inputId);
                                if (input) {
                                    input.value = value;
                                }
                            });
                            updateDerivedColors();
                            saveColorSettings();

                            showNotification('تم استيراد إعدادات الألوان بنجاح', 'success');
                        }

                        if (importData.nightMode !== undefined) {
                            isNightMode = importData.nightMode;
                            const toggle = document.querySelector('.theme-toggle');
                            if (isNightMode) {
                                toggle.classList.add('night');
                                applyNightMode();
                            } else {
                                toggle.classList.remove('night');
                                removeNightMode();
                            }
                        }

                    } catch (error) {
                        console.error('❌ خطأ في قراءة الملف:', error);
                        showNotification('ملف غير صالح', 'error');
                    }
                };

                reader.readAsText(file);
            };

            input.click();
        }

        // ==================== دوال الوضع الليلي/النهاري ====================

        // تبديل الوضع الليلي/النهاري
        function toggleTheme() {
            isNightMode = !isNightMode;
            const toggle = document.querySelector('.theme-toggle');

            if (isNightMode) {
                toggle.classList.add('night');
                applyNightMode();
                showNotification('تم تفعيل الوضع الليلي', 'info');
            } else {
                toggle.classList.remove('night');
                removeNightMode();
                showNotification('تم تفعيل الوضع النهاري', 'info');
            }

            saveColorSettings();
        }

        // تطبيق الوضع الليلي
        function applyNightMode() {
            document.documentElement.style.setProperty('--primary-bg', '#0f172a');
            document.documentElement.style.setProperty('--secondary-bg', '#1e293b');
            document.documentElement.style.setProperty('--accent-dark', '#334155');
            document.documentElement.style.setProperty('--text-primary', '#f1f5f9');
            document.documentElement.style.setProperty('--text-secondary', '#cbd5e1');
            document.documentElement.style.setProperty('--text-muted', '#64748b');
            document.documentElement.style.setProperty('--glass', 'rgba(255,255,255,0.05)');
            document.documentElement.style.setProperty('--glass-strong', 'rgba(255,255,255,0.1)');
            document.documentElement.style.setProperty('--border', 'rgba(255,255,255,0.1)');
            document.documentElement.style.setProperty('--border-strong', 'rgba(255,255,255,0.2)');
        }

        // إزالة الوضع الليلي
        function removeNightMode() {
            document.documentElement.style.setProperty('--primary-bg', '#1a1a1a');
            document.documentElement.style.setProperty('--secondary-bg', '#2d2d2d');
            document.documentElement.style.setProperty('--accent-dark', '#1e293b');
            document.documentElement.style.setProperty('--text-primary', '#ffffff');
            document.documentElement.style.setProperty('--text-secondary', '#e2e8f0');
            document.documentElement.style.setProperty('--text-muted', '#94a3b8');
            document.documentElement.style.setProperty('--glass', 'rgba(255,255,255,0.1)');
            document.documentElement.style.setProperty('--glass-strong', 'rgba(255,255,255,0.15)');
            document.documentElement.style.setProperty('--border', 'rgba(255,255,255,0.2)');
            document.documentElement.style.setProperty('--border-strong', 'rgba(255,255,255,0.3)');
        }

        // ==================== دوال مساعدة ====================

        // إظهار إشعار
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                background: var(--card-bg);
                backdrop-filter: blur(20px);
                border: 1px solid var(--border);
                border-radius: 12px;
                padding: 1rem 1.5rem;
                color: var(--text-primary);
                z-index: 10000;
                transform: translateX(100%);
                opacity: 0;
                transition: all 0.3s ease;
                max-width: 300px;
                box-shadow: var(--shadow-dark);
            `;

            const icon = type === 'success' ? 'check-circle' :
                        type === 'error' ? 'times-circle' :
                        type === 'warning' ? 'exclamation-triangle' : 'info-circle';

            const color = type === 'success' ? 'var(--success)' :
                         type === 'error' ? 'var(--danger)' :
                         type === 'warning' ? 'var(--warning)' : 'var(--info)';

            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-${icon}" style="color: ${color};"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // إظهار الإشعار
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
                notification.style.opacity = '1';
            }, 100);

            // إخفاء الإشعار
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // إعادة تعيين للإعدادات الافتراضية
        function resetToDefaults() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات للافتراضية؟')) {
                resetColors();
            }
        }

        // إنشاء العناصر العائمة
        function createFloatingElements() {
            const container = document.getElementById('floatingElements');
            const elementCount = 15;

            for (let i = 0; i < elementCount; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.animationDelay = Math.random() * 6 + 's';
                element.style.animationDuration = (Math.random() * 4 + 4) + 's';
                container.appendChild(element);
            }
        }

        // تأثير التحميل المتدرج
        function animateLoadingElements() {
            const loadingElements = document.querySelectorAll('.loading');
            loadingElements.forEach((element, index) => {
                setTimeout(() => {
                    element.style.animationDelay = (index * 0.1) + 's';
                }, index * 100);
            });
        }

        // ==================== تهيئة الصفحة ====================

        // تهيئة الصفحة عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء تحميل صفحة اختبار اللوحات المحسنة...');

            // تحميل إعدادات الألوان المحفوظة
            loadColorSettings();

            // إنشاء العناصر العائمة
            createFloatingElements();

            // تأثير التحميل المتدرج
            animateLoadingElements();

            // إغلاق أداة التخصيص عند النقر خارجها
            document.addEventListener('click', function(e) {
                const customizer = document.getElementById('colorCustomizer');
                const toggleBtn = e.target.closest('[onclick="toggleColorCustomizer()"]');

                if (!customizer.contains(e.target) && !toggleBtn && customizer.classList.contains('active')) {
                    customizer.classList.remove('active');
                }
            });

            // تحديث الألوان المشتقة عند التحميل
            setTimeout(() => {
                updateDerivedColors();
            }, 500);

            console.log('✅ تم تحميل صفحة اختبار اللوحات بنجاح!');

            // إظهار رسالة ترحيب
            setTimeout(() => {
                showNotification('مرحباً بك في أكاديمية 7C المحسنة!', 'success');
            }, 1000);
        });

        // حفظ الإعدادات عند إغلاق الصفحة
        window.addEventListener('beforeunload', function() {
            saveColorSettings();
        });

        // تحديث الألوان عند تغيير حجم النافذة
        window.addEventListener('resize', function() {
            updateDerivedColors();
        });

        console.log('🎨 تم تحميل نظام تخصيص الألوان الشامل بنجاح!');
    </script>
</body>
</html>
