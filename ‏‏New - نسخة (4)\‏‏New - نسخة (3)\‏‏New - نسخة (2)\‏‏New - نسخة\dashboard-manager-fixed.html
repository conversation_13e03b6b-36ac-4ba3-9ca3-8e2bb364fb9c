<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدير لوحات التحكم الاحترافي - أكاديمية 7C</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        :root {
            --primary-color: #1a365d;
            --secondary-color: #3182ce;
            --accent-color: #fbbf24;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --bg-dark: #1a1a1a;
            --bg-secondary: #2d2d2d;
            --bg-tertiary: #404040;
            --text-light: #e2e8f0;
            --text-dark: #1a202c;
            --border-color: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            color: var(--text-light);
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(26, 54, 93, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(251, 191, 36, 0.1) 0%, transparent 50%);
            z-index: -1;
        }

        .header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(26, 54, 93, 0.3);
        }

        .logo-text h1 {
            font-size: 1.3rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.2rem;
        }

        .logo-text p {
            font-size: 0.8rem;
            color: var(--text-light);
            opacity: 0.8;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            border: none;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-family: 'Cairo', sans-serif;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 4px 15px rgba(26, 54, 93, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(26, 54, 93, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #059669);
            color: white;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), #d97706);
            color: white;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--error-color), #dc2626);
            color: white;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        }

        .btn-secondary {
            background: var(--glass-bg);
            color: var(--text-light);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 2rem;
            min-height: calc(100vh - 120px);
        }

        .sidebar {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 1.5rem;
            height: fit-content;
            position: sticky;
            top: 120px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .sidebar-section {
            margin-bottom: 2rem;
        }

        .sidebar-title {
            font-size: 1rem;
            font-weight: 700;
            color: white;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .sidebar-item {
            padding: 0.75rem 1rem;
            margin-bottom: 0.5rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            border: 1px solid transparent;
            color: var(--text-light);
        }

        .sidebar-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary-color);
            transform: translateX(5px);
        }

        .sidebar-item.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(26, 54, 93, 0.3);
        }

        .main-content {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .content-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: white;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .dashboard-card {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            border-color: var(--accent-color);
        }

        .dashboard-preview {
            width: 100%;
            height: 180px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            position: relative;
            overflow: hidden;
            border-radius: 15px 15px 0 0;
        }

        .dashboard-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect width="30" height="15" x="5" y="10" fill="rgba(255,255,255,0.3)" rx="2"/><rect width="25" height="10" x="40" y="12" fill="rgba(255,255,255,0.2)" rx="2"/><rect width="20" height="8" x="70" y="14" fill="rgba(255,255,255,0.2)" rx="2"/><rect width="40" height="30" x="5" y="35" fill="rgba(255,255,255,0.2)" rx="3"/><rect width="40" height="20" x="50" y="40" fill="rgba(255,255,255,0.15)" rx="3"/><rect width="85" height="15" x="5" y="75" fill="rgba(255,255,255,0.1)" rx="2"/></svg>') no-repeat center;
            background-size: 80%;
        }

        .dashboard-content {
            padding: 1.5rem;
        }

        .dashboard-info h3 {
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.5rem;
        }

        .dashboard-info p {
            color: var(--text-light);
            opacity: 0.8;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .dashboard-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: var(--text-light);
            opacity: 0.7;
            margin-bottom: 1rem;
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.7rem;
            font-weight: 700;
        }

        .status-active {
            background: var(--success-color);
            color: white;
        }

        .status-draft {
            background: var(--warning-color);
            color: white;
        }

        .status-archived {
            background: var(--error-color);
            color: white;
        }

        .dashboard-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
        }

        .action-btn {
            padding: 0.5rem;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            font-size: 0.9rem;
            min-width: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn.edit {
            background: var(--warning-color);
        }

        .action-btn.delete {
            background: var(--error-color);
        }

        .action-btn.duplicate {
            background: var(--success-color);
        }

        .action-btn.settings {
            background: var(--secondary-color);
        }

        .action-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            z-index: 2000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 0;
            max-width: 900px;
            width: 90%;
            max-height: 90vh;
            overflow: hidden;
            position: relative;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            padding: 1.5rem 2rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: white;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .close-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .modal-body {
            padding: 2rem;
            max-height: calc(90vh - 200px);
            overflow-y: auto;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 2rem;
            overflow-x: auto;
        }

        .tab {
            padding: 1rem 1.5rem;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
            color: var(--text-light);
            white-space: nowrap;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .tab.active {
            color: white;
            border-bottom-color: var(--accent-color);
            background: rgba(251, 191, 36, 0.1);
        }

        .tab:hover:not(.active) {
            color: white;
            background: rgba(255, 255, 255, 0.05);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: white;
            font-size: 0.9rem;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--glass-bg);
            color: white;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.1);
            background: rgba(255, 255, 255, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: left 0.75rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-left: 2.5rem;
        }

        .modal-footer {
            padding: 1.5rem 2rem;
            border-top: 1px solid var(--border-color);
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            background: rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .sidebar {
                order: 2;
                position: static;
            }
            
            .main-content {
                order: 1;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .modal-content {
                width: 95%;
                margin: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">7C</div>
                <div class="logo-text">
                    <h1>مدير لوحات التحكم الاحترافي</h1>
                    <p>أكاديمية 7C الرياضية</p>
                </div>
            </div>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="openCreateModal()">
                    <i class="fas fa-plus"></i>
                    لوحة جديدة
                </button>
                <button class="btn btn-secondary" onclick="importDashboards()">
                    <i class="fas fa-upload"></i>
                    استيراد
                </button>
                <button class="btn btn-secondary" onclick="exportDashboards()">
                    <i class="fas fa-download"></i>
                    تصدير
                </button>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container">
        <div class="main-grid">
            <!-- Sidebar -->
            <div class="sidebar">
                <div class="sidebar-section">
                    <div class="sidebar-title">
                        <i class="fas fa-tachometer-alt"></i>
                        إدارة اللوحات
                    </div>
                    <div class="sidebar-item active" onclick="showSection('dashboards')">
                        <i class="fas fa-th-large"></i>
                        جميع اللوحات
                    </div>
                    <div class="sidebar-item" onclick="showSection('templates')">
                        <i class="fas fa-layer-group"></i>
                        القوالب الجاهزة
                    </div>
                    <div class="sidebar-item" onclick="showSection('widgets')">
                        <i class="fas fa-puzzle-piece"></i>
                        مكتبة الأدوات
                    </div>
                    <div class="sidebar-item" onclick="showSection('analytics')">
                        <i class="fas fa-chart-bar"></i>
                        التحليلات
                    </div>
                </div>

                <div class="sidebar-section">
                    <div class="sidebar-title">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </div>
                    <div class="sidebar-item" onclick="showSection('themes')">
                        <i class="fas fa-palette"></i>
                        السمات والألوان
                    </div>
                    <div class="sidebar-item" onclick="showSection('permissions')">
                        <i class="fas fa-shield-alt"></i>
                        الصلاحيات
                    </div>
                    <div class="sidebar-item" onclick="showSection('backup')">
                        <i class="fas fa-database"></i>
                        النسخ الاحتياطي
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <!-- Dashboards Section -->
                <div id="dashboards-section" class="content-section">
                    <div class="content-header">
                        <div class="content-title">
                            <i class="fas fa-th-large"></i>
                            لوحات التحكم
                        </div>
                        <div class="header-actions">
                            <select class="form-input" style="width: auto; margin-left: 1rem;" onchange="filterDashboards(this.value)">
                                <option value="">جميع اللوحات</option>
                                <option value="active">نشطة</option>
                                <option value="draft">مسودة</option>
                                <option value="archived">مؤرشفة</option>
                            </select>
                            <input type="text" class="form-input" placeholder="البحث في اللوحات..." style="width: 250px;" onkeyup="searchDashboards(this.value)">
                            <button class="btn btn-success" onclick="createQuickDashboard()">
                                <i class="fas fa-magic"></i>
                                إنشاء سريع
                            </button>
                        </div>
                    </div>

                    <div class="dashboard-grid" id="dashboardsGrid">
                        <!-- Dashboards will be loaded here -->
                    </div>
                </div>

                <!-- Other sections will be loaded dynamically -->
                <div id="dynamic-content" style="display: none;">
                    <!-- Dynamic content sections -->
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Creation Modal -->
    <div id="createModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-plus"></i>
                    إنشاء لوحة تحكم جديدة
                </h3>
                <button class="close-btn" onclick="closeCreateModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="tabs">
                    <div class="tab active" onclick="switchTab('basic')">المعلومات الأساسية</div>
                    <div class="tab" onclick="switchTab('design')">التصميم</div>
                    <div class="tab" onclick="switchTab('widgets')">الأدوات</div>
                    <div class="tab" onclick="switchTab('permissions')">الصلاحيات</div>
                </div>

                <!-- Basic Tab -->
                <div id="basic-tab" class="tab-content active">
                    <div class="form-group">
                        <label class="form-label">اسم لوحة التحكم</label>
                        <input type="text" id="dashboardName" class="form-input" placeholder="مثال: لوحة إحصائيات اللاعبين">
                    </div>
                    <div class="form-group">
                        <label class="form-label">الوصف</label>
                        <textarea id="dashboardDescription" class="form-input form-textarea" placeholder="وصف مختصر للوحة التحكم..."></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">الفئة</label>
                        <select id="dashboardCategory" class="form-input form-select">
                            <option value="">اختر الفئة</option>
                            <option value="analytics">التحليلات</option>
                            <option value="management">الإدارة</option>
                            <option value="reports">التقارير</option>
                            <option value="monitoring">المراقبة</option>
                            <option value="finance">المالية</option>
                            <option value="hr">الموارد البشرية</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">مستوى الأولوية</label>
                        <select id="dashboardPriority" class="form-input form-select">
                            <option value="high">عالية</option>
                            <option value="medium" selected>متوسطة</option>
                            <option value="low">منخفضة</option>
                        </select>
                    </div>
                </div>

                <!-- Design Tab -->
                <div id="design-tab" class="tab-content">
                    <div class="form-group">
                        <label class="form-label">السمة الأساسية</label>
                        <div class="theme-selector" id="themeSelector">
                            <div class="theme-option active" data-theme="default">
                                <div class="theme-preview" style="background: linear-gradient(135deg, #1a365d, #3182ce);"></div>
                                <span>افتراضي</span>
                            </div>
                            <div class="theme-option" data-theme="dark">
                                <div class="theme-preview" style="background: linear-gradient(135deg, #2d3748, #4a5568);"></div>
                                <span>داكن</span>
                            </div>
                            <div class="theme-option" data-theme="colorful">
                                <div class="theme-preview" style="background: linear-gradient(135deg, #667eea, #764ba2);"></div>
                                <span>ملون</span>
                            </div>
                            <div class="theme-option" data-theme="minimal">
                                <div class="theme-preview" style="background: linear-gradient(135deg, #f7fafc, #edf2f7);"></div>
                                <span>بسيط</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">اللون الأساسي</label>
                        <input type="color" id="primaryColor" class="form-input" value="#1a365d" style="height: 50px;">
                    </div>
                    <div class="form-group">
                        <label class="form-label">اللون الثانوي</label>
                        <input type="color" id="secondaryColor" class="form-input" value="#3182ce" style="height: 50px;">
                    </div>
                </div>

                <!-- Widgets Tab -->
                <div id="widgets-tab" class="tab-content">
                    <div class="form-group">
                        <label class="form-label">اختر الأدوات المطلوبة</label>
                        <div class="widgets-grid" id="widgetsGrid">
                            <!-- Widgets will be loaded here -->
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">الأدوات المحددة</label>
                        <div id="selectedWidgets" class="selected-widgets">
                            <!-- Selected widgets will appear here -->
                        </div>
                    </div>
                </div>

                <!-- Permissions Tab -->
                <div id="permissions-tab" class="tab-content">
                    <div class="form-group">
                        <label class="form-label">صلاحيات الوصول</label>
                        <div class="permissions-grid">
                            <div class="permission-item">
                                <label>
                                    <input type="checkbox" checked> المديرين
                                </label>
                            </div>
                            <div class="permission-item">
                                <label>
                                    <input type="checkbox"> المدربين
                                </label>
                            </div>
                            <div class="permission-item">
                                <label>
                                    <input type="checkbox"> اللاعبين
                                </label>
                            </div>
                            <div class="permission-item">
                                <label>
                                    <input type="checkbox"> الضيوف
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeCreateModal()">إلغاء</button>
                <button class="btn btn-warning" onclick="saveDraft()">حفظ كمسودة</button>
                <button class="btn btn-primary" onclick="createDashboard()">إنشاء اللوحة</button>
            </div>
        </div>
    </div>
