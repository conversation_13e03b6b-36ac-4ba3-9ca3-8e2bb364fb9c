/* الخط العربي */
body {
    font-family: 'Cairo', sans-serif;
    background-color: #f9f9f9;
    color: #333;
    margin: 0;
    padding: 0;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* الهيدر */
header {
    background-color: #f5f5f5;
    border-bottom: 4px solid #d4af37; /* لون ذهبي */
    padding: 20px 0;
    text-align: center;
}

header .logo {
    width: 120px;
    height: auto;
    margin-bottom: 10px;
}

header h1 {
    font-size: 2.5rem;
    color: #d4af37;
    margin: 0;
}

header p {
    font-size: 1.2rem;
    color: #555;
    margin-top: 5px;
}

/* الأقسام */
section {
    background-color: #fff;
    margin: 30px 0;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
}

section h2 {
    color: #d4af37;
    margin-bottom: 20px;
    font-size: 2rem;
    border-bottom: 2px solid #d4af37;
    padding-bottom: 10px;
}

/* معرض الصور */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.gallery-grid img {
    width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transition: transform 0.3s ease;
}

.gallery-grid img:hover {
    transform: scale(1.05);
}

/* نموذج التسجيل */
form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

form label {
    font-weight: bold;
    margin-bottom: 5px;
}

form input, form select {
    padding: 10px;
    font-size: 1rem;
    border: 2px solid #d4af37;
    border-radius: 5px;
    outline: none;
    transition: border-color 0.3s ease;
}

form input:focus, form select:focus {
    border-color: #a67c00;
}

form button {
    background-color: #d4af37;
    color: #fff;
    font-size: 1.2rem;
    padding: 12px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

form button:hover {
    background-color: #a67c00;
}

/* الفوتر */
footer {
    background-color: #222;
    color: #ccc;
    text-align: center;
    padding: 15px 0;
    font-size: 1rem;
    margin-top: 40px;
    border-top: 4px solid #d4af37;
}

/* استجابة */
@media (max-width: 768px) {
    header h1 {
        font-size: 1.8rem;
    }

    section h2 {
        font-size: 1.5rem;
    }
}
