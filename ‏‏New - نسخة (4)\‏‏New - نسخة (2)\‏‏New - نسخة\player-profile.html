<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملف اللاعب الشامل - أكاديمية 7C</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            /* AI Professional Theme Colors */
            --primary-color: #6366f1;
            --secondary-color: #8b5cf6;
            --accent-color: #06b6d4;
            --background-dark: #0f0f23;
            --background-secondary: #1a1a2e;
            --background-tertiary: #16213e;
            --background-light: #f8fafc;
            --text-primary: #ffffff;
            --text-secondary: #e2e8f0;
            --text-muted: #94a3b8;
            --border-color: rgba(255, 255, 255, 0.1);
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --info-color: #3b82f6;

            /* AI Glow Effects */
            --glow-primary: rgba(99, 102, 241, 0.4);
            --glow-secondary: rgba(139, 92, 246, 0.4);
            --glow-accent: rgba(6, 182, 212, 0.4);

            /* Glass Morphism */
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
            --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--background-dark) 0%, var(--background-secondary) 50%, var(--background-tertiary) 100%);
            color: var(--text-primary);
            overflow-x: hidden;
            position: relative;
            min-height: 100vh;
        }



        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
        }



        /* AI Professional Header */
        .header {
            background: linear-gradient(135deg,
                rgba(15, 15, 35, 0.95) 0%,
                rgba(26, 26, 46, 0.95) 50%,
                rgba(22, 33, 62, 0.95) 100%);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--glass-shadow);
            border-radius: 0 0 20px 20px;
            margin-bottom: 2rem;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            position: relative;
            box-shadow: 0 8px 25px var(--glow-primary);
        }

        .logo-text h1 {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 0.25rem;
        }

        .logo-text p {
            font-size: 0.875rem;
            color: var(--text-light);
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            border: none;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 4px 15px var(--glow-primary);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px var(--glow-primary);
        }

        .btn-secondary {
            background: var(--glass-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        /* AI Professional Card Styles */
        .card {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 2rem;
            backdrop-filter: blur(20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: var(--glass-shadow);
        }

        .card:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--primary-color);
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
        }

        /* AI Professional Profile Header */
        .profile-header {
            background: linear-gradient(135deg,
                var(--primary-color) 0%,
                var(--secondary-color) 50%,
                var(--accent-color) 100%);
            border-radius: 24px;
            padding: 3rem;
            margin-bottom: 3rem;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .profile-avatar {
            width: 140px;
            height: 140px;
            border-radius: 50%;
            border: 4px solid rgba(255, 255, 255, 0.3);
            object-fit: cover;
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .profile-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
        }

        .profile-info {
            position: relative;
            z-index: 2;
        }

        .profile-info h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #ffffff, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .profile-info p {
            opacity: 0.95;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .profile-info p i {
            color: var(--accent-color);
            font-size: 1.2rem;
        }



        /* AI Professional Navigation Tabs */
        .nav-tabs {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 3rem;
            overflow-x: auto;
            padding: 1rem;
            background: var(--glass-bg);
            border-radius: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            box-shadow: var(--glass-shadow);
        }

        .nav-tab {
            padding: 1rem 1.5rem;
            background: transparent;
            border: 1px solid transparent;
            border-radius: 12px;
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            white-space: nowrap;
            cursor: pointer;
            position: relative;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            overflow: hidden;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px var(--glow-primary);
        }

        .nav-tab:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--border-color);
            transform: translateY(-1px);
            color: var(--text-primary);
        }

        .nav-tab i {
            font-size: 1.1rem;
            transition: transform 0.3s ease;
        }

        .nav-tab:hover i {
            transform: scale(1.1);
        }

        /* AI Color Themes */
        .theme-cyber {
            --primary-color: #00ff88;
            --secondary-color: #00ccff;
            --accent-color: #ff0080;
            --glow-primary: rgba(0, 255, 136, 0.4);
            --glow-secondary: rgba(0, 204, 255, 0.4);
            --glow-accent: rgba(255, 0, 128, 0.4);
        }

        .theme-neon {
            --primary-color: #ff006e;
            --secondary-color: #8338ec;
            --accent-color: #3a86ff;
            --glow-primary: rgba(255, 0, 110, 0.4);
            --glow-secondary: rgba(131, 56, 236, 0.4);
            --glow-accent: rgba(58, 134, 255, 0.4);
        }

        .theme-ocean {
            --primary-color: #0077be;
            --secondary-color: #00a8cc;
            --accent-color: #40e0d0;
            --glow-primary: rgba(0, 119, 190, 0.4);
            --glow-secondary: rgba(0, 168, 204, 0.4);
            --glow-accent: rgba(64, 224, 208, 0.4);
        }

        .theme-sunset {
            --primary-color: #ff6b35;
            --secondary-color: #f7931e;
            --accent-color: #ffcd3c;
            --glow-primary: rgba(255, 107, 53, 0.4);
            --glow-secondary: rgba(247, 147, 30, 0.4);
            --glow-accent: rgba(255, 205, 60, 0.4);
        }

        .theme-forest {
            --primary-color: #2d5016;
            --secondary-color: #3e7b27;
            --accent-color: #8bc34a;
            --glow-primary: rgba(45, 80, 22, 0.4);
            --glow-secondary: rgba(62, 123, 39, 0.4);
            --glow-accent: rgba(139, 195, 74, 0.4);
        }

        .theme-royal {
            --primary-color: #4a148c;
            --secondary-color: #7b1fa2;
            --accent-color: #e1bee7;
            --glow-primary: rgba(74, 20, 140, 0.4);
            --glow-secondary: rgba(123, 31, 162, 0.4);
            --glow-accent: rgba(225, 190, 231, 0.4);
        }

        /* AI Theme Selector */
        .theme-selector {
            position: fixed;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            z-index: 1001;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 1rem;
            box-shadow: var(--glass-shadow);
            transition: all 0.3s ease;
        }

        .theme-selector.collapsed {
            transform: translateY(-50%) translateX(-80%);
        }

        .theme-toggle {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px var(--glow-primary);
        }

        .theme-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px var(--glow-primary);
        }

        .theme-options {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .theme-selector:not(.collapsed) .theme-options {
            max-height: 400px;
        }

        .theme-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .theme-option:hover {
            transform: scale(1.1);
            border-color: white;
        }

        .theme-option.active {
            border-color: white;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .theme-option.default { background: linear-gradient(135deg, #6366f1, #8b5cf6); }
        .theme-option.cyber { background: linear-gradient(135deg, #00ff88, #00ccff); }
        .theme-option.neon { background: linear-gradient(135deg, #ff006e, #8338ec); }
        .theme-option.ocean { background: linear-gradient(135deg, #0077be, #00a8cc); }
        .theme-option.sunset { background: linear-gradient(135deg, #ff6b35, #f7931e); }
        .theme-option.forest { background: linear-gradient(135deg, #2d5016, #3e7b27); }
        .theme-option.royal { background: linear-gradient(135deg, #4a148c, #7b1fa2); }

        /* Tab Content */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Grid Layouts */
        .grid-2 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .grid-3 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .grid-4 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        /* Stats Cards */
        .stat-card {
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 69, 19, 0.1));
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.875rem;
        }

        /* QR Code Card */
        .qr-card {
            text-align: center;
            background: white;
            color: #333;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        /* FIFA Card Style */
        .fifa-card {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            border: 2px solid var(--primary-color);
            border-radius: 16px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
            max-width: 300px;
            margin: 0 auto;
        }

        .fifa-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent 30%, rgba(139, 69, 19, 0.1) 50%, transparent 70%);
        }

        .fifa-rating {
            position: absolute;
            top: 1rem;
            left: 1rem;
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .fifa-position {
            position: absolute;
            top: 3.5rem;
            left: 1rem;
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .fifa-player-image {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin: 0 auto 1rem;
            border: 2px solid var(--primary-color);
        }

        .fifa-player-name {
            font-size: 1.125rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 0.5rem;
        }

        .fifa-academy {
            text-align: center;
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.7);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .profile-header {
                text-align: center;
            }

            .nav-tabs {
                justify-content: center;
            }

            .grid-2, .grid-3, .grid-4 {
                grid-template-columns: 1fr;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Export Menu */
        .export-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--background-dark);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 0.5rem;
            min-width: 150px;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .export-menu button {
            width: 100%;
            padding: 0.5rem 1rem;
            background: transparent;
            border: none;
            color: white;
            text-align: right;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .export-menu button:hover {
            background: var(--primary-color);
        }

        /* Edit Mode Styles */
        .edit-mode .editable {
            border: 2px dashed var(--primary-color);
            padding: 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .edit-mode .editable:hover {
            background: rgba(139, 69, 19, 0.1);
        }

        .edit-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .edit-modal {
            background: var(--background-dark);
            border-radius: 12px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .edit-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .edit-form label {
            font-weight: 500;
            color: #ffffff;
            margin-bottom: 0.5rem;
        }

        .edit-form input,
        .edit-form select,
        .edit-form textarea {
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            font-size: 0.875rem;
        }

        .edit-form input:focus,
        .edit-form select:focus,
        .edit-form textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
        }

        /* Comprehensive Editor Styles */
        .editor-tabs {
            display: flex;
            gap: 1rem;
            border-bottom: 2px solid #374151;
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .editor-tab {
            padding: 0.75rem 1.5rem;
            background: #374151;
            color: white;
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .editor-tab:hover {
            background: #4B5563;
            transform: translateY(-2px);
        }

        .editor-tab.active {
            background: linear-gradient(135deg, #8B4513, #D2691E) !important;
            color: white;
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
        }

        .editor-tab-content {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .social-icon {
            transition: all 0.3s ease;
            padding: 0.5rem;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
        }

        .social-icon:hover {
            transform: scale(1.1);
            background: rgba(255, 255, 255, 0.2);
        }

        .computed-stats {
            background: linear-gradient(135deg, rgba(139, 69, 19, 0.1), rgba(210, 105, 30, 0.1));
            border: 1px solid rgba(139, 69, 19, 0.3);
            border-radius: 1rem;
            padding: 1.5rem;
        }

        .preview-container {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            border: 2px solid #374151;
            border-radius: 1rem;
            padding: 2rem;
            min-height: 400px;
        }

        .grid-2 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }

        .grid-4 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 500;
            color: white;
        }

        .bg-blue-600 { background-color: #2563eb; }
        .bg-green-600 { background-color: #16a34a; }
        .bg-purple-600 { background-color: #9333ea; }
        .bg-orange-600 { background-color: #ea580c; }

        /* Toggle Switch Styles */
        .toggle-switch {
            appearance: none;
            width: 50px;
            height: 25px;
            background: #374151;
            border-radius: 25px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid #4B5563;
        }

        .toggle-switch:checked {
            background: linear-gradient(135deg, #8B4513, #D2691E);
            border-color: #8B4513;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            width: 19px;
            height: 19px;
            border-radius: 50%;
            background: white;
            top: 1px;
            left: 1px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .toggle-switch:checked::before {
            transform: translateX(25px);
        }

        /* Market Value Styles */
        .market-value-trend.up {
            color: #10B981 !important;
        }

        .market-value-trend.down {
            color: #EF4444 !important;
        }

        .market-value-trend.stable {
            color: #6B7280 !important;
        }

        /* QR Code Styles */
        #qrCodeContainer {
            border: 2px dashed #4B5563;
            transition: all 0.3s ease;
        }

        #qrCodeContainer.has-qr {
            border: 2px solid #8B4513;
            background: white;
        }

        /* Privacy Settings Animations */
        .privacy-setting {
            transition: all 0.3s ease;
        }

        .privacy-setting:hover {
            background: rgba(139, 69, 19, 0.1) !important;
            transform: translateX(-2px);
        }

        /* Market Value Components Animation */
        .market-component {
            transition: all 0.5s ease;
            transform: scale(0.95);
        }

        .market-component.updated {
            transform: scale(1);
            box-shadow: 0 0 20px rgba(139, 69, 19, 0.3);
        }

        /* Enhanced Player Name and Academic Number */
        .player-full-name {
            display: inline-block;
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            margin-left: 1rem;
        }

        .academic-number-badge {
            display: inline-block;
            background: linear-gradient(135deg, #8B4513, #D2691E);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: bold;
            vertical-align: middle;
            box-shadow: 0 2px 8px rgba(139, 69, 19, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .academic-number-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .academic-number-badge:hover::before {
            left: 100%;
        }

        .academic-number-badge:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.4);
        }

        /* Simplified Update Styles */
        .update-flash {
            background-color: rgba(139, 69, 19, 0.2);
            transition: background-color 0.3s ease;
        }

        .pulse-update {
            transform: scale(1.02);
            transition: transform 0.3s ease;
        }

        /* Validation Error Styles */
        .validation-error {
            border: 2px solid #EF4444 !important;
            background-color: rgba(239, 68, 68, 0.1) !important;
        }

        .validation-success {
            border: 2px solid #10B981 !important;
            background-color: rgba(16, 185, 129, 0.1) !important;
        }

        /* Enhanced Modal Styles */
        .modal {
            backdrop-filter: blur(5px);
        }

        .modal-content {
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(139, 69, 19, 0.3);
        }

        /* Preview Styles */
        .preview-content {
            background: linear-gradient(135deg, rgba(139, 69, 19, 0.1), rgba(210, 105, 30, 0.1));
            border-radius: 15px;
            padding: 2rem;
            border: 2px solid rgba(139, 69, 19, 0.3);
        }

        .preview-updated {
            opacity: 0.9;
            transition: opacity 0.3s ease;
        }

        /* Form Validation Visual Feedback */
        .field-valid {
            border-left: 4px solid #10B981;
        }

        .field-invalid {
            border-left: 4px solid #EF4444;
        }

        .field-warning {
            border-left: 4px solid #F59E0B;
        }

        /* Template Styles */
        .template-cards .nav-tabs {
            display: none;
        }

        .template-cards .tab-content {
            display: block !important;
        }

        .template-cards #tabContents {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .template-compact {
            font-size: 0.875rem;
        }

        .template-compact .card {
            padding: 1rem;
        }

        .template-compact .profile-header {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .template-professional {
            background: white;
            color: #333;
        }

        .template-professional .card {
            background: white;
            border: 1px solid #ddd;
            color: #333;
        }

        .template-professional .profile-header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
        }

        .template-fifa {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
        }

        .template-fifa .card {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            border: 2px solid var(--primary-color);
        }

        /* FIFA Ultimate Card Styles */
        .fifa-ultimate-card {
            width: 400px;
            height: 600px;
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            border-radius: 20px;
            position: relative;
            margin: 2rem auto;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            border: 3px solid var(--primary-color);
        }

        .fifa-card-header {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .fifa-rating {
            font-size: 4rem;
            font-weight: bold;
            color: var(--primary-color);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .fifa-position {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            margin-top: 1rem;
        }

        .fifa-flag {
            font-size: 2rem;
        }

        .fifa-player-image {
            position: absolute;
            top: 120px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 200px;
            border-radius: 50%;
            overflow: hidden;
            border: 4px solid var(--primary-color);
        }

        .fifa-player-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .fifa-player-info {
            position: absolute;
            top: 340px;
            left: 0;
            right: 0;
            text-align: center;
        }

        .fifa-player-name {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            margin-bottom: 0.5rem;
        }

        .fifa-player-surname {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            text-transform: uppercase;
        }

        .fifa-stats {
            position: absolute;
            bottom: 80px;
            left: 20px;
            right: 20px;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }

        .fifa-stat {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem;
            border-radius: 8px;
        }

        .fifa-stat .stat-value {
            display: block;
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .fifa-stat .stat-name {
            display: block;
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 0.25rem;
        }

        .fifa-club {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 20px;
        }

        .fifa-club img {
            width: 30px;
            height: 30px;
            border-radius: 50%;
        }

        .fifa-club span {
            color: white;
            font-weight: bold;
            font-size: 0.875rem;
        }

        /* Color Theme Variables */
        .theme-blue {
            --primary-color: #3b82f6;
            --secondary-color: #60a5fa;
        }

        .theme-green {
            --primary-color: #10b981;
            --secondary-color: #34d399;
        }

        .theme-purple {
            --primary-color: #8b5cf6;
            --secondary-color: #a78bfa;
        }

        .theme-red {
            --primary-color: #ef4444;
            --secondary-color: #f87171;
        }

        .theme-orange {
            --primary-color: #f59e0b;
            --secondary-color: #fbbf24;
        }

        .theme-pink {
            --primary-color: #ec4899;
            --secondary-color: #f472b6;
        }

        .theme-indigo {
            --primary-color: #6366f1;
            --secondary-color: #818cf8;
        }

        .theme-teal {
            --primary-color: #14b8a6;
            --secondary-color: #2dd4bf;
        }

        .theme-gray {
            --primary-color: #6b7280;
            --secondary-color: #9ca3af;
        }

        /* Modal Styles */
        .modal, .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            backdrop-filter: blur(5px);
        }

        .modal.hidden {
            display: none;
        }

        .modal-content {
            background: var(--background-dark);
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-header h2 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #ffffff;
            margin: 0;
        }

        .close-button, .modal-close {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 1.2rem;
        }

        .close-button:hover, .modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: scale(1.1);
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            padding: 1.5rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Color Theme Grid */
        .color-themes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .color-theme {
            padding: 1rem;
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .color-theme:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .color-theme.active {
            border-color: white;
        }

        .color-preview {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 0 auto 0.5rem;
        }

        /* Scrollbar Styles */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">7C</div>
                <div class="logo-text">
                    <h1>ملف اللاعب الشامل</h1>
                    <p>أكاديمية 7C للتدريب الرياضي</p>
                </div>
            </div>
            <div class="header-actions">
                <!-- Template Selector -->
                <select id="templateSelector" class="btn btn-secondary" onchange="changeTemplate(this.value)">
                    <option value="default">القالب الافتراضي</option>
                    <option value="cards">قالب البطاقات</option>
                    <option value="compact">القالب المضغوط</option>
                    <option value="professional">القالب الاحترافي</option>
                    <option value="fifa">قالب FIFA</option>
                </select>

                <!-- Color Theme Selector -->
                <button class="btn btn-secondary" onclick="openColorThemeModal()">
                    <i class="fas fa-palette"></i>
                    الألوان
                </button>

                <!-- Export Options -->
                <div class="relative">
                    <button class="btn btn-secondary" onclick="toggleExportMenu()">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                    <div id="exportMenu" class="export-menu hidden">
                        <button onclick="exportToPDF()"><i class="fas fa-file-pdf"></i> PDF</button>
                        <button onclick="exportToWord()"><i class="fas fa-file-word"></i> Word</button>
                        <button onclick="exportToExcel()"><i class="fas fa-file-excel"></i> Excel</button>
                        <button onclick="exportToImage()"><i class="fas fa-image"></i> صورة</button>
                        <button onclick="printProfile()"><i class="fas fa-print"></i> طباعة</button>
                    </div>
                </div>

                <!-- Share Options -->
                <button class="btn btn-primary" onclick="openShareModal()">
                    <i class="fas fa-share-alt"></i>
                    مشاركة
                </button>

                <!-- Attendance System Link -->
                <button class="btn btn-success" onclick="openAttendanceSystem()" style="margin-right: 1rem;">
                    <i class="fas fa-calendar-check"></i>
                    نظام الحضور
                </button>

                <!-- Stats Page -->
                <a href="player-stats.html" class="btn btn-secondary">
                    <i class="fas fa-chart-bar"></i>
                    إحصائيات صفحتي
                </a>

                <a href="admin-applications.html" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للنظام الرئيسي
                </a>

                <button class="btn btn-primary" onclick="openProfileEditor()">
                    <i class="fas fa-edit"></i>
                    تحرير الملف الشخصي
                </button>

                <button class="btn btn-secondary" onclick="toggleEditMode()">
                    <i class="fas fa-cog"></i>
                    <span id="editModeText">تحرير سريع</span>
                </button>
            </div>
        </div>
    </header>

    <!-- AI Theme Selector -->
    <div class="theme-selector collapsed" id="themeSelector">
        <button class="theme-toggle" onclick="toggleThemeSelector()">
            <i class="fas fa-palette"></i>
        </button>
        <div class="theme-options">
            <div class="theme-option default active" onclick="changeAITheme('default')" title="الافتراضي"></div>
            <div class="theme-option cyber" onclick="changeAITheme('cyber')" title="سايبر"></div>
            <div class="theme-option neon" onclick="changeAITheme('neon')" title="نيون"></div>
            <div class="theme-option ocean" onclick="changeAITheme('ocean')" title="المحيط"></div>
            <div class="theme-option sunset" onclick="changeAITheme('sunset')" title="غروب الشمس"></div>
            <div class="theme-option forest" onclick="changeAITheme('forest')" title="الغابة"></div>
            <div class="theme-option royal" onclick="changeAITheme('royal')" title="ملكي"></div>
        </div>
    </div>

    <!-- Main Container -->
    <div class="container">
        <!-- Profile Header -->
        <div class="profile-header fade-in">
            <div class="flex flex-col md:flex-row items-center gap-6">
                <img id="playerAvatar" src="https://via.placeholder.com/120x120/8B4513/FFFFFF?text=Player" alt="صورة اللاعب" class="profile-avatar" loading="lazy">
                <div class="profile-info flex-1">
                    <h1 id="playerName">محمد أحمد الزهراني</h1>
                    <p><i class="fas fa-id-card ml-2"></i>الرقم الأكاديمي: <span id="academicNumber">7C-2024-001</span></p>
                    <p><i class="fas fa-calendar ml-2"></i>العمر: <span id="playerAge">16 سنة</span></p>
                    <p><i class="fas fa-users ml-2"></i>الفئة: <span id="playerCategory">ناشئين</span></p>
                    <p><i class="fas fa-phone ml-2"></i>الجوال: <span id="playerPhone">0501234567</span></p>
                </div>
                <div class="text-center">
                    <div class="fifa-card">
                        <div class="fifa-rating" id="playerRating">85</div>
                        <div class="fifa-position" id="playerPosition">MID</div>
                        <img id="fifaPlayerImage" src="https://via.placeholder.com/80x80/8B4513/FFFFFF?text=Player" alt="صورة اللاعب" class="fifa-player-image" loading="lazy">
                        <div class="fifa-player-name" id="fifaPlayerName">محمد الزهراني</div>
                        <div class="fifa-academy">أكاديمية 7C</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <div class="nav-tab active" onclick="showTab('overview')">
                <i class="fas fa-home ml-2"></i>نظرة عامة
            </div>
            <div class="nav-tab" onclick="showTab('subscription')">
                <i class="fas fa-credit-card ml-2"></i>الاشتراك
            </div>
            <div class="nav-tab" onclick="showTab('qr-card')">
                <i class="fas fa-qrcode ml-2"></i>البطاقة الرقمية
            </div>
            <div class="nav-tab" onclick="showTab('schedule')">
                <i class="fas fa-calendar-alt ml-2"></i>جدول التدريب
            </div>
            <div class="nav-tab" onclick="showTab('attendance')">
                <i class="fas fa-check-circle ml-2"></i>الحضور والغياب
            </div>
            <div class="nav-tab" onclick="showTab('messages')">
                <i class="fas fa-comments ml-2"></i>الرسائل
            </div>
            <div class="nav-tab" onclick="showTab('loyalty')">
                <i class="fas fa-star ml-2"></i>نقاط الولاء
            </div>
            <div class="nav-tab" onclick="showTab('evaluation')">
                <i class="fas fa-chart-line ml-2"></i>التقييم
            </div>
            <div class="nav-tab" onclick="showTab('achievements')">
                <i class="fas fa-trophy ml-2"></i>الإنجازات
            </div>
            <div class="nav-tab" onclick="showTab('participation')">
                <i class="fas fa-users ml-2"></i>المشاركات
            </div>
            <div class="nav-tab" onclick="showTab('media')">
                <i class="fas fa-camera ml-2"></i>الاستوديو
            </div>
            <div class="nav-tab" onclick="showTab('store')">
                <i class="fas fa-shopping-cart ml-2"></i>المتجر
            </div>
            <div class="nav-tab" onclick="showTab('players')">
                <i class="fas fa-users ml-2"></i>عرض اللاعبين
            </div>
        </div>

        <!-- Tab Contents will be added via JavaScript -->
        <div id="tabContents">
            <!-- Content will be dynamically loaded -->
        </div>
    </div>

    <!-- Color Theme Modal -->
    <div id="colorThemeModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تخصيص الألوان</h2>
                <button onclick="closeColorThemeModal()" class="close-button">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <h3 class="text-lg font-bold mb-4">اختر مجموعة الألوان:</h3>
                <div class="color-themes">
                    <div class="color-theme" data-theme="default" onclick="selectColorTheme('default')">
                        <div class="color-preview" style="background: linear-gradient(135deg, #8B4513, #D2691E);"></div>
                        <span>الافتراضي</span>
                    </div>
                    <div class="color-theme" data-theme="blue" onclick="selectColorTheme('blue')">
                        <div class="color-preview" style="background: linear-gradient(135deg, #3b82f6, #60a5fa);"></div>
                        <span>أزرق</span>
                    </div>
                    <div class="color-theme" data-theme="green" onclick="selectColorTheme('green')">
                        <div class="color-preview" style="background: linear-gradient(135deg, #10b981, #34d399);"></div>
                        <span>أخضر</span>
                    </div>
                    <div class="color-theme" data-theme="purple" onclick="selectColorTheme('purple')">
                        <div class="color-preview" style="background: linear-gradient(135deg, #8b5cf6, #a78bfa);"></div>
                        <span>بنفسجي</span>
                    </div>
                    <div class="color-theme" data-theme="red" onclick="selectColorTheme('red')">
                        <div class="color-preview" style="background: linear-gradient(135deg, #ef4444, #f87171);"></div>
                        <span>أحمر</span>
                    </div>
                    <div class="color-theme" data-theme="orange" onclick="selectColorTheme('orange')">
                        <div class="color-preview" style="background: linear-gradient(135deg, #f59e0b, #fbbf24);"></div>
                        <span>برتقالي</span>
                    </div>
                    <div class="color-theme" data-theme="pink" onclick="selectColorTheme('pink')">
                        <div class="color-preview" style="background: linear-gradient(135deg, #ec4899, #f472b6);"></div>
                        <span>وردي</span>
                    </div>
                    <div class="color-theme" data-theme="indigo" onclick="selectColorTheme('indigo')">
                        <div class="color-preview" style="background: linear-gradient(135deg, #6366f1, #818cf8);"></div>
                        <span>نيلي</span>
                    </div>
                    <div class="color-theme" data-theme="teal" onclick="selectColorTheme('teal')">
                        <div class="color-preview" style="background: linear-gradient(135deg, #14b8a6, #2dd4bf);"></div>
                        <span>تركوازي</span>
                    </div>
                    <div class="color-theme" data-theme="gray" onclick="selectColorTheme('gray')">
                        <div class="color-preview" style="background: linear-gradient(135deg, #6b7280, #9ca3af);"></div>
                        <span>رمادي</span>
                    </div>
                </div>

                <h3 class="text-lg font-bold mb-4">ألوان مخصصة:</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-bold mb-2">اللون الأساسي:</label>
                        <input type="color" id="customPrimaryColor" value="#8B4513" class="w-full h-12 rounded-lg border-none">
                    </div>
                    <div>
                        <label class="block text-sm font-bold mb-2">اللون الثانوي:</label>
                        <input type="color" id="customSecondaryColor" value="#D2691E" class="w-full h-12 rounded-lg border-none">
                    </div>
                </div>
                <button onclick="applyCustomColors()" class="btn btn-primary w-full mt-4">
                    تطبيق الألوان المخصصة
                </button>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeColorThemeModal()">إلغاء</button>
                <button class="btn btn-primary" onclick="saveColorTheme()">حفظ</button>
            </div>
        </div>
    </div>

    <!-- Share Modal -->
    <div id="shareModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>مشاركة الملف الشخصي</h2>
                <button onclick="closeShareModal()" class="close-button">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="space-y-4">
                    <div>
                        <h3 class="text-lg font-bold mb-2">إعدادات الخصوصية:</h3>
                        <div class="space-y-2">
                            <label class="flex items-center gap-2">
                                <input type="radio" name="privacy" value="public" checked>
                                <span>عام - يمكن لأي شخص رؤية الملف</span>
                            </label>
                            <label class="flex items-center gap-2">
                                <input type="radio" name="privacy" value="private">
                                <span>خاص - أنت فقط</span>
                            </label>
                            <label class="flex items-center gap-2">
                                <input type="radio" name="privacy" value="friends">
                                <span>الأصدقاء فقط</span>
                            </label>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-lg font-bold mb-2">رابط المشاركة:</h3>
                        <div class="flex gap-2">
                            <input type="text" id="shareLink" readonly class="flex-1 p-3 bg-gray-800 border border-gray-600 rounded-lg" value="https://academy7c.com/player/7C-2024-001">
                            <button onclick="copyShareLink()" class="btn btn-secondary">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-lg font-bold mb-2">مشاركة على:</h3>
                        <div class="flex gap-2">
                            <button onclick="shareToWhatsApp()" class="btn btn-success flex-1">
                                <i class="fab fa-whatsapp"></i> واتساب
                            </button>
                            <button onclick="shareToTwitter()" class="btn btn-info flex-1">
                                <i class="fab fa-twitter"></i> تويتر
                            </button>
                            <button onclick="shareToFacebook()" class="btn btn-primary flex-1">
                                <i class="fab fa-facebook"></i> فيسبوك
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeShareModal()">إغلاق</button>
                <button class="btn btn-primary" onclick="publishProfile()">نشر الملف</button>
            </div>
        </div>
    </div>

    <!-- Edit Profile Modal -->
    <div id="editProfileModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تحرير الملف الشخصي</h2>
                <button onclick="closeEditProfileModal()" class="close-button">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="editProfileForm" class="edit-form">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label>الاسم الأول:</label>
                            <input type="text" id="editFirstName" required>
                        </div>
                        <div>
                            <label>اسم الأب:</label>
                            <input type="text" id="editFatherName" required>
                        </div>
                        <div>
                            <label>اسم العائلة:</label>
                            <input type="text" id="editFamilyName" required>
                        </div>
                        <div>
                            <label>رقم الجوال:</label>
                            <input type="tel" id="editPersonalPhone" required>
                        </div>
                        <div>
                            <label>الطول (سم):</label>
                            <input type="number" id="editHeight" min="100" max="250">
                        </div>
                        <div>
                            <label>الوزن (كغ):</label>
                            <input type="number" id="editWeight" min="20" max="150">
                        </div>
                        <div>
                            <label>القدم المفضلة:</label>
                            <select id="editPreferredFoot">
                                <option value="right">اليمين</option>
                                <option value="left">اليسار</option>
                                <option value="both">كلاهما</option>
                            </select>
                        </div>
                        <div>
                            <label>المركز:</label>
                            <select id="editPosition">
                                <option value="GK">حارس مرمى</option>
                                <option value="DEF">مدافع</option>
                                <option value="MID">وسط</option>
                                <option value="ATT">مهاجم</option>
                            </select>
                        </div>
                    </div>

                    <div class="mt-4">
                        <label>تغيير الصورة الشخصية:</label>
                        <input type="file" id="editPlayerPhoto" accept="image/*" onchange="handleEditPhotoUpload(event)">
                        <div id="editPhotoPreview" class="mt-2 hidden">
                            <img id="editPhotoImage" src="" alt="معاينة الصورة" class="w-32 h-32 object-cover rounded-lg">
                            <div class="mt-2 flex gap-2">
                                <button type="button" onclick="rotatePhoto()" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> تدوير
                                </button>
                                <button type="button" onclick="cropPhoto()" class="btn btn-secondary">
                                    <i class="fas fa-crop"></i> قص
                                </button>
                                <button type="button" onclick="applyFilter()" class="btn btn-secondary">
                                    <i class="fas fa-magic"></i> فلتر
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeEditProfileModal()">إلغاء</button>
                <button class="btn btn-primary" onclick="saveProfileChanges()">حفظ التغييرات</button>
            </div>
        </div>
    </div>

    <!-- Comprehensive Profile Editor Modal -->
    <div id="profileEditorModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 1200px; max-height: 90vh; overflow-y: auto;">
            <div class="modal-header">
                <h2>محرر الملف الشخصي الشامل</h2>
                <span class="close" onclick="closeProfileEditor()">&times;</span>
            </div>
            <div class="modal-body">
                <!-- Editor Tabs -->
                <div class="editor-tabs mb-6" style="display: flex; gap: 1rem; border-bottom: 2px solid #374151; padding-bottom: 1rem;">
                    <button class="editor-tab active" onclick="showEditorTab('personal')" style="padding: 0.75rem 1.5rem; background: linear-gradient(135deg, #8B4513, #D2691E); color: white; border: none; border-radius: 0.5rem; cursor: pointer; transition: all 0.3s;">
                        <i class="fas fa-user ml-2"></i>المعلومات الشخصية
                    </button>
                    <button class="editor-tab" onclick="showEditorTab('social')" style="padding: 0.75rem 1.5rem; background: #374151; color: white; border: none; border-radius: 0.5rem; cursor: pointer; transition: all 0.3s;">
                        <i class="fas fa-share-alt ml-2"></i>وسائل التواصل
                    </button>
                    <button class="editor-tab" onclick="showEditorTab('media')" style="padding: 0.75rem 1.5rem; background: #374151; color: white; border: none; border-radius: 0.5rem; cursor: pointer; transition: all 0.3s;">
                        <i class="fas fa-camera ml-2"></i>الصور والشعارات
                    </button>
                    <button class="editor-tab" onclick="showEditorTab('privacy')" style="padding: 0.75rem 1.5rem; background: #374151; color: white; border: none; border-radius: 0.5rem; cursor: pointer; transition: all 0.3s;">
                        <i class="fas fa-shield-alt ml-2"></i>الخصوصية والقيمة السوقية
                    </button>
                    <button class="editor-tab" onclick="showEditorTab('preview')" style="padding: 0.75rem 1.5rem; background: #374151; color: white; border: none; border-radius: 0.5rem; cursor: pointer; transition: all 0.3s;">
                        <i class="fas fa-eye ml-2"></i>معاينة مباشرة
                    </button>
                </div>

                <!-- Personal Information Tab -->
                <div id="personalTab" class="editor-tab-content">
                    <h3 class="text-xl font-bold mb-4">المعلومات الشخصية</h3>
                    <div class="grid-2 gap-4">
                        <div>
                            <label class="block text-sm font-bold mb-2">الاسم الأول:</label>
                            <input type="text" id="editFirstNameComp" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">اسم الأب:</label>
                            <input type="text" id="editFatherNameComp" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">اسم العائلة:</label>
                            <input type="text" id="editFamilyNameComp" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">رقم الهوية:</label>
                            <input type="text" id="editNationalIdComp" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">تاريخ الميلاد:</label>
                            <input type="date" id="editBirthDateComp" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">رقم الجوال:</label>
                            <input type="tel" id="editPersonalPhoneComp" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">الفئة العمرية:</label>
                            <select id="editCategoryComp" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3">
                                <option value="براعم">براعم (تحت 8 سنوات)</option>
                                <option value="أشبال">أشبال (8-10 سنوات)</option>
                                <option value="ناشئين">ناشئين (11-14 سنة)</option>
                                <option value="شباب">شباب (15-18 سنة)</option>
                                <option value="رجال">رجال (19+ سنة)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">المركز المفضل:</label>
                            <select id="editPositionComp" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3">
                                <option value="GK">حارس مرمى</option>
                                <option value="DEF">مدافع</option>
                                <option value="MID">وسط الملعب</option>
                                <option value="ATT">مهاجم</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">القدم المفضلة:</label>
                            <select id="editPreferredFootComp" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3">
                                <option value="right">اليمين</option>
                                <option value="left">اليسار</option>
                                <option value="both">كلاهما</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">الطول (سم):</label>
                            <input type="number" id="editHeightComp" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3" min="100" max="250">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">الوزن (كغ):</label>
                            <input type="number" id="editWeightComp" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3" min="20" max="150">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">اسم ولي الأمر:</label>
                            <input type="text" id="editGuardianNameComp" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">جوال ولي الأمر:</label>
                            <input type="tel" id="editGuardianPhoneComp" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">صلة القرابة:</label>
                            <select id="editRelationshipComp" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3">
                                <option value="father">الأب</option>
                                <option value="mother">الأم</option>
                                <option value="brother">الأخ</option>
                                <option value="sister">الأخت</option>
                                <option value="uncle">العم</option>
                                <option value="aunt">العمة</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                    </div>

                    <div class="mt-6">
                        <label class="block text-sm font-bold mb-2">نبذة شخصية:</label>
                        <textarea id="editBioComp" rows="4" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3" placeholder="اكتب نبذة مختصرة عن اللاعب..."></textarea>
                    </div>

                    <!-- Read-only computed fields -->
                    <div class="mt-6 p-4 bg-gray-800/50 rounded-lg">
                        <h4 class="font-bold mb-3 text-yellow-400">
                            <i class="fas fa-lock ml-2"></i>
                            البيانات المحسوبة تلقائياً (غير قابلة للتحرير)
                        </h4>
                        <div class="grid-4 gap-4 text-center">
                            <div class="p-3 bg-gray-700/50 rounded-lg">
                                <div class="text-lg font-bold text-blue-400" id="previewRating">${currentPlayer.rating}</div>
                                <div class="text-sm text-gray-400">التقييم العام</div>
                            </div>
                            <div class="p-3 bg-gray-700/50 rounded-lg">
                                <div class="text-lg font-bold text-green-400" id="previewLoyalty">${currentPlayer.loyaltyPoints}</div>
                                <div class="text-sm text-gray-400">نقاط الولاء</div>
                            </div>
                            <div class="p-3 bg-gray-700/50 rounded-lg">
                                <div class="text-lg font-bold text-purple-400" id="previewAttendance">${currentPlayer.stats.attendanceRate}%</div>
                                <div class="text-sm text-gray-400">نسبة الحضور</div>
                            </div>
                            <div class="p-3 bg-gray-700/50 rounded-lg">
                                <div class="text-lg font-bold text-orange-400" id="previewMatches">${currentPlayer.stats.matchesPlayed}</div>
                                <div class="text-sm text-gray-400">عدد المشاركات</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Social Media Tab -->
                <div id="socialTab" class="editor-tab-content" style="display: none;">
                    <h3 class="text-xl font-bold mb-4">وسائل التواصل الاجتماعي</h3>
                    <div class="grid-2 gap-4">
                        <div>
                            <label class="block text-sm font-bold mb-2">
                                <i class="fab fa-instagram text-pink-500 ml-2"></i>Instagram
                            </label>
                            <input type="url" id="editInstagram" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3" placeholder="https://instagram.com/username">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">
                                <i class="fab fa-twitter text-blue-400 ml-2"></i>X (Twitter)
                            </label>
                            <input type="url" id="editTwitter" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3" placeholder="https://x.com/username">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">
                                <i class="fab fa-tiktok text-black ml-2"></i>TikTok
                            </label>
                            <input type="url" id="editTiktok" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3" placeholder="https://tiktok.com/@username">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">
                                <i class="fab fa-snapchat text-yellow-400 ml-2"></i>Snapchat
                            </label>
                            <input type="url" id="editSnapchat" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3" placeholder="https://snapchat.com/add/username">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">
                                <i class="fab fa-youtube text-red-500 ml-2"></i>YouTube
                            </label>
                            <input type="url" id="editYoutube" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3" placeholder="https://youtube.com/@username">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">
                                <i class="fab fa-facebook text-blue-600 ml-2"></i>Facebook
                            </label>
                            <input type="url" id="editFacebook" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3" placeholder="https://facebook.com/username">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">
                                <i class="fab fa-whatsapp text-green-500 ml-2"></i>WhatsApp
                            </label>
                            <input type="tel" id="editWhatsapp" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3" placeholder="+966501234567">
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-blue-600/20 border border-blue-600/30 rounded-lg">
                        <h4 class="font-bold mb-2">
                            <i class="fas fa-info-circle ml-2"></i>
                            ملاحظات مهمة
                        </h4>
                        <ul class="text-sm text-gray-300 space-y-1">
                            <li>• ستظهر الأيقونات فقط للحسابات المُدخلة</li>
                            <li>• تأكد من صحة الروابط قبل الحفظ</li>
                            <li>• يمكن ترك الحقول فارغة إذا لم تكن متوفرة</li>
                            <li>• ستكون الروابط قابلة للنقر في الصفحة العامة</li>
                        </ul>
                    </div>
                </div>

                <!-- Media Tab -->
                <div id="mediaTab" class="editor-tab-content" style="display: none;">
                    <h3 class="text-xl font-bold mb-4">إدارة الصور والشعارات</h3>

                    <!-- Player Photo Section -->
                    <div class="card mb-6">
                        <h4 class="font-bold mb-4">
                            <i class="fas fa-user-circle ml-2"></i>
                            صورة اللاعب الشخصية
                        </h4>
                        <div class="flex items-center gap-6">
                            <div class="text-center">
                                <img id="currentPlayerPhotoComp" src="${currentPlayer.avatar}" alt="صورة اللاعب" class="w-32 h-32 rounded-full object-cover border-4 border-gray-600">
                                <p class="text-sm text-gray-400 mt-2">الصورة الحالية</p>
                            </div>
                            <div class="flex-1">
                                <input type="file" id="playerPhotoUploadComp" accept="image/*" class="hidden" onchange="handlePhotoUploadComp(this, 'player')">
                                <button class="btn btn-primary mb-3" onclick="document.getElementById('playerPhotoUploadComp').click()">
                                    <i class="fas fa-upload ml-2"></i>
                                    رفع صورة جديدة
                                </button>
                                <button class="btn btn-secondary mb-3" onclick="resetToDefaultAvatarComp()">
                                    <i class="fas fa-undo ml-2"></i>
                                    استخدام شعار الأكاديمية
                                </button>
                                <p class="text-sm text-gray-400">
                                    الحد الأقصى: 5MB | الأنواع المدعومة: JPG, PNG, GIF
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Academy Logo Section -->
                    <div class="card mb-6">
                        <h4 class="font-bold mb-4">
                            <i class="fas fa-building ml-2"></i>
                            شعار أكاديمية 7C
                        </h4>
                        <div class="flex items-center gap-6">
                            <div class="text-center">
                                <img id="academyLogoComp" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiByeD0iMTAiIGZpbGw9IiM4QjQ1MTMiLz4KPHRleHQgeD0iNTAiIHk9IjU1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMzAiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+N0M8L3RleHQ+Cjwvc3ZnPgo=" alt="شعار الأكاديمية" class="w-24 h-24 rounded-lg border-4 border-gray-600">
                                <p class="text-sm text-gray-400 mt-2">الشعار الرسمي</p>
                            </div>
                            <div class="flex-1">
                                <input type="file" id="logoUploadComp" accept="image/*" class="hidden" onchange="handlePhotoUploadComp(this, 'logo')">
                                <button class="btn btn-primary mb-3" onclick="document.getElementById('logoUploadComp').click()">
                                    <i class="fas fa-upload ml-2"></i>
                                    رفع شعار مخصص
                                </button>
                                <button class="btn btn-secondary mb-3" onclick="resetToDefaultLogoComp()">
                                    <i class="fas fa-undo ml-2"></i>
                                    استخدام الشعار الافتراضي
                                </button>
                                <p class="text-sm text-gray-400">
                                    يُفضل الأبعاد المربعة (مثل 200x200) للحصول على أفضل النتائج
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Privacy & Market Value Tab -->
                <div id="privacyTab" class="editor-tab-content" style="display: none;">
                    <h3 class="text-xl font-bold mb-4">الخصوصية والقيمة السوقية</h3>

                    <!-- Market Value Section -->
                    <div class="card mb-6">
                        <h4 class="font-bold mb-4">
                            <i class="fas fa-chart-line ml-2 text-green-400"></i>
                            القيمة السوقية للاعب
                        </h4>

                        <div class="grid-2 gap-6 mb-6">
                            <!-- Current Market Value -->
                            <div class="text-center p-6 bg-gradient-to-br from-green-600/20 to-blue-600/20 rounded-xl border border-green-500/30">
                                <div class="text-4xl font-bold text-green-400 mb-2" id="currentMarketValue">0 ر.س</div>
                                <div class="text-sm text-gray-400 mb-2">القيمة السوقية الحالية</div>
                                <div class="flex items-center justify-center gap-2" id="marketValueTrend">
                                    <i class="fas fa-minus text-gray-400"></i>
                                    <span class="text-sm text-gray-400">ثابت</span>
                                </div>
                            </div>

                            <!-- Market Value Components -->
                            <div class="space-y-3">
                                <h5 class="font-bold text-gray-300 mb-3">مكونات القيمة السوقية:</h5>

                                <div class="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
                                    <span class="text-sm">نسبة الحضور (25%)</span>
                                    <span class="font-bold text-blue-400" id="attendanceComponent">0 ر.س</span>
                                </div>

                                <div class="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
                                    <span class="text-sm">التقييم العام (30%)</span>
                                    <span class="font-bold text-purple-400" id="ratingComponent">0 ر.س</span>
                                </div>

                                <div class="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
                                    <span class="text-sm">الأداء والمشاركات (20%)</span>
                                    <span class="font-bold text-orange-400" id="performanceComponent">0 ر.س</span>
                                </div>

                                <div class="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
                                    <span class="text-sm">التفاعل الاجتماعي (10%)</span>
                                    <span class="font-bold text-pink-400" id="socialComponent">0 ر.س</span>
                                </div>

                                <div class="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
                                    <span class="text-sm">روح الفريق (15%)</span>
                                    <span class="font-bold text-yellow-400" id="teamSpiritComponent">0 ر.س</span>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <button class="btn btn-primary" onclick="calculateMarketValue()">
                                <i class="fas fa-calculator ml-2"></i>
                                إعادة حساب القيمة السوقية
                            </button>
                        </div>
                    </div>

                    <!-- QR Code Section -->
                    <div class="card mb-6">
                        <h4 class="font-bold mb-4">
                            <i class="fas fa-qrcode ml-2 text-blue-400"></i>
                            باركود اللاعب
                        </h4>

                        <div class="grid-2 gap-6">
                            <div class="text-center">
                                <div id="qrCodeContainer" class="w-48 h-48 mx-auto mb-4 bg-white rounded-lg flex items-center justify-center">
                                    <div id="qrCodeDisplay" class="text-gray-500">
                                        <i class="fas fa-qrcode text-6xl mb-2"></i>
                                        <p>سيتم إنشاء الباركود تلقائياً</p>
                                    </div>
                                </div>
                                <button class="btn btn-secondary" onclick="generateQRCode()">
                                    <i class="fas fa-sync ml-2"></i>
                                    إنشاء باركود جديد
                                </button>
                            </div>

                            <div>
                                <h5 class="font-bold mb-3">معلومات الباركود:</h5>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">رقم اللاعب:</span>
                                        <span class="font-bold">${currentPlayer.academicNumber}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">الرابط العام:</span>
                                        <span class="font-bold text-blue-400">academy7c.com/player/${currentPlayer.academicNumber}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">آخر تحديث:</span>
                                        <span class="font-bold" id="qrLastUpdated">لم يتم الإنشاء بعد</span>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <button class="btn btn-primary w-full mb-2" onclick="downloadQRCode()">
                                        <i class="fas fa-download ml-2"></i>
                                        تحميل الباركود
                                    </button>
                                    <button class="btn btn-secondary w-full" onclick="printQRCode()">
                                        <i class="fas fa-print ml-2"></i>
                                        طباعة الباركود
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Privacy Settings Section -->
                    <div class="card">
                        <h4 class="font-bold mb-4">
                            <i class="fas fa-user-shield ml-2 text-purple-400"></i>
                            إعدادات الخصوصية للصفحة العامة
                        </h4>

                        <div class="grid-2 gap-6">
                            <!-- General Privacy Settings -->
                            <div>
                                <h5 class="font-bold mb-3 text-gray-300">الإعدادات العامة:</h5>
                                <div class="space-y-3">
                                    <label class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg cursor-pointer">
                                        <span>عرض النبذة الشخصية</span>
                                        <input type="checkbox" id="showBio" class="toggle-switch" checked>
                                    </label>

                                    <label class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg cursor-pointer">
                                        <span>عرض القيمة السوقية</span>
                                        <input type="checkbox" id="showMarketValue" class="toggle-switch" checked>
                                    </label>

                                    <label class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg cursor-pointer">
                                        <span>عرض الباركود الشخصي</span>
                                        <input type="checkbox" id="showQRCode" class="toggle-switch" checked>
                                    </label>

                                    <label class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg cursor-pointer">
                                        <span>عرض المعلومات الشخصية</span>
                                        <input type="checkbox" id="showPersonalInfo" class="toggle-switch" checked>
                                    </label>

                                    <label class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg cursor-pointer">
                                        <span>عرض الإحصائيات والتقييمات</span>
                                        <input type="checkbox" id="showStats" class="toggle-switch" checked>
                                    </label>

                                    <label class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg cursor-pointer">
                                        <span>عرض معرض الصور</span>
                                        <input type="checkbox" id="showGallery" class="toggle-switch" checked>
                                    </label>
                                </div>
                            </div>

                            <!-- Social Media Privacy Settings -->
                            <div>
                                <h5 class="font-bold mb-3 text-gray-300">وسائل التواصل الاجتماعي:</h5>
                                <div class="space-y-3">
                                    <label class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg cursor-pointer">
                                        <span>عرض جميع وسائل التواصل</span>
                                        <input type="checkbox" id="showSocialMedia" class="toggle-switch" checked onchange="toggleAllSocialMedia(this)">
                                    </label>

                                    <div id="socialMediaIndividual" class="space-y-2 ml-4">
                                        <label class="flex items-center justify-between p-2 bg-gray-700/50 rounded-lg cursor-pointer">
                                            <span class="flex items-center gap-2">
                                                <i class="fab fa-instagram text-pink-500"></i>
                                                Instagram
                                            </span>
                                            <input type="checkbox" id="showInstagram" class="toggle-switch social-toggle" checked>
                                        </label>

                                        <label class="flex items-center justify-between p-2 bg-gray-700/50 rounded-lg cursor-pointer">
                                            <span class="flex items-center gap-2">
                                                <i class="fab fa-twitter text-blue-400"></i>
                                                X (Twitter)
                                            </span>
                                            <input type="checkbox" id="showTwitter" class="toggle-switch social-toggle" checked>
                                        </label>

                                        <label class="flex items-center justify-between p-2 bg-gray-700/50 rounded-lg cursor-pointer">
                                            <span class="flex items-center gap-2">
                                                <i class="fab fa-tiktok text-black"></i>
                                                TikTok
                                            </span>
                                            <input type="checkbox" id="showTiktok" class="toggle-switch social-toggle" checked>
                                        </label>

                                        <label class="flex items-center justify-between p-2 bg-gray-700/50 rounded-lg cursor-pointer">
                                            <span class="flex items-center gap-2">
                                                <i class="fab fa-snapchat text-yellow-400"></i>
                                                Snapchat
                                            </span>
                                            <input type="checkbox" id="showSnapchat" class="toggle-switch social-toggle" checked>
                                        </label>

                                        <label class="flex items-center justify-between p-2 bg-gray-700/50 rounded-lg cursor-pointer">
                                            <span class="flex items-center gap-2">
                                                <i class="fab fa-youtube text-red-500"></i>
                                                YouTube
                                            </span>
                                            <input type="checkbox" id="showYoutube" class="toggle-switch social-toggle" checked>
                                        </label>

                                        <label class="flex items-center justify-between p-2 bg-gray-700/50 rounded-lg cursor-pointer">
                                            <span class="flex items-center gap-2">
                                                <i class="fab fa-facebook text-blue-600"></i>
                                                Facebook
                                            </span>
                                            <input type="checkbox" id="showFacebook" class="toggle-switch social-toggle" checked>
                                        </label>

                                        <label class="flex items-center justify-between p-2 bg-gray-700/50 rounded-lg cursor-pointer">
                                            <span class="flex items-center gap-2">
                                                <i class="fab fa-whatsapp text-green-500"></i>
                                                WhatsApp
                                            </span>
                                            <input type="checkbox" id="showWhatsapp" class="toggle-switch social-toggle" checked>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6 p-4 bg-blue-600/20 border border-blue-600/30 rounded-lg">
                            <h5 class="font-bold mb-2">
                                <i class="fas fa-info-circle ml-2"></i>
                                ملاحظة مهمة
                            </h5>
                            <p class="text-sm text-gray-300">
                                إعدادات الخصوصية تؤثر فقط على الصفحة العامة للاعب. المعلومات ستبقى مرئية في النظام الداخلي للأكاديمية.
                                يمكنك تغيير هذه الإعدادات في أي وقت.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Preview Tab -->
                <div id="previewTab" class="editor-tab-content" style="display: none;">
                    <h3 class="text-xl font-bold mb-4">معاينة مباشرة للتغييرات</h3>
                    <div id="livePreview" class="border border-gray-600 rounded-lg p-6 bg-gray-800/50">
                        <!-- Live preview content will be generated here -->
                    </div>
                    <div class="mt-4 text-center">
                        <button class="btn btn-secondary" onclick="refreshPreview()">
                            <i class="fas fa-sync ml-2"></i>
                            تحديث المعاينة
                        </button>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeProfileEditor()">إلغاء</button>
                <button class="btn btn-warning" onclick="resetAllChanges()">إعادة تعيين</button>
                <button class="btn btn-primary" onclick="saveAllProfileChanges()">
                    <i class="fas fa-save ml-2"></i>
                    حفظ جميع التغييرات
                </button>
            </div>
        </div>
    </div>

    <script>
        // Global Variables
        let isEditMode = false;
        let currentTemplate = 'default';
        let currentTheme = 'default';
        let editedPhotoData = null;
        let currentPlayer = {
            id: 'player_001',
            firstName: 'محمد',
            fatherName: 'أحمد',
            familyName: 'الزهراني',
            nationalId: '1234567890',
            birthDate: '2008-05-15',
            age: 16,
            personalPhone: '0501234567',
            academicNumber: '7C-2024-001',
            category: 'ناشئين',
            preferredFoot: 'right',
            height: 165,
            weight: 55,
            guardianName: 'أحمد محمد الزهراني',
            guardianPhone: '0509876543',
            relationship: 'father',
            currentSubscription: {
                plan: 'الخطة المتقدمة',
                startDate: '2024-01-01',
                endDate: '2024-12-31',
                remaining: 245,
                price: 499
            },
            loyaltyPoints: 1250,
            rating: 85,
            position: 'MID',
            avatar: 'https://via.placeholder.com/120x120/8B4513/FFFFFF?text=Player',
            // Social Media Links
            socialMedia: {
                instagram: '',
                twitter: '',
                tiktok: '',
                snapchat: '',
                youtube: '',
                facebook: '',
                whatsapp: ''
            },
            // Additional Profile Data
            bio: 'لاعب موهوب في أكاديمية 7C للتدريب الرياضي',
            achievements: [
                'أفضل لاعب في الشهر - يناير 2024',
                'هداف البطولة المحلية',
                'جائزة روح الفريق'
            ],
            stats: {
                matchesPlayed: 25,
                goals: 12,
                assists: 8,
                yellowCards: 2,
                redCards: 0,
                attendanceRate: 95
            },
            // Market Value Components
            marketValue: {
                calculated: 0, // Will be calculated automatically
                lastUpdated: new Date().toISOString(),
                trend: 'stable', // 'up', 'down', 'stable'
                components: {
                    attendance: 0,      // 25% weight
                    rating: 0,          // 30% weight
                    performance: 0,     // 20% weight
                    socialEngagement: 0, // 10% weight
                    teamSpirit: 0       // 15% weight
                }
            },
            // Privacy Settings
            privacySettings: {
                showSocialMedia: true,
                showBio: true,
                showMarketValue: true,
                showQRCode: true,
                showPersonalInfo: true,
                showStats: true,
                showGallery: true,
                socialMediaVisibility: {
                    instagram: true,
                    twitter: true,
                    tiktok: true,
                    snapchat: true,
                    youtube: true,
                    facebook: true,
                    whatsapp: true
                }
            },
            // QR Code Data
            qrCode: {
                generated: false,
                data: '',
                lastGenerated: null
            }
        };

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadPlayerDataFromStorage();
            loadPlayerData();
            showTab('overview');
            generateQRCode();
            syncWithAttendanceSystem();
        });

        // Load player data from localStorage if available
        function loadPlayerDataFromStorage() {
            const storedData = localStorage.getItem('current_player_data');
            if (storedData) {
                try {
                    const playerData = JSON.parse(storedData);
                    // Merge with current player data
                    currentPlayer = { ...currentPlayer, ...playerData };
                    console.log('✅ تم تحميل بيانات اللاعب من النظام الرئيسي');
                } catch (error) {
                    console.error('❌ خطأ في قراءة بيانات اللاعب:', error);
                }
            }
        }

        // Load player data
        function loadPlayerData() {
            // Enhanced name and academic number display
            const playerNameElement = document.getElementById('playerName');
            const academicNumberElement = document.getElementById('academicNumber');

            if (playerNameElement) {
                playerNameElement.innerHTML = `
                    <span class="player-full-name">${currentPlayer.firstName} ${currentPlayer.fatherName} ${currentPlayer.familyName}</span>
                    <span class="academic-number-badge">${currentPlayer.academicNumber}</span>
                `;
            }

            if (academicNumberElement) {
                academicNumberElement.textContent = currentPlayer.academicNumber;
            }
            document.getElementById('playerAge').textContent = `${currentPlayer.age} سنة`;
            document.getElementById('playerCategory').textContent = currentPlayer.category;
            document.getElementById('playerPhone').textContent = currentPlayer.personalPhone;
            document.getElementById('playerRating').textContent = currentPlayer.rating;
            document.getElementById('playerPosition').textContent = currentPlayer.position;
            document.getElementById('fifaPlayerName').textContent = `${currentPlayer.firstName} ${currentPlayer.familyName}`;

            // Update avatar if available
            if (currentPlayer.avatar) {
                document.getElementById('playerAvatar').src = currentPlayer.avatar;
                document.getElementById('fifaPlayerImage').src = currentPlayer.avatar;
            }

            // Update social media display in overview if exists
            updateSocialMediaDisplay();

            // Calculate market value if not already calculated
            if (!currentPlayer.marketValue.calculated) {
                calculateMarketValue();
            }

            // Generate QR code if not already generated
            if (!currentPlayer.qrCode.generated) {
                setTimeout(() => {
                    generateQRCode();
                }, 1000);
            } else {
                // Update existing QR code in overview
                updateOverviewQRCode();
            }
        }

        // Update QR Code in overview section
        function updateOverviewQRCode() {
            const overviewQRContainer = document.getElementById('overviewQRCode');
            if (overviewQRContainer && currentPlayer.qrCode.generated && typeof QRCode !== 'undefined') {
                QRCode.toCanvas(document.createElement('canvas'), currentPlayer.qrCode.data, {
                    width: 128,
                    height: 128,
                    color: {
                        dark: '#1a1a1a',
                        light: '#ffffff'
                    }
                }, function (error, canvas) {
                    if (!error) {
                        overviewQRContainer.innerHTML = '';
                        overviewQRContainer.appendChild(canvas);
                    }
                });
            }
        }

        // Update social media display in the main interface
        function updateSocialMediaDisplay() {
            // Reload the overview tab content to show updated social media
            const activeTab = document.querySelector('.nav-tab.active');
            if (activeTab && activeTab.textContent.includes('نظرة عامة')) {
                loadTabContent('overview');
            }
        }

        // Show tab function
        function showTab(tabName) {
            // Update active tab
            document.querySelectorAll('.nav-tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');

            // Load tab content
            loadTabContent(tabName);
        }

        // Load tab content with performance optimization
        function loadTabContent(tabName) {
            const tabContents = document.getElementById('tabContents');

            // Show loading indicator
            tabContents.innerHTML = `
                <div class="card text-center">
                    <div class="loading-spinner"></div>
                    <p class="mt-4">جاري التحميل...</p>
                </div>
            `;

            // Load content asynchronously for better performance
            setTimeout(() => {
                switch(tabName) {
                    case 'overview':
                        tabContents.innerHTML = getOverviewContent();
                        break;
                    case 'subscription':
                        tabContents.innerHTML = getSubscriptionContent();
                        break;
                    case 'qr-card':
                        tabContents.innerHTML = getQRCardContent();
                        setTimeout(generateQRCode, 100);
                        break;
                    case 'schedule':
                        tabContents.innerHTML = getScheduleContent();
                        break;
                    case 'attendance':
                        tabContents.innerHTML = getAttendanceContent();
                        setTimeout(initAttendanceChart, 100);
                        break;
                    case 'messages':
                        tabContents.innerHTML = getMessagesContent();
                        break;
                    case 'loyalty':
                        tabContents.innerHTML = getLoyaltyContent();
                        break;
                    case 'evaluation':
                        tabContents.innerHTML = getEvaluationContent();
                        setTimeout(initEvaluationChart, 100);
                        break;
                    case 'achievements':
                        tabContents.innerHTML = getAchievementsContent();
                        break;
                    case 'participation':
                        tabContents.innerHTML = getParticipationContent();
                        break;
                    case 'media':
                        tabContents.innerHTML = getMediaContent();
                        break;
                    case 'store':
                        tabContents.innerHTML = getStoreContent();
                        break;
                    case 'players':
                        tabContents.innerHTML = getPlayersContent();
                        setTimeout(initializePlayersTab, 100);
                        break;
                    default:
                        tabContents.innerHTML = `<div class="card"><h2>قريباً...</h2><p>هذا القسم قيد التطوير</p></div>`;
                }
            }, 50); // Small delay for smooth transition
        }

        // ==================== Content Generators ====================

        // Overview Content
        function getOverviewContent() {
            return `
                <div class="grid-2 fade-in">
                    <!-- Personal Information -->
                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-user ml-2 text-blue-400"></i>
                            المعلومات الشخصية
                        </h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-400">الاسم الثلاثي:</span>
                                <span>${currentPlayer.firstName} ${currentPlayer.fatherName} ${currentPlayer.familyName}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">رقم الهوية:</span>
                                <span>${currentPlayer.nationalId}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">تاريخ الميلاد:</span>
                                <span>${formatDate(currentPlayer.birthDate)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">العمر:</span>
                                <span>${currentPlayer.age} سنة</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">الفئة العمرية:</span>
                                <span class="badge bg-blue-600">${currentPlayer.category}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Guardian Information -->
                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-users ml-2 text-green-400"></i>
                            معلومات ولي الأمر
                        </h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-400">اسم ولي الأمر:</span>
                                <span>${currentPlayer.guardianName}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">رقم الجوال:</span>
                                <span>${currentPlayer.guardianPhone}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">صلة القرابة:</span>
                                <span>${getRelationshipText(currentPlayer.relationship)}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Physical Information -->
                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-dumbbell ml-2 text-orange-400"></i>
                            المعلومات الجسمانية
                        </h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-400">القدم المفضلة:</span>
                                <span>${getFootText(currentPlayer.preferredFoot)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">الطول:</span>
                                <span>${currentPlayer.height} سم</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">الوزن:</span>
                                <span>${currentPlayer.weight} كغ</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">مؤشر كتلة الجسم:</span>
                                <span>${calculateBMI(currentPlayer.height, currentPlayer.weight)}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-chart-bar ml-2 text-purple-400"></i>
                            إحصائيات سريعة
                        </h3>
                        <div class="grid-4">
                            <div class="stat-card">
                                <div class="stat-value">${currentPlayer.loyaltyPoints}</div>
                                <div class="stat-label">نقاط الولاء</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${currentPlayer.stats.attendanceRate}%</div>
                                <div class="stat-label">نسبة الحضور</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${currentPlayer.rating}</div>
                                <div class="stat-label">التقييم العام</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${currentPlayer.achievements.length}</div>
                                <div class="stat-label">الإنجازات</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value text-green-400">${currentPlayer.marketValue.calculated ? currentPlayer.marketValue.calculated.toLocaleString() : '0'} ر.س</div>
                                <div class="stat-label">القيمة السوقية</div>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media & Bio -->
                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-user-circle ml-2 text-blue-400"></i>
                            الملف الشخصي
                        </h3>

                        ${currentPlayer.bio ? `
                        <div class="mb-4">
                            <h4 class="font-bold mb-2 text-gray-300">نبذة شخصية:</h4>
                            <p class="text-gray-400 leading-relaxed">${currentPlayer.bio}</p>
                        </div>
                        ` : ''}

                        ${getSocialMediaSection()}
                    </div>

                    <!-- Market Value & QR Code -->
                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-chart-line ml-2 text-green-400"></i>
                            القيمة السوقية والباركود
                        </h3>

                        <div class="grid-2 gap-6">
                            <!-- Market Value -->
                            <div class="text-center p-4 bg-gradient-to-br from-green-600/20 to-blue-600/20 rounded-xl border border-green-500/30">
                                <div class="text-3xl font-bold text-green-400 mb-2">
                                    ${currentPlayer.marketValue.calculated ? currentPlayer.marketValue.calculated.toLocaleString() : '0'} ر.س
                                </div>
                                <div class="text-sm text-gray-400 mb-2">القيمة السوقية</div>
                                <div class="flex items-center justify-center gap-2">
                                    ${getMarketValueTrendIcon()}
                                </div>
                                <div class="text-xs text-gray-500 mt-2">
                                    آخر تحديث: ${currentPlayer.marketValue.lastUpdated ? new Date(currentPlayer.marketValue.lastUpdated).toLocaleDateString('ar-SA') : 'لم يتم الحساب'}
                                </div>
                            </div>

                            <!-- QR Code -->
                            <div class="text-center">
                                <div class="w-32 h-32 mx-auto mb-3 bg-white rounded-lg flex items-center justify-center" id="overviewQRCode">
                                    ${currentPlayer.qrCode.generated ? '<div class="text-gray-500">باركود اللاعب</div>' : '<i class="fas fa-qrcode text-4xl text-gray-400"></i>'}
                                </div>
                                <div class="text-sm text-gray-400">باركود اللاعب</div>
                                <button class="btn btn-sm btn-secondary mt-2" onclick="openProfileEditor(); showEditorTab('privacy')">
                                    <i class="fas fa-qrcode ml-1"></i>
                                    إدارة الباركود
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Subscription Content
        function getSubscriptionContent() {
            const subscription = currentPlayer.currentSubscription;
            const progressPercentage = ((365 - subscription.remaining) / 365) * 100;

            return `
                <div class="grid-2 fade-in">
                    <!-- Current Subscription -->
                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-credit-card ml-2 text-green-400"></i>
                            الاشتراك الحالي
                        </h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-400">خطة الاشتراك:</span>
                                <span class="badge bg-green-600">${subscription.plan}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">تاريخ البداية:</span>
                                <span>${formatDate(subscription.startDate)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">تاريخ الانتهاء:</span>
                                <span>${formatDate(subscription.endDate)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">الأيام المتبقية:</span>
                                <span class="text-orange-400 font-bold">${subscription.remaining} يوم</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">قيمة الاشتراك:</span>
                                <span class="text-green-400 font-bold">${subscription.price} ر.س</span>
                            </div>

                            <!-- Progress Bar -->
                            <div class="mt-4">
                                <div class="flex justify-between text-sm mb-2">
                                    <span>تقدم الاشتراك</span>
                                    <span>${Math.round(progressPercentage)}%</span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full transition-all duration-500" style="width: ${progressPercentage}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Available Plans -->
                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-list ml-2 text-blue-400"></i>
                            خطط التجديد المتاحة
                        </h3>
                        <div class="space-y-3">
                            <div class="p-3 bg-blue-600/20 border border-blue-600/30 rounded-lg">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="font-bold">الخطة الأساسية</span>
                                    <span class="text-blue-400">299 ر.س</span>
                                </div>
                                <ul class="text-sm text-gray-300 space-y-1">
                                    <li>• تدريب 3 مرات أسبوعياً</li>
                                    <li>• متابعة أساسية</li>
                                    <li>• تقارير شهرية</li>
                                </ul>
                                <button class="btn btn-primary w-full mt-3" onclick="requestRenewal('basic')">
                                    طلب تجديد
                                </button>
                            </div>

                            <div class="p-3 bg-purple-600/20 border border-purple-600/30 rounded-lg">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="font-bold">الخطة المتقدمة</span>
                                    <span class="text-purple-400">499 ر.س</span>
                                </div>
                                <ul class="text-sm text-gray-300 space-y-1">
                                    <li>• تدريب 5 مرات أسبوعياً</li>
                                    <li>• متابعة شخصية</li>
                                    <li>• تحليل AI للأداء</li>
                                </ul>
                                <button class="btn btn-primary w-full mt-3" onclick="requestRenewal('premium')">
                                    طلب تجديد
                                </button>
                            </div>

                            <div class="p-3 bg-yellow-600/20 border border-yellow-600/30 rounded-lg">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="font-bold">خطة VIP</span>
                                    <span class="text-yellow-400">899 ر.س</span>
                                </div>
                                <ul class="text-sm text-gray-300 space-y-1">
                                    <li>• تدريب يومي</li>
                                    <li>• مدرب شخصي</li>
                                    <li>• دعم فني VIP 24/7</li>
                                </ul>
                                <button class="btn btn-primary w-full mt-3" onclick="requestRenewal('vip')">
                                    طلب تجديد
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // QR Card Content
        function getQRCardContent() {
            return `
                <div class="grid-2 fade-in">
                    <!-- Digital Player Card -->
                    <div class="card text-center">
                        <h3 class="text-xl font-bold mb-4 flex items-center justify-center">
                            <i class="fas fa-id-card ml-2 text-blue-400"></i>
                            البطاقة الرقمية
                        </h3>
                        <div class="qr-card max-w-sm mx-auto">
                            <div class="flex items-center justify-between mb-4">
                                <img src="https://via.placeholder.com/60x60/8B4513/FFFFFF?text=7C" alt="شعار الأكاديمية" class="w-12 h-12 rounded-lg">
                                <div class="text-right">
                                    <h4 class="font-bold text-lg">${currentPlayer.firstName} ${currentPlayer.familyName}</h4>
                                    <p class="text-sm text-gray-600">${formatDate(currentPlayer.birthDate)}</p>
                                </div>
                            </div>

                            <div class="flex items-center gap-4 mb-4">
                                <img src="${currentPlayer.avatar}" alt="صورة اللاعب" class="w-16 h-16 rounded-full object-cover">
                                <div class="text-left flex-1">
                                    <p class="text-sm"><strong>الرقم الأكاديمي:</strong></p>
                                    <p class="text-lg font-bold text-blue-600">${currentPlayer.academicNumber}</p>
                                    <p class="text-sm text-gray-600">${currentPlayer.category}</p>
                                </div>
                            </div>

                            <div id="qrcode" class="flex justify-center mb-4"></div>

                            <div class="text-xs text-gray-500 border-t pt-2">
                                <p>أكاديمية 7C للتدريب الرياضي</p>
                                <p>هذه البطاقة صالحة للاستخدام في جميع مرافق الأكاديمية</p>
                            </div>
                        </div>

                        <div class="mt-4 space-y-2">
                            <button class="btn btn-primary w-full" onclick="downloadCard()">
                                <i class="fas fa-download ml-2"></i>تحميل البطاقة
                            </button>
                            <button class="btn btn-secondary w-full" onclick="shareCard()">
                                <i class="fas fa-share ml-2"></i>مشاركة البطاقة
                            </button>
                        </div>
                    </div>

                    <!-- QR Code Usage -->
                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-qrcode ml-2 text-green-400"></i>
                            استخدامات الباركود
                        </h3>
                        <div class="space-y-4">
                            <div class="flex items-center gap-3 p-3 bg-green-600/20 border border-green-600/30 rounded-lg">
                                <i class="fas fa-check-circle text-green-400 text-xl"></i>
                                <div>
                                    <h4 class="font-bold">تسجيل الحضور</h4>
                                    <p class="text-sm text-gray-300">امسح الباركود عند دخول الأكاديمية</p>
                                </div>
                            </div>

                            <div class="flex items-center gap-3 p-3 bg-blue-600/20 border border-blue-600/30 rounded-lg">
                                <i class="fas fa-star text-blue-400 text-xl"></i>
                                <div>
                                    <h4 class="font-bold">تسجيل التقييم</h4>
                                    <p class="text-sm text-gray-300">المدربون يمكنهم تقييم الأداء مباشرة</p>
                                </div>
                            </div>

                            <div class="flex items-center gap-3 p-3 bg-purple-600/20 border border-purple-600/30 rounded-lg">
                                <i class="fas fa-coins text-purple-400 text-xl"></i>
                                <div>
                                    <h4 class="font-bold">نقاط الولاء</h4>
                                    <p class="text-sm text-gray-300">اكسب نقاط تلقائياً مع كل حضور</p>
                                </div>
                            </div>

                            <div class="flex items-center gap-3 p-3 bg-orange-600/20 border border-orange-600/30 rounded-lg">
                                <i class="fas fa-shopping-cart text-orange-400 text-xl"></i>
                                <div>
                                    <h4 class="font-bold">المشتريات</h4>
                                    <p class="text-sm text-gray-300">استخدم النقاط للشراء من المتجر</p>
                                </div>
                            </div>
                        </div>

                        <!-- QR Code Stats -->
                        <div class="mt-6 p-4 bg-gray-800/50 rounded-lg">
                            <h4 class="font-bold mb-3">إحصائيات الاستخدام</h4>
                            <div class="grid grid-cols-2 gap-4 text-center">
                                <div>
                                    <div class="text-2xl font-bold text-green-400">156</div>
                                    <div class="text-sm text-gray-400">مرة استخدام</div>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-blue-400">98%</div>
                                    <div class="text-sm text-gray-400">معدل النجاح</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // ==================== Helper Functions ====================

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        function getRelationshipText(relationship) {
            const relationships = {
                'father': 'الأب',
                'mother': 'الأم',
                'brother': 'الأخ',
                'sister': 'الأخت',
                'uncle': 'العم',
                'aunt': 'العمة',
                'other': 'أخرى'
            };
            return relationships[relationship] || relationship;
        }

        function getFootText(foot) {
            const feet = {
                'right': 'اليمين',
                'left': 'اليسار',
                'both': 'كلاهما'
            };
            return feet[foot] || foot;
        }

        function calculateBMI(height, weight) {
            const heightInMeters = height / 100;
            const bmi = weight / (heightInMeters * heightInMeters);
            return bmi.toFixed(1);
        }

        // Get social media section for overview
        function getSocialMediaSection() {
            if (!currentPlayer.socialMedia) return '';

            const socialMedia = currentPlayer.socialMedia;
            const icons = {
                instagram: { icon: 'fab fa-instagram', color: 'text-pink-500', name: 'Instagram' },
                twitter: { icon: 'fab fa-twitter', color: 'text-blue-400', name: 'X (Twitter)' },
                tiktok: { icon: 'fab fa-tiktok', color: 'text-black', name: 'TikTok' },
                snapchat: { icon: 'fab fa-snapchat', color: 'text-yellow-400', name: 'Snapchat' },
                youtube: { icon: 'fab fa-youtube', color: 'text-red-500', name: 'YouTube' },
                facebook: { icon: 'fab fa-facebook', color: 'text-blue-600', name: 'Facebook' },
                whatsapp: { icon: 'fab fa-whatsapp', color: 'text-green-500', name: 'WhatsApp' }
            };

            let socialHtml = '';
            let hasAnySocial = false;

            for (const [platform, url] of Object.entries(socialMedia)) {
                if (url && url.trim()) {
                    hasAnySocial = true;
                    const iconData = icons[platform];
                    const href = platform === 'whatsapp' ? `https://wa.me/${url.replace(/[^0-9]/g, '')}` : url;
                    socialHtml += `
                        <a href="${href}" target="_blank"
                           class="social-icon ${iconData.color} text-xl hover:scale-110 transition-transform mr-3"
                           title="${iconData.name}">
                            <i class="${iconData.icon}"></i>
                        </a>
                    `;
                }
            }

            if (hasAnySocial) {
                return `
                    <div class="social-media-overview">
                        <h4 class="font-bold mb-3 text-gray-300">وسائل التواصل الاجتماعي:</h4>
                        <div class="flex items-center flex-wrap">
                            ${socialHtml}
                        </div>
                    </div>
                `;
            }
            return '';
        }

        // Get market value trend icon
        function getMarketValueTrendIcon() {
            const trend = currentPlayer.marketValue.trend || 'stable';

            switch (trend) {
                case 'up':
                    return '<i class="fas fa-arrow-up text-green-400"></i><span class="text-sm text-green-400 mr-1">صاعد</span>';
                case 'down':
                    return '<i class="fas fa-arrow-down text-red-400"></i><span class="text-sm text-red-400 mr-1">نازل</span>';
                default:
                    return '<i class="fas fa-minus text-gray-400"></i><span class="text-sm text-gray-400 mr-1">ثابت</span>';
            }
        }

        // Generate QR Code
        function generateQRCode() {
            const qrData = {
                playerId: currentPlayer.id,
                academicNumber: currentPlayer.academicNumber,
                name: `${currentPlayer.firstName} ${currentPlayer.familyName}`,
                category: currentPlayer.category,
                timestamp: new Date().toISOString()
            };

            const qrContainer = document.getElementById('qrcode');
            if (qrContainer) {
                qrContainer.innerHTML = '';
                QRCode.toCanvas(qrContainer, JSON.stringify(qrData), {
                    width: 150,
                    height: 150,
                    colorDark: '#1a1a1a',
                    colorLight: '#ffffff'
                }, function (error) {
                    if (error) console.error('QR Code generation failed:', error);
                    else console.log('QR Code generated successfully');
                });
            }
        }

        // Action Functions
        function editProfile() {
            Swal.fire({
                title: 'تحرير الملف الشخصي',
                text: 'سيتم إضافة هذه الميزة قريباً',
                icon: 'info',
                confirmButtonText: 'موافق'
            });
        }

        function requestRenewal(planType) {
            Swal.fire({
                title: 'طلب تجديد الاشتراك',
                text: `هل تريد إرسال طلب تجديد للخطة المختارة؟`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، أرسل الطلب',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'تم إرسال الطلب!',
                        text: 'سيتم التواصل معك قريباً لتأكيد التجديد',
                        icon: 'success',
                        timer: 3000,
                        showConfirmButton: false
                    });
                }
            });
        }

        function downloadCard() {
            // Simulate card download
            Swal.fire({
                title: 'تحميل البطاقة',
                text: 'سيتم تحميل البطاقة الرقمية',
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });
        }

        function shareCard() {
            if (navigator.share) {
                navigator.share({
                    title: 'بطاقة اللاعب - أكاديمية 7C',
                    text: `بطاقة اللاعب ${currentPlayer.firstName} ${currentPlayer.familyName}`,
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                navigator.clipboard.writeText(window.location.href).then(() => {
                    Swal.fire({
                        title: 'تم النسخ!',
                        text: 'تم نسخ رابط البطاقة إلى الحافظة',
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });
                });
            }
        }

        // ==================== Additional Content Generators ====================

        // Schedule Content
        function getScheduleContent() {
            const schedule = [
                { day: 'الأحد', time: '16:00 - 18:00', activity: 'تدريب تقني', coach: 'المدرب أحمد' },
                { day: 'الثلاثاء', time: '16:00 - 18:00', activity: 'تدريب بدني', coach: 'المدرب محمد' },
                { day: 'الخميس', time: '16:00 - 18:00', activity: 'مباراة تطبيقية', coach: 'المدرب أحمد' },
                { day: 'السبت', time: '09:00 - 11:00', activity: 'تدريب خططي', coach: 'المدرب سعد' }
            ];

            return `
                <div class="fade-in">
                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-calendar-alt ml-2 text-blue-400"></i>
                            جدول التدريب الأسبوعي
                        </h3>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b border-gray-700">
                                        <th class="text-right p-3">اليوم</th>
                                        <th class="text-right p-3">الوقت</th>
                                        <th class="text-right p-3">النشاط</th>
                                        <th class="text-right p-3">المدرب</th>
                                        <th class="text-right p-3">الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${schedule.map(session => `
                                        <tr class="border-b border-gray-800 hover:bg-gray-800/50">
                                            <td class="p-3 font-bold">${session.day}</td>
                                            <td class="p-3">${session.time}</td>
                                            <td class="p-3">${session.activity}</td>
                                            <td class="p-3">${session.coach}</td>
                                            <td class="p-3">
                                                <span class="badge bg-green-600">مؤكد</span>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="stat-card">
                                <div class="stat-value">4</div>
                                <div class="stat-label">جلسات أسبوعية</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">8</div>
                                <div class="stat-label">ساعات تدريب</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">2</div>
                                <div class="stat-label">مدربين</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Attendance Content
        function getAttendanceContent() {
            return `
                <div class="grid-2 fade-in">
                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-chart-line ml-2 text-green-400"></i>
                            إحصائيات الحضور
                        </h3>
                        <canvas id="attendanceChart" width="400" height="200"></canvas>

                        <div class="mt-6 grid grid-cols-2 gap-4">
                            <div class="stat-card">
                                <div class="stat-value text-green-400">156</div>
                                <div class="stat-label">أيام حضور</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value text-red-400">8</div>
                                <div class="stat-label">أيام غياب</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value text-blue-400">95%</div>
                                <div class="stat-label">نسبة الحضور</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value text-purple-400">24</div>
                                <div class="stat-label">أيام متتالية</div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-history ml-2 text-blue-400"></i>
                            سجل الحضور الأخير
                        </h3>
                        <div class="space-y-3 max-h-96 overflow-y-auto">
                            ${generateAttendanceHistory()}
                        </div>
                    </div>
                </div>
            `;
        }

        // Messages Content
        function getMessagesContent() {
            return `
                <div class="grid-2 fade-in">
                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-inbox ml-2 text-blue-400"></i>
                            الرسائل الواردة
                        </h3>
                        <div class="space-y-3">
                            <div class="p-3 bg-blue-600/20 border border-blue-600/30 rounded-lg">
                                <div class="flex justify-between items-start mb-2">
                                    <span class="font-bold">المدرب أحمد</span>
                                    <span class="text-xs text-gray-400">منذ ساعتين</span>
                                </div>
                                <p class="text-sm">ممتاز! أداؤك في التدريب اليوم كان رائعاً. استمر على هذا المستوى.</p>
                                <div class="mt-2">
                                    <span class="badge bg-green-600 text-xs">جديد</span>
                                </div>
                            </div>

                            <div class="p-3 bg-gray-800/50 border border-gray-700 rounded-lg">
                                <div class="flex justify-between items-start mb-2">
                                    <span class="font-bold">الإدارة</span>
                                    <span class="text-xs text-gray-400">أمس</span>
                                </div>
                                <p class="text-sm">تذكير: مباراة ودية يوم السبت القادم الساعة 4 مساءً</p>
                            </div>
                        </div>

                        <button class="btn btn-primary w-full mt-4" onclick="openMessages()">
                            <i class="fas fa-comments ml-2"></i>
                            فتح نظام الرسائل
                        </button>
                    </div>

                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-paper-plane ml-2 text-green-400"></i>
                            إرسال رسالة جديدة
                        </h3>
                        <form onsubmit="sendMessage(event)">
                            <div class="mb-4">
                                <label class="block text-sm font-bold mb-2">إلى:</label>
                                <select class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3">
                                    <option>المدرب أحمد</option>
                                    <option>المدرب محمد</option>
                                    <option>الإدارة</option>
                                </select>
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-bold mb-2">الموضوع:</label>
                                <input type="text" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3" placeholder="موضوع الرسالة">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-bold mb-2">الرسالة:</label>
                                <textarea rows="4" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3" placeholder="اكتب رسالتك هنا..."></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary w-full">
                                <i class="fas fa-send ml-2"></i>
                                إرسال الرسالة
                            </button>
                        </form>
                    </div>
                </div>
            `;
        }

        // Loyalty Content
        function getLoyaltyContent() {
            const pointsValue = (currentPlayer.loyaltyPoints * 0.5).toFixed(2);

            return `
                <div class="grid-2 fade-in">
                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-star ml-2 text-yellow-400"></i>
                            نقاط الولاء الحالية
                        </h3>
                        <div class="text-center mb-6">
                            <div class="text-6xl font-bold text-yellow-400 mb-2">${currentPlayer.loyaltyPoints}</div>
                            <div class="text-lg text-gray-300">نقطة ولاء</div>
                            <div class="text-sm text-gray-400">قيمة: ${pointsValue} ر.س</div>
                        </div>

                        <div class="space-y-3">
                            <div class="flex justify-between p-3 bg-green-600/20 border border-green-600/30 rounded-lg">
                                <span>نقاط الحضور</span>
                                <span class="font-bold">+780</span>
                            </div>
                            <div class="flex justify-between p-3 bg-blue-600/20 border border-blue-600/30 rounded-lg">
                                <span>نقاط التقييم</span>
                                <span class="font-bold">+340</span>
                            </div>
                            <div class="flex justify-between p-3 bg-purple-600/20 border border-purple-600/30 rounded-lg">
                                <span>نقاط الدعوات</span>
                                <span class="font-bold">+100</span>
                            </div>
                            <div class="flex justify-between p-3 bg-orange-600/20 border border-orange-600/30 rounded-lg">
                                <span>نقاط المكافآت</span>
                                <span class="font-bold">+30</span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-gift ml-2 text-purple-400"></i>
                            استبدال النقاط
                        </h3>
                        <div class="space-y-3">
                            <div class="p-3 border border-gray-600 rounded-lg hover:border-purple-400 transition-all cursor-pointer">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <h4 class="font-bold">قميص الأكاديمية</h4>
                                        <p class="text-sm text-gray-400">قميص رسمي بشعار الأكاديمية</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-purple-400 font-bold">500 نقطة</div>
                                        <div class="text-xs text-gray-400">250 ر.س</div>
                                    </div>
                                </div>
                            </div>

                            <div class="p-3 border border-gray-600 rounded-lg hover:border-purple-400 transition-all cursor-pointer">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <h4 class="font-bold">حقيبة رياضية</h4>
                                        <p class="text-sm text-gray-400">حقيبة عالية الجودة</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-purple-400 font-bold">800 نقطة</div>
                                        <div class="text-xs text-gray-400">400 ر.س</div>
                                    </div>
                                </div>
                            </div>

                            <div class="p-3 border border-gray-600 rounded-lg hover:border-purple-400 transition-all cursor-pointer">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <h4 class="font-bold">جلسة تدريب إضافية</h4>
                                        <p class="text-sm text-gray-400">جلسة تدريب شخصية</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-purple-400 font-bold">1000 نقطة</div>
                                        <div class="text-xs text-gray-400">500 ر.س</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button class="btn btn-primary w-full mt-4" onclick="openStore()">
                            <i class="fas fa-shopping-cart ml-2"></i>
                            تصفح المتجر
                        </button>
                    </div>
                </div>
            `;
        }

        // ==================== Additional Helper Functions ====================

        function generateAttendanceHistory() {
            const history = [];
            const today = new Date();

            for (let i = 0; i < 10; i++) {
                const date = new Date(today);
                date.setDate(date.getDate() - i);
                const status = Math.random() > 0.1 ? 'present' : 'absent';

                history.push({
                    date: date.toLocaleDateString('ar-SA'),
                    status: status,
                    time: status === 'present' ? '16:05' : null
                });
            }

            return history.map(record => `
                <div class="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
                    <div>
                        <span class="font-bold">${record.date}</span>
                        ${record.time ? `<span class="text-sm text-gray-400 mr-2">الساعة ${record.time}</span>` : ''}
                    </div>
                    <span class="badge ${record.status === 'present' ? 'bg-green-600' : 'bg-red-600'}">
                        ${record.status === 'present' ? 'حاضر' : 'غائب'}
                    </span>
                </div>
            `).join('');
        }

        function initAttendanceChart() {
            const ctx = document.getElementById('attendanceChart');
            if (!ctx) return;

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'نسبة الحضور',
                        data: [92, 95, 88, 96, 94, 98],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff'
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                color: '#ffffff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: '#ffffff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    }
                }
            });
        }

        function initEvaluationChart() {
            const ctx = document.getElementById('evaluationChart');
            if (!ctx) return;

            new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: ['التقنية', 'البدنية', 'التكتيك', 'الذهنية', 'الجماعية'],
                    datasets: [{
                        label: 'التقييم الحالي',
                        data: [85, 78, 82, 88, 90],
                        borderColor: '#8B4513',
                        backgroundColor: 'rgba(139, 69, 19, 0.2)',
                        pointBackgroundColor: '#8B4513'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff'
                            }
                        }
                    },
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                color: '#ffffff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            pointLabels: {
                                color: '#ffffff'
                            }
                        }
                    }
                }
            });
        }

        // Action Functions
        function openMessages() {
            window.open('messages.html', '_blank');
        }

        function sendMessage(event) {
            event.preventDefault();
            Swal.fire({
                title: 'تم إرسال الرسالة!',
                text: 'سيتم الرد عليك قريباً',
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });
            event.target.reset();
        }

        function openStore() {
            Swal.fire({
                title: 'المتجر',
                text: 'سيتم إضافة المتجر قريباً',
                icon: 'info',
                confirmButtonText: 'موافق'
            });
        }

        // Additional Content Generators
        function getEvaluationContent() {
            return `
                <div class="grid-2 fade-in">
                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-chart-radar ml-2 text-blue-400"></i>
                            تقييم الأداء
                        </h3>
                        <canvas id="evaluationChart" width="400" height="300"></canvas>
                    </div>

                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-trophy ml-2 text-yellow-400"></i>
                            التقييم العام
                        </h3>
                        <div class="text-center mb-6">
                            <div class="text-6xl font-bold text-yellow-400 mb-2">${currentPlayer.rating}</div>
                            <div class="text-lg text-gray-300">من 100</div>
                        </div>

                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span>المهارات التقنية</span>
                                <div class="flex items-center gap-2">
                                    <div class="w-24 bg-gray-700 rounded-full h-2">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: 85%"></div>
                                    </div>
                                    <span class="text-sm font-bold">85%</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <span>اللياقة البدنية</span>
                                <div class="flex items-center gap-2">
                                    <div class="w-24 bg-gray-700 rounded-full h-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 78%"></div>
                                    </div>
                                    <span class="text-sm font-bold">78%</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <span>الفهم التكتيكي</span>
                                <div class="flex items-center gap-2">
                                    <div class="w-24 bg-gray-700 rounded-full h-2">
                                        <div class="bg-purple-500 h-2 rounded-full" style="width: 82%"></div>
                                    </div>
                                    <span class="text-sm font-bold">82%</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <span>القوة الذهنية</span>
                                <div class="flex items-center gap-2">
                                    <div class="w-24 bg-gray-700 rounded-full h-2">
                                        <div class="bg-orange-500 h-2 rounded-full" style="width: 88%"></div>
                                    </div>
                                    <span class="text-sm font-bold">88%</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <span>روح الفريق</span>
                                <div class="flex items-center gap-2">
                                    <div class="w-24 bg-gray-700 rounded-full h-2">
                                        <div class="bg-red-500 h-2 rounded-full" style="width: 90%"></div>
                                    </div>
                                    <span class="text-sm font-bold">90%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getAchievementsContent() {
            const achievements = [
                { name: 'أفضل لاعب في الشهر', date: '2024-01-15', icon: 'fa-star', color: 'text-yellow-400' },
                { name: 'حضور مثالي', date: '2024-02-01', icon: 'fa-calendar-check', color: 'text-green-400' },
                { name: 'هداف البطولة', date: '2024-03-10', icon: 'fa-futbol', color: 'text-blue-400' },
                { name: 'روح الفريق', date: '2024-04-05', icon: 'fa-users', color: 'text-purple-400' }
            ];

            return `
                <div class="fade-in">
                    <div class="grid-3">
                        ${achievements.map(achievement => `
                            <div class="card text-center">
                                <i class="fas ${achievement.icon} text-4xl ${achievement.color} mb-4"></i>
                                <h4 class="font-bold text-lg mb-2">${achievement.name}</h4>
                                <p class="text-sm text-gray-400">${formatDate(achievement.date)}</p>
                                <div class="mt-4">
                                    <span class="badge bg-yellow-600">إنجاز</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>

                    <div class="card mt-6">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-target ml-2 text-orange-400"></i>
                            الأهداف القادمة
                        </h3>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-3 bg-orange-600/20 border border-orange-600/30 rounded-lg">
                                <span>تحسين نسبة التمرير</span>
                                <div class="flex items-center gap-2">
                                    <div class="w-24 bg-gray-700 rounded-full h-2">
                                        <div class="bg-orange-500 h-2 rounded-full" style="width: 65%"></div>
                                    </div>
                                    <span class="text-sm">65%</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-blue-600/20 border border-blue-600/30 rounded-lg">
                                <span>زيادة السرعة</span>
                                <div class="flex items-center gap-2">
                                    <div class="w-24 bg-gray-700 rounded-full h-2">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: 40%"></div>
                                    </div>
                                    <span class="text-sm">40%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getParticipationContent() {
            return `
                <div class="grid-2 fade-in">
                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-calendar ml-2 text-blue-400"></i>
                            المشاركات الحالية
                        </h3>
                        <div class="space-y-3">
                            <div class="p-3 bg-green-600/20 border border-green-600/30 rounded-lg">
                                <h4 class="font-bold">بطولة الأكاديميات</h4>
                                <p class="text-sm text-gray-300">بطولة محلية للفئات العمرية</p>
                                <div class="mt-2 flex justify-between">
                                    <span class="badge bg-green-600">نشط</span>
                                    <span class="text-xs text-gray-400">تنتهي في 15 يوم</span>
                                </div>
                            </div>

                            <div class="p-3 bg-blue-600/20 border border-blue-600/30 rounded-lg">
                                <h4 class="font-bold">معسكر الصيف</h4>
                                <p class="text-sm text-gray-300">معسكر تدريبي مكثف</p>
                                <div class="mt-2 flex justify-between">
                                    <span class="badge bg-blue-600">مسجل</span>
                                    <span class="text-xs text-gray-400">يبدأ في 30 يوم</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-history ml-2 text-purple-400"></i>
                            المشاركات السابقة
                        </h3>
                        <div class="space-y-3">
                            <div class="p-3 bg-gray-800/50 border border-gray-700 rounded-lg">
                                <h4 class="font-bold">كأس الربيع</h4>
                                <p class="text-sm text-gray-300">المركز الثاني</p>
                                <span class="text-xs text-gray-400">مارس 2024</span>
                            </div>

                            <div class="p-3 bg-gray-800/50 border border-gray-700 rounded-lg">
                                <h4 class="font-bold">معسكر الشتاء</h4>
                                <p class="text-sm text-gray-300">مشارك</p>
                                <span class="text-xs text-gray-400">ديسمبر 2023</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getMediaContent() {
            return `
                <div class="fade-in">
                    <div class="card">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-camera ml-2 text-purple-400"></i>
                            استوديو اللاعب
                        </h3>

                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                            ${Array.from({length: 8}, (_, i) => `
                                <div class="aspect-square bg-gray-800 rounded-lg overflow-hidden cursor-pointer hover:opacity-80 transition-all">
                                    <img src="https://via.placeholder.com/200x200/8B4513/FFFFFF?text=Photo+${i+1}" alt="صورة ${i+1}" class="w-full h-full object-cover">
                                </div>
                            `).join('')}
                        </div>

                        <div class="flex gap-4">
                            <button class="btn btn-primary flex-1" onclick="uploadMedia('photo')">
                                <i class="fas fa-camera ml-2"></i>
                                إضافة صورة
                            </button>
                            <button class="btn btn-secondary flex-1" onclick="uploadMedia('video')">
                                <i class="fas fa-video ml-2"></i>
                                إضافة فيديو
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        function getStoreContent() {
            return `
                <div class="grid-3 fade-in">
                    <div class="card text-center">
                        <img src="https://via.placeholder.com/200x150/8B4513/FFFFFF?text=Jersey" alt="قميص" class="w-full h-32 object-cover rounded-lg mb-4">
                        <h4 class="font-bold mb-2">قميص الأكاديمية</h4>
                        <p class="text-sm text-gray-400 mb-4">قميص رسمي عالي الجودة</p>
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-lg font-bold">250 ر.س</span>
                            <span class="text-purple-400">500 نقطة</span>
                        </div>
                        <button class="btn btn-primary w-full">شراء</button>
                    </div>

                    <div class="card text-center">
                        <img src="https://via.placeholder.com/200x150/8B4513/FFFFFF?text=Bag" alt="حقيبة" class="w-full h-32 object-cover rounded-lg mb-4">
                        <h4 class="font-bold mb-2">حقيبة رياضية</h4>
                        <p class="text-sm text-gray-400 mb-4">حقيبة متعددة الاستخدامات</p>
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-lg font-bold">400 ر.س</span>
                            <span class="text-purple-400">800 نقطة</span>
                        </div>
                        <button class="btn btn-primary w-full">شراء</button>
                    </div>

                    <div class="card text-center">
                        <img src="https://via.placeholder.com/200x150/8B4513/FFFFFF?text=Shoes" alt="حذاء" class="w-full h-32 object-cover rounded-lg mb-4">
                        <h4 class="font-bold mb-2">حذاء تدريب</h4>
                        <p class="text-sm text-gray-400 mb-4">حذاء احترافي للتدريب</p>
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-lg font-bold">600 ر.س</span>
                            <span class="text-purple-400">1200 نقطة</span>
                        </div>
                        <button class="btn btn-primary w-full">شراء</button>
                    </div>
                </div>
            `;
        }

        function getPlayersContent() {
            return `
                <div class="fade-in">
                    <!-- Search and Filters Section -->
                    <div class="card mb-6">
                        <div class="flex flex-col lg:flex-row gap-4 items-center justify-between mb-6">
                            <h2 class="text-2xl font-bold">
                                <i class="fas fa-users ml-2"></i>
                                عرض اللاعبين
                            </h2>
                            <div class="flex gap-2">
                                <button class="btn btn-primary" onclick="addNewPlayer()">
                                    <i class="fas fa-plus ml-2"></i>
                                    إضافة لاعب جديد
                                </button>
                                <button class="btn btn-secondary" onclick="exportPlayersData()">
                                    <i class="fas fa-download ml-2"></i>
                                    تصدير البيانات
                                </button>
                            </div>
                        </div>

                        <!-- Search Bar -->
                        <div class="mb-4">
                            <div class="relative">
                                <input type="text" id="playersSearch" placeholder="البحث عن لاعب (الاسم، الرقم الأكاديمي، رقم الجوال...)"
                                       class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3 pr-10"
                                       oninput="filterPlayers()">
                                <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>

                        <!-- Filters -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                            <div>
                                <label class="block text-sm font-bold mb-2">الفئة العمرية:</label>
                                <select id="categoryFilter" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3" onchange="filterPlayers()">
                                    <option value="">جميع الفئات</option>
                                    <option value="براعم">براعم (تحت 8 سنوات)</option>
                                    <option value="أشبال">أشبال (8-10 سنوات)</option>
                                    <option value="ناشئين">ناشئين (11-14 سنة)</option>
                                    <option value="شباب">شباب (15-18 سنة)</option>
                                    <option value="رجال">رجال (19+ سنة)</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-bold mb-2">المركز:</label>
                                <select id="positionFilter" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3" onchange="filterPlayers()">
                                    <option value="">جميع المراكز</option>
                                    <option value="GK">حارس مرمى</option>
                                    <option value="DEF">مدافع</option>
                                    <option value="MID">وسط الملعب</option>
                                    <option value="ATT">مهاجم</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-bold mb-2">حالة الاشتراك:</label>
                                <select id="subscriptionFilter" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3" onchange="filterPlayers()">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="expired">منتهي</option>
                                    <option value="pending">معلق</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-bold mb-2">ترتيب حسب:</label>
                                <select id="sortFilter" class="w-full bg-gray-800 border border-gray-600 rounded-lg p-3" onchange="filterPlayers()">
                                    <option value="name">الاسم</option>
                                    <option value="rating">التقييم</option>
                                    <option value="loyalty">نقاط الولاء</option>
                                    <option value="attendance">نسبة الحضور</option>
                                    <option value="age">العمر</option>
                                    <option value="joinDate">تاريخ الانضمام</option>
                                </select>
                            </div>
                        </div>

                        <!-- Quick Stats -->
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                            <div class="bg-blue-600/20 border border-blue-600/30 rounded-lg p-4 text-center">
                                <div class="text-2xl font-bold text-blue-400" id="totalPlayers">0</div>
                                <div class="text-sm text-gray-400">إجمالي اللاعبين</div>
                            </div>
                            <div class="bg-green-600/20 border border-green-600/30 rounded-lg p-4 text-center">
                                <div class="text-2xl font-bold text-green-400" id="activePlayers">0</div>
                                <div class="text-sm text-gray-400">اشتراك نشط</div>
                            </div>
                            <div class="bg-yellow-600/20 border border-yellow-600/30 rounded-lg p-4 text-center">
                                <div class="text-2xl font-bold text-yellow-400" id="averageRating">0</div>
                                <div class="text-sm text-gray-400">متوسط التقييم</div>
                            </div>
                            <div class="bg-purple-600/20 border border-purple-600/30 rounded-lg p-4 text-center">
                                <div class="text-2xl font-bold text-purple-400" id="averageAttendance">0%</div>
                                <div class="text-sm text-gray-400">متوسط الحضور</div>
                            </div>
                        </div>
                    </div>

                    <!-- Players Grid -->
                    <div id="playersGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        <!-- Players will be loaded here -->
                    </div>

                    <!-- Pagination -->
                    <div class="flex justify-center items-center gap-4 mt-8" id="playersPagination">
                        <!-- Pagination will be loaded here -->
                    </div>
                </div>
            `;
        }

        function uploadMedia(type) {
            Swal.fire({
                title: `رفع ${type === 'photo' ? 'صورة' : 'فيديو'}`,
                text: 'سيتم إضافة هذه الميزة قريباً',
                icon: 'info',
                confirmButtonText: 'موافق'
            });
        }

        // ==================== Players Management Functions ====================

        // Sample players data
        let allPlayers = [
            {
                id: 1,
                firstName: 'محمد',
                fatherName: 'أحمد',
                familyName: 'الزهراني',
                academicNumber: '7C-2024-001',
                age: 16,
                category: 'ناشئين',
                position: 'MID',
                rating: 85,
                loyaltyPoints: 1250,
                attendanceRate: 95,
                subscriptionStatus: 'active',
                joinDate: '2024-01-15',
                avatar: 'https://via.placeholder.com/80x80/8B4513/FFFFFF?text=M',
                personalPhone: '0501234567'
            },
            {
                id: 2,
                firstName: 'عبدالله',
                fatherName: 'سعد',
                familyName: 'القحطاني',
                academicNumber: '7C-2024-002',
                age: 14,
                category: 'ناشئين',
                position: 'ATT',
                rating: 78,
                loyaltyPoints: 980,
                attendanceRate: 88,
                subscriptionStatus: 'active',
                joinDate: '2024-02-01',
                avatar: 'https://via.placeholder.com/80x80/8B4513/FFFFFF?text=A',
                personalPhone: '0507654321'
            },
            {
                id: 3,
                firstName: 'فهد',
                fatherName: 'عبدالرحمن',
                familyName: 'الغامدي',
                academicNumber: '7C-2024-003',
                age: 17,
                category: 'شباب',
                position: 'DEF',
                rating: 82,
                loyaltyPoints: 1100,
                attendanceRate: 92,
                subscriptionStatus: 'active',
                joinDate: '2024-01-20',
                avatar: 'https://via.placeholder.com/80x80/8B4513/FFFFFF?text=F',
                personalPhone: '0509876543'
            },
            {
                id: 4,
                firstName: 'خالد',
                fatherName: 'محمد',
                familyName: 'العتيبي',
                academicNumber: '7C-2024-004',
                age: 15,
                category: 'ناشئين',
                position: 'GK',
                rating: 80,
                loyaltyPoints: 850,
                attendanceRate: 90,
                subscriptionStatus: 'expired',
                joinDate: '2024-03-10',
                avatar: 'https://via.placeholder.com/80x80/8B4513/FFFFFF?text=K',
                personalPhone: '0502468135'
            },
            {
                id: 5,
                firstName: 'سلطان',
                fatherName: 'فهد',
                familyName: 'الدوسري',
                academicNumber: '7C-2024-005',
                age: 12,
                category: 'أشبال',
                position: 'MID',
                rating: 75,
                loyaltyPoints: 650,
                attendanceRate: 85,
                subscriptionStatus: 'active',
                joinDate: '2024-02-15',
                avatar: 'https://via.placeholder.com/80x80/8B4513/FFFFFF?text=S',
                personalPhone: '0503691472'
            }
        ];

        let filteredPlayers = [...allPlayers];
        let currentPage = 1;
        const playersPerPage = 8;

        function initializePlayersTab() {
            loadPlayersData();
            updatePlayersStats();
            renderPlayersGrid();
            renderPagination();
        }

        function loadPlayersData() {
            // Load from localStorage if available
            const savedPlayers = localStorage.getItem('academy_players');
            if (savedPlayers) {
                try {
                    allPlayers = JSON.parse(savedPlayers);
                    filteredPlayers = [...allPlayers];
                } catch (error) {
                    console.error('Error loading players data:', error);
                }
            }
        }

        function savePlayersData() {
            localStorage.setItem('academy_players', JSON.stringify(allPlayers));
        }

        function updatePlayersStats() {
            const totalPlayers = filteredPlayers.length;
            const activePlayers = filteredPlayers.filter(p => p.subscriptionStatus === 'active').length;
            const averageRating = totalPlayers > 0 ? Math.round(filteredPlayers.reduce((sum, p) => sum + p.rating, 0) / totalPlayers) : 0;
            const averageAttendance = totalPlayers > 0 ? Math.round(filteredPlayers.reduce((sum, p) => sum + p.attendanceRate, 0) / totalPlayers) : 0;

            document.getElementById('totalPlayers').textContent = totalPlayers;
            document.getElementById('activePlayers').textContent = activePlayers;
            document.getElementById('averageRating').textContent = averageRating;
            document.getElementById('averageAttendance').textContent = averageAttendance + '%';
        }

        function filterPlayers() {
            const searchTerm = document.getElementById('playersSearch').value.toLowerCase();
            const categoryFilter = document.getElementById('categoryFilter').value;
            const positionFilter = document.getElementById('positionFilter').value;
            const subscriptionFilter = document.getElementById('subscriptionFilter').value;
            const sortFilter = document.getElementById('sortFilter').value;

            filteredPlayers = allPlayers.filter(player => {
                const matchesSearch = !searchTerm ||
                    player.firstName.toLowerCase().includes(searchTerm) ||
                    player.familyName.toLowerCase().includes(searchTerm) ||
                    player.academicNumber.toLowerCase().includes(searchTerm) ||
                    player.personalPhone.includes(searchTerm);

                const matchesCategory = !categoryFilter || player.category === categoryFilter;
                const matchesPosition = !positionFilter || player.position === positionFilter;
                const matchesSubscription = !subscriptionFilter || player.subscriptionStatus === subscriptionFilter;

                return matchesSearch && matchesCategory && matchesPosition && matchesSubscription;
            });

            // Sort players
            filteredPlayers.sort((a, b) => {
                switch (sortFilter) {
                    case 'name':
                        return a.firstName.localeCompare(b.firstName, 'ar');
                    case 'rating':
                        return b.rating - a.rating;
                    case 'loyalty':
                        return b.loyaltyPoints - a.loyaltyPoints;
                    case 'attendance':
                        return b.attendanceRate - a.attendanceRate;
                    case 'age':
                        return a.age - b.age;
                    case 'joinDate':
                        return new Date(b.joinDate) - new Date(a.joinDate);
                    default:
                        return 0;
                }
            });

            currentPage = 1;
            updatePlayersStats();
            renderPlayersGrid();
            renderPagination();
        }

        function renderPlayersGrid() {
            const grid = document.getElementById('playersGrid');
            const startIndex = (currentPage - 1) * playersPerPage;
            const endIndex = startIndex + playersPerPage;
            const playersToShow = filteredPlayers.slice(startIndex, endIndex);

            if (playersToShow.length === 0) {
                grid.innerHTML = `
                    <div class="col-span-full text-center py-12">
                        <i class="fas fa-users text-6xl text-gray-600 mb-4"></i>
                        <h3 class="text-xl font-bold text-gray-400 mb-2">لا توجد نتائج</h3>
                        <p class="text-gray-500">لم يتم العثور على لاعبين يطابقون معايير البحث</p>
                    </div>
                `;
                return;
            }

            grid.innerHTML = playersToShow.map(player => `
                <div class="card hover:transform hover:scale-105 transition-all duration-300 cursor-pointer" onclick="viewPlayerProfile('${player.academicNumber}')">
                    <div class="flex items-center gap-4 mb-4">
                        <img src="${player.avatar}" alt="صورة ${player.firstName}" class="w-16 h-16 rounded-full object-cover border-2 border-gray-600">
                        <div class="flex-1">
                            <h3 class="font-bold text-lg">${player.firstName} ${player.familyName}</h3>
                            <p class="text-sm text-gray-400">${player.academicNumber}</p>
                            <div class="flex items-center gap-2 mt-1">
                                <span class="px-2 py-1 text-xs rounded-full ${getPositionColor(player.position)}">${getPositionText(player.position)}</span>
                                <span class="px-2 py-1 text-xs rounded-full ${getSubscriptionColor(player.subscriptionStatus)}">${getSubscriptionText(player.subscriptionStatus)}</span>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="text-center p-3 bg-gray-800/50 rounded-lg">
                            <div class="text-lg font-bold text-blue-400">${player.rating}</div>
                            <div class="text-xs text-gray-400">التقييم</div>
                        </div>
                        <div class="text-center p-3 bg-gray-800/50 rounded-lg">
                            <div class="text-lg font-bold text-green-400">${player.attendanceRate}%</div>
                            <div class="text-xs text-gray-400">الحضور</div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center text-sm text-gray-400 mb-4">
                        <span><i class="fas fa-birthday-cake ml-1"></i>${player.age} سنة</span>
                        <span><i class="fas fa-star ml-1"></i>${player.loyaltyPoints} نقطة</span>
                    </div>

                    <div class="flex gap-2">
                        <button class="btn btn-sm btn-primary flex-1" onclick="event.stopPropagation(); editPlayer('${player.academicNumber}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-secondary flex-1" onclick="event.stopPropagation(); viewPlayerStats('${player.academicNumber}')">
                            <i class="fas fa-chart-bar"></i>
                        </button>
                        <button class="btn btn-sm btn-success flex-1" onclick="event.stopPropagation(); contactPlayer('${player.personalPhone}')">
                            <i class="fas fa-phone"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function renderPagination() {
            const pagination = document.getElementById('playersPagination');
            const totalPages = Math.ceil(filteredPlayers.length / playersPerPage);

            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let paginationHTML = '';

            // Previous button
            if (currentPage > 1) {
                paginationHTML += `<button class="btn btn-secondary" onclick="changePage(${currentPage - 1})">
                    <i class="fas fa-chevron-right"></i>
                </button>`;
            }

            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                if (i === currentPage) {
                    paginationHTML += `<button class="btn btn-primary">${i}</button>`;
                } else if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    paginationHTML += `<button class="btn btn-secondary" onclick="changePage(${i})">${i}</button>`;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHTML += `<span class="px-3 py-2 text-gray-400">...</span>`;
                }
            }

            // Next button
            if (currentPage < totalPages) {
                paginationHTML += `<button class="btn btn-secondary" onclick="changePage(${currentPage + 1})">
                    <i class="fas fa-chevron-left"></i>
                </button>`;
            }

            pagination.innerHTML = paginationHTML;
        }

        function changePage(page) {
            currentPage = page;
            renderPlayersGrid();
            renderPagination();
        }

        // Helper functions for player display
        function getPositionText(position) {
            const positions = {
                'GK': 'حارس مرمى',
                'DEF': 'مدافع',
                'MID': 'وسط الملعب',
                'ATT': 'مهاجم'
            };
            return positions[position] || position;
        }

        function getPositionColor(position) {
            const colors = {
                'GK': 'bg-yellow-600 text-yellow-100',
                'DEF': 'bg-blue-600 text-blue-100',
                'MID': 'bg-green-600 text-green-100',
                'ATT': 'bg-red-600 text-red-100'
            };
            return colors[position] || 'bg-gray-600 text-gray-100';
        }

        function getSubscriptionText(status) {
            const statuses = {
                'active': 'نشط',
                'expired': 'منتهي',
                'pending': 'معلق'
            };
            return statuses[status] || status;
        }

        function getSubscriptionColor(status) {
            const colors = {
                'active': 'bg-green-600 text-green-100',
                'expired': 'bg-red-600 text-red-100',
                'pending': 'bg-yellow-600 text-yellow-100'
            };
            return colors[status] || 'bg-gray-600 text-gray-100';
        }

        // Player actions
        function viewPlayerProfile(academicNumber) {
            // Redirect to player profile page
            window.location.href = `player-profile.html?player=${academicNumber}`;
        }

        function editPlayer(academicNumber) {
            const player = allPlayers.find(p => p.academicNumber === academicNumber);
            if (!player) return;

            Swal.fire({
                title: 'تحرير بيانات اللاعب',
                html: `
                    <div class="text-right space-y-4">
                        <div>
                            <label class="block text-sm font-bold mb-2">الاسم الأول:</label>
                            <input type="text" id="editFirstName" value="${player.firstName}" class="w-full p-3 border rounded-lg">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">اسم العائلة:</label>
                            <input type="text" id="editFamilyName" value="${player.familyName}" class="w-full p-3 border rounded-lg">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">رقم الجوال:</label>
                            <input type="tel" id="editPhone" value="${player.personalPhone}" class="w-full p-3 border rounded-lg">
                        </div>
                        <div>
                            <label class="block text-sm font-bold mb-2">المركز:</label>
                            <select id="editPosition" class="w-full p-3 border rounded-lg">
                                <option value="GK" ${player.position === 'GK' ? 'selected' : ''}>حارس مرمى</option>
                                <option value="DEF" ${player.position === 'DEF' ? 'selected' : ''}>مدافع</option>
                                <option value="MID" ${player.position === 'MID' ? 'selected' : ''}>وسط الملعب</option>
                                <option value="ATT" ${player.position === 'ATT' ? 'selected' : ''}>مهاجم</option>
                            </select>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'حفظ التغييرات',
                cancelButtonText: 'إلغاء',
                preConfirm: () => {
                    const firstName = document.getElementById('editFirstName').value;
                    const familyName = document.getElementById('editFamilyName').value;
                    const phone = document.getElementById('editPhone').value;
                    const position = document.getElementById('editPosition').value;

                    if (!firstName || !familyName || !phone) {
                        Swal.showValidationMessage('يرجى ملء جميع الحقول المطلوبة');
                        return false;
                    }

                    return { firstName, familyName, phone, position };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    const { firstName, familyName, phone, position } = result.value;

                    // Update player data
                    player.firstName = firstName;
                    player.familyName = familyName;
                    player.personalPhone = phone;
                    player.position = position;

                    savePlayersData();
                    renderPlayersGrid();
                    showNotification('تم تحديث بيانات اللاعب بنجاح!', 'success');
                }
            });
        }

        function viewPlayerStats(academicNumber) {
            const player = allPlayers.find(p => p.academicNumber === academicNumber);
            if (!player) return;

            Swal.fire({
                title: `إحصائيات ${player.firstName} ${player.familyName}`,
                html: `
                    <div class="text-right space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-blue-100 p-4 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600">${player.rating}</div>
                                <div class="text-sm text-blue-800">التقييم العام</div>
                            </div>
                            <div class="bg-green-100 p-4 rounded-lg">
                                <div class="text-2xl font-bold text-green-600">${player.attendanceRate}%</div>
                                <div class="text-sm text-green-800">نسبة الحضور</div>
                            </div>
                            <div class="bg-purple-100 p-4 rounded-lg">
                                <div class="text-2xl font-bold text-purple-600">${player.loyaltyPoints}</div>
                                <div class="text-sm text-purple-800">نقاط الولاء</div>
                            </div>
                            <div class="bg-orange-100 p-4 rounded-lg">
                                <div class="text-2xl font-bold text-orange-600">${player.age}</div>
                                <div class="text-sm text-orange-800">العمر</div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p><strong>الفئة العمرية:</strong> ${player.category}</p>
                            <p><strong>المركز:</strong> ${getPositionText(player.position)}</p>
                            <p><strong>حالة الاشتراك:</strong> ${getSubscriptionText(player.subscriptionStatus)}</p>
                            <p><strong>تاريخ الانضمام:</strong> ${new Date(player.joinDate).toLocaleDateString('ar-SA')}</p>
                        </div>
                    </div>
                `,
                confirmButtonText: 'إغلاق',
                width: '600px'
            });
        }

        function contactPlayer(phone) {
            Swal.fire({
                title: 'التواصل مع اللاعب',
                html: `
                    <div class="text-center space-y-4">
                        <p class="text-lg mb-4">رقم الجوال: <strong>${phone}</strong></p>
                        <div class="flex gap-4 justify-center">
                            <button class="btn btn-success" onclick="window.open('tel:${phone}')">
                                <i class="fas fa-phone ml-2"></i>اتصال
                            </button>
                            <button class="btn btn-primary" onclick="window.open('sms:${phone}')">
                                <i class="fas fa-sms ml-2"></i>رسالة نصية
                            </button>
                            <button class="btn btn-secondary" onclick="window.open('https://wa.me/${phone.replace(/[^0-9]/g, '')}')">
                                <i class="fab fa-whatsapp ml-2"></i>واتساب
                            </button>
                        </div>
                    </div>
                `,
                showConfirmButton: false,
                showCloseButton: true
            });
        }

        function addNewPlayer() {
            Swal.fire({
                title: 'إضافة لاعب جديد',
                html: `
                    <div class="text-right space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-bold mb-2">الاسم الأول:</label>
                                <input type="text" id="newFirstName" class="w-full p-3 border rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-bold mb-2">اسم الأب:</label>
                                <input type="text" id="newFatherName" class="w-full p-3 border rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-bold mb-2">اسم العائلة:</label>
                                <input type="text" id="newFamilyName" class="w-full p-3 border rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-bold mb-2">العمر:</label>
                                <input type="number" id="newAge" min="5" max="25" class="w-full p-3 border rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-bold mb-2">رقم الجوال:</label>
                                <input type="tel" id="newPhone" class="w-full p-3 border rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-bold mb-2">المركز:</label>
                                <select id="newPosition" class="w-full p-3 border rounded-lg">
                                    <option value="GK">حارس مرمى</option>
                                    <option value="DEF">مدافع</option>
                                    <option value="MID">وسط الملعب</option>
                                    <option value="ATT">مهاجم</option>
                                </select>
                            </div>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'إضافة اللاعب',
                cancelButtonText: 'إلغاء',
                width: '700px',
                preConfirm: () => {
                    const firstName = document.getElementById('newFirstName').value;
                    const fatherName = document.getElementById('newFatherName').value;
                    const familyName = document.getElementById('newFamilyName').value;
                    const age = parseInt(document.getElementById('newAge').value);
                    const phone = document.getElementById('newPhone').value;
                    const position = document.getElementById('newPosition').value;

                    if (!firstName || !fatherName || !familyName || !age || !phone) {
                        Swal.showValidationMessage('يرجى ملء جميع الحقول المطلوبة');
                        return false;
                    }

                    return { firstName, fatherName, familyName, age, phone, position };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    const { firstName, fatherName, familyName, age, phone, position } = result.value;

                    // Generate new player data
                    const newPlayer = {
                        id: allPlayers.length + 1,
                        firstName,
                        fatherName,
                        familyName,
                        academicNumber: `7C-2024-${String(allPlayers.length + 1).padStart(3, '0')}`,
                        age,
                        category: getCategoryByAge(age),
                        position,
                        rating: 70, // Default rating
                        loyaltyPoints: 0,
                        attendanceRate: 100,
                        subscriptionStatus: 'active',
                        joinDate: new Date().toISOString().split('T')[0],
                        avatar: `https://via.placeholder.com/80x80/8B4513/FFFFFF?text=${firstName.charAt(0)}`,
                        personalPhone: phone
                    };

                    allPlayers.push(newPlayer);
                    filteredPlayers = [...allPlayers];
                    savePlayersData();
                    updatePlayersStats();
                    renderPlayersGrid();
                    renderPagination();

                    showNotification('تم إضافة اللاعب بنجاح!', 'success');
                }
            });
        }

        function getCategoryByAge(age) {
            if (age < 8) return 'براعم';
            if (age <= 10) return 'أشبال';
            if (age <= 14) return 'ناشئين';
            if (age <= 18) return 'شباب';
            return 'رجال';
        }

        function exportPlayersData() {
            const csvContent = "data:text/csv;charset=utf-8,\uFEFF" +
                "الاسم الكامل,الرقم الأكاديمي,العمر,الفئة,المركز,التقييم,نقاط الولاء,نسبة الحضور,حالة الاشتراك,رقم الجوال\n" +
                filteredPlayers.map(player =>
                    `"${player.firstName} ${player.fatherName} ${player.familyName}","${player.academicNumber}",${player.age},"${player.category}","${getPositionText(player.position)}",${player.rating},${player.loyaltyPoints},${player.attendanceRate}%,"${getSubscriptionText(player.subscriptionStatus)}","${player.personalPhone}"`
                ).join("\n");

            const link = document.createElement('a');
            link.href = encodeURI(csvContent);
            link.download = `players_data_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            showNotification('تم تصدير بيانات اللاعبين بنجاح!', 'success');
        }

        // Open Attendance System
        function openAttendanceSystem() {
            const attendanceUrl = './attendance-system.html';
            window.open(attendanceUrl, '_blank');
            showNotification('تم فتح نظام الحضور والغياب المتقدم!', 'success');
        }

        // Update Attendance-based Loyalty Points
        function updateAttendanceLoyaltyPoints() {
            // This function integrates with the attendance system
            const attendanceData = localStorage.getItem('attendance_session_data');

            if (attendanceData) {
                try {
                    const sessionData = JSON.parse(attendanceData);
                    const playerAttendance = sessionData.attendanceData?.find(
                        record => record.playerNumber === currentPlayer.academicNumber
                    );

                    if (playerAttendance) {
                        let pointsToAdd = 0;

                        switch (playerAttendance.status) {
                            case 'present':
                                pointsToAdd = 10; // Full points for being present
                                break;
                            case 'late':
                                pointsToAdd = 5; // Half points for being late
                                break;
                            case 'absent':
                                pointsToAdd = 0; // No points for absence
                                break;
                        }

                        if (pointsToAdd > 0) {
                            currentPlayer.loyaltyPoints += pointsToAdd;

                            // Update attendance rate
                            if (currentPlayer.stats.attendanceRate < 100) {
                                currentPlayer.stats.attendanceRate = Math.min(
                                    currentPlayer.stats.attendanceRate + 2,
                                    100
                                );
                            }

                            showNotification(`تم إضافة ${pointsToAdd} نقطة ولاء للحضور!`, 'success');
                        }
                    }
                } catch (error) {
                    console.error('Error processing attendance data:', error);
                }
            }
        }

        // Sync with Attendance System
        function syncWithAttendanceSystem() {
            // Check for attendance updates every 30 seconds
            setInterval(() => {
                updateAttendanceLoyaltyPoints();
            }, 30000);
        }

        // ==================== Advanced Profile Management System ====================

        // Toggle Edit Mode
        function toggleEditMode() {
            isEditMode = !isEditMode;
            const editModeText = document.getElementById('editModeText');
            const body = document.body;

            if (isEditMode) {
                body.classList.add('edit-mode');
                editModeText.textContent = 'إنهاء التحرير';
                addEditableElements();
                showNotification('تم تفعيل وضع التحرير', 'info');
            } else {
                body.classList.remove('edit-mode');
                editModeText.textContent = 'تحرير الملف';
                removeEditableElements();
                showNotification('تم إنهاء وضع التحرير', 'info');
            }
        }

        // Add editable elements
        function addEditableElements() {
            const editableElements = [
                { id: 'playerName', type: 'text' },
                { id: 'playerAge', type: 'text' },
                { id: 'playerPhone', type: 'text' },
                { id: 'playerCategory', type: 'text' }
            ];

            editableElements.forEach(element => {
                const el = document.getElementById(element.id);
                if (el) {
                    el.classList.add('editable');
                    el.addEventListener('click', () => openEditProfileModal());
                }
            });
        }

        // Remove editable elements
        function removeEditableElements() {
            document.querySelectorAll('.editable').forEach(el => {
                el.classList.remove('editable');
                el.removeEventListener('click', openEditProfileModal);
            });
        }

        // Template Management
        function changeTemplate(templateName) {
            currentTemplate = templateName;
            const body = document.body;

            // Remove existing template classes
            body.classList.remove('template-default', 'template-cards', 'template-compact', 'template-professional', 'template-fifa');

            // Add new template class
            body.classList.add(`template-${templateName}`);

            // Apply template-specific changes
            switch(templateName) {
                case 'cards':
                    applyCardsTemplate();
                    break;
                case 'compact':
                    applyCompactTemplate();
                    break;
                case 'professional':
                    applyProfessionalTemplate();
                    break;
                case 'fifa':
                    applyFifaTemplate();
                    break;
                default:
                    applyDefaultTemplate();
            }

            // Save preference
            localStorage.setItem('player_template', templateName);
            showNotification(`تم تطبيق ${getTemplateName(templateName)}`, 'success');
        }

        function getTemplateName(template) {
            const names = {
                'default': 'القالب الافتراضي',
                'cards': 'قالب البطاقات',
                'compact': 'القالب المضغوط',
                'professional': 'القالب الاحترافي',
                'fifa': 'قالب FIFA'
            };
            return names[template] || template;
        }

        // Template Applications
        function applyDefaultTemplate() {
            document.querySelector('.nav-tabs').style.display = 'flex';
            document.getElementById('tabContents').style.display = 'block';
            document.getElementById('tabContents').style.gridTemplateColumns = '';
        }

        function applyCardsTemplate() {
            document.querySelector('.nav-tabs').style.display = 'none';
            const tabContents = document.getElementById('tabContents');

            // Load all content at once
            const allContent = `
                ${getOverviewContent()}
                ${getSubscriptionContent()}
                ${getQRCardContent()}
                ${getScheduleContent()}
                ${getAttendanceContent()}
                ${getMessagesContent()}
                ${getLoyaltyContent()}
                ${getEvaluationContent()}
                ${getAchievementsContent()}
                ${getParticipationContent()}
                ${getMediaContent()}
                ${getStoreContent()}
            `;

            tabContents.innerHTML = allContent;
            tabContents.style.display = 'grid';
            tabContents.style.gridTemplateColumns = 'repeat(auto-fit, minmax(300px, 1fr))';
        }

        function applyCompactTemplate() {
            document.querySelector('.nav-tabs').style.display = 'none';
            const tabContents = document.getElementById('tabContents');

            tabContents.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="card">
                        <h3 class="font-bold mb-2">المعلومات الأساسية</h3>
                        <p><strong>الاسم:</strong> ${currentPlayer.firstName} ${currentPlayer.familyName}</p>
                        <p><strong>العمر:</strong> ${currentPlayer.age} سنة</p>
                        <p><strong>المركز:</strong> ${currentPlayer.position}</p>
                        <p><strong>التقييم:</strong> ${currentPlayer.rating}/100</p>
                    </div>
                    <div class="card">
                        <h3 class="font-bold mb-2">الاشتراك</h3>
                        <p><strong>الخطة:</strong> ${currentPlayer.currentSubscription.plan}</p>
                        <p><strong>المتبقي:</strong> ${currentPlayer.currentSubscription.remaining} يوم</p>
                    </div>
                    <div class="card">
                        <h3 class="font-bold mb-2">نقاط الولاء</h3>
                        <p class="text-2xl font-bold text-yellow-400">${currentPlayer.loyaltyPoints}</p>
                    </div>
                </div>
            `;
        }

        function applyProfessionalTemplate() {
            document.querySelector('.nav-tabs').style.display = 'none';
            const tabContents = document.getElementById('tabContents');

            tabContents.innerHTML = `
                <div class="bg-white text-black p-8 rounded-lg">
                    <div class="text-center mb-8">
                        <img src="${currentPlayer.avatar}" alt="صورة اللاعب" class="w-32 h-32 rounded-full mx-auto mb-4 border-4 border-gray-300">
                        <h1 class="text-3xl font-bold">${currentPlayer.firstName} ${currentPlayer.familyName}</h1>
                        <p class="text-lg text-gray-600">${currentPlayer.academicNumber}</p>
                    </div>

                    <div class="grid grid-cols-2 gap-8">
                        <div>
                            <h2 class="text-xl font-bold mb-4 border-b-2 border-gray-300 pb-2">المعلومات الشخصية</h2>
                            <table class="w-full">
                                <tr><td class="font-bold">العمر:</td><td>${currentPlayer.age} سنة</td></tr>
                                <tr><td class="font-bold">الطول:</td><td>${currentPlayer.height} سم</td></tr>
                                <tr><td class="font-bold">الوزن:</td><td>${currentPlayer.weight} كغ</td></tr>
                                <tr><td class="font-bold">القدم المفضلة:</td><td>${getFootText(currentPlayer.preferredFoot)}</td></tr>
                            </table>
                        </div>

                        <div>
                            <h2 class="text-xl font-bold mb-4 border-b-2 border-gray-300 pb-2">الأداء والتقييم</h2>
                            <table class="w-full">
                                <tr><td class="font-bold">التقييم العام:</td><td>${currentPlayer.rating}/100</td></tr>
                                <tr><td class="font-bold">نقاط الولاء:</td><td>${currentPlayer.loyaltyPoints}</td></tr>
                                <tr><td class="font-bold">نسبة الحضور:</td><td>95%</td></tr>
                                <tr><td class="font-bold">المركز:</td><td>${currentPlayer.position}</td></tr>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        }

        function applyFifaTemplate() {
            document.querySelector('.nav-tabs').style.display = 'none';
            const tabContents = document.getElementById('tabContents');

            tabContents.innerHTML = `
                <div class="fifa-ultimate-card">
                    <div class="fifa-card-header">
                        <div class="fifa-rating">${currentPlayer.rating}</div>
                        <div class="fifa-position">${currentPlayer.position}</div>
                        <div class="fifa-flag">🇸🇦</div>
                    </div>

                    <div class="fifa-player-image">
                        <img src="${currentPlayer.avatar}" alt="صورة اللاعب">
                    </div>

                    <div class="fifa-player-info">
                        <h2 class="fifa-player-name">${currentPlayer.firstName}</h2>
                        <h3 class="fifa-player-surname">${currentPlayer.familyName}</h3>
                    </div>

                    <div class="fifa-stats">
                        <div class="fifa-stat">
                            <span class="stat-value">85</span>
                            <span class="stat-name">تقنية</span>
                        </div>
                        <div class="fifa-stat">
                            <span class="stat-value">78</span>
                            <span class="stat-name">سرعة</span>
                        </div>
                        <div class="fifa-stat">
                            <span class="stat-value">82</span>
                            <span class="stat-name">تسديد</span>
                        </div>
                        <div class="fifa-stat">
                            <span class="stat-value">88</span>
                            <span class="stat-name">تمرير</span>
                        </div>
                        <div class="fifa-stat">
                            <span class="stat-value">75</span>
                            <span class="stat-name">دفاع</span>
                        </div>
                        <div class="fifa-stat">
                            <span class="stat-value">80</span>
                            <span class="stat-name">بدني</span>
                        </div>
                    </div>

                    <div class="fifa-club">
                        <img src="https://via.placeholder.com/40x40/8B4513/FFFFFF?text=7C" alt="شعار الأكاديمية">
                        <span>أكاديمية 7C</span>
                    </div>
                </div>
            `;
        }

        // Color Theme Management
        function openColorThemeModal() {
            document.getElementById('colorThemeModal').classList.remove('hidden');
            // Mark current theme as active
            document.querySelectorAll('.color-theme').forEach(theme => {
                theme.classList.remove('active');
            });
            document.querySelector(`[data-theme="${currentTheme}"]`)?.classList.add('active');
        }

        function closeColorThemeModal() {
            document.getElementById('colorThemeModal').classList.add('hidden');
        }

        function selectColorTheme(theme) {
            document.querySelectorAll('.color-theme').forEach(t => t.classList.remove('active'));
            document.querySelector(`[data-theme="${theme}"]`).classList.add('active');

            // Apply theme immediately for preview
            applyColorTheme(theme);
        }

        function applyColorTheme(theme) {
            const body = document.body;

            // Remove existing theme classes
            body.classList.remove('theme-default', 'theme-blue', 'theme-green', 'theme-purple', 'theme-red', 'theme-orange', 'theme-pink', 'theme-indigo', 'theme-teal', 'theme-gray');

            // Add new theme class
            if (theme !== 'default') {
                body.classList.add(`theme-${theme}`);
            }

            currentTheme = theme;
        }

        function applyCustomColors() {
            const primaryColor = document.getElementById('customPrimaryColor').value;
            const secondaryColor = document.getElementById('customSecondaryColor').value;

            document.documentElement.style.setProperty('--primary-color', primaryColor);
            document.documentElement.style.setProperty('--secondary-color', secondaryColor);

            showNotification('تم تطبيق الألوان المخصصة', 'success');
        }

        function saveColorTheme() {
            localStorage.setItem('player_color_theme', currentTheme);
            closeColorThemeModal();
            showNotification('تم حفظ مجموعة الألوان', 'success');
        }

        // ==================== Export and Share Functions ====================

        // Toggle Export Menu
        function toggleExportMenu() {
            const menu = document.getElementById('exportMenu');
            menu.classList.toggle('hidden');
        }

        // Export to PDF
        function exportToPDF() {
            showNotification('جاري تحضير ملف PDF...', 'info');

            // Create a clean version for PDF
            const originalContent = document.body.innerHTML;
            const cleanContent = createCleanContentForExport();

            // Use html2pdf library (would need to be included)
            setTimeout(() => {
                showNotification('تم تصدير الملف بصيغة PDF بنجاح!', 'success');
                // Simulate download
                const link = document.createElement('a');
                link.download = `${currentPlayer.firstName}_${currentPlayer.familyName}_Profile.pdf`;
                link.click();
            }, 2000);
        }

        // Export to Word
        function exportToWord() {
            showNotification('جاري تحضير ملف Word...', 'info');

            const content = `
                <html>
                <head>
                    <meta charset="utf-8">
                    <title>ملف اللاعب - ${currentPlayer.firstName} ${currentPlayer.familyName}</title>
                </head>
                <body style="font-family: Arial, sans-serif; direction: rtl;">
                    <h1>ملف اللاعب الشخصي</h1>
                    <h2>${currentPlayer.firstName} ${currentPlayer.fatherName} ${currentPlayer.familyName}</h2>
                    <p><strong>الرقم الأكاديمي:</strong> ${currentPlayer.academicNumber}</p>
                    <p><strong>العمر:</strong> ${currentPlayer.age} سنة</p>
                    <p><strong>الفئة:</strong> ${currentPlayer.category}</p>
                    <p><strong>رقم الجوال:</strong> ${currentPlayer.personalPhone}</p>
                    <p><strong>التقييم العام:</strong> ${currentPlayer.rating}/100</p>
                    <p><strong>نقاط الولاء:</strong> ${currentPlayer.loyaltyPoints}</p>
                </body>
                </html>
            `;

            const blob = new Blob([content], { type: 'application/msword' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${currentPlayer.firstName}_${currentPlayer.familyName}_Profile.doc`;
            link.click();

            showNotification('تم تصدير الملف بصيغة Word بنجاح!', 'success');
        }

        // Export to Excel
        function exportToExcel() {
            showNotification('جاري تحضير ملف Excel...', 'info');

            const data = [
                ['الحقل', 'القيمة'],
                ['الاسم الكامل', `${currentPlayer.firstName} ${currentPlayer.fatherName} ${currentPlayer.familyName}`],
                ['الرقم الأكاديمي', currentPlayer.academicNumber],
                ['العمر', `${currentPlayer.age} سنة`],
                ['الفئة العمرية', currentPlayer.category],
                ['رقم الجوال', currentPlayer.personalPhone],
                ['الطول', `${currentPlayer.height} سم`],
                ['الوزن', `${currentPlayer.weight} كغ`],
                ['القدم المفضلة', getFootText(currentPlayer.preferredFoot)],
                ['التقييم العام', `${currentPlayer.rating}/100`],
                ['نقاط الولاء', currentPlayer.loyaltyPoints],
                ['خطة الاشتراك', currentPlayer.currentSubscription.plan],
                ['الأيام المتبقية', currentPlayer.currentSubscription.remaining]
            ];

            let csvContent = "data:text/csv;charset=utf-8,\uFEFF";
            data.forEach(row => {
                csvContent += row.join(",") + "\r\n";
            });

            const link = document.createElement('a');
            link.href = encodeURI(csvContent);
            link.download = `${currentPlayer.firstName}_${currentPlayer.familyName}_Profile.csv`;
            link.click();

            showNotification('تم تصدير الملف بصيغة Excel بنجاح!', 'success');
        }

        // Export to Image
        function exportToImage() {
            showNotification('جاري تحضير الصورة...', 'info');

            // Use html2canvas library (would need to be included)
            setTimeout(() => {
                // Simulate image creation
                const canvas = document.createElement('canvas');
                canvas.width = 800;
                canvas.height = 600;
                const ctx = canvas.getContext('2d');

                // Create a simple card design
                ctx.fillStyle = '#1a1a1a';
                ctx.fillRect(0, 0, 800, 600);

                ctx.fillStyle = '#8B4513';
                ctx.fillRect(20, 20, 760, 560);

                ctx.fillStyle = '#ffffff';
                ctx.font = '32px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`${currentPlayer.firstName} ${currentPlayer.familyName}`, 400, 100);

                ctx.font = '24px Arial';
                ctx.fillText(`${currentPlayer.academicNumber}`, 400, 150);
                ctx.fillText(`التقييم: ${currentPlayer.rating}/100`, 400, 200);
                ctx.fillText(`نقاط الولاء: ${currentPlayer.loyaltyPoints}`, 400, 250);

                canvas.toBlob(blob => {
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = `${currentPlayer.firstName}_${currentPlayer.familyName}_Card.png`;
                    link.click();
                    showNotification('تم تصدير البطاقة كصورة بنجاح!', 'success');
                });
            }, 1500);
        }

        // Print Profile
        function printProfile() {
            const printWindow = window.open('', '_blank');
            const printContent = createCleanContentForExport();

            printWindow.document.write(`
                <html>
                <head>
                    <title>ملف اللاعب - ${currentPlayer.firstName} ${currentPlayer.familyName}</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
                        .info-section { margin-bottom: 20px; }
                        .info-section h3 { border-bottom: 2px solid #8B4513; padding-bottom: 5px; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${printContent}
                </body>
                </html>
            `);

            printWindow.document.close();
            printWindow.print();
        }

        function createCleanContentForExport() {
            return `
                <div class="header">
                    <h1>ملف اللاعب الشخصي</h1>
                    <h2>${currentPlayer.firstName} ${currentPlayer.fatherName} ${currentPlayer.familyName}</h2>
                    <p>الرقم الأكاديمي: ${currentPlayer.academicNumber}</p>
                </div>

                <div class="info-grid">
                    <div class="info-section">
                        <h3>المعلومات الأساسية</h3>
                        <p><strong>العمر:</strong> ${currentPlayer.age} سنة</p>
                        <p><strong>الفئة:</strong> ${currentPlayer.category}</p>
                        <p><strong>رقم الجوال:</strong> ${currentPlayer.personalPhone}</p>
                        <p><strong>الطول:</strong> ${currentPlayer.height} سم</p>
                        <p><strong>الوزن:</strong> ${currentPlayer.weight} كغ</p>
                        <p><strong>القدم المفضلة:</strong> ${getFootText(currentPlayer.preferredFoot)}</p>
                    </div>

                    <div class="info-section">
                        <h3>الأداء والتقييم</h3>
                        <p><strong>التقييم العام:</strong> ${currentPlayer.rating}/100</p>
                        <p><strong>نقاط الولاء:</strong> ${currentPlayer.loyaltyPoints}</p>
                        <p><strong>نسبة الحضور:</strong> 95%</p>
                        <p><strong>المركز:</strong> ${currentPlayer.position}</p>
                    </div>

                    <div class="info-section">
                        <h3>معلومات الاشتراك</h3>
                        <p><strong>الخطة الحالية:</strong> ${currentPlayer.currentSubscription.plan}</p>
                        <p><strong>الأيام المتبقية:</strong> ${currentPlayer.currentSubscription.remaining} يوم</p>
                        <p><strong>قيمة الاشتراك:</strong> ${currentPlayer.currentSubscription.price} ر.س</p>
                    </div>

                    <div class="info-section">
                        <h3>معلومات ولي الأمر</h3>
                        <p><strong>الاسم:</strong> ${currentPlayer.guardianName}</p>
                        <p><strong>رقم الجوال:</strong> ${currentPlayer.guardianPhone}</p>
                        <p><strong>صلة القرابة:</strong> ${getRelationshipText(currentPlayer.relationship)}</p>
                    </div>
                </div>
            `;
        }

        // Share Functions
        function openShareModal() {
            const shareModal = document.createElement('div');
            shareModal.className = 'modal-overlay';
            shareModal.id = 'shareModal';
            shareModal.innerHTML = `
                <div class="modal-content" style="max-width: 500px;">
                    <div class="modal-header">
                        <h3>مشاركة ملف اللاعب</h3>
                        <button class="modal-close" onclick="closeShareModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="space-y-4">
                            <div class="text-center mb-4">
                                <img src="${currentPlayer.avatar}" alt="صورة اللاعب" class="w-20 h-20 rounded-full mx-auto mb-2 object-cover">
                                <h4 class="font-bold">${currentPlayer.firstName} ${currentPlayer.familyName}</h4>
                                <p class="text-sm text-gray-400">${currentPlayer.academicNumber}</p>
                            </div>

                            <div class="grid-2 gap-4">
                                <button class="btn btn-primary" onclick="shareViaWhatsApp()">
                                    <i class="fab fa-whatsapp ml-2"></i>
                                    مشاركة عبر واتساب
                                </button>
                                <button class="btn btn-secondary" onclick="copyProfileLink()">
                                    <i class="fas fa-copy ml-2"></i>
                                    نسخ الرابط
                                </button>
                                <button class="btn btn-success" onclick="generatePublicPage()">
                                    <i class="fas fa-external-link-alt ml-2"></i>
                                    إنشاء صفحة عامة
                                </button>
                                <button class="btn btn-warning" onclick="downloadPlayerCard()">
                                    <i class="fas fa-download ml-2"></i>
                                    تحميل بطاقة اللاعب
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(shareModal);
        }

        function closeShareModal() {
            const modal = document.getElementById('shareModal');
            if (modal) {
                modal.remove();
            }
        }

        function shareViaWhatsApp() {
            const text = `🏆 ملف اللاعب: ${currentPlayer.firstName} ${currentPlayer.familyName}
📋 الرقم الأكاديمي: ${currentPlayer.academicNumber}
⭐ التقييم: ${currentPlayer.rating}/100
🎯 المركز: ${currentPlayer.position}
🏃‍♂️ الفئة: ${currentPlayer.category}
💰 القيمة السوقية: ${currentPlayer.marketValue.calculated ? currentPlayer.marketValue.calculated.toLocaleString() + ' ر.س' : 'غير محسوبة'}
📱 أكاديمية 7C للتدريب الرياضي`;

            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(text)}`;
            window.open(whatsappUrl, '_blank');
            closeShareModal();
            showNotification('تم فتح واتساب للمشاركة!', 'success');
        }

        function copyProfileLink() {
            const profileUrl = `${window.location.origin}${window.location.pathname}?player=${currentPlayer.academicNumber}`;
            navigator.clipboard.writeText(profileUrl).then(() => {
                showNotification('تم نسخ رابط الملف الشخصي!', 'success');
                closeShareModal();
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = profileUrl;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('تم نسخ رابط الملف الشخصي!', 'success');
                closeShareModal();
            });
        }

        function generatePublicPage() {
            createPublicPlayerPage();
            closeShareModal();
            showNotification('تم إنشاء الصفحة العامة!', 'success');
        }

        function downloadPlayerCard() {
            // Generate a player card image
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 400;
            canvas.height = 600;

            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, 0, 600);
            gradient.addColorStop(0, '#1a1a1a');
            gradient.addColorStop(1, '#8B4513');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 400, 600);

            // Header
            ctx.fillStyle = 'white';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('أكاديمية 7C للتدريب الرياضي', 200, 50);

            // Player info
            ctx.font = 'bold 28px Arial';
            ctx.fillText(`${currentPlayer.firstName} ${currentPlayer.familyName}`, 200, 120);

            ctx.font = '20px Arial';
            ctx.fillText(`رقم اللاعب: ${currentPlayer.academicNumber}`, 200, 160);
            ctx.fillText(`المركز: ${currentPlayer.position}`, 200, 190);
            ctx.fillText(`الفئة: ${currentPlayer.category}`, 200, 220);
            ctx.fillText(`التقييم: ${currentPlayer.rating}/100`, 200, 250);

            if (currentPlayer.marketValue.calculated) {
                ctx.fillText(`القيمة السوقية: ${currentPlayer.marketValue.calculated.toLocaleString()} ر.س`, 200, 280);
            }

            // Stats
            ctx.font = 'bold 18px Arial';
            ctx.fillText('الإحصائيات:', 200, 330);
            ctx.font = '16px Arial';
            ctx.fillText(`المشاركات: ${currentPlayer.stats.matchesPlayed}`, 200, 360);
            ctx.fillText(`الأهداف: ${currentPlayer.stats.goals}`, 200, 385);
            ctx.fillText(`التمريرات الحاسمة: ${currentPlayer.stats.assists}`, 200, 410);
            ctx.fillText(`نسبة الحضور: ${currentPlayer.stats.attendanceRate}%`, 200, 435);

            // Footer
            ctx.font = '14px Arial';
            ctx.fillText(`تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-SA')}`, 200, 550);

            // Download
            canvas.toBlob(function(blob) {
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `${currentPlayer.firstName}_${currentPlayer.familyName}_Card.png`;
                link.click();
                showNotification('تم تحميل بطاقة اللاعب!', 'success');
                closeShareModal();
            });
        }

        function updateShareLink() {
            const link = `https://academy7c.com/player/${currentPlayer.academicNumber}`;
            document.getElementById('shareLink').value = link;
        }

        function copyShareLink() {
            const shareLink = document.getElementById('shareLink');
            shareLink.select();
            document.execCommand('copy');
            showNotification('تم نسخ الرابط بنجاح!', 'success');
        }

        function shareToWhatsApp() {
            const text = `شاهد ملف اللاعب ${currentPlayer.firstName} ${currentPlayer.familyName} في أكاديمية 7C`;
            const url = document.getElementById('shareLink').value;
            window.open(`https://wa.me/?text=${encodeURIComponent(text + '\n' + url)}`, '_blank');
        }

        function shareToTwitter() {
            const text = `شاهد ملف اللاعب ${currentPlayer.firstName} ${currentPlayer.familyName} في أكاديمية 7C`;
            const url = document.getElementById('shareLink').value;
            window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`, '_blank');
        }

        function shareToFacebook() {
            const url = document.getElementById('shareLink').value;
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
        }

        function publishProfile() {
            const privacy = document.querySelector('input[name="privacy"]:checked').value;

            // Save publication settings
            const publicationData = {
                isPublished: true,
                privacy: privacy,
                publishDate: new Date().toISOString(),
                views: 0
            };

            localStorage.setItem(`player_publication_${currentPlayer.id}`, JSON.stringify(publicationData));

            showNotification('تم نشر الملف الشخصي بنجاح!', 'success');
            closeShareModal();

            // Create public page
            createPublicPlayerPage();
        }

        // ==================== Profile Editing Functions ====================

        // Open Edit Profile Modal
        function openEditProfileModal() {
            document.getElementById('editProfileModal').classList.remove('hidden');
            populateEditForm();
        }

        function closeEditProfileModal() {
            document.getElementById('editProfileModal').classList.add('hidden');
        }

        function populateEditForm() {
            document.getElementById('editFirstName').value = currentPlayer.firstName;
            document.getElementById('editFatherName').value = currentPlayer.fatherName;
            document.getElementById('editFamilyName').value = currentPlayer.familyName;
            document.getElementById('editPersonalPhone').value = currentPlayer.personalPhone;
            document.getElementById('editHeight').value = currentPlayer.height;
            document.getElementById('editWeight').value = currentPlayer.weight;
            document.getElementById('editPreferredFoot').value = currentPlayer.preferredFoot;
            document.getElementById('editPosition').value = currentPlayer.position;
        }

        // Handle photo upload in edit mode
        function handleEditPhotoUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Check file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                showNotification('حجم الصورة كبير جداً (الحد الأقصى 5 ميجابايت)', 'error');
                return;
            }

            // Check file type
            if (!file.type.startsWith('image/')) {
                showNotification('يرجى اختيار ملف صورة صحيح', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                editedPhotoData = e.target.result;
                document.getElementById('editPhotoImage').src = e.target.result;
                document.getElementById('editPhotoPreview').classList.remove('hidden');
            };
            reader.readAsDataURL(file);
        }

        // Photo editing functions
        let photoRotation = 0;
        function rotatePhoto() {
            photoRotation += 90;
            if (photoRotation >= 360) photoRotation = 0;

            const img = document.getElementById('editPhotoImage');
            img.style.transform = `rotate(${photoRotation}deg)`;

            showNotification(`تم تدوير الصورة ${photoRotation} درجة`, 'info');
        }

        function cropPhoto() {
            showNotification('ميزة قص الصورة ستكون متاحة قريباً', 'info');
            // Here you would implement cropping functionality
            // Could use libraries like Cropper.js
        }

        function applyFilter() {
            const img = document.getElementById('editPhotoImage');
            const filters = ['none', 'grayscale(100%)', 'sepia(100%)', 'brightness(1.2)', 'contrast(1.2)'];
            const currentFilter = img.style.filter || 'none';
            const currentIndex = filters.indexOf(currentFilter);
            const nextIndex = (currentIndex + 1) % filters.length;

            img.style.filter = filters[nextIndex];

            const filterNames = ['بدون فلتر', 'أبيض وأسود', 'كلاسيكي', 'مضيء', 'عالي التباين'];
            showNotification(`تم تطبيق فلتر: ${filterNames[nextIndex]}`, 'info');
        }

        // Save profile changes
        function saveProfileChanges() {
            // Validate form
            const firstName = document.getElementById('editFirstName').value.trim();
            const fatherName = document.getElementById('editFatherName').value.trim();
            const familyName = document.getElementById('editFamilyName').value.trim();
            const phone = document.getElementById('editPersonalPhone').value.trim();

            if (!firstName || !fatherName || !familyName || !phone) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            // Update player data
            currentPlayer.firstName = firstName;
            currentPlayer.fatherName = fatherName;
            currentPlayer.familyName = familyName;
            currentPlayer.personalPhone = phone;
            currentPlayer.height = parseInt(document.getElementById('editHeight').value) || currentPlayer.height;
            currentPlayer.weight = parseInt(document.getElementById('editWeight').value) || currentPlayer.weight;
            currentPlayer.preferredFoot = document.getElementById('editPreferredFoot').value;
            currentPlayer.position = document.getElementById('editPosition').value;

            // Update photo if changed
            if (editedPhotoData) {
                currentPlayer.avatar = editedPhotoData;
            }

            // Save to localStorage
            localStorage.setItem('current_player_data', JSON.stringify(currentPlayer));

            // Update UI
            loadPlayerData();

            // Reload current tab content
            const activeTab = document.querySelector('.nav-tab.active');
            if (activeTab) {
                const tabName = activeTab.textContent.trim();
                // Find tab name and reload content
                if (tabName.includes('نظرة عامة')) showTab('overview');
            }

            closeEditProfileModal();
            showNotification('تم حفظ التغييرات بنجاح!', 'success');

            // Reset photo editing
            editedPhotoData = null;
            photoRotation = 0;
        }

        // ==================== Comprehensive Profile Editor Functions ====================

        // Open Comprehensive Profile Editor
        function openProfileEditor() {
            try {
                populateEditorFields();
                document.getElementById('profileEditorModal').style.display = 'flex';
                showEditorTab('personal');
            } catch (error) {
                console.error('Error opening profile editor:', error);
                // Fallback: show modal anyway
                document.getElementById('profileEditorModal').style.display = 'flex';
                showEditorTab('personal');
            }
        }

        function closeProfileEditor() {
            document.getElementById('profileEditorModal').style.display = 'none';
        }

        // Populate editor fields with current data
        function populateEditorFields() {
            // Personal Information
            document.getElementById('editFirstNameComp').value = currentPlayer.firstName || '';
            document.getElementById('editFatherNameComp').value = currentPlayer.fatherName || '';
            document.getElementById('editFamilyNameComp').value = currentPlayer.familyName || '';
            document.getElementById('editNationalIdComp').value = currentPlayer.nationalId || '';
            document.getElementById('editBirthDateComp').value = currentPlayer.birthDate || '';
            document.getElementById('editPersonalPhoneComp').value = currentPlayer.personalPhone || '';
            document.getElementById('editCategoryComp').value = currentPlayer.category || '';
            document.getElementById('editPositionComp').value = currentPlayer.position || '';
            document.getElementById('editPreferredFootComp').value = currentPlayer.preferredFoot || '';
            document.getElementById('editHeightComp').value = currentPlayer.height || '';
            document.getElementById('editWeightComp').value = currentPlayer.weight || '';
            document.getElementById('editGuardianNameComp').value = currentPlayer.guardianName || '';
            document.getElementById('editGuardianPhoneComp').value = currentPlayer.guardianPhone || '';
            document.getElementById('editRelationshipComp').value = currentPlayer.relationship || '';
            document.getElementById('editBioComp').value = currentPlayer.bio || '';

            // Social Media
            if (currentPlayer.socialMedia) {
                document.getElementById('editInstagram').value = currentPlayer.socialMedia.instagram || '';
                document.getElementById('editTwitter').value = currentPlayer.socialMedia.twitter || '';
                document.getElementById('editTiktok').value = currentPlayer.socialMedia.tiktok || '';
                document.getElementById('editSnapchat').value = currentPlayer.socialMedia.snapchat || '';
                document.getElementById('editYoutube').value = currentPlayer.socialMedia.youtube || '';
                document.getElementById('editFacebook').value = currentPlayer.socialMedia.facebook || '';
                document.getElementById('editWhatsapp').value = currentPlayer.socialMedia.whatsapp || '';
            }

            // Update preview images
            document.getElementById('currentPlayerPhotoComp').src = currentPlayer.avatar;

            // Update computed fields
            document.getElementById('previewRating').textContent = currentPlayer.rating;
            document.getElementById('previewLoyalty').textContent = currentPlayer.loyaltyPoints;
            document.getElementById('previewAttendance').textContent = currentPlayer.stats.attendanceRate + '%';
            document.getElementById('previewMatches').textContent = currentPlayer.stats.matchesPlayed;

            // Auto-refresh preview if it's visible
            if (document.getElementById('previewTab').style.display !== 'none') {
                setTimeout(refreshPreview, 100);
            }

            // Populate privacy settings
            populatePrivacySettings();

            // Calculate and display market value
            calculateMarketValue();

            // Generate QR code if needed
            if (!currentPlayer.qrCode.generated) {
                generateQRCode();
            } else {
                displayExistingQRCode();
            }

            // Add event listeners for live preview updates
            addLivePreviewListeners();
        }

        // Populate privacy settings
        function populatePrivacySettings() {
            if (currentPlayer.privacySettings) {
                document.getElementById('showBio').checked = currentPlayer.privacySettings.showBio;
                document.getElementById('showMarketValue').checked = currentPlayer.privacySettings.showMarketValue;
                document.getElementById('showQRCode').checked = currentPlayer.privacySettings.showQRCode;
                document.getElementById('showPersonalInfo').checked = currentPlayer.privacySettings.showPersonalInfo;
                document.getElementById('showStats').checked = currentPlayer.privacySettings.showStats;
                document.getElementById('showGallery').checked = currentPlayer.privacySettings.showGallery;
                document.getElementById('showSocialMedia').checked = currentPlayer.privacySettings.showSocialMedia;

                // Individual social media settings
                if (currentPlayer.privacySettings.socialMediaVisibility) {
                    document.getElementById('showInstagram').checked = currentPlayer.privacySettings.socialMediaVisibility.instagram;
                    document.getElementById('showTwitter').checked = currentPlayer.privacySettings.socialMediaVisibility.twitter;
                    document.getElementById('showTiktok').checked = currentPlayer.privacySettings.socialMediaVisibility.tiktok;
                    document.getElementById('showSnapchat').checked = currentPlayer.privacySettings.socialMediaVisibility.snapchat;
                    document.getElementById('showYoutube').checked = currentPlayer.privacySettings.socialMediaVisibility.youtube;
                    document.getElementById('showFacebook').checked = currentPlayer.privacySettings.socialMediaVisibility.facebook;
                    document.getElementById('showWhatsapp').checked = currentPlayer.privacySettings.socialMediaVisibility.whatsapp;
                }
            }
        }

        // Enhanced Live Preview Listeners
        function addLivePreviewListeners() {
            const fieldsToWatch = [
                { id: 'editFirstNameComp', type: 'text', required: true },
                { id: 'editFatherNameComp', type: 'text', required: true },
                { id: 'editFamilyNameComp', type: 'text', required: true },
                { id: 'editPositionComp', type: 'select', required: false },
                { id: 'editCategoryComp', type: 'select', required: false },
                { id: 'editBioComp', type: 'textarea', required: false },
                { id: 'editHeightComp', type: 'number', required: false },
                { id: 'editWeightComp', type: 'number', required: false },
                { id: 'editPersonalPhoneComp', type: 'tel', required: false },
                { id: 'editNationalIdComp', type: 'text', required: false },
                { id: 'editInstagram', type: 'url', required: false },
                { id: 'editTwitter', type: 'url', required: false },
                { id: 'editTiktok', type: 'url', required: false },
                { id: 'editSnapchat', type: 'url', required: false },
                { id: 'editYoutube', type: 'url', required: false },
                { id: 'editFacebook', type: 'url', required: false },
                { id: 'editWhatsapp', type: 'url', required: false }
            ];

            // Privacy settings checkboxes
            const privacyFields = [
                'showBio', 'showMarketValue', 'showQRCode', 'showPersonalInfo',
                'showStats', 'showSocialMedia', 'showInstagram', 'showTwitter',
                'showTiktok', 'showSnapchat', 'showYoutube', 'showFacebook', 'showWhatsapp'
            ];

            fieldsToWatch.forEach(fieldConfig => {
                const field = document.getElementById(fieldConfig.id);
                if (field) {
                    // Add input validation and preview update
                    field.addEventListener('input', (e) => {
                        validateField(field, fieldConfig);

                        if (document.getElementById('previewTab').style.display !== 'none') {
                            debouncePreviewUpdate();
                        }
                    });

                    // Add blur validation
                    field.addEventListener('blur', (e) => {
                        validateField(field, fieldConfig);
                    });

                    // Add focus styling
                    field.addEventListener('focus', (e) => {
                        field.classList.add('field-focused');
                    });

                    field.addEventListener('blur', (e) => {
                        field.classList.remove('field-focused');
                    });
                }
            });

            // Add privacy settings listeners
            privacyFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.addEventListener('change', () => {
                        if (document.getElementById('previewTab').style.display !== 'none') {
                            debouncePreviewUpdate();
                        }
                    });
                }
            });
        }

        // Debounced Preview Update
        let previewUpdateTimeout;
        function debouncePreviewUpdate() {
            clearTimeout(previewUpdateTimeout);
            previewUpdateTimeout = setTimeout(refreshPreview, 300);
        }

        // Field Validation
        function validateField(field, config) {
            const value = field.value.trim();

            // Remove previous validation classes
            field.classList.remove('field-valid', 'field-invalid', 'field-warning');

            if (config.required && !value) {
                field.classList.add('field-invalid');
                return false;
            }

            if (!value) {
                return true; // Empty non-required field is valid
            }

            let isValid = true;

            switch (config.type) {
                case 'text':
                    if (config.id.includes('Name')) {
                        // Arabic name validation
                        const arabicNameRegex = /^[\u0600-\u06FF\s]+$/;
                        isValid = arabicNameRegex.test(value);
                    }
                    break;

                case 'tel':
                    // Saudi phone validation
                    isValid = /^(05|5)\d{8}$/.test(value.replace(/[\s-]/g, ''));
                    break;

                case 'number':
                    const num = parseInt(value);
                    if (config.id === 'editHeightComp') {
                        isValid = num >= 100 && num <= 250;
                    } else if (config.id === 'editWeightComp') {
                        isValid = num >= 20 && num <= 200;
                    }
                    break;

                case 'url':
                    if (value.startsWith('http')) {
                        try {
                            new URL(value);
                            isValid = true;
                        } catch {
                            isValid = false;
                        }
                    } else {
                        field.classList.add('field-warning');
                        return true; // Warning but not invalid
                    }
                    break;
            }

            field.classList.add(isValid ? 'field-valid' : 'field-invalid');
            return isValid;
        }

        // Editor Tab Management
        function showEditorTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.editor-tab-content').forEach(tab => {
                tab.style.display = 'none';
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.editor-tab').forEach(btn => {
                btn.classList.remove('active');
                btn.style.background = '#374151';
            });

            // Show selected tab
            document.getElementById(tabName + 'Tab').style.display = 'block';

            // Add active class to clicked button
            event.target.classList.add('active');
            event.target.style.background = 'linear-gradient(135deg, #8B4513, #D2691E)';
        }

        // Enhanced Data Validation System
        function validatePlayerData() {
            const errors = [];

            // Required fields validation
            const firstName = document.getElementById('editFirstNameComp').value.trim();
            const fatherName = document.getElementById('editFatherNameComp').value.trim();
            const familyName = document.getElementById('editFamilyNameComp').value.trim();
            const nationalId = document.getElementById('editNationalIdComp').value.trim();
            const birthDate = document.getElementById('editBirthDateComp').value;
            const personalPhone = document.getElementById('editPersonalPhoneComp').value.trim();

            if (!firstName) errors.push('الاسم الأول مطلوب');
            if (!fatherName) errors.push('اسم الأب مطلوب');
            if (!familyName) errors.push('اسم العائلة مطلوب');

            // Arabic name validation
            const arabicNameRegex = /^[\u0600-\u06FF\s]+$/;
            if (firstName && !arabicNameRegex.test(firstName)) {
                errors.push('الاسم الأول يجب أن يكون باللغة العربية فقط');
            }
            if (fatherName && !arabicNameRegex.test(fatherName)) {
                errors.push('اسم الأب يجب أن يكون باللغة العربية فقط');
            }
            if (familyName && !arabicNameRegex.test(familyName)) {
                errors.push('اسم العائلة يجب أن يكون باللغة العربية فقط');
            }

            // National ID validation (Saudi format)
            if (nationalId && !/^[12]\d{9}$/.test(nationalId)) {
                errors.push('رقم الهوية يجب أن يكون 10 أرقام ويبدأ بـ 1 أو 2');
            }

            // Birth date validation
            if (birthDate) {
                const birthYear = new Date(birthDate).getFullYear();
                const currentYear = new Date().getFullYear();
                const age = currentYear - birthYear;
                if (age < 5 || age > 50) {
                    errors.push('العمر يجب أن يكون بين 5 و 50 سنة');
                }
            }

            // Saudi phone number validation
            if (personalPhone && !/^(05|5)\d{8}$/.test(personalPhone.replace(/[\s-]/g, ''))) {
                errors.push('رقم الجوال يجب أن يكون بالصيغة السعودية (05xxxxxxxx)');
            }

            // Guardian phone validation
            const guardianPhone = document.getElementById('editGuardianPhoneComp').value.trim();
            if (guardianPhone && !/^(05|5)\d{8}$/.test(guardianPhone.replace(/[\s-]/g, ''))) {
                errors.push('رقم جوال ولي الأمر يجب أن يكون بالصيغة السعودية');
            }

            // Physical measurements validation
            const height = parseInt(document.getElementById('editHeightComp').value);
            const weight = parseInt(document.getElementById('editWeightComp').value);

            if (height && (height < 100 || height > 250)) {
                errors.push('الطول يجب أن يكون بين 100 و 250 سم');
            }
            if (weight && (weight < 20 || weight > 200)) {
                errors.push('الوزن يجب أن يكون بين 20 و 200 كغ');
            }

            // Social media URLs validation
            const socialMediaFields = [
                { id: 'editInstagram', name: 'إنستغرام', pattern: /^https?:\/\/(www\.)?instagram\.com\/.+/ },
                { id: 'editTwitter', name: 'تويتر', pattern: /^https?:\/\/(www\.)?(twitter\.com|x\.com)\/.+/ },
                { id: 'editTiktok', name: 'تيك توك', pattern: /^https?:\/\/(www\.)?tiktok\.com\/.+/ },
                { id: 'editYoutube', name: 'يوتيوب', pattern: /^https?:\/\/(www\.)?youtube\.com\/.+/ },
                { id: 'editFacebook', name: 'فيسبوك', pattern: /^https?:\/\/(www\.)?facebook\.com\/.+/ }
            ];

            socialMediaFields.forEach(field => {
                const value = document.getElementById(field.id).value.trim();
                if (value && !field.pattern.test(value)) {
                    errors.push(`رابط ${field.name} غير صحيح`);
                }
            });

            return errors;
        }

        // Basic Encryption for Sensitive Data
        function encryptSensitiveData(data) {
            // Simple base64 encoding with salt (not secure for production)
            const salt = 'academy7c_salt_2024';
            const dataString = JSON.stringify(data);
            const saltedData = salt + dataString + salt;
            return btoa(unescape(encodeURIComponent(saltedData)));
        }

        function decryptSensitiveData(encryptedData) {
            try {
                const salt = 'academy7c_salt_2024';
                const decodedData = decodeURIComponent(escape(atob(encryptedData)));
                const dataString = decodedData.replace(new RegExp(salt, 'g'), '');
                return JSON.parse(dataString);
            } catch (error) {
                console.error('Decryption failed:', error);
                return null;
            }
        }

        // Save All Profile Changes with Enhanced Validation
        function saveAllProfileChanges() {
            try {
                // Validate all data
                const validationErrors = validatePlayerData();
                if (validationErrors.length > 0) {
                    showNotification(`أخطاء في البيانات:\n${validationErrors.join('\n')}`, 'error');
                    return;
                }

                // Get validated data
                const firstName = document.getElementById('editFirstNameComp').value.trim();
                const fatherName = document.getElementById('editFatherNameComp').value.trim();
                const familyName = document.getElementById('editFamilyNameComp').value.trim();

                // Update personal information with sanitization
                currentPlayer.firstName = firstName;
                currentPlayer.fatherName = fatherName;
                currentPlayer.familyName = familyName;
                currentPlayer.nationalId = document.getElementById('editNationalIdComp').value.trim();
                currentPlayer.birthDate = document.getElementById('editBirthDateComp').value;
                currentPlayer.personalPhone = document.getElementById('editPersonalPhoneComp').value.trim().replace(/[\s-]/g, '');
                currentPlayer.category = document.getElementById('editCategoryComp').value;
                currentPlayer.position = document.getElementById('editPositionComp').value;
                currentPlayer.preferredFoot = document.getElementById('editPreferredFootComp').value;
                currentPlayer.height = parseInt(document.getElementById('editHeightComp').value) || 0;
                currentPlayer.weight = parseInt(document.getElementById('editWeightComp').value) || 0;
                currentPlayer.guardianName = document.getElementById('editGuardianNameComp').value.trim();
                currentPlayer.guardianPhone = document.getElementById('editGuardianPhoneComp').value.trim().replace(/[\s-]/g, '');
                currentPlayer.relationship = document.getElementById('editRelationshipComp').value;
                currentPlayer.bio = document.getElementById('editBioComp').value.trim();

                // Add modification timestamp
                currentPlayer.lastModified = new Date().toISOString();
                currentPlayer.modificationHistory = currentPlayer.modificationHistory || [];
                currentPlayer.modificationHistory.push({
                    timestamp: new Date().toISOString(),
                    action: 'profile_update',
                    fields: ['personal_info', 'social_media', 'privacy_settings']
                });

                // Keep only last 50 modification records
                if (currentPlayer.modificationHistory.length > 50) {
                    currentPlayer.modificationHistory = currentPlayer.modificationHistory.slice(-50);
                }

                // Calculate age from birth date
                if (currentPlayer.birthDate) {
                    const birthDate = new Date(currentPlayer.birthDate);
                    const today = new Date();
                    currentPlayer.age = today.getFullYear() - birthDate.getFullYear();
                    const monthDiff = today.getMonth() - birthDate.getMonth();
                    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                        currentPlayer.age--;
                    }
                }

                // Update social media
                if (!currentPlayer.socialMedia) {
                    currentPlayer.socialMedia = {};
                }
                currentPlayer.socialMedia.instagram = document.getElementById('editInstagram').value.trim();
                currentPlayer.socialMedia.twitter = document.getElementById('editTwitter').value.trim();
                currentPlayer.socialMedia.tiktok = document.getElementById('editTiktok').value.trim();
                currentPlayer.socialMedia.snapchat = document.getElementById('editSnapchat').value.trim();
                currentPlayer.socialMedia.youtube = document.getElementById('editYoutube').value.trim();
                currentPlayer.socialMedia.facebook = document.getElementById('editFacebook').value.trim();
                currentPlayer.socialMedia.whatsapp = document.getElementById('editWhatsapp').value.trim();

                // Save privacy settings
                savePrivacySettings();

                // Recalculate market value with new data
                calculateMarketValue();

                // Enhanced Multi-Level Backup System
                const playerDataCopy = JSON.parse(JSON.stringify(currentPlayer));

                // Create backup before saving
                createDataBackup();

                // Save to localStorage with multiple keys for redundancy
                try {
                    localStorage.setItem('player_profile_data', JSON.stringify(currentPlayer));
                    localStorage.setItem('current_player_data', JSON.stringify(currentPlayer));
                    localStorage.setItem(`player_${currentPlayer.id}_data`, JSON.stringify(currentPlayer));
                    localStorage.setItem(`academy_player_${currentPlayer.academicNumber}`, JSON.stringify(currentPlayer));

                    // Encrypted backup for sensitive data
                    const sensitiveData = {
                        nationalId: currentPlayer.nationalId,
                        personalPhone: currentPlayer.personalPhone,
                        guardianPhone: currentPlayer.guardianPhone,
                        birthDate: currentPlayer.birthDate
                    };
                    localStorage.setItem(`encrypted_${currentPlayer.id}`, encryptSensitiveData(sensitiveData));

                    // Save success timestamp
                    localStorage.setItem('last_successful_save', new Date().toISOString());

                } catch (saveError) {
                    console.error('Save error:', saveError);
                    // Attempt recovery
                    if (saveError.name === 'QuotaExceededError') {
                        cleanupOldData();
                        // Retry save
                        localStorage.setItem('player_profile_data', JSON.stringify(currentPlayer));
                        showNotification('تم تنظيف البيانات القديمة وإعادة المحاولة', 'warning');
                    } else {
                        throw saveError;
                    }
                }

                // Enhanced UI Update System
                updateAllUIElements();

                // Update preview if visible
                if (document.getElementById('previewTab').style.display !== 'none') {
                    refreshPreview();
                }

                // Close modal
                closeProfileEditor();

                showNotification('تم حفظ جميع التغييرات بنجاح! تم تحديث القيمة السوقية والباركود. 🎉', 'success');

            } catch (error) {
                console.error('Error saving profile changes:', error);

                // Attempt data recovery
                const recoveredData = attemptDataRecovery();
                if (recoveredData) {
                    showNotification('تم استرداد البيانات من النسخة الاحتياطية', 'warning');
                    currentPlayer = recoveredData;
                    loadPlayerData();
                } else {
                    showNotification('حدث خطأ أثناء حفظ التغييرات. يرجى المحاولة مرة أخرى.', 'error');
                }
            }
        }

        // Create Data Backup
        function createDataBackup() {
            try {
                const backupData = {
                    timestamp: new Date().toISOString(),
                    playerData: JSON.parse(JSON.stringify(currentPlayer)),
                    version: '1.0'
                };

                // Keep last 5 backups
                const existingBackups = JSON.parse(localStorage.getItem('player_backups') || '[]');
                existingBackups.push(backupData);

                if (existingBackups.length > 5) {
                    existingBackups.splice(0, existingBackups.length - 5);
                }

                localStorage.setItem('player_backups', JSON.stringify(existingBackups));
            } catch (error) {
                console.error('Backup creation failed:', error);
            }
        }

        // Attempt Data Recovery
        function attemptDataRecovery() {
            try {
                // Try to recover from backups
                const backups = JSON.parse(localStorage.getItem('player_backups') || '[]');
                if (backups.length > 0) {
                    const latestBackup = backups[backups.length - 1];
                    return latestBackup.playerData;
                }

                // Try alternative storage keys
                const alternativeKeys = [
                    'current_player_data',
                    `player_${currentPlayer.id}_data`,
                    `academy_player_${currentPlayer.academicNumber}`
                ];

                for (const key of alternativeKeys) {
                    const data = localStorage.getItem(key);
                    if (data) {
                        return JSON.parse(data);
                    }
                }

                return null;
            } catch (error) {
                console.error('Data recovery failed:', error);
                return null;
            }
        }

        // Cleanup Old Data
        function cleanupOldData() {
            try {
                // Remove old backup data
                const keysToCheck = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (key.includes('backup_') || key.includes('temp_') || key.includes('old_'))) {
                        keysToCheck.push(key);
                    }
                }

                keysToCheck.forEach(key => {
                    localStorage.removeItem(key);
                });

                // Cleanup old modification history
                if (currentPlayer.modificationHistory && currentPlayer.modificationHistory.length > 20) {
                    currentPlayer.modificationHistory = currentPlayer.modificationHistory.slice(-20);
                }

                console.log(`Cleaned up ${keysToCheck.length} old data entries`);
            } catch (error) {
                console.error('Cleanup failed:', error);
            }
        }

        // Enhanced UI Update System
        function updateAllUIElements() {
            try {
                // Update basic player data
                loadPlayerData();

                // Update social media display
                updateSocialMediaDisplay();

                // Update market value display
                updateMarketValueDisplay();

                // Update QR code if needed
                if (currentPlayer.qrCode.generated) {
                    updateOverviewQRCode();
                } else {
                    // Regenerate QR code if data changed significantly
                    const significantFields = ['firstName', 'familyName', 'academicNumber', 'personalPhone'];
                    const hasSignificantChanges = significantFields.some(field =>
                        currentPlayer[field] !== currentPlayer.originalData?.[field]
                    );

                    if (hasSignificantChanges) {
                        generateQRCode();
                    }
                }

                // Update all tabs content
                updateTabsContent();

                // Update statistics
                updatePlayerStatistics();

                // Trigger visual feedback
                addUpdateAnimation();

            } catch (error) {
                console.error('UI update failed:', error);
                showNotification('تم الحفظ ولكن حدث خطأ في تحديث الواجهة', 'warning');
            }
        }

        // Update Tabs Content
        function updateTabsContent() {
            // Update overview tab
            const overviewContent = getOverviewContent();
            const overviewTab = document.getElementById('overviewContent');
            if (overviewTab) {
                overviewTab.innerHTML = overviewContent;
            }

            // Update subscription tab if visible
            if (document.querySelector('[data-tab="subscription"]')?.classList.contains('active')) {
                showTab('subscription');
            }

            // Update other tabs as needed
            const activeTabs = document.querySelectorAll('.nav-tab.active');
            activeTabs.forEach(tab => {
                const tabName = tab.getAttribute('data-tab');
                if (tabName && tabName !== 'overview') {
                    showTab(tabName);
                }
            });
        }

        // Update Player Statistics
        function updatePlayerStatistics() {
            // Update attendance rate based on recent activity
            const attendanceData = localStorage.getItem('attendance_session_data');
            if (attendanceData) {
                try {
                    const sessionData = JSON.parse(attendanceData);
                    const playerRecord = sessionData.attendanceData?.find(
                        record => record.playerNumber === currentPlayer.academicNumber
                    );

                    if (playerRecord && playerRecord.status === 'present') {
                        currentPlayer.stats.attendanceRate = Math.min(
                            currentPlayer.stats.attendanceRate + 1,
                            100
                        );
                    }
                } catch (error) {
                    console.error('Error updating attendance stats:', error);
                }
            }

            // Update loyalty points display
            const loyaltyElement = document.getElementById('playerLoyalty');
            if (loyaltyElement) {
                loyaltyElement.textContent = currentPlayer.loyaltyPoints;
            }

            // Update rating display
            const ratingElement = document.getElementById('playerRating');
            if (ratingElement) {
                ratingElement.textContent = currentPlayer.rating;
            }
        }

        // Add Update Animation
        function addUpdateAnimation() {
            const mainContainer = document.querySelector('.main-content');
            if (mainContainer) {
                mainContainer.classList.add('update-flash');
                setTimeout(() => {
                    mainContainer.classList.remove('update-flash');
                }, 1000);
            }

            // Animate updated elements
            const elementsToAnimate = [
                '#playerName',
                '#playerRating',
                '#playerLoyalty',
                '#currentMarketValue'
            ];

            elementsToAnimate.forEach(selector => {
                const element = document.querySelector(selector);
                if (element) {
                    element.classList.add('pulse-update');
                    setTimeout(() => {
                        element.classList.remove('pulse-update');
                    }, 2000);
                }
            });
        }

        // Reset All Changes
        function resetAllChanges() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع التغييرات؟')) {
                populateEditorFields();
                showNotification('تم إعادة تعيين جميع التغييرات', 'info');
            }
        }

        // Handle Photo Upload for Comprehensive Editor
        function handlePhotoUploadComp(input, type) {
            const file = input.files[0];
            if (!file) return;

            // Validate file size (5MB max)
            if (file.size > 5 * 1024 * 1024) {
                showNotification('حجم الملف كبير جداً. الحد الأقصى 5MB', 'error');
                return;
            }

            // Validate file type
            if (!file.type.startsWith('image/')) {
                showNotification('يرجى اختيار ملف صورة صحيح', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                if (type === 'player') {
                    currentPlayer.avatar = e.target.result;
                    document.getElementById('currentPlayerPhotoComp').src = e.target.result;
                    showNotification('تم تحديث صورة اللاعب', 'success');
                } else if (type === 'logo') {
                    document.getElementById('academyLogoComp').src = e.target.result;
                    showNotification('تم تحديث شعار الأكاديمية', 'success');
                }
            };
            reader.readAsDataURL(file);
        }

        // Reset to Default Avatar
        function resetToDefaultAvatarComp() {
            const defaultAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiByeD0iNjAiIGZpbGw9IiM4QjQ1MTMiLz4KPHRleHQgeD0iNjAiIHk9IjY1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMzAiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+N0M8L3RleHQ+Cjwvc3ZnPgo=';
            currentPlayer.avatar = defaultAvatar;
            document.getElementById('currentPlayerPhotoComp').src = defaultAvatar;
            showNotification('تم استخدام شعار الأكاديمية كصورة افتراضية', 'info');
        }

        // Reset to Default Logo
        function resetToDefaultLogoComp() {
            const defaultLogo = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiByeD0iMTAiIGZpbGw9IiM4QjQ1MTMiLz4KPHRleHQgeD0iNTAiIHk9IjU1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMzAiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+N0M8L3RleHQ+Cjwvc3ZnPgo=';
            document.getElementById('academyLogoComp').src = defaultLogo;
            showNotification('تم استخدام الشعار الافتراضي', 'info');
        }

        // Enhanced Live Preview Functions
        function refreshPreview() {
            const previewContainer = document.getElementById('livePreview');
            if (!previewContainer) return;

            // Get current form data with validation
            const formData = getFormData();

            // Check privacy settings
            const privacySettings = getCurrentPrivacySettings();

            // Generate preview based on privacy settings
            const previewContent = generatePreviewContent(formData, privacySettings);

            previewContainer.innerHTML = previewContent;

            // Add preview-specific styling
            previewContainer.classList.add('preview-updated');
            setTimeout(() => {
                previewContainer.classList.remove('preview-updated');
            }, 500);
        }

        // Get Form Data
        function getFormData() {
            return {
                firstName: document.getElementById('editFirstNameComp').value || currentPlayer.firstName,
                fatherName: document.getElementById('editFatherNameComp').value || currentPlayer.fatherName,
                familyName: document.getElementById('editFamilyNameComp').value || currentPlayer.familyName,
                position: document.getElementById('editPositionComp').value || currentPlayer.position,
                category: document.getElementById('editCategoryComp').value || currentPlayer.category,
                bio: document.getElementById('editBioComp').value || currentPlayer.bio,
                height: document.getElementById('editHeightComp').value || currentPlayer.height,
                weight: document.getElementById('editWeightComp').value || currentPlayer.weight,
                personalPhone: document.getElementById('editPersonalPhoneComp').value || currentPlayer.personalPhone,
                nationalId: document.getElementById('editNationalIdComp').value || currentPlayer.nationalId
            };
        }

        // Get Current Privacy Settings
        function getCurrentPrivacySettings() {
            return {
                showBio: document.getElementById('showBio')?.checked ?? true,
                showMarketValue: document.getElementById('showMarketValue')?.checked ?? true,
                showQRCode: document.getElementById('showQRCode')?.checked ?? true,
                showPersonalInfo: document.getElementById('showPersonalInfo')?.checked ?? false,
                showStats: document.getElementById('showStats')?.checked ?? true,
                showSocialMedia: document.getElementById('showSocialMedia')?.checked ?? true
            };
        }

        // Generate Preview Content
        function generatePreviewContent(formData, privacySettings) {
            let content = `
                <div class="text-center preview-content">
                    <div class="relative mb-4">
                        <img src="${currentPlayer.avatar}" alt="صورة اللاعب"
                             class="w-24 h-24 rounded-full mx-auto object-cover border-4 border-orange-500">
                        <div class="absolute -bottom-2 -right-2 bg-green-500 w-6 h-6 rounded-full flex items-center justify-center">
                            <i class="fas fa-check text-white text-xs"></i>
                        </div>
                    </div>

                    <h3 class="text-2xl font-bold mb-2 text-white">
                        ${formData.firstName} ${formData.fatherName} ${formData.familyName}
                    </h3>

                    <div class="flex justify-center items-center gap-4 mb-4">
                        <span class="badge bg-blue-600">${formData.position}</span>
                        <span class="badge bg-green-600">${formData.category}</span>
                        <span class="academic-number-badge">${currentPlayer.academicNumber}</span>
                    </div>
            `;

            // Add bio if enabled
            if (privacySettings.showBio && formData.bio) {
                content += `<p class="text-gray-300 mb-4 italic">"${formData.bio}"</p>`;
            }

            // Add social media if enabled
            if (privacySettings.showSocialMedia) {
                content += generateSocialMediaIcons();
            }

            // Add personal info if enabled
            if (privacySettings.showPersonalInfo) {
                content += `
                    <div class="bg-gray-800/50 rounded-lg p-4 mb-4 text-sm">
                        <h4 class="font-bold mb-2 text-orange-400">المعلومات الشخصية</h4>
                        <div class="grid grid-cols-2 gap-2 text-right">
                            ${formData.height ? `<div>الطول: ${formData.height} سم</div>` : ''}
                            ${formData.weight ? `<div>الوزن: ${formData.weight} كغ</div>` : ''}
                            ${formData.personalPhone ? `<div>الجوال: ${formData.personalPhone}</div>` : ''}
                        </div>
                    </div>
                `;
            }

            // Add stats if enabled
            if (privacySettings.showStats) {
                content += `
                    <div class="grid-4 gap-4 mt-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-400">${currentPlayer.rating}</div>
                            <div class="text-sm text-gray-400">التقييم</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-400">${currentPlayer.loyaltyPoints}</div>
                            <div class="text-sm text-gray-400">النقاط</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-400">${currentPlayer.stats.attendanceRate}%</div>
                            <div class="text-sm text-gray-400">الحضور</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-orange-400">${currentPlayer.stats.matchesPlayed}</div>
                            <div class="text-sm text-gray-400">المشاركات</div>
                        </div>
                    </div>
                `;
            }

            // Add market value if enabled
            if (privacySettings.showMarketValue && currentPlayer.marketValue.calculated) {
                content += `
                    <div class="bg-gradient-to-r from-orange-600/20 to-yellow-600/20 rounded-lg p-4 mt-4">
                        <h4 class="font-bold text-yellow-400 mb-2">القيمة السوقية</h4>
                        <div class="text-2xl font-bold text-yellow-300">
                            ${currentPlayer.marketValue.calculated.toLocaleString()} ر.س
                        </div>
                    </div>
                `;
            }

            content += '</div>';
            return content;
        }

        // Generate Social Media Icons
        function generateSocialMediaIcons() {
            const socialMedia = {
                instagram: document.getElementById('editInstagram').value,
                twitter: document.getElementById('editTwitter').value,
                tiktok: document.getElementById('editTiktok').value,
                snapchat: document.getElementById('editSnapchat').value,
                youtube: document.getElementById('editYoutube').value,
                facebook: document.getElementById('editFacebook').value,
                whatsapp: document.getElementById('editWhatsapp').value
            };

            const icons = {
                instagram: { icon: 'fab fa-instagram', color: 'text-pink-500' },
                twitter: { icon: 'fab fa-twitter', color: 'text-blue-400' },
                tiktok: { icon: 'fab fa-tiktok', color: 'text-black' },
                snapchat: { icon: 'fab fa-snapchat', color: 'text-yellow-400' },
                youtube: { icon: 'fab fa-youtube', color: 'text-red-500' },
                facebook: { icon: 'fab fa-facebook', color: 'text-blue-600' },
                whatsapp: { icon: 'fab fa-whatsapp', color: 'text-green-500' }
            };

            let socialHtml = '<div class="flex justify-center gap-3 mb-4">';

            for (const [platform, url] of Object.entries(socialMedia)) {
                if (url && url.trim()) {
                    const iconData = icons[platform];
                    socialHtml += `
                        <a href="${url}" target="_blank" class="social-icon ${iconData.color} text-2xl hover:scale-110 transition-transform">
                            <i class="${iconData.icon}"></i>
                        </a>
                    `;
                }
            }

            socialHtml += '</div>';
            return socialHtml;
        }

        // ==================== Market Value Calculation Functions ====================

        // Calculate Market Value
        function calculateMarketValue() {
            const baseValue = 10000; // Base value in SAR

            // Component calculations (each component contributes to total value)
            const attendanceScore = (currentPlayer.stats.attendanceRate / 100) * 0.25; // 25% weight
            const ratingScore = (currentPlayer.rating / 100) * 0.30; // 30% weight

            // Performance score based on goals, assists, and matches
            const performanceScore = calculatePerformanceScore() * 0.20; // 20% weight

            // Social engagement score (placeholder - can be enhanced)
            const socialScore = calculateSocialEngagementScore() * 0.10; // 10% weight

            // Team spirit score (based on cards and behavior)
            const teamSpiritScore = calculateTeamSpiritScore() * 0.15; // 15% weight

            // Calculate total multiplier
            const totalMultiplier = attendanceScore + ratingScore + performanceScore + socialScore + teamSpiritScore;

            // Calculate final market value
            const marketValue = Math.round(baseValue * (1 + totalMultiplier));

            // Update market value components
            currentPlayer.marketValue.components.attendance = Math.round(baseValue * attendanceScore);
            currentPlayer.marketValue.components.rating = Math.round(baseValue * ratingScore);
            currentPlayer.marketValue.components.performance = Math.round(baseValue * performanceScore);
            currentPlayer.marketValue.components.socialEngagement = Math.round(baseValue * socialScore);
            currentPlayer.marketValue.components.teamSpirit = Math.round(baseValue * teamSpiritScore);

            // Determine trend
            const previousValue = currentPlayer.marketValue.calculated || marketValue;
            let trend = 'stable';
            if (marketValue > previousValue * 1.05) trend = 'up';
            else if (marketValue < previousValue * 0.95) trend = 'down';

            // Update market value data
            currentPlayer.marketValue.calculated = marketValue;
            currentPlayer.marketValue.trend = trend;
            currentPlayer.marketValue.lastUpdated = new Date().toISOString();

            // Update UI
            updateMarketValueDisplay();

            return marketValue;
        }

        // Calculate performance score
        function calculatePerformanceScore() {
            const stats = currentPlayer.stats;
            let score = 0;

            // Goals contribution (40% of performance)
            if (stats.matchesPlayed > 0) {
                const goalRatio = stats.goals / stats.matchesPlayed;
                score += Math.min(goalRatio * 0.4, 0.4);
            }

            // Assists contribution (30% of performance)
            if (stats.matchesPlayed > 0) {
                const assistRatio = stats.assists / stats.matchesPlayed;
                score += Math.min(assistRatio * 0.3, 0.3);
            }

            // Match participation (30% of performance)
            const participationScore = Math.min(stats.matchesPlayed / 30, 1) * 0.3;
            score += participationScore;

            return Math.min(score, 1); // Cap at 1.0
        }

        // Calculate social engagement score
        function calculateSocialEngagementScore() {
            let score = 0;

            // Check if social media is filled
            if (currentPlayer.socialMedia) {
                const socialPlatforms = Object.values(currentPlayer.socialMedia).filter(url => url && url.trim());
                score = Math.min(socialPlatforms.length / 7, 1); // Max score if all 7 platforms are filled
            }

            return score;
        }

        // Calculate team spirit score
        function calculateTeamSpiritScore() {
            const stats = currentPlayer.stats;
            let score = 1.0; // Start with perfect score

            // Deduct for yellow cards
            if (stats.yellowCards > 0) {
                score -= (stats.yellowCards * 0.1);
            }

            // Deduct more for red cards
            if (stats.redCards > 0) {
                score -= (stats.redCards * 0.3);
            }

            // Bonus for achievements
            if (currentPlayer.achievements && currentPlayer.achievements.length > 0) {
                score += (currentPlayer.achievements.length * 0.1);
            }

            return Math.max(Math.min(score, 1), 0); // Keep between 0 and 1
        }

        // Update market value display
        function updateMarketValueDisplay() {
            const marketValue = currentPlayer.marketValue;

            // Update main value
            document.getElementById('currentMarketValue').textContent = `${marketValue.calculated.toLocaleString()} ر.س`;

            // Update trend
            const trendElement = document.getElementById('marketValueTrend');
            const trendIcon = trendElement.querySelector('i');
            const trendText = trendElement.querySelector('span');

            trendElement.className = `flex items-center justify-center gap-2 market-value-trend ${marketValue.trend}`;

            switch (marketValue.trend) {
                case 'up':
                    trendIcon.className = 'fas fa-arrow-up text-green-400';
                    trendText.textContent = 'صاعد';
                    trendText.className = 'text-sm text-green-400';
                    break;
                case 'down':
                    trendIcon.className = 'fas fa-arrow-down text-red-400';
                    trendText.textContent = 'نازل';
                    trendText.className = 'text-sm text-red-400';
                    break;
                default:
                    trendIcon.className = 'fas fa-minus text-gray-400';
                    trendText.textContent = 'ثابت';
                    trendText.className = 'text-sm text-gray-400';
            }

            // Update components
            document.getElementById('attendanceComponent').textContent = `${marketValue.components.attendance.toLocaleString()} ر.س`;
            document.getElementById('ratingComponent').textContent = `${marketValue.components.rating.toLocaleString()} ر.س`;
            document.getElementById('performanceComponent').textContent = `${marketValue.components.performance.toLocaleString()} ر.س`;
            document.getElementById('socialComponent').textContent = `${marketValue.components.socialEngagement.toLocaleString()} ر.س`;
            document.getElementById('teamSpiritComponent').textContent = `${marketValue.components.teamSpirit.toLocaleString()} ر.س`;

            // Add animation to components
            document.querySelectorAll('[id$="Component"]').forEach(el => {
                el.parentElement.classList.add('market-component', 'updated');
                setTimeout(() => {
                    el.parentElement.classList.remove('updated');
                }, 1000);
            });
        }

        // ==================== QR Code Functions ====================

        // Generate QR Code
        function generateQRCode() {
            const qrData = {
                playerNumber: currentPlayer.academicNumber,
                playerName: `${currentPlayer.firstName} ${currentPlayer.familyName}`,
                publicUrl: `https://academy7c.com/player/${currentPlayer.academicNumber}`,
                phone: currentPlayer.personalPhone,
                academy: 'أكاديمية 7C للتدريب الرياضي',
                generated: new Date().toISOString()
            };

            const qrString = JSON.stringify(qrData);

            // Generate QR code
            QRCode.toCanvas(document.createElement('canvas'), qrString, {
                width: 192,
                height: 192,
                color: {
                    dark: '#1a1a1a',
                    light: '#ffffff'
                }
            }, function (error, canvas) {
                if (error) {
                    console.error('QR Code generation error:', error);
                    showNotification('حدث خطأ في إنشاء الباركود', 'error');
                    return;
                }

                // Display QR code
                const container = document.getElementById('qrCodeDisplay');
                container.innerHTML = '';
                container.appendChild(canvas);

                // Update container style
                document.getElementById('qrCodeContainer').classList.add('has-qr');

                // Update player data
                currentPlayer.qrCode.generated = true;
                currentPlayer.qrCode.data = qrString;
                currentPlayer.qrCode.lastGenerated = new Date().toISOString();

                // Update UI
                document.getElementById('qrLastUpdated').textContent = new Date().toLocaleDateString('ar-SA');

                // Update overview QR code
                updateOverviewQRCode();

                showNotification('تم إنشاء الباركود بنجاح!', 'success');
            });
        }

        // Display existing QR code
        function displayExistingQRCode() {
            if (currentPlayer.qrCode.data) {
                QRCode.toCanvas(document.createElement('canvas'), currentPlayer.qrCode.data, {
                    width: 192,
                    height: 192,
                    color: {
                        dark: '#1a1a1a',
                        light: '#ffffff'
                    }
                }, function (error, canvas) {
                    if (!error) {
                        const container = document.getElementById('qrCodeDisplay');
                        container.innerHTML = '';
                        container.appendChild(canvas);
                        document.getElementById('qrCodeContainer').classList.add('has-qr');

                        if (currentPlayer.qrCode.lastGenerated) {
                            document.getElementById('qrLastUpdated').textContent =
                                new Date(currentPlayer.qrCode.lastGenerated).toLocaleDateString('ar-SA');
                        }
                    }
                });
            }
        }

        // Download QR Code
        function downloadQRCode() {
            if (!currentPlayer.qrCode.generated) {
                showNotification('يرجى إنشاء الباركود أولاً', 'warning');
                return;
            }

            QRCode.toCanvas(document.createElement('canvas'), currentPlayer.qrCode.data, {
                width: 512,
                height: 512,
                color: {
                    dark: '#1a1a1a',
                    light: '#ffffff'
                }
            }, function (error, canvas) {
                if (error) {
                    showNotification('حدث خطأ في تحميل الباركود', 'error');
                    return;
                }

                // Create download link
                canvas.toBlob(function(blob) {
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = `${currentPlayer.firstName}_${currentPlayer.familyName}_QRCode.png`;
                    link.click();
                    showNotification('تم تحميل الباركود بنجاح!', 'success');
                });
            });
        }

        // Print QR Code
        function printQRCode() {
            if (!currentPlayer.qrCode.generated) {
                showNotification('يرجى إنشاء الباركود أولاً', 'warning');
                return;
            }

            QRCode.toCanvas(document.createElement('canvas'), currentPlayer.qrCode.data, {
                width: 400,
                height: 400,
                color: {
                    dark: '#000000',
                    light: '#ffffff'
                }
            }, function (error, canvas) {
                if (error) {
                    showNotification('حدث خطأ في طباعة الباركود', 'error');
                    return;
                }

                const printWindow = window.open('', '_blank');
                const qrDataUrl = canvas.toDataURL();

                printWindow.document.write(`
                    <html>
                    <head>
                        <title>باركود اللاعب - ${currentPlayer.firstName} ${currentPlayer.familyName}</title>
                        <style>
                            body {
                                font-family: Arial, sans-serif;
                                direction: rtl;
                                text-align: center;
                                margin: 20px;
                                background: white;
                                color: black;
                            }
                            .qr-container {
                                border: 2px solid #8B4513;
                                padding: 20px;
                                margin: 20px auto;
                                width: fit-content;
                                border-radius: 10px;
                            }
                            .player-info {
                                margin: 20px 0;
                                font-size: 18px;
                            }
                            .academy-logo {
                                font-size: 24px;
                                font-weight: bold;
                                color: #8B4513;
                                margin-bottom: 10px;
                            }
                            @media print {
                                body { margin: 0; }
                                .no-print { display: none; }
                            }
                        </style>
                    </head>
                    <body>
                        <div class="academy-logo">أكاديمية 7C للتدريب الرياضي</div>
                        <div class="qr-container">
                            <img src="${qrDataUrl}" alt="QR Code" style="width: 400px; height: 400px;">
                            <div class="player-info">
                                <h2>${currentPlayer.firstName} ${currentPlayer.familyName}</h2>
                                <p>رقم اللاعب: ${currentPlayer.academicNumber}</p>
                                <p>تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-SA')}</p>
                            </div>
                        </div>
                        <p style="font-size: 14px; color: #666;">
                            امسح الباركود للوصول إلى الملف الشخصي للاعب
                        </p>
                    </body>
                    </html>
                `);

                printWindow.document.close();
                printWindow.print();
                showNotification('تم إرسال الباركود للطباعة!', 'success');
            });
        }

        // ==================== Privacy Settings Functions ====================

        // Toggle all social media visibility
        function toggleAllSocialMedia(checkbox) {
            const socialToggles = document.querySelectorAll('.social-toggle');
            const isChecked = checkbox.checked;

            socialToggles.forEach(toggle => {
                toggle.checked = isChecked;
            });

            // Update individual container visibility
            const container = document.getElementById('socialMediaIndividual');
            container.style.opacity = isChecked ? '1' : '0.5';
            container.style.pointerEvents = isChecked ? 'auto' : 'none';
        }

        // Save privacy settings
        function savePrivacySettings() {
            currentPlayer.privacySettings = {
                showSocialMedia: document.getElementById('showSocialMedia').checked,
                showBio: document.getElementById('showBio').checked,
                showMarketValue: document.getElementById('showMarketValue').checked,
                showQRCode: document.getElementById('showQRCode').checked,
                showPersonalInfo: document.getElementById('showPersonalInfo').checked,
                showStats: document.getElementById('showStats').checked,
                showGallery: document.getElementById('showGallery').checked,
                socialMediaVisibility: {
                    instagram: document.getElementById('showInstagram').checked,
                    twitter: document.getElementById('showTwitter').checked,
                    tiktok: document.getElementById('showTiktok').checked,
                    snapchat: document.getElementById('showSnapchat').checked,
                    youtube: document.getElementById('showYoutube').checked,
                    facebook: document.getElementById('showFacebook').checked,
                    whatsapp: document.getElementById('showWhatsapp').checked
                }
            };

            return currentPlayer.privacySettings;
        }

        // ==================== Public Player Page Creation ====================

        // Helper functions for public page generation
        function generatePublicSocialMediaHTML(currentPlayer, privacy) {
            if (!privacy.showSocialMedia || !currentPlayer.socialMedia) return '';

            const socialMedia = currentPlayer.socialMedia;
            const socialVisibility = privacy.socialMediaVisibility || {};
            const icons = {
                instagram: { icon: 'fab fa-instagram', color: '#E4405F' },
                twitter: { icon: 'fab fa-twitter', color: '#1DA1F2' },
                tiktok: { icon: 'fab fa-tiktok', color: '#000000' },
                snapchat: { icon: 'fab fa-snapchat', color: '#FFFC00' },
                youtube: { icon: 'fab fa-youtube', color: '#FF0000' },
                facebook: { icon: 'fab fa-facebook', color: '#1877F2' },
                whatsapp: { icon: 'fab fa-whatsapp', color: '#25D366' }
            };

            let socialHtml = '';
            let hasAnySocial = false;

            for (const [platform, url] of Object.entries(socialMedia)) {
                if (url && url.trim() && socialVisibility[platform] !== false) {
                    hasAnySocial = true;
                    const iconData = icons[platform];
                    const href = platform === 'whatsapp' ? `https://wa.me/${url.replace(/[^0-9]/g, '')}` : url;
                    socialHtml += `
                        <a href="${href}" target="_blank"
                           style="color: ${iconData.color}; font-size: 2rem; margin: 0 0.5rem; transition: transform 0.3s;"
                           onmouseover="this.style.transform='scale(1.2)'"
                           onmouseout="this.style.transform='scale(1)'">
                            <i class="${iconData.icon}"></i>
                        </a>
                    `;
                }
            }

            if (hasAnySocial) {
                return `
                    <div class="text-center mb-6">
                        <h3 class="text-xl font-bold mb-3">تابعني على وسائل التواصل</h3>
                        <div class="flex justify-center items-center flex-wrap gap-4">
                            ${socialHtml}
                        </div>
                    </div>
                `;
            }
            return '';
        }

        function generatePublicMarketValueHTML(currentPlayer, privacy) {
            if (!privacy.showMarketValue || !currentPlayer.marketValue?.calculated) return '';

            const marketValue = currentPlayer.marketValue;
            const trendIcon = marketValue.trend === 'up' ? '📈' : marketValue.trend === 'down' ? '📉' : '➖';
            const trendText = marketValue.trend === 'up' ? 'صاعد' : marketValue.trend === 'down' ? 'نازل' : 'ثابت';
            const trendColor = marketValue.trend === 'up' ? '#10B981' : marketValue.trend === 'down' ? '#EF4444' : '#6B7280';

            return `
                <div class="text-center mb-6 p-4 bg-gradient-to-br from-green-600/20 to-blue-600/20 rounded-xl border border-green-500/30">
                    <h3 class="text-xl font-bold mb-3">القيمة السوقية</h3>
                    <div class="text-3xl font-bold text-green-400 mb-2">${marketValue.calculated.toLocaleString()} ر.س</div>
                    <div class="flex items-center justify-center gap-2">
                        <span style="color: ${trendColor};">${trendIcon} ${trendText}</span>
                    </div>
                </div>
            `;
        }

        function generatePublicBioHTML(currentPlayer, privacy) {
            if (!privacy.showBio || !currentPlayer.bio) return '';

            return `
                <div class="text-center mb-6 p-4 bg-white/10 rounded-xl">
                    <h3 class="text-xl font-bold mb-3">نبذة شخصية</h3>
                    <p class="text-gray-200 leading-relaxed">${currentPlayer.bio}</p>
                </div>
            `;
        }

        function generatePublicQRCodeHTML(currentPlayer, privacy) {
            if (!privacy.showQRCode || !currentPlayer.qrCode?.generated) return '';

            return `
                <div class="text-center mb-6">
                    <h3 class="text-xl font-bold mb-3">باركود اللاعب</h3>
                    <div class="w-32 h-32 mx-auto bg-white rounded-lg flex items-center justify-center mb-2" id="publicQRCodeContainer">
                        <div class="text-gray-500 text-sm">باركود اللاعب</div>
                    </div>
                    <p class="text-xs opacity-50">امسح الباركود للوصول السريع</p>
                </div>
            `;
        }

        function generatePublicAchievementsHTML(currentPlayer) {
            const achievements = currentPlayer.achievements || ['🏆 أفضل لاعب في الشهر', '⭐ حضور مثالي', '⚽ هداف البطولة', '🤝 روح الفريق'];
            return achievements.map(achievement => `<span class="achievement">${achievement}</span>`).join('');
        }

        // Enhanced Public Stats HTML
        function generatePublicStatsHTML(currentPlayer, privacy) {
            if (!privacy.showStats) return '';

            return `
                <div class="stats-section mb-6">
                    <h3 class="text-xl font-bold mb-4 text-center">الإحصائيات التفصيلية</h3>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <div class="stat-item">
                            <div class="text-2xl font-bold text-blue-400">${currentPlayer.stats.matchesPlayed}</div>
                            <div class="text-sm opacity-75">المشاركات</div>
                        </div>
                        <div class="stat-item">
                            <div class="text-2xl font-bold text-green-400">${currentPlayer.stats.goals}</div>
                            <div class="text-sm opacity-75">الأهداف</div>
                        </div>
                        <div class="stat-item">
                            <div class="text-2xl font-bold text-yellow-400">${currentPlayer.stats.assists}</div>
                            <div class="text-sm opacity-75">التمريرات الحاسمة</div>
                        </div>
                        <div class="stat-item">
                            <div class="text-2xl font-bold text-purple-400">${currentPlayer.stats.attendanceRate}%</div>
                            <div class="text-sm opacity-75">معدل الحضور</div>
                        </div>
                        <div class="stat-item">
                            <div class="text-2xl font-bold text-red-400">${currentPlayer.stats.yellowCards || 0}</div>
                            <div class="text-sm opacity-75">البطاقات الصفراء</div>
                        </div>
                        <div class="stat-item">
                            <div class="text-2xl font-bold text-orange-400">${currentPlayer.stats.redCards || 0}</div>
                            <div class="text-sm opacity-75">البطاقات الحمراء</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Enhanced Public Contact HTML
        function generatePublicContactHTML(currentPlayer, privacy) {
            if (!privacy.showPersonalInfo) return '';

            let contactInfo = [];

            if (currentPlayer.personalPhone && privacy.showPersonalInfo) {
                contactInfo.push(`<i class="fas fa-phone"></i> ${currentPlayer.personalPhone}`);
            }

            if (currentPlayer.email && privacy.showPersonalInfo) {
                contactInfo.push(`<i class="fas fa-envelope"></i> ${currentPlayer.email}`);
            }

            if (contactInfo.length === 0) return '';

            return `
                <div class="contact-section mb-6">
                    <h3 class="text-xl font-bold mb-4 text-center">معلومات التواصل</h3>
                    <div class="bg-gray-800/50 rounded-lg p-4">
                        ${contactInfo.map(info => `<div class="mb-2">${info}</div>`).join('')}
                    </div>
                </div>
            `;
        }

        // Enhanced Performance Chart HTML
        function generatePerformanceChartHTML(currentPlayer) {
            return `
                <div class="performance-chart mb-6">
                    <h3 class="text-xl font-bold mb-4 text-center">مخطط الأداء</h3>
                    <div class="bg-gray-800/50 rounded-lg p-4">
                        <canvas id="performanceChart" width="400" height="200"></canvas>
                    </div>
                </div>
            `;
        }

        // Enhanced Timeline HTML
        function generateTimelineHTML(currentPlayer) {
            const timeline = [
                { date: '2024-01-15', event: 'انضمام للأكاديمية', icon: 'fas fa-star' },
                { date: '2024-02-20', event: 'أول مشاركة رسمية', icon: 'fas fa-futbol' },
                { date: '2024-03-10', event: 'أول هدف', icon: 'fas fa-trophy' },
                { date: '2024-04-05', event: 'جائزة أفضل لاعب', icon: 'fas fa-medal' }
            ];

            return `
                <div class="timeline-section mb-6">
                    <h3 class="text-xl font-bold mb-4 text-center">المسيرة الرياضية</h3>
                    <div class="timeline">
                        ${timeline.map(item => `
                            <div class="timeline-item flex items-center mb-4">
                                <div class="timeline-icon bg-orange-600 rounded-full p-2 ml-4">
                                    <i class="${item.icon} text-white"></i>
                                </div>
                                <div class="timeline-content">
                                    <div class="font-bold">${item.event}</div>
                                    <div class="text-sm opacity-75">${new Date(item.date).toLocaleDateString('ar-SA')}</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // Enhanced Public Player Page Creation
        function createPublicPlayerPage() {
            const currentDate = new Date().toLocaleDateString('ar-SA');
            const currentTime = new Date().toLocaleTimeString('ar-SA');
            const privacy = currentPlayer.privacySettings || {};

            // Generate unique page ID
            const pageId = `player_${currentPlayer.academicNumber}_${Date.now()}`;

            // Generate all HTML sections with enhanced privacy controls
            const marketValueHTML = generatePublicMarketValueHTML(currentPlayer, privacy);
            const bioHTML = generatePublicBioHTML(currentPlayer, privacy);
            const socialMediaHTML = generatePublicSocialMediaHTML(currentPlayer, privacy);
            const qrCodeHTML = generatePublicQRCodeHTML(currentPlayer, privacy);
            const achievementsHTML = generatePublicAchievementsHTML(currentPlayer);
            const statsHTML = generatePublicStatsHTML(currentPlayer, privacy);
            const contactHTML = generatePublicContactHTML(currentPlayer, privacy);

            // Enhanced metadata for SEO and sharing
            const metaData = {
                title: `${currentPlayer.firstName} ${currentPlayer.familyName} - لاعب أكاديمية 7C`,
                description: `ملف ${currentPlayer.firstName} ${currentPlayer.familyName} الشامل في أكاديمية 7C للتدريب الرياضي. ${currentPlayer.position} - ${currentPlayer.category}`,
                keywords: `أكاديمية 7C, ${currentPlayer.firstName}, ${currentPlayer.familyName}, ${currentPlayer.position}, كرة القدم, تدريب رياضي`,
                image: currentPlayer.avatar,
                url: `https://academy7c.com/player/${currentPlayer.academicNumber}`
            };

            // Build enhanced HTML content with advanced features
            const publicPageContent = `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${metaData.title}</title>
    <meta name="description" content="${metaData.description}">
    <meta name="keywords" content="${metaData.keywords}">
    <meta name="author" content="أكاديمية 7C للتدريب الرياضي">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="${metaData.title}">
    <meta property="og:description" content="${metaData.description}">
    <meta property="og:image" content="${metaData.image}">
    <meta property="og:url" content="${metaData.url}">
    <meta property="og:type" content="profile">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="${metaData.title}">
    <meta name="twitter:description" content="${metaData.description}">
    <meta name="twitter:image" content="${metaData.image}">

    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"><\/script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"><\/script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }

        .player-card {
            background: linear-gradient(135deg, #8B4513, #D2691E);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
            position: relative;
            overflow: hidden;
        }

        .player-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.1;
            pointer-events: none;
        }

        .player-avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            border: 5px solid rgba(255,255,255,0.3);
            object-fit: cover;
            transition: transform 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .player-avatar:hover {
            transform: scale(1.05);
        }

        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(5px);
        }

        .stat-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .achievement {
            background: rgba(255,215,0,0.2);
            border: 1px solid rgba(255,215,0,0.3);
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            display: inline-block;
            margin: 0.5rem;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .achievement:hover {
            background: rgba(255,215,0,0.3);
            transform: scale(1.05);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            text-align: center;
            color: #FFD700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .timeline-item {
            background: rgba(255,255,255,0.05);
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            border-left: 4px solid #D2691E;
        }

        .timeline-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .social-link {
            display: inline-block;
            padding: 0.75rem;
            margin: 0.5rem;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 1.2rem;
        }

        .social-link:hover {
            background: rgba(255,255,255,0.2);
            transform: scale(1.1);
        }

        .fade-in {
            opacity: 1;
            transition: opacity 0.5s ease;
        }

        .pulse {
            transform: scale(1.02);
            transition: transform 0.3s ease;
        }

        /* Loading Spinner */
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(139, 69, 19, 0.3);
            border-top: 4px solid #8B4513;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .player-card {
                margin: 1rem;
                padding: 1.5rem;
            }

            .player-avatar {
                width: 120px;
                height: 120px;
            }

            .container {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="text-center mb-8 fade-in">
            <h1 class="text-4xl font-bold mb-2 text-yellow-400">أكاديمية 7C للتدريب الرياضي</h1>
            <p class="text-lg opacity-75">الملف الشخصي العام للاعب</p>
        </div>

        <!-- Main Player Card -->
        <div class="player-card fade-in">
            <!-- Player Header -->
            <div class="text-center mb-8">
                <div class="relative inline-block">
                    <img src="${currentPlayer.avatar}" alt="صورة اللاعب" class="player-avatar mx-auto mb-4">
                    <div class="absolute -bottom-2 -right-2 bg-green-500 w-8 h-8 rounded-full flex items-center justify-center pulse">
                        <i class="fas fa-check text-white"></i>
                    </div>
                </div>
                <h1 class="text-4xl font-bold mb-2">${currentPlayer.firstName} ${currentPlayer.familyName}</h1>
                <div class="flex justify-center items-center gap-4 mb-4">
                    <span class="bg-blue-600 px-4 py-2 rounded-full font-bold">${currentPlayer.academicNumber}</span>
                    <span class="bg-green-600 px-4 py-2 rounded-full">${currentPlayer.category}</span>
                    <span class="bg-purple-600 px-4 py-2 rounded-full">${currentPlayer.position}</span>
                </div>
                <p class="text-lg opacity-90">⭐ التقييم: ${currentPlayer.rating}/100</p>
            </div>

            <!-- Quick Stats -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                <div class="stat-item">
                    <div class="text-3xl font-bold text-blue-400">${currentPlayer.rating}</div>
                    <div class="text-sm opacity-75">التقييم العام</div>
                </div>
                <div class="stat-item">
                    <div class="text-3xl font-bold text-green-400">${currentPlayer.age}</div>
                    <div class="text-sm opacity-75">العمر</div>
                </div>
                <div class="stat-item">
                    <div class="text-3xl font-bold text-yellow-400">${currentPlayer.loyaltyPoints}</div>
                    <div class="text-sm opacity-75">نقاط الولاء</div>
                </div>
                <div class="stat-item">
                    <div class="text-3xl font-bold text-purple-400">${currentPlayer.stats.attendanceRate}%</div>
                    <div class="text-sm opacity-75">معدل الحضور</div>
                </div>
            </div>

            <!-- Market Value Section -->
            ${marketValueHTML}

            <!-- Bio Section -->
            ${bioHTML}

            <!-- Detailed Stats -->
            ${statsHTML}

            <!-- Contact Information -->
            ${contactHTML}

            <!-- Social Media -->
            ${socialMediaHTML}

            <!-- Timeline -->
            ${generateTimelineHTML(currentPlayer)}

            <!-- Achievements -->
            <div class="text-center mb-8">
                <h3 class="section-title">🏆 الإنجازات والجوائز</h3>
                <div class="achievements">
                    ${achievementsHTML}
                </div>
            </div>

            <!-- QR Code -->
            ${qrCodeHTML}

            <!-- Footer -->
            <div class="text-center border-t border-gray-600 pt-6">
                <img src="https://via.placeholder.com/80x80/8B4513/FFFFFF?text=7C" alt="شعار الأكاديمية" class="w-16 h-16 mx-auto mb-4 rounded-lg">
                <h4 class="text-xl font-bold mb-2">أكاديمية 7C للتدريب الرياضي</h4>
                <p class="text-sm opacity-75 mb-2">تطوير المواهب الرياضية وبناء الشخصية</p>
                <div class="flex justify-center gap-4 mb-4">
                    <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                </div>
                <p class="text-xs opacity-50">تم إنشاء هذا الملف في ${currentDate} الساعة ${currentTime}</p>
                <p class="text-xs opacity-50">ID: ${pageId}</p>
            </div>
        </div>

        <!-- Analytics Section -->
        <div class="text-center mt-8 bg-gray-800/50 rounded-lg p-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <div class="text-2xl font-bold text-blue-400" id="viewCount">1</div>
                    <div class="text-sm opacity-75">عدد المشاهدات</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-green-400" id="shareCount">0</div>
                    <div class="text-sm opacity-75">عدد المشاركات</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-yellow-400" id="lastVisit">الآن</div>
                    <div class="text-sm opacity-75">آخر زيارة</div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"><\/script>
    <script>
        // Enhanced Analytics and Tracking
        class PlayerPageAnalytics {
            constructor(playerNumber) {
                this.playerNumber = playerNumber;
                this.sessionStart = new Date();
                this.init();
            }

            init() {
                this.trackPageView();
                this.trackVisitData();
                this.setupScrollTracking();
                this.setupTimeTracking();
                this.updateLastVisit();
            }

            trackPageView() {
                let views = parseInt(localStorage.getItem(\`player_views_\${this.playerNumber}\`) || '0');
                views++;
                localStorage.setItem(\`player_views_\${this.playerNumber}\`, views.toString());
                document.getElementById('viewCount').textContent = views;

                // Animate view counter
                const viewElement = document.getElementById('viewCount');
                viewElement.classList.add('pulse');
                setTimeout(() => viewElement.classList.remove('pulse'), 2000);
            }

            trackVisitData() {
                const visitData = {
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent,
                    referrer: document.referrer,
                    screenResolution: \`\${screen.width}x\${screen.height}\`,
                    language: navigator.language,
                    sessionId: this.generateSessionId()
                };

                const visits = JSON.parse(localStorage.getItem(\`player_visits_\${this.playerNumber}\`) || '[]');
                visits.push(visitData);

                // Keep only last 100 visits
                if (visits.length > 100) {
                    visits.splice(0, visits.length - 100);
                }

                localStorage.setItem(\`player_visits_\${this.playerNumber}\`, JSON.stringify(visits));
            }

            setupScrollTracking() {
                let maxScroll = 0;
                window.addEventListener('scroll', () => {
                    const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
                    maxScroll = Math.max(maxScroll, scrollPercent);

                    // Track scroll milestones
                    if (scrollPercent >= 50 && !this.scrolledHalf) {
                        this.scrolledHalf = true;
                        this.trackEvent('scroll_50_percent');
                    }
                    if (scrollPercent >= 90 && !this.scrolledMost) {
                        this.scrolledMost = true;
                        this.trackEvent('scroll_90_percent');
                    }
                });
            }

            setupTimeTracking() {
                // Track time spent on page
                setInterval(() => {
                    const timeSpent = Math.round((new Date() - this.sessionStart) / 1000);
                    localStorage.setItem(\`player_time_\${this.playerNumber}\`, timeSpent.toString());

                    // Track engagement milestones
                    if (timeSpent === 30 && !this.engaged30s) {
                        this.engaged30s = true;
                        this.trackEvent('engaged_30_seconds');
                    }
                    if (timeSpent === 120 && !this.engaged2m) {
                        this.engaged2m = true;
                        this.trackEvent('engaged_2_minutes');
                    }
                }, 1000);
            }

            updateLastVisit() {
                const lastVisit = new Date().toLocaleString('ar-SA');
                document.getElementById('lastVisit').textContent = lastVisit;
            }

            trackEvent(eventName) {
                const events = JSON.parse(localStorage.getItem(\`player_events_\${this.playerNumber}\`) || '[]');
                events.push({
                    event: eventName,
                    timestamp: new Date().toISOString()
                });
                localStorage.setItem(\`player_events_\${this.playerNumber}\`, JSON.stringify(events));
            }

            generateSessionId() {
                return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            }
        }

        // Initialize analytics
        const analytics = new PlayerPageAnalytics('${currentPlayer.academicNumber}');

        // Enhanced QR Code Generation
        function generateEnhancedQRCode() {
            const qrContainer = document.getElementById('publicQRCodeContainer');
            if (qrContainer && typeof QRCode !== 'undefined') {
                const qrData = {
                    playerNumber: '${currentPlayer.academicNumber}',
                    playerName: '${currentPlayer.firstName} ${currentPlayer.familyName}',
                    publicUrl: window.location.href,
                    academy: 'أكاديمية 7C للتدريب الرياضي',
                    rating: ${currentPlayer.rating},
                    position: '${currentPlayer.position}',
                    generated: new Date().toISOString()
                };

                QRCode.toCanvas(document.createElement('canvas'), JSON.stringify(qrData), {
                    width: 150,
                    height: 150,
                    margin: 2,
                    color: {
                        dark: '#1a1a1a',
                        light: '#ffffff'
                    },
                    errorCorrectionLevel: 'M'
                }, function (error, canvas) {
                    if (!error) {
                        qrContainer.innerHTML = '';
                        canvas.style.borderRadius = '10px';
                        canvas.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
                        qrContainer.appendChild(canvas);

                        // Add download button
                        const downloadBtn = document.createElement('button');
                        downloadBtn.innerHTML = '<i class="fas fa-download"></i> تحميل الباركود';
                        downloadBtn.className = 'bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg mt-2 transition-all';
                        downloadBtn.onclick = () => downloadQRCode(canvas);
                        qrContainer.appendChild(downloadBtn);
                    }
                });
            }
        }

        // Download QR Code
        function downloadQRCode(canvas) {
            const link = document.createElement('a');
            link.download = \`qr_\${analytics.playerNumber}_\${Date.now()}.png\`;
            link.href = canvas.toDataURL();
            link.click();
            analytics.trackEvent('qr_code_downloaded');
        }

        // Performance Chart
        function createPerformanceChart() {
            const ctx = document.getElementById('performanceChart');
            if (ctx && typeof Chart !== 'undefined') {
                new Chart(ctx, {
                    type: 'radar',
                    data: {
                        labels: ['السرعة', 'القوة', 'التحمل', 'المهارة', 'التكتيك', 'الروح الرياضية'],
                        datasets: [{
                            label: 'الأداء الحالي',
                            data: [${currentPlayer.rating * 0.8}, ${currentPlayer.rating * 0.9}, ${currentPlayer.rating * 0.85}, ${currentPlayer.rating * 0.95}, ${currentPlayer.rating * 0.75}, ${currentPlayer.rating * 0.9}],
                            backgroundColor: 'rgba(139, 69, 19, 0.2)',
                            borderColor: 'rgba(139, 69, 19, 1)',
                            borderWidth: 2,
                            pointBackgroundColor: 'rgba(139, 69, 19, 1)',
                            pointBorderColor: '#fff',
                            pointHoverBackgroundColor: '#fff',
                            pointHoverBorderColor: 'rgba(139, 69, 19, 1)'
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                labels: {
                                    color: 'white'
                                }
                            }
                        },
                        scales: {
                            r: {
                                angleLines: {
                                    color: 'rgba(255, 255, 255, 0.3)'
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.3)'
                                },
                                pointLabels: {
                                    color: 'white'
                                },
                                ticks: {
                                    color: 'white',
                                    backdropColor: 'transparent'
                                }
                            }
                        }
                    }
                });
            }
        }

        // Share Functions
        function shareProfile() {
            if (navigator.share) {
                navigator.share({
                    title: '${currentPlayer.firstName} ${currentPlayer.familyName} - أكاديمية 7C',
                    text: 'شاهد ملف ${currentPlayer.firstName} ${currentPlayer.familyName} في أكاديمية 7C للتدريب الرياضي',
                    url: window.location.href
                }).then(() => {
                    analytics.trackEvent('profile_shared_native');
                    updateShareCount();
                });
            } else {
                // Fallback to copy link
                navigator.clipboard.writeText(window.location.href).then(() => {
                    showNotification('تم نسخ رابط الملف الشخصي!', 'success');
                    analytics.trackEvent('profile_link_copied');
                    updateShareCount();
                });
            }
        }

        function updateShareCount() {
            let shares = parseInt(localStorage.getItem(\`player_shares_\${analytics.playerNumber}\`) || '0');
            shares++;
            localStorage.setItem(\`player_shares_\${analytics.playerNumber}\`, shares.toString());
            document.getElementById('shareCount').textContent = shares;
        }

        // Notification System
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = \`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full\`;

            const colors = {
                'success': 'bg-green-600 text-white',
                'error': 'bg-red-600 text-white',
                'warning': 'bg-yellow-600 text-white',
                'info': 'bg-blue-600 text-white'
            };

            notification.className += \` \${colors[type] || colors.info}\`;
            notification.innerHTML = \`
                <div class="flex items-center gap-2">
                    <i class="fas fa-\${type === 'success' ? 'check' : type === 'error' ? 'times' : type === 'warning' ? 'exclamation' : 'info'}-circle"></i>
                    <span>\${message}</span>
                </div>
            \`;

            document.body.appendChild(notification);

            setTimeout(() => notification.classList.remove('translate-x-full'), 100);
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', function() {
            generateEnhancedQRCode();
            createPerformanceChart();

            // Add share button if supported
            if (navigator.share || navigator.clipboard) {
                const shareBtn = document.createElement('button');
                shareBtn.innerHTML = '<i class="fas fa-share-alt"></i> مشاركة الملف';
                shareBtn.className = 'bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-bold transition-all fixed bottom-4 right-4 z-50';
                shareBtn.onclick = shareProfile;
                document.body.appendChild(shareBtn);
            }

            // Initialize share count
            const shares = parseInt(localStorage.getItem(\`player_shares_\${analytics.playerNumber}\`) || '0');
            document.getElementById('shareCount').textContent = shares;

            // Add fade-in animation to elements
            const elements = document.querySelectorAll('.fade-in');
            elements.forEach((el, index) => {
                setTimeout(() => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });

        // Page visibility tracking
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                analytics.trackEvent('page_hidden');
            } else {
                analytics.trackEvent('page_visible');
            }
        });

        console.log('🚀 تم تحميل الصفحة العامة المتقدمة للاعب ${currentPlayer.firstName} ${currentPlayer.familyName}');
    <\/script>
</body>
</html>`;

            // Save public page
            localStorage.setItem(`public_player_page_${currentPlayer.id}`, publicPageContent);

            // Open public page in new tab
            const newWindow = window.open('', '_blank');
            newWindow.document.write(publicPageContent);
            newWindow.document.close();
        }

        // ==================== Utility Functions ====================

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

            // Set colors based on type
            const colors = {
                'success': 'bg-green-600 text-white',
                'error': 'bg-red-600 text-white',
                'warning': 'bg-yellow-600 text-white',
                'info': 'bg-blue-600 text-white'
            };

            notification.className += ` ${colors[type] || colors.info}`;
            notification.innerHTML = `
                <div class="flex items-center gap-2">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : type === 'warning' ? 'exclamation' : 'info'}-circle"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Load saved preferences on page load
        function loadSavedPreferences() {
            const savedTemplate = localStorage.getItem('player_template');
            const savedTheme = localStorage.getItem('player_color_theme');

            if (savedTemplate) {
                document.getElementById('templateSelector').value = savedTemplate;
                changeTemplate(savedTemplate);
            }

            if (savedTheme) {
                applyColorTheme(savedTheme);
            }
        }

        // Initialize advanced features
        document.addEventListener('DOMContentLoaded', function() {
            loadSavedPreferences();

            // Close export menu when clicking outside
            document.addEventListener('click', function(e) {
                const exportMenu = document.getElementById('exportMenu');
                const exportButton = e.target.closest('[onclick="toggleExportMenu()"]');

                if (!exportButton && !exportMenu.contains(e.target)) {
                    exportMenu.classList.add('hidden');
                }
            });
        });

        // ==================== AI Theme System ====================

        let currentAITheme = 'default';
        let themeExpanded = false;

        function toggleThemeSelector() {
            const selector = document.getElementById('themeSelector');
            themeExpanded = !themeExpanded;

            if (themeExpanded) {
                selector.classList.remove('collapsed');
            } else {
                selector.classList.add('collapsed');
            }
        }

        function changeAITheme(theme) {
            // Remove current theme class
            document.body.classList.remove(`theme-${currentAITheme}`);

            // Add new theme class
            if (theme !== 'default') {
                document.body.classList.add(`theme-${theme}`);
            }

            // Update active theme option
            document.querySelectorAll('.theme-option').forEach(option => {
                option.classList.remove('active');
            });
            document.querySelector(`.theme-option.${theme}`).classList.add('active');

            // Save theme preference
            currentAITheme = theme;
            localStorage.setItem('ai_theme_preference', theme);

            // Show notification
            showNotification(`تم تطبيق ثيم ${getThemeName(theme)}`, 'success');

            // Add theme change animation
            document.body.style.transition = 'all 0.5s ease';
            setTimeout(() => {
                document.body.style.transition = '';
            }, 500);
        }

        function getThemeName(theme) {
            const names = {
                'default': 'الافتراضي',
                'cyber': 'السايبر',
                'neon': 'النيون',
                'ocean': 'المحيط',
                'sunset': 'غروب الشمس',
                'forest': 'الغابة',
                'royal': 'الملكي'
            };
            return names[theme] || theme;
        }

        function loadSavedAITheme() {
            const savedTheme = localStorage.getItem('ai_theme_preference');
            if (savedTheme && savedTheme !== 'default') {
                changeAITheme(savedTheme);
            }
        }

        // Initialize AI theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadSavedAITheme();

            // Add AI loading animation
            const body = document.body;
            body.style.opacity = '0';
            body.style.transform = 'scale(0.95)';

            setTimeout(() => {
                body.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
                body.style.opacity = '1';
                body.style.transform = 'scale(1)';
            }, 100);

            // Simplified loading effect
        });

        // Simplified performance-optimized loading

        console.log('🚀 تم تحميل نظام إدارة ملف اللاعب المتقدم مع الذكاء الاصطناعي!');
    </script>
</body>
</html>
