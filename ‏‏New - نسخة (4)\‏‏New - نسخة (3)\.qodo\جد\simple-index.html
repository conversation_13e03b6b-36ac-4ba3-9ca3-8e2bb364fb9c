<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام 7C CMS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 50px;
            margin: 0;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            max-width: 800px;
            margin: 0 auto;
            backdrop-filter: blur(10px);
        }
        .btn {
            background: #28a745;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 18px;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        .btn-blue { background: #007bff; }
        .btn-blue:hover { background: #0056b3; }
        .btn-purple { background: #6f42c1; }
        .btn-purple:hover { background: #5a32a3; }
        .btn-orange { background: #fd7e14; }
        .btn-orange:hover { background: #e8650e; }
        .status {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏆 نظام 7C CMS</h1>
        <h2>✅ مرحباً بك في نظام إدارة أكاديمية كرة القدم</h2>
        
        <div class="status">
            <h3>📊 حالة النظام</h3>
            <p><strong>الخادم:</strong> Apache يعمل ✅</p>
            <p><strong>المجلد:</strong> C:\xampp\htdocs\7c-cms\ ✅</p>
            <p><strong>الملفات:</strong> 28 ملف منسوخ ✅</p>
        </div>
        
        <div>
            <h3>🚀 الروابط المتاحة:</h3>
            <a href="install.php" class="btn">🔧 معالج التثبيت</a>
            <a href="admin.php" class="btn btn-blue">🎛️ لوحة التحكم</a>
            <a href="login.php" class="btn btn-purple">🔐 تسجيل الدخول</a>
            <a href="test-php.php" class="btn btn-orange">🧪 اختبار PHP</a>
        </div>
        
        <hr style="margin: 30px 0; border: 1px solid rgba(255,255,255,0.3);">
        
        <div>
            <h3>📋 معلومات النظام:</h3>
            <p><strong>الإصدار:</strong> 1.0 CMS</p>
            <p><strong>الاسم الرمزي:</strong> Phoenix</p>
            <p><strong>الرابط الحالي:</strong> <span id="current-url"></span></p>
        </div>
        
        <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
            <p>إذا لم تعمل الروابط أعلاه، تأكد من:</p>
            <p>✅ تشغيل Apache في XAMPP Control Panel</p>
            <p>✅ تفعيل PHP في Apache</p>
            <p>✅ وجود الملفات في المجلد الصحيح</p>
        </div>
    </div>

    <script>
        document.getElementById('current-url').textContent = window.location.href;
        
        // فحص الروابط
        function checkLink(url, element) {
            fetch(url, {method: 'HEAD'})
                .then(response => {
                    if (response.ok) {
                        element.style.borderLeft = '5px solid #28a745';
                        element.title = 'الرابط يعمل ✅';
                    } else {
                        element.style.borderLeft = '5px solid #dc3545';
                        element.title = 'الرابط لا يعمل ❌';
                    }
                })
                .catch(() => {
                    element.style.borderLeft = '5px solid #ffc107';
                    element.title = 'خطأ في الاتصال ⚠️';
                });
        }
        
        // فحص جميع الروابط
        document.querySelectorAll('a[href$=".php"]').forEach(link => {
            checkLink(link.href, link);
        });
    </script>
</body>
</html>
