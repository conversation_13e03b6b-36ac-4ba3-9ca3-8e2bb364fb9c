<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام QR Code - أكاديمية 7C</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #1a1a1a;
            color: #ffffff;
        }
        .card {
            background: #2d2d2d;
            border-radius: 15px;
            padding: 20px;
            margin: 10px 0;
            border: 1px solid #1e40af;
        }
        .btn {
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
        }
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .warning { color: #f59e0b; }
    </style>
</head>
<body>
    <div class="container mx-auto p-6">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-blue-400 mb-4">
                <i class="fas fa-vial text-2xl ml-3"></i>
                اختبار نظام QR Code
            </h1>
            <p class="text-gray-400">اختبار شامل لجميع وظائف نظام QR Code</p>
        </div>

        <!-- Test Controls -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div class="card">
                <h3 class="text-lg font-bold mb-4">
                    <i class="fas fa-database text-blue-400 ml-2"></i>
                    إعداد البيانات التجريبية
                </h3>
                <button onclick="setupTestData()" class="btn w-full">
                    <i class="fas fa-plus ml-2"></i>
                    إنشاء بيانات تجريبية
                </button>
                <button onclick="clearTestData()" class="btn w-full bg-red-600">
                    <i class="fas fa-trash ml-2"></i>
                    مسح البيانات التجريبية
                </button>
            </div>

            <div class="card">
                <h3 class="text-lg font-bold mb-4">
                    <i class="fas fa-qrcode text-green-400 ml-2"></i>
                    اختبار إنشاء QR Code
                </h3>
                <button onclick="testQRGeneration()" class="btn w-full">
                    <i class="fas fa-play ml-2"></i>
                    اختبار الإنشاء
                </button>
                <button onclick="openQRSystem()" class="btn w-full">
                    <i class="fas fa-external-link-alt ml-2"></i>
                    فتح نظام QR Code
                </button>
            </div>

            <div class="card">
                <h3 class="text-lg font-bold mb-4">
                    <i class="fas fa-check-circle text-purple-400 ml-2"></i>
                    اختبار التكامل
                </h3>
                <button onclick="testIntegration()" class="btn w-full">
                    <i class="fas fa-link ml-2"></i>
                    اختبار التكامل
                </button>
                <button onclick="testAttendance()" class="btn w-full">
                    <i class="fas fa-calendar-check ml-2"></i>
                    اختبار الحضور
                </button>
            </div>
        </div>

        <!-- Test Results -->
        <div class="card">
            <h3 class="text-xl font-bold mb-4">
                <i class="fas fa-clipboard-list text-yellow-400 ml-2"></i>
                نتائج الاختبار
            </h3>
            <div id="test-results" class="space-y-3">
                <p class="text-gray-400">لم يتم تشغيل أي اختبارات بعد</p>
            </div>
        </div>

        <!-- System Status -->
        <div class="card">
            <h3 class="text-xl font-bold mb-4">
                <i class="fas fa-info-circle text-blue-400 ml-2"></i>
                حالة النظام
            </h3>
            <div id="system-status" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </div>
    </div>

    <script>
        // ==================== بيانات تجريبية ====================
        const testPlayers = [
            {
                id: 'TEST001',
                firstName: 'أحمد',
                familyName: 'محمد',
                academicNumber: '7C-2024-TEST001',
                age: 16,
                category: 'ناشئين',
                joinDate: '2024-01-15',
                loyaltyPoints: 50,
                subscriptionStatus: 'active'
            },
            {
                id: 'TEST002',
                firstName: 'سارة',
                familyName: 'علي',
                academicNumber: '7C-2024-TEST002',
                age: 14,
                category: 'براعم',
                joinDate: '2024-02-01',
                loyaltyPoints: 30,
                subscriptionStatus: 'active'
            },
            {
                id: 'TEST003',
                firstName: 'محمد',
                familyName: 'خالد',
                academicNumber: '7C-2024-TEST003',
                age: 18,
                category: 'شباب',
                joinDate: '2024-01-20',
                loyaltyPoints: 75,
                subscriptionStatus: 'active'
            }
        ];

        // ==================== إعداد البيانات التجريبية ====================
        function setupTestData() {
            try {
                testPlayers.forEach(player => {
                    const key = `academy_player_${player.academicNumber}`;
                    localStorage.setItem(key, JSON.stringify(player));
                });
                
                addTestResult('✅ تم إنشاء البيانات التجريبية بنجاح', 'success');
                updateSystemStatus();
                
                Swal.fire({
                    title: 'تم بنجاح!',
                    text: `تم إنشاء ${testPlayers.length} لاعب تجريبي`,
                    icon: 'success',
                    background: '#2d2d2d',
                    color: '#ffffff'
                });
                
            } catch (error) {
                addTestResult('❌ خطأ في إنشاء البيانات التجريبية: ' + error.message, 'error');
            }
        }

        // ==================== مسح البيانات التجريبية ====================
        function clearTestData() {
            try {
                testPlayers.forEach(player => {
                    const key = `academy_player_${player.academicNumber}`;
                    localStorage.removeItem(key);
                });
                
                // مسح QR codes التجريبية
                const qrCodes = JSON.parse(localStorage.getItem('qr_codes') || '[]');
                const filteredQRCodes = qrCodes.filter(qr => 
                    !qr.academicNumber.includes('TEST')
                );
                localStorage.setItem('qr_codes', JSON.stringify(filteredQRCodes));
                
                addTestResult('🗑️ تم مسح البيانات التجريبية', 'warning');
                updateSystemStatus();
                
                Swal.fire({
                    title: 'تم المسح!',
                    text: 'تم مسح جميع البيانات التجريبية',
                    icon: 'info',
                    background: '#2d2d2d',
                    color: '#ffffff'
                });
                
            } catch (error) {
                addTestResult('❌ خطأ في مسح البيانات: ' + error.message, 'error');
            }
        }

        // ==================== اختبار إنشاء QR Code ====================
        function testQRGeneration() {
            try {
                // التحقق من وجود البيانات التجريبية
                const testPlayer = testPlayers[0];
                const playerKey = `academy_player_${testPlayer.academicNumber}`;
                const playerData = localStorage.getItem(playerKey);
                
                if (!playerData) {
                    addTestResult('⚠️ لا توجد بيانات تجريبية - يرجى إنشاؤها أولاً', 'warning');
                    return;
                }
                
                // محاكاة إنشاء QR Code
                const qrData = {
                    type: 'academy_player',
                    academicNumber: testPlayer.academicNumber,
                    playerName: testPlayer.firstName + ' ' + testPlayer.familyName,
                    category: testPlayer.category,
                    joinDate: testPlayer.joinDate,
                    timestamp: new Date().toISOString()
                };
                
                // حفظ QR Code
                const qrCodes = JSON.parse(localStorage.getItem('qr_codes') || '[]');
                const existingIndex = qrCodes.findIndex(qr => qr.academicNumber === testPlayer.academicNumber);
                
                if (existingIndex >= 0) {
                    qrCodes[existingIndex] = {
                        academicNumber: testPlayer.academicNumber,
                        playerName: qrData.playerName,
                        category: testPlayer.category,
                        createdAt: new Date().toISOString(),
                        qrString: JSON.stringify(qrData)
                    };
                } else {
                    qrCodes.push({
                        academicNumber: testPlayer.academicNumber,
                        playerName: qrData.playerName,
                        category: testPlayer.category,
                        createdAt: new Date().toISOString(),
                        qrString: JSON.stringify(qrData)
                    });
                }
                
                localStorage.setItem('qr_codes', JSON.stringify(qrCodes));
                
                addTestResult('✅ تم اختبار إنشاء QR Code بنجاح للاعب: ' + qrData.playerName, 'success');
                updateSystemStatus();
                
            } catch (error) {
                addTestResult('❌ خطأ في اختبار إنشاء QR Code: ' + error.message, 'error');
            }
        }

        // ==================== اختبار التكامل ====================
        function testIntegration() {
            try {
                let passedTests = 0;
                let totalTests = 0;
                
                // اختبار 1: localStorage
                totalTests++;
                if (typeof(Storage) !== "undefined") {
                    addTestResult('✅ localStorage متاح', 'success');
                    passedTests++;
                } else {
                    addTestResult('❌ localStorage غير متاح', 'error');
                }
                
                // اختبار 2: QRCode library
                totalTests++;
                if (typeof QRCode !== 'undefined') {
                    addTestResult('✅ مكتبة QRCode محملة', 'success');
                    passedTests++;
                } else {
                    addTestResult('❌ مكتبة QRCode غير محملة', 'error');
                }
                
                // اختبار 3: Html5Qrcode library
                totalTests++;
                if (typeof Html5Qrcode !== 'undefined') {
                    addTestResult('✅ مكتبة Html5Qrcode محملة', 'success');
                    passedTests++;
                } else {
                    addTestResult('❌ مكتبة Html5Qrcode غير محملة', 'error');
                }
                
                // اختبار 4: SweetAlert2
                totalTests++;
                if (typeof Swal !== 'undefined') {
                    addTestResult('✅ مكتبة SweetAlert2 محملة', 'success');
                    passedTests++;
                } else {
                    addTestResult('❌ مكتبة SweetAlert2 غير محملة', 'error');
                }
                
                // اختبار 5: Camera API
                totalTests++;
                if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                    addTestResult('✅ Camera API متاح', 'success');
                    passedTests++;
                } else {
                    addTestResult('⚠️ Camera API قد لا يكون متاحاً (HTTPS مطلوب)', 'warning');
                }
                
                const successRate = Math.round((passedTests / totalTests) * 100);
                addTestResult(`📊 معدل نجاح الاختبارات: ${successRate}% (${passedTests}/${totalTests})`, 
                    successRate >= 80 ? 'success' : 'warning');
                
            } catch (error) {
                addTestResult('❌ خطأ في اختبار التكامل: ' + error.message, 'error');
            }
        }

        // ==================== اختبار الحضور ====================
        function testAttendance() {
            try {
                const testPlayer = testPlayers[0];
                const today = new Date().toISOString().split('T')[0];
                
                // محاكاة تسجيل حضور
                const attendanceRecord = {
                    playerId: testPlayer.academicNumber,
                    playerName: testPlayer.firstName + ' ' + testPlayer.familyName,
                    status: 'present',
                    timestamp: new Date(),
                    method: 'qr_code_test',
                    location: 'أكاديمية 7C'
                };
                
                // حفظ في localStorage
                const attendanceKey = `qr_attendance_${today}`;
                const todayAttendance = JSON.parse(localStorage.getItem(attendanceKey) || '[]');
                
                // إزالة السجل السابق إن وجد
                const filteredAttendance = todayAttendance.filter(record => 
                    record.playerId !== testPlayer.academicNumber
                );
                
                filteredAttendance.push(attendanceRecord);
                localStorage.setItem(attendanceKey, JSON.stringify(filteredAttendance));
                
                addTestResult('✅ تم اختبار تسجيل الحضور للاعب: ' + attendanceRecord.playerName, 'success');
                updateSystemStatus();
                
            } catch (error) {
                addTestResult('❌ خطأ في اختبار الحضور: ' + error.message, 'error');
            }
        }

        // ==================== فتح نظام QR Code ====================
        function openQRSystem() {
            window.open('qr-code-system.html', '_blank');
        }

        // ==================== إضافة نتيجة اختبار ====================
        function addTestResult(message, type) {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            
            const resultElement = document.createElement('div');
            resultElement.className = `p-3 rounded-lg border-l-4 ${
                type === 'success' ? 'bg-green-900 border-green-500' :
                type === 'error' ? 'bg-red-900 border-red-500' :
                type === 'warning' ? 'bg-yellow-900 border-yellow-500' :
                'bg-blue-900 border-blue-500'
            }`;
            
            resultElement.innerHTML = `
                <div class="flex justify-between items-start">
                    <span class="${type}">${message}</span>
                    <span class="text-xs text-gray-400">${timestamp}</span>
                </div>
            `;
            
            // إضافة في المقدمة
            if (resultsDiv.firstChild && resultsDiv.firstChild.textContent.includes('لم يتم تشغيل')) {
                resultsDiv.innerHTML = '';
            }
            
            resultsDiv.insertBefore(resultElement, resultsDiv.firstChild);
            
            // الاحتفاظ بآخر 10 نتائج فقط
            while (resultsDiv.children.length > 10) {
                resultsDiv.removeChild(resultsDiv.lastChild);
            }
        }

        // ==================== تحديث حالة النظام ====================
        function updateSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            
            // عدد اللاعبين
            let playersCount = 0;
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('academy_player_')) {
                    playersCount++;
                }
            }
            
            // عدد QR Codes
            const qrCodes = JSON.parse(localStorage.getItem('qr_codes') || '[]');
            
            // عدد عمليات المسح اليوم
            const today = new Date().toISOString().split('T')[0];
            const scanHistory = JSON.parse(localStorage.getItem('qr_scan_history') || '[]');
            const todayScans = scanHistory.filter(scan => scan.scanTime.startsWith(today));
            
            statusDiv.innerHTML = `
                <div class="bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold mb-2">📊 إحصائيات البيانات</h4>
                    <p>اللاعبين: ${playersCount}</p>
                    <p>رموز QR: ${qrCodes.length}</p>
                    <p>مسح اليوم: ${todayScans.length}</p>
                </div>
                <div class="bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold mb-2">🔧 حالة النظام</h4>
                    <p class="success">✅ النظام يعمل بشكل طبيعي</p>
                    <p>آخر تحديث: ${new Date().toLocaleTimeString('ar-SA')}</p>
                </div>
            `;
        }

        // ==================== تحميل الصفحة ====================
        document.addEventListener('DOMContentLoaded', function() {
            updateSystemStatus();
            addTestResult('🚀 تم تحميل صفحة الاختبار بنجاح', 'success');
        });
    </script>
</body>
</html>
